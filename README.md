# NODE v2.0.0

## 平台简介

* 采用前后端分离的模式。
* 后端采用Spring Boot、Spring Cloud & Alibaba。
* 注册中心、配置中心选型Nacos，权限认证使用Redis。
* 流量控制框架选型Sentinel，分布式事务选型Seata。
* 
* 后端：Spring Cloud Alibaba版本：2021.0.5.0、   Spring Boot版本：2.3.7.RELEASE、   JDK版本：1.8
* 前端：Vue 3.3.4、  Pinia：2.1.6、  Echarts：5.4.3、   Element-plus：2.3.14
* 数据库：MySQL 5.7   MongoDB: 4.4

## 系统模块

~~~
org.biosino.node     
├── node-gateway         // 网关模块 [8080]
├── node-auth            // 认证中心 [9200]
├── node-api             // 接口模块
│       └── node-api-system                          // 系统接口
├── node-common          // 通用模块
│       └── node-common-core                         // 核心模块
│       └── node-common-datascope                    // 权限范围
│       └── node-common-datasource                   // 多数据源
│       └── node-common-log                          // 日志记录
│       └── node-common-redis                        // 缓存服务
│       └── node-common-seata                        // 分布式事务
│       └── node-common-security                     // 安全模块
│       └── node-common-swagger                      // 系统接口
├── node-modules         // 业务模块
│       └── node-api-service                         // 对外接口 [9215]
│       └── node-app                                 // 门户网站 [9201]
│       └── node-downlaod                            // 数据下载 [9202]
│       └── node-es-index                            // 数据下载 [9203]
│       └── node-file                                // 文件服务 [9304]（实际业务中未使用）
│       └── node-gen                                 // 代码生成 [9205]（实际业务中未使用）
│       └── node-job                                 // 定时任务 [9206]
│       └── node-member                              // 用户服务 [9207]
│       └── node-notification                        // 通知服务 [9208]
│       └── node-qc                                  // 数据审核 [9209]
│       └── node-sftp                                // 数据上传 [9210]
│       └── node-system                              // 系统模块 [9211]
│       └── node-task                                // 同步任务 [9212]
│       └── node-upload                              // 数据提交 [9213]
├── node-visual          // 图形化管理模块
│       └── node-visual-monitor                      // 监控中心 [9100]
├──pom.xml                // 公共依赖
│
├── view-ui         // 前端Vue3代码
│       └── admin   // NODE 管理后台      [81]
│       └── app     // NODE 门户网站      [80]
│       └── qc      // NODE 数据审核      [82]
~~~

## 架构图

https://www.processon.com/v/656ad386392b526090c83ea7

## 测试账号

- 管理员： admin/admin123  

## 数据库
MySQL
- IP：************
- 端口：32526
- 账号：root
- 密码：保密，问罗瑞金
- 一期数据库：node
- 二期数据库：node2

MongoDB
- IP：************
- 端口：31944
- 验证数据库：admin
- 用户名：root
- 密码：保密，问罗瑞金
- 一期数据库：node
- 二期数据库：node2

一期 -> 二期 数据库变化文档
https://gpji0d3vts.feishu.cn/wiki/Cugqw9IEWizFQOkgiSzcnj3Bnch


## 开发资料
二期需求：https://gpji0d3vts.feishu.cn/docx/HU4wdaY2NompWcx2OmqcbKCdn2c

二期项目分析：https://gpji0d3vts.feishu.cn/wiki/T7tfwX06RiUNwYkjfSXcDsFLnVs

一期项目分析：https://gpji0d3vts.feishu.cn/wiki/Psi8wEoUwiaiISkkmnvc4hdAn5e
测试账号：<EMAIL>   / lrj@1998

