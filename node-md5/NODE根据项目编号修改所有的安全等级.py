from datetime import datetime

from pymongo import MongoClient


def find_data_by(collection, field, value):
    rs = mongo_db.get_collection(collection).find({
        field: value
    })
    return [i for i in rs]


def run(proj_no, security, public_date):
    # --------------------
    mongo_db.get_collection("project").update_many({"proj_no": proj_no},
                                                   {"$set": {"security": security, "public_date": public_date}})
    print("已更新 project")

    # --------------------
    mongo_db.get_collection("experiment").update_many({"proj_no": proj_no},
                                                      {"$set": {"security": security,
                                                                "public_date": public_date}})
    print("已更新 experiment")

    # ---------------------
    experiments = find_data_by("experiment", "proj_no", proj_no)
    for experiment in experiments:
        mongo_db.get_collection("run").update_many({"exp_no": experiment["exp_no"]},
                                                   {"$set": {"security": security,
                                                             "public_date": public_date}})
        print(f"已更新 run, exp_no {experiment['exp_no']}")

        # ------------------------
        runs = find_data_by("run", "exp_no", experiment["exp_no"])
        for run in runs:
            mongo_db.get_collection("data").update_many({"run_no": run["run_no"]},
                                                        {"$set": {"security": security,
                                                                  "public_date": public_date}})
            print(f"已更新 data, run_no {run['run_no']}")


if __name__ == "__main__":
    try:
        # mongo = MongoClient("*******************************************")
        # mongo_db = mongo.get_database("node")

        mongo = MongoClient("mongodb://bdpwww:node@2018@mongo-node:27017/node")
        mongo_db = mongo.get_database("node")

        run("OEP003746", "Restricted", datetime(year=2025, month=12, day=31))
        print("更新完成")
    finally:
        mongo.close()
