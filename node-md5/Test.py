import os


def is_not_integrated(file_path):
    _is_not_integrated = False
    if ".gz" in file_path:
        try:
            log_file = file_path + '.log'
            r1 = os.popen('zcat -t "/bdp-picb/node/ftp_home/20170323/<EMAIL>/2._test  R2.fq.gz" 2>&1 | wc -l')
            print(r1.read())
            # 因为 popen 是非阻塞的 所以 必须 阻塞到
            r1.close()

            print("==== {}".format(os.path.exists(log_file)))

            with open(log_file, 'r') as f:
                error_message = f.read()
            if error_message:
                print(f"压缩文件不完整 {file_path}: {error_message}")
                _is_not_integrated = True
            r3 = os.popen('rm -rf {}'.format(log_file))
        except BaseException as e:
            print(f"压缩文件完整校验失败 {file_path}: {e}")
            _is_not_integrated = True
        finally:
            if r1:
                r1.close()
            if r3:
                r3.close()

    if _is_not_integrated:
        print(f"压缩文件不完整 {file_path}")
    return _is_not_integrated


print(is_not_integrated(r"/bdp-picb/node/ftp_home/20170323/<EMAIL>/2.R2.fq.gz"))
