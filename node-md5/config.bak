[rabbitmq]
host = rabbitmq-node-v2
port = 5672
user = guest
pwd = guest
virtual_host = /
heartbeat = 1800
md5_exchange = verification_exchange
md5_result_exchange = verification_result_exchange
md5_queue = verification_queue
md5_result_queue = verification_result_queue
md5_exchange_key = verification_key
md5_result_exchange_key = verification_result_key

[redis]
host = redis-node-v2
port = 6379
passwd = ''

[logging]
file = /md5/access.log

[file]
block_size = 8388608
timeout = 300

