[rabbitmq]
host = ************
port = 31230
user = guest
pwd = guest
virtual_host = node-test
heartbeat = 1800
md5_exchange = node_delay
md5_result_exchange = node_delay
md5_queue = verification_delay_queue
md5_result_queue = verification_result_queue
md5_exchange_key = verification_delay_key
md5_result_exchange_key = verification_result_key

[redis]
host = ************
port = 32641
passwd = ''

[logging]
file = ./access.log

[file]
block_size = 8388608
timeout = 300