import pymongo


if __name__ == '__main__':
    mongo_client = pymongo.MongoClient(host='mongo-node', username='bdpwww', password='node@2018', port=27017, authSource='node')
    db = mongo_client["node"]

    download_data_info = db["download_data_info"]
    data = db["data"]
    data_user_list = ['226UOOJHYBDTXCN7I4BFDVFMIA', '5DTPFU4B7BHELPSCMIFDU6PI64','5QEBUFLORVE27CIUF2EDF2PQQM','7EH3IJEI6BCIHCS5AW3N2ATBMQ','A4UJMMOD2NCSVLDEH5IRQMILAM',
                      'BJURZAV6DVDZHPZHFWTRYFYEUU','CKJV5AVGB5FWZA6GSP7IQU5FEA','GCO4CDE4GZA5BDYWWCLK73DZ44','GVNK5X6MEBD7ZNP7FM4DOQGC2U','JU2O5XVKVFEXPE4BTDVB2EVJQ4',
                      'MOOUBZSD3NA6VIGFDNNWZXBDTA','TEA253TXEBCEXNKXGFZHOV3GAA','UTLY4C7TBRGT7BML4QISV2O7MA','UWIII2YZGRD6JI2ZARSGSTZIAQ','WCDME6QESBDZVJRHSCUOGZDVGU',
                      'YGEI2UKMRBEB3DD6ZBVV2UTRKM','YIHRU2ZAWRE3LNV2MK4N7RXL5Q']

    head = ['data_user', 'dat_no', 'security', 'creator', 'updater']
    with open("/bdp-picb/node/result_file111111.txt", 'a') as result_file:

        for h in head:
            result_file.write(h + '\t')
        result_file.write('\n')
        for data_user in data_user_list:
            doc = download_data_info.find({'data_user': data_user, 'ip': 'ftp'},
                                          {'data_no': 1, '_id': 0})
            for item in doc:
                data_no = item['data_no']
                doc2 = data.find({'dat_no': data_no},
                                 {'dat_no': 1, '_id': 0, 'creator': 1, 'updater': 1, 'security': 1})
                for item2 in doc2:
                    result_file.write(data_user + '\t')
                    result_file.write(str(item2['dat_no']) + '\t')
                    result_file.write(str(item2['security']) + '\t')
                    result_file.write(str(item2['creator']) + '\t')
                    result_file.write(str(item2['updater']) + '\t')
                    result_file.write('\n')




