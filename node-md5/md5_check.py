import configparser
import hashlib
import json
import logging
import math
import multiprocessing as mp
import os
import pika
import threading
import time
from decimal import Decimal


class Config:
    cf = configparser.ConfigParser()
    cf.read(r'./config')

    # redis 配置
    redisHost = cf.get('redis', 'host')
    redisPort = cf.get('redis', 'port')
    redisPasswd = cf.get('redis', 'passwd')

    # rabbitmq 配置
    rabbitmqHost = cf.get('rabbitmq', 'host')
    rabbitmqPort = cf.get('rabbitmq', 'port')
    rabbitmqUser = cf.get('rabbitmq', 'user')
    rabbitmqPwd = cf.get('rabbitmq', 'pwd')
    rabbitmqVirtualHost = cf.get('rabbitmq', 'virtual_host')
    rabbitmqHeartbeat = int(cf.get('rabbitmq', 'heartbeat'))

    md5_queue = cf.get('rabbitmq', 'md5_queue')
    md5_exchange = cf.get('rabbitmq', 'md5_exchange')
    md5_exchange_key = cf.get('rabbitmq', 'md5_exchange_key')

    md5_result_queue = cf.get('rabbitmq', 'md5_result_queue')
    md5_result_exchange = cf.get('rabbitmq', 'md5_result_exchange')
    md5_result_exchange_key = cf.get('rabbitmq', 'md5_result_exchange_key')

    # md5
    block_size = int(cf.get('file', 'block_size'))
    task_timeout = int(cf.get('file', 'timeout'))

    # 日志
    logFormatter = '%(asctime)s - %(process)d - %(threadName)s - %(name)s - %(levelname)s - %(message)s'
    logFile = cf.get('logging', 'file')
    logLevel = logging.INFO

    @staticmethod
    def log(module='access'):
        logger = logging.getLogger(module)
        logger.setLevel(Config.logLevel)
        # 判断logger是否已经添加过handler，是则直接返回logger对象，否则执行handler设定以及addHandler(console_handle)
        if not logger.handlers:
            logging.basicConfig(filename=Config.logFile, level=Config.logLevel,
                                format=Config.logFormatter)
            console_handle = logging.StreamHandler()
            formatter = logging.Formatter(Config.logFormatter)
            console_handle.setFormatter(formatter)
            logger.addHandler(console_handle)
        return logger


class Verification(threading.Thread):
    def __init__(self, file_path, file_md5=None):
        threading.Thread.__init__(self)

        if not os.path.exists(file_path):
            raise ValueError(f'file not exist')
        if not os.path.isfile(file_path):
            raise ValueError(f'file not exist')

        self.file_path = file_path
        self.file_md5 = file_md5
        self.cause = None
        self.stop = False

    def run(self) -> None:
        try:
            # 验证完整性
            self._is_integrated(timeout=Config.task_timeout)
            # 如何没有file_md5或者file_md5等于''
            if self.file_md5 is None or self.file_md5 == '':
                # 验证 md5
                self.file_md5 = self._calc_md5()
        except Exception as e:
            Config.log("MD5_CHECK").error(f"MD5验证失败：{e}, {self.file_path}")
            self.cause = str(e)

    def stop(self):
        self.stop = True

    # 计算单个文件md5
    def _calc_md5(self):

        Config.log('MD5_CHECK').info(f"正在计算文件的MD5值：{self.file_path}")
        start_time = time.time()
        with open(self.file_path, 'rb') as f:
            md5 = hashlib.md5()
            while not self.stop:
                data = f.read(Config.block_size)
                if not data:
                    break
                md5.update(data)
        end_time = time.time()
        Config.log('MD5_CHECK').info(f"生成文件MD5完成：{end_time - start_time}s, {self.file_path}")
        return md5.hexdigest()

    # 判断压缩文件完整性
    def _is_integrated(self, timeout=30 * 60):
        filename = os.path.basename(self.file_path).strip().lower()
        # 不是 gz 且不是 zip 文件 跳过
        if not filename.endswith(".gz") and not filename.endswith(".zip"):
            return
        start_time = time.time()
        Config.log('MD5_CHECK').info(f"正在验证文件完整性：{self.file_path}")
        # 如果是gz类型
        if filename.endswith(".gz"):
            with os.popen(f'timeout {timeout}s zcat -t "{self.file_path}" 2>&1 | wc -l') as r1:
                r1_content = r1.read()
                if r1_content is None:
                    raise ValueError("file can not be extracted")
                rs_content = str(r1_content).strip()
                if rs_content.isdigit():
                    error_message = int(rs_content)
                    if error_message != 0:
                        Config.log('MD5_CHECK').info(f"文件完整性验证失败：{rs_content}, {self.file_path}")
                        raise ValueError("file can not be extracted")
                else:
                    Config.log('MD5_CHECK').info(f"文件完整性验证失败：{rs_content}, {self.file_path}")
                    raise ValueError(f"file integrity validation timeout in {timeout}s")
        # 如果是zip文件
        elif filename.endswith(".zip"):
            command = f'timeout {timeout}s unzip -t -qq "{self.file_path}"'
            return_code = os.system(command)

            if return_code != 0:
                Config.log('MD5_CHECK').info(f"文件完整性验证失败：{return_code}, {self.file_path}")
                raise ValueError("file can not be extracted")
            else:
                Config.log('MD5_CHECK').info(f"文件完整性验证成功：{self.file_path}")

        end_time = time.time()
        Config.log('MD5_CHECK').info(f"文件完整性验证成功：耗时{end_time - start_time}s, {self.file_path}")


class TaskListener:
    def __int__(self):
        pass

    def create_mq_connection(self):
        credential = pika.credentials.PlainCredentials(username=Config.rabbitmqUser,
                                                       password=Config.rabbitmqPwd)
        mq_parameter = pika.ConnectionParameters(host=Config.rabbitmqHost,
                                                 port=Config.rabbitmqPort,
                                                 credentials=credential,
                                                 virtual_host=Config.rabbitmqVirtualHost,
                                                 socket_timeout=5,
                                                 heartbeat=Config.rabbitmqHeartbeat,
                                                 retry_delay=5,
                                                 connection_attempts=3)
        return pika.BlockingConnection(parameters=mq_parameter)

    def send_queue(self, exchange, routing_key, body):
        connection = None
        try:
            connection = self.create_mq_connection()
            channel = connection.channel(channel_number=1)
            channel.basic_publish(exchange=exchange, routing_key=routing_key, body=body)
        finally:
            try:
                if connection:
                    connection.close()
            except Exception as e:
                Config.log("TaskListener").error(e, exc_info=True, stack_info=True)

    # 往md5结果队列发送结果消息
    def send_result_msg(self, id="", status='success', file_type='file', file_path='', cause="", md5_value=""):
        _msg = {
            "id": id,
            "result": status,
            "type": file_type,
            "file_path": file_path,
            "cause": cause,
            "md5_value": md5_value
        }
        Config.log('Monitor').info(f"反馈MD5结果消息：{_msg}")
        self.send_queue(exchange=Config.md5_result_exchange,
                        routing_key=Config.md5_result_exchange_key,
                        body=json.dumps(_msg))

    # 获取到消息后回调
    def callback(self, ch, method, properties, body):
        id = None
        file_path = None
        _type = None
        try:
            Config.log('Monitor').info(f"接收到MD5验证消息：{body}")
            body = json.loads(body.decode())
            id = body['id']
            _type = body['type']
            file_path = body['value']
            file_md5 = body['file_md5']
            if _type != 'file':
                Config.log('Monitor').info(f"接收到MD5验证消息格式错误：{body}")
                return

            start_time = Decimal(str(time.time()))
            v = Verification(file_path=file_path, file_md5=file_md5)
            v.setDaemon(True)
            # 发送分析开始的消息
            self.send_result_msg(id=id, status="checking", file_path=file_path)
            v.start()

            file_size = os.path.getsize(file_path)
            file_size = math.ceil(file_size / 1024 / 1024 / 1024)  # GB
            file_size = file_size if file_size > 1 else 1
            timeout = Config.task_timeout * file_size

            Config.log("TaskListener").info(
                f"正在计算文件MD5, 文件大小：<= {file_size} GB，超时时间：{timeout}s，文件: {file_path}")

            while v.is_alive():
                # 不断的重试，保持连接，防止mq消息断开
                cur_time = Decimal(str(time.time()))
                if cur_time - start_time > timeout:
                    Config.log("TaskListener").info(
                        f"计算文件MD5失败, 文件大小：<= {file_size} GB，超时时间：{timeout}s，实际：{cur_time - start_time}，文件: {file_path}")
                    v.stop()
                    raise ValueError("md5 check timeout")

                if int(cur_time - start_time) % 10 == 0:
                    ch.basic_qos(prefetch_count=1)
                    Config.log("TaskListener").info(f"心跳保持, {v.ident}")

                time.sleep(1)

            if v.cause is not None:
                raise ValueError(v.cause)

            if not v.file_md5 or str(v.file_md5).strip() == "":
                raise ValueError("md5 check error")

            self.send_result_msg(id=id, file_path=file_path, md5_value=v.file_md5)

        except Exception as e1:
            Config.log('Monitor').error(e1, exc_info=True, stack_info=True)
            self.send_result_msg(id=id, status='fail', file_path=file_path, cause=str(e1))
        finally:
            # 收到消息就移除，放到最后，如果长时任务会超时错误
            ch.basic_ack(delivery_tag=method.delivery_tag)

    # 监听md5_queue队列
    def monitor(self):
        connection = None
        while True:
            try:
                # heartbeat 参数重要：
                # 默认是None，代表由服务端确定什么时候断开连接
                # 设置0代表不断开连接
                # 设置一个 >0 的整数代表这么长时间内没有发送心跳就断开连接，
                # 但是要注意 >0 是和服务器“协商”，不一定会用这个值，而是取较小的那个值（服务器默认60s），两次心跳时间没收到心跳，服务器会断开连接。
                Config.log('Monitor').info(' [*****] Creating mq connection ...')
                connection = self.create_mq_connection()

                # 接收 md5 任务消息的 channel
                channel = connection.channel()
                channel.basic_qos(prefetch_count=1)
                channel.queue_declare(queue=Config.md5_queue, durable=True)
                channel.basic_consume(queue=Config.md5_queue, on_message_callback=self.callback, auto_ack=False)
                Config.log('Monitor').info(' [*****] Waiting for messages. To exit press CTRL+C')

                channel.start_consuming()
                Config.log('Monitor').info(' [*****] Ending for messages')
            except Exception as e:
                try:
                    Config.log('Monitor').error(e, exc_info=True, stack_info=True)
                    if connection:
                        connection.close()
                except Exception as e1:
                    Config.log('Monitor').error(e1, exc_info=True, stack_info=True)
                finally:
                    connection = None
                    time.sleep(3)
        Config.log('Monitor').info("*** 消息消费端监听结束... ***")


def main(process_size):
    p = mp.Pool(processes=process_size)
    for i in range(process_size):
        v = TaskListener()
        p.apply_async(v.monitor)
    p.close()
    p.join()


if __name__ == '__main__':
    # 监听verification-queue队列
    # 接收消息格式: {"type":"file","value":"/bdp-picb/node/*/**/***.*","timestamp":"11111111111"}
    # redis存入key:file_path,value:timestamp
    # 根据timestamp判断是否需要计算md5
    # 当timestamp小于等于r_timestamp时,表示任务已过期,不计算；当timestamp大于r_timestamp时,计算md5
    # 相同任务取消之前任务
    # 当相同文件,timestamp不同,并且先前任务未完成时,自动取消先前任务的计算
    # 校验文件完整性
    # 对.gz文件先校验文件完整性,不完整时不计算md5
    # v = TaskListener()
    # v.monitor()
    main(process_size=1)
