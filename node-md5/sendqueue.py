import pika,os,json

class verification():


    def sendQueue(self,body ):
        connection = pika.BlockingConnection(pika.ConnectionParameters(host = "rabbitmq-node"))
        channel = connection.channel()
        channel.queue_declare(queue = "verification_queue",durable = True)
        channel.basic_publish(exchange = "verification_exchange",routing_key = "verification_key",body = body)
        connection.close()
if __name__ == '__main__':
    verification().sendQueue(json.dumps({"type":"file","value":"/bdp-picb/node/data/********/*************.gz","timestamp":"*************"}))

