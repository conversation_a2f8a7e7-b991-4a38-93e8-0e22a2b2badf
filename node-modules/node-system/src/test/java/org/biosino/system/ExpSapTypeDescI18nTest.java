package org.biosino.system;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.mongo.entity.ExpSampleType;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.mongodb.core.MongoTemplate;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/8/19
 */
@SpringBootTest
@Slf4j
public class ExpSapTypeDescI18nTest {

    public static class AuthV3Util {

        /**
         * 添加鉴权相关参数 -
         * appKey : 应用ID
         * salt : 随机值
         * curtime : 当前时间戳(秒)
         * signType : 签名版本
         * sign : 请求签名
         *
         * @param appKey    您的应用ID
         * @param appSecret 您的应用密钥
         * @param paramsMap 请求参数表
         */
        public static void addAuthParams(String appKey, String appSecret, Map<String, Object> paramsMap)
                throws NoSuchAlgorithmException {
            String[] qArray = (String[]) paramsMap.get("q");
            if (qArray == null) {
                qArray = (String[]) paramsMap.get("img");
            }
            StringBuilder q = new StringBuilder();
            for (String item : qArray) {
                q.append(item);
            }
            String salt = UUID.randomUUID().toString();
            String curtime = String.valueOf(System.currentTimeMillis() / 1000);
            String sign = calculateSign(appKey, appSecret, q.toString(), salt, curtime);
            paramsMap.put("appKey", new String[]{appKey});
            paramsMap.put("salt", new String[]{salt});
            paramsMap.put("curtime", new String[]{curtime});
            paramsMap.put("signType", new String[]{"v3"});
            paramsMap.put("sign", new String[]{sign});
        }

        /**
         * 计算鉴权签名 -
         * 计算方式 : sign = sha256(appKey + input(q) + salt + curtime + appSecret)
         *
         * @param appKey    您的应用ID
         * @param appSecret 您的应用密钥
         * @param q         请求内容
         * @param salt      随机值
         * @param curtime   当前时间戳(秒)
         * @return 鉴权签名sign
         */
        public static String calculateSign(String appKey, String appSecret, String q, String salt, String curtime)
                throws NoSuchAlgorithmException {
            String strSrc = appKey + getInput(q) + salt + curtime + appSecret;
            return encrypt(strSrc);
        }

        private static String encrypt(String strSrc) throws NoSuchAlgorithmException {
            byte[] bt = strSrc.getBytes();
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            md.update(bt);
            byte[] bts = md.digest();
            StringBuilder des = new StringBuilder();
            for (byte b : bts) {
                String tmp = (Integer.toHexString(b & 0xFF));
                if (tmp.length() == 1) {
                    des.append("0");
                }
                des.append(tmp);
            }
            return des.toString();
        }

        private static String getInput(String input) {
            if (input == null) {
                return null;
            }
            String result;
            int len = input.length();
            if (len <= 20) {
                result = input;
            } else {
                String startStr = input.substring(0, 10);
                String endStr = input.substring(len - 10, len);
                result = startStr + len + endStr;
            }
            return result;
        }
    }

    public static void main(String[] args) throws NoSuchAlgorithmException {
        String hello = translationEn2Zh("hello");
        System.out.println("有道翻译结果: " + hello);

        String googleHello = googleTranslateEn2Zh("hello");
        System.out.println("谷歌翻译结果: " + googleHello);
    }

    public static String translationEn2Zh(String text) throws NoSuchAlgorithmException {
        if (StrUtil.isBlank(text)) {
            return null;
        }
        // 添加请求参数
        Map<String, Object> params = createRequestParams(text);
        // 添加鉴权相关参数
        AuthV3Util.addAuthParams(APP_KEY, APP_SECRET, params);
        // 请求api服务
        String result = HttpUtil.post("https://openapi.youdao.com/api", params);
        // 打印返回结果
        JSONObject jsonObject = JSON.parseObject(result);
        JSONArray translationArr = jsonObject.getJSONArray("translation");

        try {
            String string = translationArr.getString(0);
            if (StrUtil.isBlank(string)) {
                log.error("未成功翻译：{}", text);
                return null;
            }
            return string;
        } catch (Exception e) {
            log.error("response:{}，未成功翻译：{}", result, text);
        }
        return null;
    }

    /**
     * 使用谷歌翻译API进行英文到中文的翻译
     *
     * @param text 要翻译的英文文本
     * @return 翻译后的中文文本
     */
    public static String googleTranslateEn2Zh(String text) {
        if (StrUtil.isBlank(text)) {
            return null;
        }

        try {

            HashMap<String, Object> params = new HashMap<>();
            params.put("client", "gtx");
            params.put("dt", "t");
            params.put("sl", "en");
            params.put("tl", "zh-CN");
            params.put("q", text);

            String result = HttpUtil.get("http://proxy.wuxie0ne.eu.org/https://translate.googleapis.com/translate_a/single", params, 30 * 1000);
            JSONArray objects = JSON.parseArray(result);
            String string = objects.getJSONArray(0)    // 第一层
                    .getJSONArray(0)    // 第二层
                    .getString(0);// 第三层

            return string;

        } catch (Exception e) {
            e.printStackTrace();
            log.error("谷歌翻译失败，原文：{}，错误：{}", text, e.getMessage());
            return null;
        }
    }


    private static Map<String, Object> createRequestParams(String text) {
        /*
         * note: 将下列变量替换为需要请求的参数
         * 取值参考文档: https://ai.youdao.com/DOCSIRMA/html/%E8%87%AA%E7%84%B6%E8%AF%AD%E8%A8%80%E7%BF%BB%E8%AF%91/API%E6%96%87%E6%A1%A3/%E6%96%87%E6%9C%AC%E7%BF%BB%E8%AF%91%E6%9C%8D%E5%8A%A1/%E6%96%87%E6%9C%AC%E7%BF%BB%E8%AF%91%E6%9C%8D%E5%8A%A1-API%E6%96%87%E6%A1%A3.html
         */
        String q = text;
        String from = "en";
        String to = "zh-CHS";

        return new HashMap<String, Object>() {{
            put("q", new String[]{q});
            put("from", new String[]{from});
            put("to", new String[]{to});
        }};
    }

    private static final String APP_KEY = "076e0eb0d11a2668";     // 您的应用ID
    private static final String APP_SECRET = "BFoImvlh4S6JSAWgQJAT1mVbQ7sTM4Ws";  // 您的应用密钥


    @Data
    public static class ExpTypeDescExcel {
        private String id;

        private String type;

        private String attrId;

        private String attributesName;

        private String description;

        private String zhDescription;
    }

    @Autowired
    private MongoTemplate mongoTemplate;

    @Test
    public void translateExpSapTypeDesc() throws NoSuchAlgorithmException {
        List<ExpSampleType> all = mongoTemplate.findAll(ExpSampleType.class);

        for (ExpSampleType expSampleType : all) {
            List<ExpSampleType.Attributes> attributes = expSampleType.getAttributes();
            for (ExpSampleType.Attributes attribute : attributes) {
                String description = attribute.getDescription();
                if (StrUtil.isBlank(description)) {
                    continue;
                }
                ThreadUtil.safeSleep(1000);
                String zhDesc = translationEn2Zh(description);
                if (StrUtil.isBlank(zhDesc)) {
                    continue;
                }
                attribute.setZhDescription(zhDesc);
            }
            mongoTemplate.save(expSampleType);
        }

    }

    @Test
    public void exportExpSapTypeDesc() {
        List<ExpSampleType> all = mongoTemplate.findAll(ExpSampleType.class);

        ArrayList<ExpTypeDescExcel> list = new ArrayList<>();
        for (ExpSampleType expSampleType : all) {
            List<ExpSampleType.Attributes> attributes = expSampleType.getAttributes();
            for (ExpSampleType.Attributes attribute : attributes) {
                String description = attribute.getDescription();
                if (StrUtil.isBlank(description)) {
                    continue;
                }
                ExpTypeDescExcel excel = new ExpTypeDescExcel();

                excel.setId(expSampleType.getId());
                excel.setType(expSampleType.getType());
                excel.setAttrId(attribute.getId());
                excel.setAttributesName(attribute.getAttributesName());
                excel.setDescription(description);

                list.add(excel);

            }
        }

        ExcelWriter writer = ExcelUtil.getWriter("E:\\IdeaProjects\\node\\node-modules\\node-system\\src\\test\\java\\org\\biosino\\system/exp_sap_desc.xlsx");
        writer.write(list, true);
        writer.close();
    }

    @Test
    public void importExpSapTypeDesc() {
        ExcelReader reader = ExcelUtil.getReader("E:\\IdeaProjects\\node\\node-modules\\node-system\\src\\test\\java\\org\\biosino\\system\\exp_sap_desc（中文）.xlsx");
        List<ExpTypeDescExcel> excels = reader.readAll(ExpTypeDescExcel.class);
        Map<String, ExpTypeDescExcel> attrIdToItem = excels.stream().collect(Collectors.toMap(ExpTypeDescExcel::getAttrId, Function.identity(), (oldItem, newItem) -> oldItem));

        List<ExpSampleType> all = mongoTemplate.findAll(ExpSampleType.class);

        for (ExpSampleType expSampleType : all) {
            List<ExpSampleType.Attributes> attributes = expSampleType.getAttributes();
            for (ExpSampleType.Attributes attribute : attributes) {
                String attrId = attribute.getId();
                if (attrIdToItem.containsKey(attrId)) {
                    String zhDescription = attrIdToItem.get(attrId).getZhDescription();
                    if (StrUtil.isNotBlank(zhDescription)) {
                        attribute.setZhDescription(zhDescription);
                    }
                }
            }
            mongoTemplate.save(expSampleType);
        }
    }

    /**
     * 测试谷歌翻译功能
     */
    @Test
    public void testGoogleTranslate() {
        String testText = "hello world";
        String result = googleTranslateEn2Zh(testText);
        log.info("谷歌翻译测试 - 原文：{}，译文：{}", testText, result);
    }

    /**
     * 使用谷歌翻译批量翻译ExpSampleType描述
     */
    @Test
    public void translateExpSapTypeDescWithGoogle() {
        List<ExpSampleType> all = mongoTemplate.findAll(ExpSampleType.class);
        HashMap<String, String> cacheMap = new HashMap<>();

        for (ExpSampleType expSampleType : all) {
            List<ExpSampleType.Attributes> attributes = expSampleType.getAttributes();
            for (ExpSampleType.Attributes attribute : attributes) {
                String description = attribute.getDescription();
                if (StrUtil.isBlank(description)) {
                    continue;
                }
                if (cacheMap.containsKey(description)) {
                    attribute.setZhDescription(cacheMap.get(description));
                    continue;
                }
                ThreadUtil.safeSleep(1000);
                String zhDesc = googleTranslateEn2Zh(description);
                if (StrUtil.isBlank(zhDesc)) {
                    continue;
                }
                attribute.setZhDescription(zhDesc);
                cacheMap.put(description, zhDesc);
            }
            mongoTemplate.save(expSampleType);
        }
    }

}
