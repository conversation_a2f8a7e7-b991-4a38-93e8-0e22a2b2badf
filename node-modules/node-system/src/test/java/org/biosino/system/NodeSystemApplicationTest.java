package org.biosino.system;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.file.MyFileUtils;
import org.biosino.common.mongo.BiomeCurated;
import org.biosino.common.mongo.entity.Sample;
import org.biosino.system.api.RemoteMemberService;
import org.biosino.system.api.domain.DbCheckLog;
import org.biosino.system.service.impl.fd.FeatureDataWebServiceImpl;
import org.biosino.system.service.meta.DbCheckService;
import org.biosino.system.service.meta.PublishService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.util.Pair;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@SpringBootTest
public class NodeSystemApplicationTest {


    @Autowired
    private DbCheckService dbCheckService;
    @Autowired
    private PublishService publishService;
    @Autowired
    private RemoteMemberService remoteMemberService;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private FeatureDataWebServiceImpl featureDataWebService;

    @Test
    public void checkProjectDB() {
        dbCheckService.checkProjectDB();
    }

    @Test
    public void checkExperimentDB() {
        dbCheckService.checkExperimentDB();
    }

    @Test
    public void checkSampleDB() {
        dbCheckService.checkSampleDB();
    }

    @Test
    public void checkAnalysisDB() {
        dbCheckService.checkAnalysisDB();
    }

    @Test
    public void checkDataDB() {
        dbCheckService.checkDataDB();
    }


    @Test
    public void checkShareDB() {
        dbCheckService.checkShareDB();
    }

    @Test
    public void checkReviewDB() {
        dbCheckService.checkReviewDB();
    }

    @Test
    void name() {
        publishService.updatePublishCreator();
    }

    @Test
    void getNodeMemberEmailList() {
        R<List<String>> r = remoteMemberService.getNodeMemberEmailList("FtpUser", SecurityConstants.INNER);
        List<String> data = r.getData();
        System.out.println(data);
    }

    @Test
    void relatedDataCheck() {
        List<String> list = new ArrayList<>();
        list.add("OED00933226");
        list.add("OED00933227");
        Map<String, List<DbCheckLog>> stringListMap = dbCheckService.relatedDataCheckByDataNos(list);
        System.out.println(stringListMap);
    }

    @Test
    void mashSampleProcess() {
        ExcelReader reader = ExcelUtil.getReader("D:\\Documents\\WeChat Files\\wxid_txw829zp38x122\\FileStorage\\File\\2025-01\\水圈数据治理-v2-标签.xlsx");
        List<Map<String, Object>> lines = reader.readAll();
        HashSet<String> sapIdSet = new HashSet<>();
        BulkOperations operations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, Sample.class);
        List<Pair<Query, Update>> updateList = new ArrayList<>();
        int i = 0;
        for (Map<String, Object> line : lines) {

            String sampleId = String.valueOf(line.get("sample_id"));
            String biomeCurated = String.valueOf(line.get("biome_curated"));
            String tag = String.valueOf(line.get("集成项目"));
            Update update = new Update()
                    .addToSet("source_project").each(tag);
            if (StrUtil.isNotBlank(biomeCurated)) {
                // System.out.println("有空数据");
                update.set("attributes.biome_curated", biomeCurated);
            }
            if (sapIdSet.contains(sampleId)) {
                continue;
            } else {
                sapIdSet.add(sampleId);
            }
            Query query = new Query(Criteria.where("sap_no").is(getNewNo(sampleId)));
            updateList.add(Pair.of(query, update));
            // mongoTemplate.updateFirst(query, update, Sample.class);
            log.info("更新第 {} 个sample", ++i);
        }
        log.info("开始时间：{}", DateUtil.now());
        if (CollUtil.isNotEmpty(updateList)) {
            // operations.updateMulti(updateList);
            // operations.execute();
        }
        log.info("结束时间：{}", DateUtil.now());
    }

    public final static Pattern pattern = Pattern.compile("^([A-Za-z]+)([0-9]{2,8})$");

    public static String getNewNo(String oldNo) {
        if (StrUtil.isBlank(oldNo)) {
            return oldNo;
        }
        Matcher matcher = pattern.matcher(oldNo);

        if (matcher.matches()) {
            String prefix = matcher.group(1);
            String num = matcher.group(2);
            // 补到8位
            String no = StrUtil.padPre(num, 8, '0');
            String newNo = prefix + no;
            return newNo;
        }
        return oldNo;
    }

    @Test
    public void stat() {
        ExcelReader reader = ExcelUtil.getReader("D:\\开发数据文档\\node\\水圈数据治理-v2-标签.xlsx", 1);
        List<BiomeCurated> list = reader.read(0, 1, BiomeCurated.class);
        int order = 0;
        HashSet<String> strings = new HashSet<>();

        for (BiomeCurated biomeCurated : list) {
            if (!strings.contains(biomeCurated.getDataTypeCurated())) {
                ++order;
            }
            strings.add(biomeCurated.getDataTypeCurated());
            biomeCurated.setOrderNum(order);
        }
        // mongoTemplate.insertAll(list);
    }

    @SneakyThrows
    public void updateHmdsSap(String token, MultipartFile file) {
        if (!StrUtil.equals(token, "qwert132134123") || file == null) {
            throw new ServiceException("非法请求");
        }
        File targetFile = FileUtil.file(MyFileUtils.getTempDir(), file.getOriginalFilename());
        FileUtil.mkParentDirs(targetFile);
        file.transferTo(targetFile);

        ExcelReader reader = ExcelUtil.getReader(targetFile);
        List<Map<String, Object>> lines = reader.readAll();
        HashSet<String> sapIdSet = new HashSet<>();

        BulkOperations operations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, Sample.class);

        List<Pair<Query, Update>> updateList = new ArrayList<>();
        int i = 0;
        for (Map<String, Object> line : lines) {
            String sampleId = String.valueOf(line.get("sample_id"));
            String biomeCurated = String.valueOf(line.get("biome_curated"));
            String tag = String.valueOf(line.get("集成项目"));
            Update update = new Update()
                    .addToSet("source_project").each(tag);
            if (StrUtil.isNotBlank(biomeCurated)) {
                // System.out.println("有空数据");
                update.set("attributes.biome_curated", biomeCurated);
            }
            if (sapIdSet.contains(sampleId)) {
                continue;
            } else {
                sapIdSet.add(sampleId);
            }
            Query query = new Query(Criteria.where("sap_no").is(getNewNo(sampleId)));
            updateList.add(Pair.of(query, update));
            // mongoTemplate.updateFirst(query, update, Sample.class);
        }
        log.info("开始时间：{}", DateUtil.now());
        if (CollUtil.isNotEmpty(updateList)) {
            operations.updateMulti(updateList);
            operations.execute();
        }
        log.info("结束时间：{}", DateUtil.now());
        reader.close();

        ExcelReader sheet2 = ExcelUtil.getReader(targetFile, 1);
        List<BiomeCurated> list = sheet2.read(0, 1, BiomeCurated.class);
        int order = 0;
        HashSet<String> strings = new HashSet<>();

        for (BiomeCurated biomeCurated : list) {
            if (!strings.contains(biomeCurated.getDataTypeCurated())) {
                ++order;
            }
            strings.add(biomeCurated.getDataTypeCurated());
            biomeCurated.setOrderNum(order);
        }
        mongoTemplate.insertAll(list);
    }


    @Test
    public void featureData() {
        featureDataWebService.cacheBiomeCuratedData();
    }

}
