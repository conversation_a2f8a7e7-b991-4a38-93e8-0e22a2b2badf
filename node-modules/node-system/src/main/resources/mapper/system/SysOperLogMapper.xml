<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.biosino.system.mapper.SysOperLogMapper">

    <resultMap type="SysOperLog" id="SysOperLogResult">
        <id property="operId" column="oper_id"/>
        <result property="system" column="system"/>
        <result property="module1" column="module1"/>
        <result property="module2" column="module2"/>
        <result property="module3" column="module3"/>
        <result property="description" column="description"/>
        <result property="businessType" column="business_type"/>
        <result property="method" column="method"/>
        <result property="requestMethod" column="request_method"/>
        <result property="operUserId" column="oper_user_id"/>
        <result property="operName" column="oper_name"/>
        <result property="operUrl" column="oper_url"/>
        <result property="operIp" column="oper_ip"/>
        <result property="operArgs" column="oper_args"/>
        <result property="operParam" column="oper_param"/>
        <result property="jsonResult" column="json_result"/>
        <result property="status" column="status"/>
        <result property="errorMsg" column="error_msg"/>
        <result property="operTime" column="oper_time"/>
        <result property="costTime" column="cost_time"/>
    </resultMap>

    <sql id="selectOperLogVo">
        select oper_id,
               system,
               module1,
               module2,
               module3,
               description,
               business_type,
               method,
               request_method,
               oper_user_id,
               oper_name,
               oper_url,
               oper_ip,
               oper_args,
               oper_param,
               json_result,
               status,
               error_msg,
               oper_time,
               cost_time
        from sys_oper_log
    </sql>

    <insert id="insertOperlog" parameterType="SysOperLog">
        insert into sys_oper_log(system, module1, module2, module3, description, business_type, method, request_method, oper_user_id, oper_name,
                                 oper_url, oper_ip, oper_args, oper_param, json_result, status, error_msg, cost_time, oper_time)
        values (#{system}, #{module1}, #{module2}, #{module3}, #{description}, #{businessType}, #{method}, #{requestMethod},
                #{operUserId}, #{operName},
                #{operUrl}, #{operIp}, #{operArgs}, #{operParam}, #{jsonResult}, #{status}, #{errorMsg}, #{costTime}, sysdate())
    </insert>

    <select id="selectOperLogList" parameterType="SysOperLog" resultMap="SysOperLogResult">
        <include refid="selectOperLogVo"/>
        <where>
            <if test="operIp != null and operIp != ''">
                AND oper_ip like concat('%', #{operIp}, '%')
            </if>
            <if test="system != null and system != ''">
                AND system = #{system}
            </if>
            <if test="module1 != null and module1 != ''">
                AND module1 = #{module1}
            </if>
            <if test="module2 != null and module2 != ''">
                AND module2 = #{module2}
            </if>
            <if test="module3 != null and module3 != ''">
                AND module3 = #{module3}
            </if>
            <if test="businessType != null">
                AND business_type = #{businessType}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="operName != null and operName != ''">
                AND oper_name like concat('%', #{operName}, '%')
            </if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND oper_time &gt;= #{params.beginTime}
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND oper_time &lt;= #{params.endTime}
            </if>
        </where>
        order by oper_id desc
    </select>

    <delete id="deleteOperLogByIds" parameterType="Long">
        delete from sys_oper_log where oper_id in
        <foreach collection="array" item="operId" open="(" separator="," close=")">
            #{operId}
        </foreach>
    </delete>

    <select id="selectOperLogById" parameterType="Long" resultMap="SysOperLogResult">
        <include refid="selectOperLogVo"/>
        where oper_id = #{operId}
    </select>

    <update id="cleanOperLog">
        truncate table sys_oper_log
    </update>

</mapper>
