package org.biosino.system.repository;

import org.biosino.common.mongo.entity.admin.MultipleSampleResource;
import org.biosino.es.api.vo.fd.MultipleSampleQueryVO;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Map;

@Repository
public interface MultipleSampleResourceCustomRepository {

    Map<String, MultipleSampleResource> findMapByProjIDIn(Collection<String> projIDs);

    PageImpl<MultipleSampleResource> multSampleList(MultipleSampleQueryVO searchVO);

    List<MultipleSampleResource> findByNoLike(String searchNo);
}
