package org.biosino.system.service.meta;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.enums.AuthorizeType;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.es.api.RemoteDataService;
import org.biosino.es.api.dto.RelatedDataDTO;
import org.biosino.es.api.vo.detail.DataListSearchVO;
import org.biosino.system.api.RemoteMemberService;
import org.biosino.system.api.dto.MemberDTO;
import org.biosino.system.dto.dto.DataQueryDTO;
import org.biosino.system.dto.dto.MetadataQueryDTO;
import org.biosino.system.dto.dto.PublishDTO;
import org.biosino.system.mq.index.IndexUpdateEvent;
import org.biosino.system.vo.metadata.PublishVO;
import org.biosino.upload.api.vo.DeleteCheckResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Li
 * @date 2024/4/24
 */
@Service
public class BaseService {
    @Autowired
    private PublishService publishService;

    @Autowired
    private RemoteMemberService remoteMemberService;

    @Autowired
    private ChangeCreatorLogService changeCreatorLogService;

    @Autowired
    private RemoteDataService remoteDataService;

    @Autowired
    private ApplicationContext applicationContext;

    public void savePublish(List<PublishDTO> publishDTO, AuthorizeType authorizeType, String typeId, String creator) {
        publishService.save(publishDTO, authorizeType.name(), typeId, creator);
    }


    public List<PublishVO> getPublishVo(AuthorizeType authorizeType, String typeId) {
        return publishService.getPublishVo(authorizeType.name(), typeId);
    }

    public MemberDTO getOneMemberByMemberId(String memberId) {
        R<MemberDTO> rMember = remoteMemberService.getOneMemberByMemberId(memberId, "FtpUser", SecurityConstants.INNER);
        if (R.isError(rMember)) {
            throw new ServiceException(rMember.getMsg());
        }
        return rMember.getData();
    }

    public String getEmailByMemberId(String memberId) {
        R<MemberDTO> result = remoteMemberService.getOneMemberByMemberId(memberId, "FtpUser", SecurityConstants.INNER);
        if (R.isError(result)) {
            return null;
        }
        if (result == null || result.getData() == null) {
            return null;
        }
        return result.getData().getEmail();
    }

    public MemberDTO getMemberInfoByEmail(String email) {
        R<MemberDTO> rMember = remoteMemberService.getMemberInfoByEmail(email, "FtpUser", SecurityConstants.INNER);
        if (R.isError(rMember) || rMember.getData() == null) {
            throw new ServiceException(StrUtil.format("用户 {} 未找到", email));
        }
        return rMember.getData();
    }

    public Map<String, String> getMemberIdToEmailMap(Collection<String> memberIds) {
        R<Map<String, String>> r = remoteMemberService.getMemberIdToEmailMapByMemberIds(memberIds, "FtpUser", SecurityConstants.INNER);
        if (R.isError(r) || r.getData() == null) {
            throw new ServiceException(r.getMsg());
        }
        return r.getData();
    }

    public void setMetaQueryDTOCreator(MetadataQueryDTO queryDTO) {
        if (StrUtil.isNotBlank(queryDTO.getCreatorEmail())) {
            String creatorEmail = queryDTO.getCreatorEmail();
            MemberDTO memberDTO = getMemberInfoByEmail(creatorEmail.trim());
            queryDTO.setCreator(memberDTO.getId());
        }
    }

    public void setDataQueryCreator(DataQueryDTO queryDTO) {
        if (StrUtil.isNotBlank(queryDTO.getCreatorEmail())) {
            String creatorEmail = queryDTO.getCreatorEmail();
            MemberDTO memberDTO = getMemberInfoByEmail(creatorEmail);
            queryDTO.setCreator(memberDTO.getId());
        }
    }

    /**
     * 添加changecreator日志
     */
    public void addChangeCreatorLog(String no, String type, String sourceCreator, String targetCreator, DeleteCheckResultVO checkResultVO) {
        String sourceEmail = getOneMemberByMemberId(sourceCreator).getEmail();
        String targetEmail = getOneMemberByMemberId(targetCreator).getEmail();

        changeCreatorLogService.addChangeCreatorLog(no, type, sourceCreator, sourceEmail, targetCreator, targetEmail, checkResultVO);
    }

    public void updateEsData(String type, String typeNo) {
        final DataListSearchVO searchVO = new DataListSearchVO();
        searchVO.setType(type);
        searchVO.setTypeNo(typeNo);
        searchVO.setMustHasData(false);
        R<List<RelatedDataDTO>> r = remoteDataService.findAllByTypeAndNo(searchVO, SecurityConstants.INNER);
        if (R.isError(r)) {
            throw new ServiceException("数据服务错误");
        }
        if (CollUtil.isEmpty(r.getData())) {
            return;
        }
        List<RelatedDataDTO> relatedDataDTOS = r.getData();

        List<String> projNos = relatedDataDTOS.stream().map(RelatedDataDTO::getProjNo).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        List<String> analNos = relatedDataDTOS.stream().map(RelatedDataDTO::getAnalNo).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        List<String> dataNos = relatedDataDTOS.stream().map(RelatedDataDTO::getDatNo).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());

        if (CollUtil.isNotEmpty(projNos)) {
            applicationContext.publishEvent(new IndexUpdateEvent(this, AuthorizeType.project, projNos));
        }
        if (CollUtil.isNotEmpty(analNos)) {
            applicationContext.publishEvent(new IndexUpdateEvent(this, AuthorizeType.analysis, analNos));
        }
        if (CollUtil.isNotEmpty(dataNos)) {
            applicationContext.publishEvent(new IndexUpdateEvent(this, AuthorizeType.data, dataNos));
        }
    }
}
