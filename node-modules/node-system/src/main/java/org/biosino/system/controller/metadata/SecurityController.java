package org.biosino.system.controller.metadata;

import lombok.RequiredArgsConstructor;
import org.biosino.app.api.RemoteAppSecurityService;
import org.biosino.app.api.dto.UpdateSecurityDTO;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.web.controller.BaseController;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.log.annotation.Log;
import org.biosino.common.log.enums.BusinessType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 更新数据安全等级
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/security")
@RequiredArgsConstructor
public class SecurityController extends BaseController {

    private final RemoteAppSecurityService remoteAppSecurityService;

    /**
     * 更新安全等级
     */
    @Log(module1 = "Metadata Mgmt", module2 = "Change Security", businessType = BusinessType.UPDATE)
    @PostMapping("/updateDataSecurity")
    public AjaxResult updateDataSecurity(@RequestBody @Validated UpdateSecurityDTO dto) {
        dto.setAdmin(true);
        R<List<String>> r = remoteAppSecurityService.updateDataSecurity(dto);
        return AjaxResult.success(r.getData());
    }

}
