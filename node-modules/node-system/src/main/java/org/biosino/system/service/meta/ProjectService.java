package org.biosino.system.service.meta;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONWriter;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.domain.Select;
import org.biosino.common.core.enums.AuthorizeType;
import org.biosino.common.core.enums.RouterKeyEnum;
import org.biosino.common.core.enums.SecurityEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.DownloadUtils;
import org.biosino.common.core.utils.file.MyFileUtils;
import org.biosino.common.core.utils.poi.ExcelUtil;
import org.biosino.common.mongo.dto.TypeInformation;
import org.biosino.common.mongo.entity.*;
import org.biosino.common.mongo.entity.other.Submitter;
import org.biosino.common.mongo.iterator.MongoPagingIterator;
import org.biosino.common.rabbitmq.MessageSender;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.es.api.RemoteDataService;
import org.biosino.es.api.msg.IndexUpdateMsg;
import org.biosino.es.api.vo.detail.DataListSearchVO;
import org.biosino.system.api.dto.MemberDTO;
import org.biosino.system.dto.dto.*;
import org.biosino.system.dto.dto.export.ProjectExportDTO;
import org.biosino.system.mq.index.IndexUpdateEvent;
import org.biosino.system.repository.*;
import org.biosino.system.vo.SourceProjectMetadataVO;
import org.biosino.system.vo.metadata.ProjectListVO;
import org.biosino.system.vo.metadata.ProjectVO;
import org.biosino.system.vo.metadata.PublishVO;
import org.biosino.upload.api.RemoteUploadProjectService;
import org.biosino.upload.api.vo.DeleteCheckResultVO;
import org.springframework.context.ApplicationContext;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileWriter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Li
 * @date 2024/4/23
 */
@RequiredArgsConstructor
@Service
public class ProjectService extends BaseService {

    private final ProjectRepository projectRepository;

    private final ExperimentRepository experimentRepository;

    private final RunRepository runRepository;

    private final SampleRepository sampleRepository;

    private final DataRepository dataRepository;

    private final PublishRepository publishRepository;

    private final MessageSender messageSender;

    private final RemoteUploadProjectService remoteUploadProjectService;

    private final RemoteDataService remoteDataService;
    private final ApplicationContext applicationContext;


    public Page<ProjectListVO> listAuditedProject(MetadataQueryDTO queryDTO) {
        // 将email转为memberId
        setMetaQueryDTOCreator(queryDTO);

        // 分页查询数据
        Page<Project> page = projectRepository.findProjectPage(queryDTO);

        List<String> projNos = page.getContent().stream().map(Project::getProjectNo).collect(Collectors.toList());

        // 一次性查询所有相关experiment
        Map<String, List<Experiment>> expMap = experimentRepository.findDetailByProjNoIn(projNos)
                .stream().collect(Collectors.groupingBy(Experiment::getProjectNo));

        // 一次性查询所有相关run
        Map<String, List<Run>> runMap = runRepository
                .findDetailByExpNoIn(expMap.values().stream()
                        .flatMap(List::stream)
                        .map(Experiment::getExpNo)
                        .collect(Collectors.toList()))
                .stream().collect(Collectors.groupingBy(Run::getExpNo));

        // 一次性查询所有相关样本
        Map<String, List<Sample>> sapMap = sampleRepository
                .findDetailBySapNoIn(runMap.values().stream()
                        .flatMap(List::stream)
                        .map(Run::getSapNo)
                        .collect(Collectors.toList()))
                .stream().collect(Collectors.groupingBy(Sample::getSapNo));

        // 一次性查询所有相关data
        Map<String, List<Data>> dataMap = dataRepository
                .findDetailByRunNoIn(runMap.values().stream()
                        .flatMap(List::stream)
                        .map(Run::getRunNo)
                        .collect(Collectors.toList()))
                .stream().collect(Collectors.groupingBy(Data::getRunNo));

        List<String> creators = page.getContent().stream().map(Project::getCreator).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());

        // 获取memberId到email对映射关系
        Map<String, String> memberIdToEmailMap = getMemberIdToEmailMap(creators);


        Page<ProjectListVO> result = page.map(x -> {
            ProjectListVO vo = new ProjectListVO();
            BeanUtil.copyProperties(x, vo);

            List<Experiment> expList = expMap.getOrDefault(x.getProjectNo(), Collections.emptyList());

            List<Run> runList = expList.stream()
                    .map(Experiment::getExpNo)
                    .flatMap(expNo -> runMap.getOrDefault(expNo, Collections.emptyList()).stream())
                    .collect(Collectors.toList());

            vo.setExpTypes(expList.stream()
                    .map(Experiment::getExpType)
                    .distinct()
                    .collect(Collectors.toList()));

            List<Sample> sapList = runList.stream()
                    .map(Run::getSapNo)
                    .flatMap(sapNo -> sapMap.getOrDefault(sapNo, Collections.emptyList()).stream())
                    .collect(Collectors.toList());

            vo.setSapTypes(sapList.stream()
                    .map(Sample::getSubjectType)
                    .distinct()
                    .collect(Collectors.toList()));

            vo.setOrganisms(sapList.stream()
                    .map(Sample::getOrganism)
                    .distinct()
                    .collect(Collectors.toList()));

            List<Data> dataList = runList.stream()
                    .map(Run::getRunNo)
                    .flatMap(runNo -> dataMap.getOrDefault(runNo, Collections.emptyList()).stream())
                    .collect(Collectors.toList());

            Map<String, Long> securityMap = dataList.stream()
                    .collect(Collectors.groupingBy(Data::getSecurity, Collectors.counting()));

            for (String s : SecurityEnum.includeAllSecurity()) {
                securityMap.putIfAbsent(s, 0L);
            }

            vo.setDataCount(securityMap);

            vo.setSubmitter(x.getSubmitter().getFirstName() + " " + x.getSubmitter().getLastName());

            // 设置creatorEmail
            vo.setCreatorEmail(memberIdToEmailMap.get(x.getCreator()));
            return vo;
        });

        return result;
    }

    public ProjectVO getProjectByNo(String projNo) {
        if (projNo == null) {
            throw new ServiceException("项目ID不能为空");
        }

        Project project = projectRepository.findTopByProjectNo(projNo).orElseThrow(() -> new ServiceException("未找到数据"));

        ProjectVO result = new ProjectVO();
        BeanUtil.copyProperties(project, result);

        List<PublishVO> publishVo = getPublishVo(AuthorizeType.project, projNo);
        result.setPublish(publishVo);

        return result;
    }

    public ProjectVO updateProject(ProjectDTO projectDTO) {
        Project project = projectRepository.findTopByProjectNo(projectDTO.getProjectNo()).orElseThrow(() -> new ServiceException("Not found project"));
        // 更新信息以及submitter
        BeanUtil.copyProperties(projectDTO, project);
        // 更新日期
        project.setOperator(SecurityUtils.getUserId().toString());
        project.setOperationDate(new Date());
        // 更新publish
        savePublish(projectDTO.getPublish(), AuthorizeType.project, project.getProjectNo(), project.getCreator());
        projectRepository.save(project);
        // 通知es更新索引
        updateEsData(AuthorizeType.project.name(), project.getProjectNo());

        return getProjectByNo(project.getProjectNo());
    }

    public DeleteCheckResultVO deleteCheck(String projNo) {
        Project project = projectRepository.findTopByProjectNo(projNo).orElseThrow(() -> new ServiceException("未找到项目"));
        R<DeleteCheckResultVO> r = remoteUploadProjectService.deleteCheck(project.getProjectNo(), project.getCreator());
        if (R.isError(r)) {
            throw new ServiceException(r.getMsg());
        }
        return r.getData();
    }

    public void deleteProjectAll(String projectNo) {
        Project project = projectRepository.findTopByProjectNo(projectNo).orElseThrow(() -> new ServiceException("未找到项目"));

        R r = remoteUploadProjectService.deleteProjectAll(project.getProjectNo(), project.getCreator());
        if (R.isError(r)) {
            throw new ServiceException(r.getMsg());
        }
    }

    public void updateCreator(String projectNo, String creator) {
        if (StrUtil.isBlank(creator) || StrUtil.isBlank(projectNo)) {
            throw new ServiceException("参数不能为空");
        }
        Project project = projectRepository.findTopByProjectNo(projectNo).orElseThrow(() -> new ServiceException("未找到项目"));
        // 查询邮件的用户
        MemberDTO data = getMemberInfoByEmail(creator);

        String newCreator = data.getId();
        if (newCreator == null) {
            throw new ServiceException(StrUtil.format("用户 {} 未找到", creator));
        }
        DeleteCheckResultVO checkResultVO = deleteCheck(projectNo);
        // 删除数据
        if (CollUtil.isNotEmpty(checkResultVO.getErrors())) {
            throw new ServiceException("该项目无法更改创建者，因为它与其他数据关联");
        }
        // 添加修改的日志
        addChangeCreatorLog(projectNo, AuthorizeType.project.name(), project.getCreator(), newCreator, checkResultVO);

        // 修复project以及关联数据的creator
        projectRepository.updateCreatorByProjectNo(checkResultVO.getProjNos(), newCreator);
        experimentRepository.updateCreatorByExpNoIn(checkResultVO.getExpNos(), newCreator);
        runRepository.updateCreatorByRunNoIn(checkResultVO.getRunNos(), newCreator);
        sampleRepository.updateCreatorBySapNoIn(checkResultVO.getSapNos(), newCreator);
        dataRepository.updateCreatorByDatNoIn(checkResultVO.getDataNos(), newCreator);

        // 修改数据相关publish的creator
        publishRepository.updateCreatorByTypeAndTypeId(AuthorizeType.project.name(), checkResultVO.getProjNos(), newCreator);
        publishRepository.updateCreatorByTypeAndTypeId(AuthorizeType.experiment.name(), checkResultVO.getExpNos(), newCreator);
        publishRepository.updateCreatorByTypeAndTypeId(AuthorizeType.sample.name(), checkResultVO.getSapNos(), newCreator);

        // 通知更新索引
        if (CollUtil.isNotEmpty(checkResultVO.getDataNos())) {
            messageSender.sendDelayMsg(RouterKeyEnum.es_index_update_key.name(), new IndexUpdateMsg(AuthorizeType.data.name(), checkResultVO.getDataNos()));
        }

    }

    public List<Select> getProjectList(String creator) {
        if (StrUtil.isBlank(creator)) {
            throw new ServiceException("参数不能为空");
        }
        MetadataQueryDTO queryDTO = new MetadataQueryDTO();
        queryDTO.setCreatorEmail(creator);
        queryDTO.setPageSize(Integer.MAX_VALUE);
        queryDTO.setPageNum(1);
        Page<Project> page = projectRepository.findProjectPage(queryDTO);
        List<Select> selectList = new ArrayList<>();
        page.forEach(x -> {
            Select select = new Select();
            select.setValue(x.getProjectNo());
            select.setLabel(x.getProjectNo() + " (" + x.getName() + ")");
            selectList.add(select);
        });

        return selectList;
    }

    public void batchModifySource(ModifySourceDTO modifySourceDTO) {
        modifySourceDTO.setNos(modifySourceDTO.getNos().stream().filter(StrUtil::isNotBlank).collect(Collectors.toList()));
        TypeInformation typeInformation = TypeInformation.typeInfoMap.get(modifySourceDTO.getType());
        if (typeInformation == null) {
            throw new ServiceException(StrUtil.format("类型 {} 未找到", modifySourceDTO.getType()));
        }
        projectRepository.batchModifySource(modifySourceDTO, typeInformation.getMongoField(), typeInformation.getClazz());
    }

    public Page<SourceProjectMetadataVO> getSourceProjectMetadataPage(SourceProjectMetadataQueryDTO dto) {
        TypeInformation typeInformation = TypeInformation.typeInfoMap.get(dto.getType());
        if (typeInformation == null) {
            throw new ServiceException(StrUtil.format("类型 {} 未找到", dto.getType()));
        }
        Page page = projectRepository.getSourceProjectMetadataPage(dto, typeInformation);
        List content = page.getContent();
        ArrayList<String> creators = new ArrayList<>();
        for (Object o : content) {
            String creator = (String) ReflectUtil.getFieldValue(o, "creator");
            if (StrUtil.isNotBlank(creator)) {
                creators.add(creator);
            }
        }
        // 获取memberId到email对映射关系
        Map<String, String> memberIdToEmailMap = getMemberIdToEmailMap(CollUtil.distinct(creators));
        Page<SourceProjectMetadataVO> result = page.map(x -> {
            SourceProjectMetadataVO vo = new SourceProjectMetadataVO();
            vo.setTypeNo((String) ReflectUtil.getFieldValue(x, typeInformation.getField()));
            vo.setName((String) ReflectUtil.getFieldValue(x, "name"));
            vo.setDescription((String) ReflectUtil.getFieldValue(x, "description"));
            vo.setSourceProject((List<String>) ReflectUtil.getFieldValue(x, "sourceProject"));
            String creator = (String) ReflectUtil.getFieldValue(x, "creator");
            vo.setCreator(creator);
            String creatorEmail = memberIdToEmailMap.get(creator);
            vo.setCreatorEmail(creatorEmail);
            Submitter submitter = (Submitter) ReflectUtil.getFieldValue(x, "submitter");
            vo.setSubmitter(submitter.getFirstName() + " " + submitter.getLastName());
            vo.setCreateDate((Date) ReflectUtil.getFieldValue(x, "createDate"));
            return vo;
        });
        return result;
    }

    public List<String> getSourceProjectMetadataId(SourceProjectMetadataQueryDTO queryDTO) {
        queryDTO.setPageSize(-1);
        TypeInformation typeInformation = TypeInformation.typeInfoMap.get(queryDTO.getType());
        if (typeInformation == null) {
            throw new ServiceException(StrUtil.format("类型 {} 未找到", queryDTO.getType()));
        }
        List<String> result = projectRepository.getSourceProjectMetadataId(queryDTO, typeInformation);
        return result;
    }

    public void downloadData(DataListSearchVO searchVO, HttpServletRequest request, HttpServletResponse response) {
        if (StrUtil.isBlank(searchVO.getType()) || !AuthorizeType.project.name().equals(searchVO.getType())) {
            throw new ServiceException("类型错误");
        }
        final String typeNo = searchVO.getTypeNo();
        final Project project = projectRepository.findByNo(typeNo);
        if (project == null) {
            throw new ServiceException("项目未找到");
        }

        searchVO.setCurrMemberId(project.getCreator());
        final Submitter submitter = project.getSubmitter();
        if (submitter != null) {
            searchVO.setCurrMemberEmail(submitter.getEmail());
        }

        searchVO.setFindFilePath(true);
        final R<String> r = remoteDataService.generateDataExcel(searchVO, SecurityConstants.INNER);
        if (R.isSuccess(r)) {
            DownloadUtils.downloadAndDelete(new File(r.getData()), request, response);
        } else {
            throw new ServiceException(r.getMsg());
        }
    }

    public void refreshIndex(String projectNo) {
        Project project = projectRepository.findByNo(projectNo);
        if (project == null) {
            throw new ServiceException("未找到该项目");
        }
        // 更新浏览页索引
        applicationContext.publishEvent(new IndexUpdateEvent(this, AuthorizeType.project, project.getProjectNo()));

        List<Experiment> experimentList = experimentRepository.findAllByProjectNo(projectNo);
        if (CollUtil.isEmpty(experimentList)) {
            return;
        }
        List<String> expList = experimentList.stream().map(Experiment::getExpNo).collect(Collectors.toList());
        List<Run> runList = runRepository.findAllByExpNoIn(expList);
        if (CollUtil.isEmpty(runList)) {
            return;
        }
        List<String> runNoList = runList.stream().map(Run::getRunNo).collect(Collectors.toList());
        List<Data> dataList = dataRepository.findAllByRunNoIn(runNoList);
        if (CollUtil.isEmpty(dataList)) {
            return;
        }
        List<String> dataNos = dataList.stream().map(Data::getDatNo).collect(Collectors.toList());
        // 更新Data索引
        applicationContext.publishEvent(new IndexUpdateEvent(this, AuthorizeType.data, dataNos));
    }

    public void exportProjectEmail(HttpServletResponse response) {
        List<Project> projectList = projectRepository.findAllProject();
        if (CollUtil.isEmpty(projectList)) {
            throw new ServiceException("未查询到有效数据");
        }

        List<ProjectEmailExportDTO> result = new ArrayList<>();

        Map<String, String> nodeEmailMap = new HashMap<>();
        for (Project project : projectList) {
            String creator = project.getCreator();

            ProjectEmailExportDTO projectEmailExportDTO = new ProjectEmailExportDTO();
            projectEmailExportDTO.setProjectNo(project.getProjectNo());
            Submitter submitter = project.getSubmitter();
            if (submitter != null) {
                projectEmailExportDTO.setSubmitterEmail(submitter.getEmail());
            }

            String email = "未在BMDC中查询到该用户";
            if (nodeEmailMap.containsKey(creator)) {
                email = nodeEmailMap.get(creator);
            } else {
                String nodeEmail = getEmailByMemberId(creator);
                if (nodeEmail != null) {
                    email = nodeEmail;
                    nodeEmailMap.put(creator, nodeEmail);
                }
            }
            projectEmailExportDTO.setNodeEmail(email);
            projectEmailExportDTO.setUserId(creator);
            if (projectEmailExportDTO.getNodeEmail().equals(projectEmailExportDTO.getSubmitterEmail())) {
                projectEmailExportDTO.setAgreement("是");
            } else {
                projectEmailExportDTO.setAgreement("否");
            }
            result.add(projectEmailExportDTO);
        }

        if (CollUtil.isNotEmpty(result)) {
            final ExcelUtil<ProjectEmailExportDTO> util = new ExcelUtil<>(ProjectEmailExportDTO.class);
            util.exportExcel(response, result, "sheet1");
        }
    }

    public File exportProject(MetadataQueryDTO queryDTO) {
        // 将email转为memberId
        setMetaQueryDTOCreator(queryDTO);
        MongoPagingIterator<Project> iterator = projectRepository.getPagingIterator(queryDTO);
        File tempDir = MyFileUtils.getTempDir();
        File resultFile = FileUtil.file(tempDir, "project.json");
        FileUtil.touch(resultFile);
        try (FileWriter fileWriter = new FileWriter(resultFile);
             JSONWriter jsonWriter = new JSONWriter(fileWriter)) {
            jsonWriter.config(SerializerFeature.PrettyFormat, true);
            jsonWriter.config(SerializerFeature.WriteDateUseDateFormat, true);
            jsonWriter.startArray();
            while (iterator.hasNext()) {
                List<Project> next = iterator.next();
                if (CollUtil.isEmpty(next)) {
                    break;
                }
                for (Project project : next) {
                    ProjectExportDTO item = BeanUtil.copyProperties(project, ProjectExportDTO.class);
                    jsonWriter.writeObject(item);
                }
            }
            jsonWriter.endArray();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultFile;
    }
}
