package org.biosino.system.controller.fd;

import lombok.RequiredArgsConstructor;
import org.biosino.common.core.utils.poi.ExcelUtil;
import org.biosino.common.core.web.controller.BaseController;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.log.annotation.Log;
import org.biosino.common.log.enums.BusinessType;
import org.biosino.common.mongo.entity.admin.HumanResource;
import org.biosino.common.security.annotation.Logical;
import org.biosino.common.security.annotation.RequiresPermissions;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.system.domain.dto.FeatureDataDTO;
import org.biosino.system.service.fd.IFeatureDataService;
import org.biosino.system.service.impl.fd.FeatureDataServiceImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 特殊数据集 人类（微生物）资源
 *
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/fdHumanResource")
public class FeatureDataController extends BaseController {
    private final IFeatureDataService featureDataService;

    /**
     * 特殊数据集列表
     */
    @RequiresPermissions(value = {"fd:humanResource:list", "fd:microbeResource:list"}, logical = Logical.OR)
    @GetMapping("/list")
    public AjaxResult list(HumanResource search, @PageableDefault(size = 1000) Pageable pageable) {
        return success(featureDataService.list(search, pageable));
    }

    /**
     * 模糊查询项目编号列表
     */
    @GetMapping("/searchPrjId/{keyword}")
    public AjaxResult searchPrjId(@PathVariable("keyword") final String keyword) {
        return success(featureDataService.searchPrjId(keyword));
    }

    /**
     * 新增特殊数据集
     */
    @PostMapping("/saveHumanResource")
    @RequiresPermissions(value = {"fd:humanResource:list", "fd:microbeResource:list"}, logical = Logical.OR)
    @Log(module1 = "Feature Data", module2 = "Human Resource/Microbe Resource", businessType = BusinessType.INSERT)
    public AjaxResult saveHumanResource(@Validated @RequestBody final HumanResource humanResource) {
        return success(featureDataService.saveHumanResource(humanResource, SecurityUtils.getUsername(), featureDataService.initAllCat1(humanResource.isMicrobeFlag())));
    }

    /**
     * 批量修改状态
     */
    @PostMapping("/batchUpdateHrStatus")
    @RequiresPermissions(value = {"fd:humanResource:list", "fd:microbeResource:list"}, logical = Logical.OR)
    @Log(module1 = "Feature Data", module2 = "Human Resource/Microbe Resource", businessType = BusinessType.UPDATE)
    public AjaxResult batchUpdateHrStatus(@RequestBody final FeatureDataDTO featureDataDTO) {
        return success(featureDataService.batchUpdateHrStatus(featureDataDTO));
    }

    /**
     * 批量删除
     */
    @PostMapping("/batchDeleteHr")
    @RequiresPermissions(value = {"fd:humanResource:list", "fd:microbeResource:list"}, logical = Logical.OR)
    @Log(module1 = "Feature Data", module2 = "Human Resource/Microbe Resource", businessType = BusinessType.DELETE)
    public AjaxResult batchDeleteHr(@RequestBody final FeatureDataDTO featureDataDTO) {
        return success(featureDataService.batchDeleteHr(featureDataDTO));
    }

    /**
     * 数据导出
     */
    @RequiresPermissions(value = {"fd:humanResource:list", "fd:microbeResource:list"}, logical = Logical.OR)
    @Log(module1 = "Feature Data", module2 = "Human Resource/Microbe Resource", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadHr")
    public void downloadHr(HttpServletResponse response, final Boolean microbeFlag) {
        featureDataService.downloadHr(response, microbeFlag);
    }

    /**
     * 导出模板
     */
    @RequiresPermissions(value = {"fd:humanResource:list", "fd:microbeResource:list"}, logical = Logical.OR)
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response, final Boolean microbeFlag) {
        ExcelUtil<HumanResource> util = new ExcelUtil<>(HumanResource.class);
        util.importTemplateExcel(response, FeatureDataServiceImpl.sheetName(microbeFlag));
    }

    /**
     * 数据批量导入
     */
    @RequiresPermissions(value = {"fd:humanResource:list", "fd:microbeResource:list"}, logical = Logical.OR)
    @Log(module1 = "Feature Data", module2 = "Human Resource/Microbe Resource", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, final Boolean microbeFlag, final Boolean deleteOld) {
        return success(featureDataService.importData(file, SecurityUtils.getUsername(), microbeFlag, deleteOld));
    }

}
