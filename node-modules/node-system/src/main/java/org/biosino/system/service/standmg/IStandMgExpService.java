package org.biosino.system.service.standmg;

import org.biosino.common.mongo.entity.ExpSampleType;
import org.biosino.system.domain.dto.StandMgDataDTO;
import org.biosino.system.dto.dto.standmg.StandMgAttrDTO;
import org.biosino.system.dto.dto.standmg.StandMgQueryDTO;
import org.biosino.system.vo.standmg.StandMgAttrVO;
import org.biosino.system.vo.standmg.StandMgExpVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 组学类型字典配置 服务层
 *
 * <AUTHOR>
 */
public interface IStandMgExpService {

    StandMgExpVO list(StandMgQueryDTO search);

    boolean updateStatus(StandMgDataDTO standMgDataDTO);

    String uploadTemplate(MultipartFile file, String name, String version, String type, String parentName, Boolean isExample, String id);

    boolean saveExpSampleType(ExpSampleType expSampleType, String username);

    void exportStand(HttpServletRequest request, HttpServletResponse response, String standType);

    StandMgAttrVO attrDetail(String standType, String standId);

    boolean saveAttr(StandMgAttrDTO dto, String username);

    boolean delAttr(String standId, String id);

    boolean updateAttrStatus(StandMgDataDTO standMgDataDTO);

    void exportAttr(HttpServletResponse response, StandMgDataDTO standMgDataDTO);

    String importAttrData(MultipartFile file, String username, String type, Boolean deleteOld, String standId);

    List<String> getAllNames();
}
