package org.biosino.system.service.statistics;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.NodeUtils;
import org.biosino.common.mongo.entity.statistics.StatisticsDataVolume;
import org.biosino.common.mongo.entity.statistics.StatisticsSample;
import org.biosino.common.mongo.entity.statistics.StatisticsSampleExp;
import org.biosino.system.dto.mapper.StatisticsSampleMapper;
import org.biosino.system.repository.StatisticsDataVolumeRepository;
import org.biosino.system.repository.StatisticsSampleExpRepository;
import org.biosino.system.repository.StatisticsSampleRepository;
import org.biosino.system.repository.util.RepositoryUtil;
import org.biosino.system.vo.SampleExpStatVO;
import org.biosino.system.vo.SampleStatVO;
import org.biosino.system.vo.excel.SampleExcel;
import org.biosino.system.vo.excel.SampleExpExcel;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 统计模块 - Sample样本统计
 *
 * <AUTHOR>
 * @date 2024/7/24
 */
@Service
@RequiredArgsConstructor
public class SampleStatService {
    private final StatisticsSampleRepository statisticsSampleRepository;
    private final StatisticsSampleExpRepository statisticsSampleExpRepository;

    private final StatisticsDataVolumeRepository statisticsDataVolumeRepository;

    private final MongoTemplate mongoTemplate;

    public List<SampleStatVO> statData() {
        List<StatisticsSample> all = RepositoryUtil.findLatestMonthData(mongoTemplate, StatisticsSample.class);
        if (CollUtil.isEmpty(all)) {
            throw new ServiceException("No statistic data");
        }

        return StatisticsSampleMapper.INSTANCE.dbToVO(all);
    }

    public List<SampleExpStatVO> sapExpStatData() {
        List<StatisticsSampleExp> all = RepositoryUtil.findLatestMonthData(mongoTemplate, StatisticsSampleExp.class);
        if (CollUtil.isEmpty(all)) {
            throw new ServiceException("No statistic data");
        }

        return StatisticsSampleMapper.INSTANCE.sapExpDbToVO(all);
    }

    /**
     * 样本基础统计数据导出
     */
    public List<SampleExcel> exportSap() {
        final List<StatisticsSample> all = RepositoryUtil.findBaseList(mongoTemplate, true, StatisticsSample.class);
        if (CollUtil.isEmpty(all)) {
            throw new ServiceException("No statistic data");
        }

        final List<SampleExcel> excelData = new ArrayList<>();
        for (StatisticsSample item : all) {
            final SampleExcel excel = StatisticsSampleMapper.INSTANCE.dbToExcel(item);
            final Optional<StatisticsDataVolume> dataVolumeOptional = statisticsDataVolumeRepository.findFirstByMonth(excel.getMonth());
            if (dataVolumeOptional.isPresent()) {
                final StatisticsDataVolume dataVolume = dataVolumeOptional.get();
                excel.setTotalFileSizeRate(NodeUtils.div(item.getTotalFileSize(), rawDataTotalSize(dataVolume)));
                excel.setTotalRate(NodeUtils.div(item.getTotal(), sapTotal(dataVolume)));
            }
            excelData.add(excel);
        }
        return excelData;
    }

    public static long rawDataTotalSize(final StatisticsDataVolume dataVolume) {
        return dataVolume.getRawDataRestrictedSize() + dataVolume.getRawDataPublicSize() + dataVolume.getRawDataPrivateSize();
    }

    public static long sapTotal(final StatisticsDataVolume dataVolume) {
        return dataVolume.getSapAccessible() + dataVolume.getSapUnAccessible();
    }

    public List<SampleExpExcel> exportSapExp() {
        final List<StatisticsSampleExp> all = RepositoryUtil.findBaseList(mongoTemplate, true, StatisticsSampleExp.class);
        if (CollUtil.isEmpty(all)) {
            throw new ServiceException("No statistic data");
        }

        final List<SampleExpExcel> excelData = new ArrayList<>();
        for (StatisticsSampleExp item : all) {
            final SampleExpExcel excel = StatisticsSampleMapper.INSTANCE.sapExpDbToExcel(item);
            excelData.add(excel);
        }
        return excelData;
    }


}
