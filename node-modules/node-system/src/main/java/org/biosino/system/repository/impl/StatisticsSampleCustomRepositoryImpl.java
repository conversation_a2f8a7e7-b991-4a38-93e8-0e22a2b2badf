package org.biosino.system.repository.impl;

import lombok.RequiredArgsConstructor;
import org.biosino.common.mongo.entity.statistics.StatisticsSample;
import org.biosino.system.repository.StatisticsSampleCustomRepository;
import org.biosino.system.repository.util.RepositoryUtil;
import org.springframework.data.mongodb.core.MongoTemplate;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/24
 */
@RequiredArgsConstructor
public class StatisticsSampleCustomRepositoryImpl implements StatisticsSampleCustomRepository {
    private final MongoTemplate mongoTemplate;

    /*@Override
    public List<StatisticsSample> findLatestMonthData() {
        return RepositoryUtil.findBaseList(mongoTemplate, getClz());
    }*/

}
