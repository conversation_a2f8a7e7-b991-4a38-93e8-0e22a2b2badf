package org.biosino.system.controller;

import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.log.annotation.Log;
import org.biosino.common.log.enums.BusinessType;
import org.biosino.system.service.DataProcessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static org.biosino.common.core.web.domain.AjaxResult.success;

/**
 * 处理一些字典数据的临时接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/data-process")
public class DataProcessController {

    @Autowired
    private DataProcessService dataProcessService;

    /**
     * 刷新所有的Biome数据
     */
    @PostMapping("/refreshBiome")
    @Log(module1 = "人工操作", desc = "刷新Biome数据", businessType = BusinessType.UPDATE)
    public AjaxResult excludeChild() {
        long biome = dataProcessService.processAllBiome();
        return success("刷新完毕所有Biome数据, 数据量：" + biome);
    }
}
