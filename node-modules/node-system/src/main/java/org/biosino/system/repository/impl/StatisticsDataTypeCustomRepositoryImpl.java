package org.biosino.system.repository.impl;

import lombok.RequiredArgsConstructor;
import org.biosino.common.mongo.entity.statistics.StatisticsDataType;
import org.biosino.system.repository.StatisticsDataTypeCustomRepository;
import org.biosino.system.repository.util.RepositoryUtil;
import org.springframework.data.mongodb.core.MongoTemplate;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/31
 */
@RequiredArgsConstructor
public class StatisticsDataTypeCustomRepositoryImpl implements StatisticsDataTypeCustomRepository {
    private final MongoTemplate mongoTemplate;

    /*@Override
    public List<StatisticsDataType> findLatestMonthData() {
        return RepositoryUtil.findBaseList(mongoTemplate, getClz());
    }*/

}
