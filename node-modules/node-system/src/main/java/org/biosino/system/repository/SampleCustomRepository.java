package org.biosino.system.repository;

import org.biosino.common.mongo.entity.Sample;
import org.biosino.common.mongo.iterator.MongoPagingIterator;
import org.biosino.system.dto.dto.MetadataQueryDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;


@Repository
public interface SampleCustomRepository {

    Class<Sample> clz();

    Sample findByNo(String sapNo);

    List<Sample> findDetailBySapNoIn(Collection<String> sapNos);

    List<Sample> findAllBySapNoIn(List<String> sapNos);

    List<Sample> findTempBySapNoIn(Collection<String> sapNos);

    void updateToDeleteAllBySapNoIn(Collection<String> sapNos);

    void updateCreatorBySapNoIn(Collection<String> sapNos, String creator);

    Page<Sample> findSamplePage(MetadataQueryDTO queryDTO);

    Optional<Sample> findTopBySapNo(String sapNo);

    List<String> getAuditedOrganism();

    List<String> getAuditedSapType();

    List<String> getSampleNos(MetadataQueryDTO queryDTO);

    Page<Sample> findPublicPageBySapNoIn(Collection<String> sapNos, Pageable pageable);

    MongoPagingIterator<Sample> getPagingIterator(MetadataQueryDTO queryDTO);
}
