package org.biosino.system.service.statistics;

public class LongToPercentage {
    public static void main(String[] args) {
        Long num1 = 10L;
        Long num2 = 11L;

        // 调用方法进行计算并格式化
        float result = calculatePercentage(num1, num2);
        System.out.println("结果: " + result);
    }

    public static float calculatePercentage(Long num1, Long num2) {
        // 检查除数是否为零
        if (num2 == 0) {
            throw new ArithmeticException("除数不能为零");
        }

        // 将Long类型转换为float类型进行除法运算
        float percentage = (float) num1 / num2 * 100;

        // 使用Math.round保留两位小数
        percentage = Math.round(percentage * 100) / 100.0f;

        return percentage;
    }
}
