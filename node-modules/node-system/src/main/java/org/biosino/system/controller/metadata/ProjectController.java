package org.biosino.system.controller.metadata;

import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson2.JSON;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.domain.Select;
import org.biosino.common.core.utils.DownloadUtils;
import org.biosino.common.core.utils.file.MyFileUtils;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.core.web.page.TableDataInfo;
import org.biosino.common.log.annotation.Log;
import org.biosino.common.log.enums.BusinessType;
import org.biosino.common.security.annotation.RequiresPermissions;
import org.biosino.es.api.vo.detail.DataListSearchVO;
import org.biosino.system.dto.dto.MetadataQueryDTO;
import org.biosino.system.dto.dto.ModifySourceDTO;
import org.biosino.system.dto.dto.ProjectDTO;
import org.biosino.system.dto.dto.SourceProjectMetadataQueryDTO;
import org.biosino.system.service.meta.ProjectService;
import org.biosino.system.vo.SourceProjectMetadataVO;
import org.biosino.system.vo.metadata.ProjectListVO;
import org.biosino.system.vo.metadata.ProjectVO;
import org.biosino.upload.api.vo.DeleteCheckResultVO;
import org.springframework.data.domain.Page;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR> Li
 * @date 2024/4/23
 */

@RestController
@RequiredArgsConstructor
@RequestMapping("/metadata/project")
public class ProjectController {

    private final ProjectService projectService;

    /**
     * 列出用户的Project列表
     */
    @RequestMapping("/listProject")
    public TableDataInfo listProject(@RequestBody MetadataQueryDTO queryDTO) {
        Page<ProjectListVO> page = projectService.listAuditedProject(queryDTO);
        return new TableDataInfo(page.getContent(), (int) page.getTotalElements());
    }

    /**
     * 后端项目详情导出data列表数据
     */
    @PostMapping("/downloadData")
    public void downloadData(DataListSearchVO searchVO, HttpServletRequest request, HttpServletResponse response) {
        projectService.downloadData(searchVO, request, response);
    }

    /**
     * 查询project详细信息
     */
    @GetMapping("/getByNo/{projNo}")
    public AjaxResult getProjectByNo(@PathVariable String projNo) {
        ProjectVO projectVO = projectService.getProjectByNo(projNo);
        return AjaxResult.success(projectVO);
    }

    /**
     * 获取用户下面的Project
     */
    @GetMapping("/getProjectList")
    public AjaxResult getProjectList(String creator) {
        List<Select> result = projectService.getProjectList(creator);
        return AjaxResult.success(result);
    }

    /**
     * 保存编辑的project
     */
    @Log(module1 = "Metadata Mgmt", module2 = "Project", businessType = BusinessType.UPDATE)
    @RequestMapping("/edit")
    public AjaxResult edit(@RequestBody ProjectDTO projectDTO) {
        ProjectVO result = projectService.updateProject(projectDTO);
        return AjaxResult.success(result);
    }

    /**
     * 修改用户、删除 预检查
     */
    @GetMapping("/deleteCheck/{projectNo}")
    public AjaxResult deleteCheck(@PathVariable("projectNo") String projectNo) {
        DeleteCheckResultVO result = projectService.deleteCheck(projectNo);
        return AjaxResult.success(result);
    }

    /**
     * 删除Project 及其下面相关的内容
     */
    @Log(module1 = "Metadata Mgmt", module2 = "Project", businessType = BusinessType.DELETE)
    @RequestMapping("/deleteProjectAll")
    public AjaxResult deleteProjectAll(String projectNo) {
        // 删除数据
        projectService.deleteProjectAll(projectNo);
        return AjaxResult.success();
    }

    /**
     * 修改creator
     */
    @Log(module1 = "Metadata Mgmt", module2 = "Project", module3 = "Change Creator", businessType = BusinessType.UPDATE)
    @RequestMapping("/updateCreator")
    public AjaxResult changeCreator(String projectNo, String newCreator) {
        // 删除数据
        projectService.updateCreator(projectNo, newCreator);
        return AjaxResult.success();
    }

    /**
     * Batch Modify Tag
     */
    @Log(module1 = "Tools", module2 = "Batch Modify Tag", businessType = BusinessType.UPDATE)
    @RequestMapping("/batchModifySource")
    public AjaxResult batchModifySource(@RequestBody @Validated ModifySourceDTO modifySourceDTO) {
        projectService.batchModifySource(modifySourceDTO);
        return AjaxResult.success();
    }

    /**
     * 获取有tag的数据
     */
    @RequestMapping("/getSourceProjectMetadataList")
    public TableDataInfo getSourceProjectMetadataList(SourceProjectMetadataQueryDTO queryDTO) {
        Page<SourceProjectMetadataVO> page = projectService.getSourceProjectMetadataPage(queryDTO);
        return new TableDataInfo(page.getContent(), (int) page.getTotalElements());
    }

    /**
     * 导出项目元数据Id
     */
    @Log(module1 = "Tools", module2 = "Batch Modify Tag", businessType = BusinessType.EXPORT)
    @RequestMapping("/exportSourceProjectMetadataId")
    public void exportSourceProjectMetadataId(SourceProjectMetadataQueryDTO queryDTO, HttpServletRequest request, HttpServletResponse response) throws IOException {
        List<String> list = projectService.getSourceProjectMetadataId(queryDTO);
        File tempDir = MyFileUtils.getTempDir();
        File file = FileUtil.writeUtf8Lines(list, FileUtil.file(tempDir, queryDTO.getType() + "_id.txt"));
        DownloadUtils.download(request, response, file, queryDTO.getType() + "_id.txt");
    }

    /**
     * 导出项目邮箱
     */
    @Log(module1 = "Metadata Mgmt", module2 = "Project", desc = "导出项目邮箱", businessType = BusinessType.EXPORT)
    @RequestMapping("/exportProjectEmail")
    public void exportProjectEmail(HttpServletResponse response) {
        projectService.exportProjectEmail(response);
    }

    /**
     * 刷新Project下面的 浏览页的索引 和 Data关联大表
     */
    @Log(module1 = "Metadata Mgmt", module2 = "Project", desc = "刷新ES索引", businessType = BusinessType.OTHER)
    @RequestMapping("/refreshIndex/{projectNo}")
    public AjaxResult refreshIndex(@PathVariable("projectNo") String projectNo) {
        projectService.refreshIndex(projectNo);
        return AjaxResult.success();
    }

    /**
     * 导出project的数据
     */
    @Log(module1 = "Metadata Mgmt", module2 = "Project", module3 = "Export Data", businessType = BusinessType.EXPORT)
    @RequestMapping("/exportData")
    @RequiresPermissions("metadata:project:export")
    public void exportData(String query, HttpServletRequest request, HttpServletResponse response) throws IOException {
        MetadataQueryDTO queryDTO = JSON.parseObject(query, MetadataQueryDTO.class);
        queryDTO.checkNonQuery();
        File file = projectService.exportProject(queryDTO);
        DownloadUtils.download(request, response, file, "project.json");
    }

}
