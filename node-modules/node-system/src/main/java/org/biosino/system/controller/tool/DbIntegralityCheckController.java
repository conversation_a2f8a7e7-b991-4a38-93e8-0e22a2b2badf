package org.biosino.system.controller.tool;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.poi.ExcelUtil;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.log.annotation.Log;
import org.biosino.common.log.enums.BusinessType;
import org.biosino.system.api.domain.DbCheckLog;
import org.biosino.system.service.meta.DbCheckService;
import org.biosino.system.vo.IntegralityExportVO;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/5/23
 */
@RequestMapping("/dataIntegralityCheck")
@RestController
@RequiredArgsConstructor
public class DbIntegralityCheckController {

    private final DbCheckService dbCheckService;

    @RequestMapping("/checkAllDB")
    @Log(businessType = BusinessType.INSERT, module1 = "Data Integrality Check")
    public AjaxResult checkAllDB() {
        dbCheckService.checkAllDB();
        return AjaxResult.success();
    }

    //===========================================
    // 以下代码废弃
    @RequestMapping("/allRelatedDataCheck")
    public AjaxResult allRelatedDataCheck() {
        dbCheckService.allRelatedDataCheck();
        return AjaxResult.success();
    }

    @RequestMapping("/relatedDataCheck")
    public R<Map<String, List<DbCheckLog>>> relatedDataCheck(String[] dataNos) {
        Map<String, List<DbCheckLog>> result = dbCheckService.relatedDataCheckByDataNos(ListUtil.toList(dataNos));
        return R.ok(result);
    }

    /**
     * 导出完整性校验不通过的data no
     */
    @Log(businessType = BusinessType.EXPORT, module1 = "Data Integrality Check")
    @RequestMapping("/export")
    public void exportFtpHomeFile(HttpServletResponse response) {
        List<String> rows = dbCheckService.findDataCheckNotPass();
        if (CollUtil.isEmpty(rows)) {
            throw new ServiceException("未找到完整性验证不通过的数据");
        }
        List<IntegralityExportVO> integralityExports = new ArrayList<>(rows.size());
        for (String row : rows) {
            IntegralityExportVO vo = new IntegralityExportVO();
            vo.setDatNo(row);
            integralityExports.add(vo);
        }
        ExcelUtil<IntegralityExportVO> util = new ExcelUtil<>(IntegralityExportVO.class);
        util.exportExcel(response, integralityExports, "sheet1");
    }
}
