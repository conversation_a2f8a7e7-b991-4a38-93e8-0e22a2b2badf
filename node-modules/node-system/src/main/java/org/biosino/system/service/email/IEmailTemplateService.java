package org.biosino.system.service.email;

import org.biosino.common.mongo.entity.email.EmailSendLog;
import org.biosino.common.mongo.entity.email.EmailTemplate;
import org.biosino.system.domain.vo.SendEmailVO;
import org.biosino.system.vo.email.BatchEmailQueryVO;
import org.springframework.data.domain.PageImpl;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * Tools - Batch Sending Email
 * 批量发送邮件 服务层
 *
 * <AUTHOR>
 */
public interface IEmailTemplateService {

    List<EmailTemplate> templateList();

    PageImpl<EmailSendLog> sendLogList(BatchEmailQueryVO queryVO);

    Map<String, String> uploadEmailTemplate(MultipartFile file, String id);

    boolean save(EmailTemplate emailTemplate, String username);

    String preview(String name);

    void sendBatchEmail(Send<PERSON>mailVO sendEmailVO, String username);

}
