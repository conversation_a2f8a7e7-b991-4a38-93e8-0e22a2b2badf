package org.biosino.system.repository.impl;

import lombok.RequiredArgsConstructor;
import org.biosino.common.mongo.entity.email.EmailSendLog;
import org.biosino.system.repository.EmailSendLogCustomRepository;
import org.biosino.system.vo.email.BatchEmailQueryVO;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;

import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
public class EmailSendLogCustomRepositoryImpl implements EmailSendLogCustomRepository {
    private final MongoTemplate mongoTemplate;

    @Override
    public PageImpl<EmailSendLog> sendLogList(BatchEmailQueryVO queryVO) {
        final Pageable pageable = queryVO.initPageInfo();
        final Query query = new Query();
        final long total = mongoTemplate.count(query, clz());
        List<EmailSendLog> emailSendLogs = new ArrayList<>();
        if (total > 0) {
            query.with(pageable);
            emailSendLogs = mongoTemplate.find(query, clz());
        }
        return new PageImpl<>(emailSendLogs, pageable, total);
    }

}
