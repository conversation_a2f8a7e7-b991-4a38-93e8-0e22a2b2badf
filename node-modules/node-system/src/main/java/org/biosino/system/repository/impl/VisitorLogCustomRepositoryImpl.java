package org.biosino.system.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.mongo.entity.VisitorLog;
import org.biosino.system.dto.dto.LogQueryDTO;
import org.biosino.system.repository.VisitorLogCustomRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2024/5/20
 */
@RequiredArgsConstructor
public class VisitorLogCustomRepositoryImpl implements VisitorLogCustomRepository {

    private final MongoTemplate mongoTemplate;

    @Override
    public Page<VisitorLog> findLogPage(LogQueryDTO queryDTO) {
        List<Criteria> criteriaList = new ArrayList<>();
        if (StrUtil.isNotBlank(queryDTO.getUserId())) {
            criteriaList.add(Criteria.where("member_id").is(queryDTO.getUserId()));
        }

        if (StrUtil.isNotBlank(queryDTO.getOwnerId())) {
            criteriaList.add(Criteria.where("owner_id").is(queryDTO.getOwnerId()));
        }

        if (StrUtil.isNotBlank(queryDTO.getTypeNo())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getTypeNo() + ".*$", Pattern.CASE_INSENSITIVE);
            criteriaList.add(Criteria.where("type_id").regex(pattern));
        }

        if (StrUtil.isNotBlank(queryDTO.getIp())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getIp() + ".*$", Pattern.CASE_INSENSITIVE);
            criteriaList.add(Criteria.where("ip").regex(pattern));
        }

        if (ObjectUtil.isNotEmpty(queryDTO.getBeginTime()) && ObjectUtil.isNotEmpty(queryDTO.getEndTime())) {
            criteriaList.add(Criteria.where("create_time").gte(DateUtil.beginOfDay(queryDTO.getBeginTime())).lte(DateUtil.endOfDay(queryDTO.getEndTime())));
        } else if (ObjectUtil.isNotEmpty(queryDTO.getBeginTime())) {
            criteriaList.add(Criteria.where("create_time").gte(DateUtil.beginOfDay(queryDTO.getBeginTime())));
        } else if (ObjectUtil.isNotEmpty(queryDTO.getEndTime())) {
            criteriaList.add(Criteria.where("create_time").lte(DateUtil.endOfDay(queryDTO.getEndTime())));
        }
        Query query;
        if (CollUtil.isNotEmpty(criteriaList)) {
            query = new Query(new Criteria().andOperator(criteriaList));
        } else {
            query = new Query();
        }

        // 查询数据量
        long total = mongoTemplate.count(query, VisitorLog.class);

        // 添加分页和排序
        query.with(queryDTO.getPageable());

        // 查询query
        List<VisitorLog> content = mongoTemplate.find(query, VisitorLog.class);

        return new PageImpl<>(content, queryDTO.getPageable(), total);
    }
}
