package org.biosino.system.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.enums.sys.DataStatusEnum;
import org.biosino.common.mongo.entity.admin.SingleSampleResource;
import org.biosino.es.api.vo.fd.SingleSapQueryVO;
import org.biosino.system.repository.SingleSampleResourceCustomRepository;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.*;

import static org.biosino.system.repository.impl.MultipleOmicsResourceCustomRepositoryImpl.like;

@RequiredArgsConstructor
public class SingleSampleResourceCustomRepositoryImpl implements SingleSampleResourceCustomRepository {
    private final MongoTemplate mongoTemplate;

    @Override
    public Map<String, SingleSampleResource> findMapBySapIDIn(Collection<String> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new HashMap<>();
        }
        final List<SingleSampleResource> list = mongoTemplate.find(new Query(Criteria.where("sap_no").in(ids)), SingleSampleResource.class);
        final Map<String, SingleSampleResource> map = new HashMap<>();
        if (CollUtil.isNotEmpty(list)) {
            for (SingleSampleResource item : list) {
                map.put(item.getSapID(), item);
            }
        }
        return map;
    }

    @Override
    public PageImpl<SingleSampleResource> list(SingleSapQueryVO searchVO) {
        final List<Criteria> conditions = new ArrayList<>();

        final String projID = searchVO.getSapID();
        if (StrUtil.isNotBlank(projID)) {
            conditions.add(Criteria.where("sap_no").regex(like(projID)));
        }

        final String sapType = searchVO.getSapType();
        if (StrUtil.isNotBlank(sapType)) {
            conditions.add(Criteria.where("sapType").regex(like(sapType)));
        }

        final String sapName = searchVO.getSapName();
        if (StrUtil.isNotBlank(sapName)) {
            conditions.add(Criteria.where("sapName").regex(like(sapName)));
        }

        final String expType = searchVO.getExpType();
        if (StrUtil.isNotBlank(expType)) {
            conditions.add(Criteria.where("expTypes").regex(like(expType)));
        }

        final Query query = new Query();
        if (CollUtil.isNotEmpty(conditions)) {
            query.addCriteria(new Criteria().andOperator(conditions));
        }
        final long total = mongoTemplate.count(query, SingleSampleResource.class);
        List<SingleSampleResource> list = new ArrayList<>();
        final Pageable pageable = searchVO.initPageInfo();
        if (total > 0) {
            query.with(pageable);
            list = mongoTemplate.find(query, SingleSampleResource.class);
        }
        return new PageImpl<>(list, pageable, total);
    }

    @Override
    public List<SingleSampleResource> findByNoLike(String searchNo) {
        final List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("status").is(DataStatusEnum.enable.name()));
        if (StrUtil.isNotBlank(searchNo)) {
            condition.add(Criteria.where("sap_no").regex(like(searchNo)));
        }
        final Query query = new Query(new Criteria().andOperator(condition));
        return mongoTemplate.find(query, SingleSampleResource.class);
    }

}
