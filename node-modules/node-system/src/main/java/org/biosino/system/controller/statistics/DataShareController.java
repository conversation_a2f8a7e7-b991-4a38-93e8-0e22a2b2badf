package org.biosino.system.controller.statistics;

import lombok.RequiredArgsConstructor;
import org.biosino.common.core.utils.poi.ExcelUtil;
import org.biosino.common.core.web.controller.BaseController;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.log.annotation.Log;
import org.biosino.common.log.enums.BusinessType;
import org.biosino.system.service.statistics.DataShareService;
import org.biosino.system.vo.excel.ShareExcel;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 统计模块 - Data Share数据共享页面
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/statistics/share")
@RequiredArgsConstructor
public class DataShareController extends BaseController {

    private final DataShareService dataShareService;

    /**
     * 数据共享统计数据
     */
    @GetMapping("/statData")
    public AjaxResult statData() {
        return success(dataShareService.statData());
    }

    /**
     * Data Share Statistic导出
     */
    @PostMapping("/export")
    @Log(module1 = "统计", module2 = "数据共享", businessType = BusinessType.EXPORT)
    public void exportShare(HttpServletResponse response) {
        List<ShareExcel> excelList = dataShareService.exportShare();
        ExcelUtil<ShareExcel> util = new ExcelUtil<>(ShareExcel.class);
        util.exportExcel(response, excelList, "Data Share");
    }

}
