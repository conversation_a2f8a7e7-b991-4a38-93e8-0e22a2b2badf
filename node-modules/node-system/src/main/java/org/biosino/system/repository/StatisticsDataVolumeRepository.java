package org.biosino.system.repository;

import org.biosino.common.mongo.entity.statistics.StatisticsDataVolume;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface StatisticsDataVolumeRepository extends MongoRepository<StatisticsDataVolume, String> {

    Optional<StatisticsDataVolume> findFirstByMonth(String month);

}
