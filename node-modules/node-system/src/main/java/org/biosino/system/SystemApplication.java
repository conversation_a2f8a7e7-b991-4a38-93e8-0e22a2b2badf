package org.biosino.system;

import org.biosino.common.security.annotation.EnableCustomConfig;
import org.biosino.common.security.annotation.EnableRyFeignClients;
import org.biosino.common.swagger.annotation.EnableCustomSwagger2;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

/**
 * 系统模块
 *
 * <AUTHOR>
 */
@EnableCustomConfig
@EnableCustomSwagger2
@EnableRyFeignClients
@SpringBootApplication
@ComponentScan(basePackages = {"org.biosino.common.mongo.entity", "org.biosino.system"})
public class SystemApplication {
    public static void main(String[] args) {
        SpringApplication.run(SystemApplication.class, args);
        System.out.println("系统模块启动成功");
    }
}
