package org.biosino.system.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.enums.AuditEnum;
import org.biosino.common.core.enums.OwnershipEnum;
import org.biosino.common.core.enums.VisibleStatusEnum;
import org.biosino.common.mongo.entity.Run;
import org.biosino.common.mongo.iterator.MongoPagingIterator;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.system.dto.dto.MetadataQueryDTO;
import org.biosino.system.repository.RunCustomRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static org.biosino.common.mongo.authorize.IJudgeAuthorize.tempKey;

/**
 * <AUTHOR> Li
 * @date 2024/1/15
 */
@RequiredArgsConstructor
public class RunCustomRepositoryImpl implements RunCustomRepository {
    private final MongoTemplate mongoTemplate;

    public Criteria baseCriteria() {
        return Criteria.where("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus())
                .and("audited").is(AuditEnum.audited.name());
    }

    @Override
    public List<Run> findDetailByExpNoIn(Collection<String> expNos) {
        Criteria criteria = Criteria.where("exp_no").in(expNos)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        Query query = new Query(criteria);
        query.fields().include("run_no")
                .include("exp_no")
                .include("sap_no")
                .include("name")
                .include("visible_status");
        return mongoTemplate.find(query, Run.class);
    }

    @Override
    public List<Run> findAllByExpNoIn(List<String> expNos) {
        List<Criteria> condition = new ArrayList<>();

        condition.add(baseCriteria());
        condition.add(Criteria.where("exp_no").in(expNos));

        return mongoTemplate.find(new Query(new Criteria().andOperator(condition)), Run.class);
    }

    @Override
    public List<Run> findTempByExpNoIn(Collection<String> expNos) {
        Criteria criteria = Criteria.where(tempKey("exp_no")).in(expNos)
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        return mongoTemplate.find(new Query(criteria), Run.class);
    }

    @Override
    public List<Run> findDetailBySapNoIn(Collection<String> sapNos) {
        Criteria criteria = Criteria.where("sap_no").in(sapNos)
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        Query query = new Query(criteria);
        query.fields().include("run_no").include("sap_no").include("exp_no");
        return mongoTemplate.find(query, Run.class);
    }

    @Override
    public void updateToDeleteAllByRunNoIn(Collection<String> runNos) {
        if (CollUtil.isEmpty(runNos)) {
            return;
        }
        Query query = new Query(Criteria.where("run_no").in(runNos)
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        Update update = new Update().set("visible_status", VisibleStatusEnum.Deleted.name()).set("update_date", new Date());
        mongoTemplate.updateMulti(query, update, Run.class);
    }

    @Override
    public void updateCreatorByRunNoIn(Collection<String> runNos, String creator) {
        if (CollUtil.isEmpty(runNos)) {
            return;
        }
        Query query = new Query(Criteria.where("run_no").in(runNos)
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        Update update = new Update().set("creator", creator)
                .set("operator", SecurityUtils.getUserId().toString())
                .set("operation_date", new Date());
        mongoTemplate.updateMulti(query, update, Run.class);
    }

    @Override
    public Optional<Run> findFirstByRunNo(String runNo) {
        if (StrUtil.isBlank(runNo)) {
            return Optional.empty();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(baseCriteria());
        condition.add(new Criteria().orOperator(
                Criteria.where("run_no").is(runNo),
                Criteria.where("used_ids").in(runNo)
        ));
        Query query = new Query(new Criteria().andOperator(condition));
        Run run = mongoTemplate.findOne(query, Run.class);
        return Optional.ofNullable(run);
    }

    @Override
    public List<Run> findAllByRunNoIn(Collection<String> runNos) {
        if (CollUtil.isEmpty(runNos)) {
            return Collections.emptyList();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(baseCriteria());
        condition.add(new Criteria().orOperator(
                Criteria.where("run_no").in(runNos),
                Criteria.where("used_ids").in(runNos)
        ));
        Query query = new Query(new Criteria().andOperator(condition));

        return mongoTemplate.find(query, Run.class);
    }

    @Override
    public List<String> findAllRunNosByExpNosAndSapNos(List<String> expNos, List<String> sapNos) {
        if (CollUtil.isEmpty(expNos) && CollUtil.isEmpty(sapNos)) {
            return Collections.emptyList();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(baseCriteria());
        if (CollUtil.isNotEmpty(expNos)) {
            condition.add(Criteria.where("exp_no").in(expNos));
        }
        if (CollUtil.isNotEmpty(sapNos)) {
            condition.add(Criteria.where("sap_no").in(sapNos));
        }
        Query query = new Query(new Criteria().andOperator(condition));

        List<String> runNos = mongoTemplate.findDistinct(query, "run_no", Run.class, String.class).stream().filter(StrUtil::isNotBlank).collect(Collectors.toList());
        return runNos;
    }

    @Override
    public Page<Run> findRunPage(MetadataQueryDTO queryDTO) {
        Query query = getRunQuery(queryDTO);

        // 查询数量
        long total = mongoTemplate.count(query, Run.class);

        // 添加分页和排序
        query.with(queryDTO.getPageable());

        // 查询query
        List<Run> content = mongoTemplate.find(query, Run.class);

        return new PageImpl<>(content, queryDTO.getPageable(), total);
    }

    private Query getRunQuery(MetadataQueryDTO queryDTO) {
        List<Criteria> condition = new ArrayList<>();
        condition.add(baseCriteria());

        String name = queryDTO.getName();
        if (StrUtil.isNotBlank(name)) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getName() + ".*$", Pattern.CASE_INSENSITIVE);
            condition.add(Criteria.where("name").regex(pattern));
        }

        if (CollUtil.isNotEmpty(queryDTO.getNos())) {
            condition.add(Criteria.where("run_no").in(queryDTO.getNos()));
        }

        // 时间范围查询
        if (ObjectUtil.isNotEmpty(queryDTO.getBeginTime()) && ObjectUtil.isNotEmpty(queryDTO.getEndTime())) {
            condition.add(Criteria.where("submission_date").gte(DateUtil.beginOfDay(queryDTO.getBeginTime())).lte(DateUtil.endOfDay(queryDTO.getEndTime())));
        } else if (ObjectUtil.isNotEmpty(queryDTO.getBeginTime())) {
            condition.add(Criteria.where("submission_date").gte(DateUtil.beginOfDay(queryDTO.getBeginTime())));
        } else if (ObjectUtil.isNotEmpty(queryDTO.getEndTime())) {
            condition.add(Criteria.where("submission_date").lte(DateUtil.endOfDay(queryDTO.getEndTime())));
        }

        if (StrUtil.isNotBlank(queryDTO.getCreator())) {
            condition.add(Criteria.where("creator").is(queryDTO.getCreator()));
        }

        Query query = new Query(new Criteria().andOperator(condition));
        return query;
    }

    @Override
    public MongoPagingIterator<Run> getPagingIterator(MetadataQueryDTO queryDTO) {
        return new MongoPagingIterator<>(mongoTemplate, Run.class, getRunQuery(queryDTO), 5000);
    }
}
