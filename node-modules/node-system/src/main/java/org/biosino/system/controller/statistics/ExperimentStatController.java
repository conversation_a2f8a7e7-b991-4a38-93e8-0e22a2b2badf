package org.biosino.system.controller.statistics;

import lombok.RequiredArgsConstructor;
import org.biosino.common.core.utils.poi.ExcelUtil;
import org.biosino.common.core.web.controller.BaseController;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.log.annotation.Log;
import org.biosino.common.log.enums.BusinessType;
import org.biosino.system.service.statistics.ExperimentStatService;
import org.biosino.system.vo.excel.ExperimentExcel;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 统计模块 - Experiment组学统计页面
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/statistics/exp")
@RequiredArgsConstructor
public class ExperimentStatController extends BaseController {

    private final ExperimentStatService experimentStatService;

    /**
     * echarts统计数据
     */
    @GetMapping("/statData")
    public AjaxResult statData() {
        return success(experimentStatService.statData());
    }

    /**
     * Experiment Statistic导出
     */
    @PostMapping("/export")
    @Log(module1 = "统计", module2 = "组学", businessType = BusinessType.EXPORT)
    public void exportExp(HttpServletResponse response) {
        List<ExperimentExcel> excelList = experimentStatService.exportExp();
        ExcelUtil<ExperimentExcel> util = new ExcelUtil<>(ExperimentExcel.class);
        util.exportExcel(response, excelList, "Experiment");
    }

}
