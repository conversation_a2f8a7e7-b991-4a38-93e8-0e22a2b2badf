package org.biosino.system.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.enums.AuditEnum;
import org.biosino.common.core.enums.OwnershipEnum;
import org.biosino.common.core.enums.VisibleStatusEnum;
import org.biosino.common.mongo.dto.TypeInformation;
import org.biosino.common.mongo.entity.Project;
import org.biosino.common.mongo.iterator.MongoPagingIterator;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.system.dto.dto.MetadataQueryDTO;
import org.biosino.system.dto.dto.ModifySourceDTO;
import org.biosino.system.dto.dto.SourceProjectMetadataQueryDTO;
import org.biosino.system.repository.ProjectCustomRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.*;
import java.util.regex.Pattern;

@RequiredArgsConstructor
public class ProjectCustomRepositoryImpl implements ProjectCustomRepository {
    private final MongoTemplate mongoTemplate;

    @Override
    public Project findByNo(String projectNo) {
        Query query = new Query();
        Criteria criteria = Criteria.where("proj_no").is(projectNo);
        criteria.and("ownership").is(OwnershipEnum.self_support.getDesc());
        criteria.and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        query.addCriteria(criteria);
        return mongoTemplate.findOne(query, Project.class);
    }

    @Override
    public List<Project> findAllProject() {
        Query query = new Query();
        Criteria criteria = Criteria.where("audited").in(AuditEnum.allNotInit());
        criteria.and("ownership").is(OwnershipEnum.self_support.getDesc());
        criteria.and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        query.addCriteria(criteria);
        return mongoTemplate.find(query, Project.class);
    }

    @Override
    public Project findPublicProjectByNo(final String projectNo) {
        if (StrUtil.isBlank(projectNo)) {
            return null;
        }
        final List<Criteria> condition = baseCondition();
        condition.add(new Criteria().orOperator(
                Criteria.where("proj_no").is(projectNo),
                Criteria.where("used_ids").in(projectNo)
        ));
        return mongoTemplate.findOne(new Query(new Criteria().andOperator(condition)), Project.class);
    }

    private List<Criteria> baseCondition() {
        final List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("visible_status").is(VisibleStatusEnum.Accessible.name()));
        condition.add(Criteria.where("ownership").is(OwnershipEnum.self_support.getDesc()));
        return condition;
    }

    @Override
    public List<Project> findPublicProjectByNos(Collection<String> projectNos) {
        if (CollUtil.isEmpty(projectNos)) {
            return new ArrayList<>();
        }
        final List<Criteria> condition = baseCondition();
        condition.add(new Criteria().orOperator(
                Criteria.where("proj_no").in(projectNos),
                Criteria.where("used_ids").in(projectNos)
        ));
        return mongoTemplate.find(new Query(new Criteria().andOperator(condition)), Project.class);
    }

    @Override
    public Page<Project> findProjectPage(MetadataQueryDTO queryDTO) {
        Query query = getProjectQuery(queryDTO);

        // 查询数据量
        long total = mongoTemplate.count(query, Project.class);

        // 添加分页和排序
        query.with(queryDTO.getPageable());

        // 查询query
        List<Project> content = mongoTemplate.find(query, Project.class);

        return new PageImpl<>(content, queryDTO.getPageable(), total);
    }

    private static Query getProjectQuery(MetadataQueryDTO queryDTO) {
        List<Criteria> criteriaList = new ArrayList<>();
        criteriaList.add(Criteria.where("audited").is(AuditEnum.audited.name()));
        criteriaList.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        // 动态查询条件
        String name = queryDTO.getName();
        if (StrUtil.isNotBlank(name)) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getName() + ".*$", Pattern.CASE_INSENSITIVE);
            criteriaList.add(Criteria.where("name").regex(pattern));
        }

        if (CollUtil.isNotEmpty(queryDTO.getNos())) {
            criteriaList.add(Criteria.where("proj_no").in(queryDTO.getNos()));
        }

        if (StrUtil.isNotEmpty(queryDTO.getSubmitterEmail())) {
            criteriaList.add(Criteria.where("submitter.email").is(queryDTO.getSubmitterEmail()));
        }

        if (StrUtil.isNotBlank(queryDTO.getSubmitterOrgName())) {
            criteriaList.add(Criteria.where("submitter.org_name").is(queryDTO.getSubmitterOrgName()));
        }
        if (CollUtil.isNotEmpty(queryDTO.getTags())) {
            criteriaList.add(Criteria.where("source_project").in(queryDTO.getTags()));
        }

        // 时间范围查询
        if (ObjectUtil.isNotEmpty(queryDTO.getBeginTime()) && ObjectUtil.isNotEmpty(queryDTO.getEndTime())) {
            criteriaList.add(Criteria.where("submission_date").gte(DateUtil.beginOfDay(queryDTO.getBeginTime())).lte(DateUtil.endOfDay(queryDTO.getEndTime())));
        } else if (ObjectUtil.isNotEmpty(queryDTO.getBeginTime())) {
            criteriaList.add(Criteria.where("submission_date").gte(DateUtil.beginOfDay(queryDTO.getBeginTime())));
        } else if (ObjectUtil.isNotEmpty(queryDTO.getEndTime())) {
            criteriaList.add(Criteria.where("submission_date").lte(DateUtil.endOfDay(queryDTO.getEndTime())));
        }

        if (StrUtil.isNotBlank(queryDTO.getCreator())) {
            criteriaList.add(Criteria.where("creator").is(queryDTO.getCreator()));
        }


        Query query = new Query(new Criteria().andOperator(criteriaList));
        return query;
    }

    @Override
    public Optional<Project> findTopByProjectNo(String projectNo) {
        if (StrUtil.isBlank(projectNo)) {
            return Optional.empty();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        condition.add(Criteria.where("audited").is(AuditEnum.audited.name()));
        condition.add(new Criteria().orOperator(
                Criteria.where("proj_no").is(projectNo),
                Criteria.where("used_ids").in(projectNo)
        ));
        Project project = mongoTemplate.findOne(new Query(new Criteria().andOperator(condition)), Project.class);
        return Optional.ofNullable(project);
    }

    @Override
    public void updateToDeleteAllByProjectNo(Collection<String> projNos) {
        if (CollUtil.isEmpty(projNos)) {
            return;
        }
        // 将对应projNo的数据状态改为删除
        Query query = new Query(Criteria.where("proj_no").in(projNos));
        Update update = new Update().set("visible_status", VisibleStatusEnum.Deleted.name()).set("update_date", new Date());
        mongoTemplate.updateMulti(query, update, Project.class);
    }

    @Override
    public void updateCreatorByProjectNo(Collection<String> projNos, String creator) {
        if (CollUtil.isEmpty(projNos)) {
            return;
        }
        // 修改creator
        Query query = new Query(Criteria.where("proj_no").in(projNos));
        Update update = new Update().set("creator", creator)
                .set("operator", SecurityUtils.getUserId().toString())
                .set("operation_date", new Date());
        mongoTemplate.updateMulti(query, update, Project.class);
    }

    @Override
    public void batchModifySource(ModifySourceDTO modifySourceDTO, String mongoField, Class clazz) {
        Query query = new Query(Criteria.where(mongoField).in(modifySourceDTO.getNos()));
        Update update;
        if (modifySourceDTO.getAdd()) {
            update = new Update()
                    .addToSet("source_project").each(ArrayUtil.toArray(modifySourceDTO.getSourceProject(), String.class))
                    .set("update_date", new Date());
        } else {
            update = new Update()
                    .pullAll("source_project", ArrayUtil.toArray(modifySourceDTO.getSourceProject(), String.class))
                    .set("update_date", new Date());
        }
        mongoTemplate.updateMulti(query, update, clazz);
    }

    @Override
    public Page getSourceProjectMetadataPage(SourceProjectMetadataQueryDTO queryDTO, TypeInformation typeInformation) {

        Query query = getMetadataQuery(queryDTO, typeInformation);
        // 查询数据量
        long total = mongoTemplate.count(query, typeInformation.getClazz());

        // 添加分页和排序
        query.with(queryDTO.getPageable());

        // 查询query
        List content = mongoTemplate.find(query, typeInformation.getClazz());

        return new PageImpl<>(content, queryDTO.getPageable(), total);

    }

    private Query getMetadataQuery(SourceProjectMetadataQueryDTO queryDTO, TypeInformation typeInformation) {
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("source_project").exists(true).ne(null).ne("").not().size(0));
        condition.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));

        if (CollUtil.isNotEmpty(queryDTO.getSourceProjectIn())) {
            condition.add(Criteria.where("source_project").all(queryDTO.getSourceProjectIn()));
        }
        if (CollUtil.isNotEmpty(queryDTO.getSourceProjectNotIn())) {
            condition.add(Criteria.where("source_project").not().all(queryDTO.getSourceProjectNotIn()));
        }
        if (StrUtil.isNotBlank(queryDTO.getTypeNo())) {
            condition.add(Criteria.where(typeInformation.getMongoField()).is(queryDTO.getTypeNo()));
        }

        Query query = new Query(new Criteria().andOperator(condition));
        return query;
    }

    @Override
    public List<String> getSourceProjectMetadataId(SourceProjectMetadataQueryDTO queryDTO, TypeInformation typeInformation) {
        Query query = getMetadataQuery(queryDTO, typeInformation);
        return mongoTemplate.findDistinct(query, typeInformation.getMongoField(), typeInformation.getClazz(), String.class);
    }

    @Override
    public MongoPagingIterator<Project> getPagingIterator(MetadataQueryDTO queryDTO) {
        return new MongoPagingIterator<>(mongoTemplate, Project.class, getProjectQuery(queryDTO), 5000);
    }
}
