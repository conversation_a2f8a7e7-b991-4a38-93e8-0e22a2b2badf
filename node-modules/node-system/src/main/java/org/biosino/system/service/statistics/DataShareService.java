package org.biosino.system.service.statistics;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import org.biosino.common.mongo.entity.statistics.StatisticsShare;
import org.biosino.system.dto.mapper.StatisticsShareMapper;
import org.biosino.system.repository.StatisticsShareRepository;
import org.biosino.system.vo.ShareStatVO;
import org.biosino.system.vo.excel.ShareExcel;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class DataShareService {
    private final StatisticsShareRepository statisticsShareRepository;

    /**
     * Data Share Statistic导出
     */
    public List<ShareExcel> exportShare() {
        final List<StatisticsShare> all = statisticsShareRepository.findAll();

        final List<ShareExcel> excelData = new ArrayList<>();
        for (StatisticsShare item : all) {
            final ShareExcel shareExcel = StatisticsShareMapper.INSTANCE.dbToExcel(item);
            excelData.add(shareExcel);
        }
        return excelData;
    }

    private Map<String, StatisticsShare> genYearMap() {
        final List<StatisticsShare> all = statisticsShareRepository.findAll();
        final Map<String, StatisticsShare> yearMap = new LinkedHashMap<>();
        for (StatisticsShare statisticsShare : all) {
            final String year = parseYear(statisticsShare.getMonth());
            StatisticsShare item = yearMap.get(year);
            if (item == null) {
                yearMap.put(year, statisticsShare);
            } else {
                yearMap.put(year, add(item, statisticsShare));
            }
        }
        return yearMap;
    }

    private String parseYear(String month) {
        return month.substring(0, 4);
    }

    private StatisticsShare add(StatisticsShare base, StatisticsShare ext) {
        final JSONObject baseJson = (JSONObject) JSON.toJSON(base);
        final JSONObject extJson = (JSONObject) JSON.toJSON(ext);
        for (Map.Entry<String, Object> entry : extJson.entrySet()) {
            final Object value = entry.getValue();
            if (value != null) {
                final String key = entry.getKey();
                if (value instanceof Long) {
                    baseJson.put(key, longAdd(baseJson.getLong(key), (Long) value));
                } else if (value instanceof Integer) {
                    baseJson.put(key, longAdd(baseJson.getLong(key), ((Integer) value).longValue()));
                } else if (value instanceof Double) {
                    baseJson.put(key, doubleAdd(baseJson.getDouble(key), (Double) value));
                }
            }

        }
        return baseJson.to(StatisticsShare.class);
    }

    private long longAdd(Long n1, Long n2) {
        return defaultLongNum(n1) + defaultLongNum(n2);
    }

    private long defaultLongNum(Long n) {
        return n == null ? 0 : n;
    }

    private double doubleAdd(Double n1, Double n2) {
        return defaultDoubleNum(n1) + defaultDoubleNum(n2);
    }

    private double defaultDoubleNum(Double n) {
        return n == null ? 0 : n;
    }

    /**
     * 数据共享统计数据
     */
    public ShareStatVO statData() {
        final Map<String, StatisticsShare> yearMap = genYearMap();
        StatisticsShare total = new StatisticsShare();
        final List<ShareExcel> excelData = new ArrayList<>();
        for (Map.Entry<String, StatisticsShare> entry : yearMap.entrySet()) {
            final StatisticsShare value = entry.getValue();
            total = add(total, value);

            final ShareExcel shareExcel = StatisticsShareMapper.INSTANCE.dbToExcelNoFormat(value);
            shareExcel.setYear(entry.getKey());
            excelData.add(shareExcel);
        }

        final ShareStatVO vo = StatisticsShareMapper.INSTANCE.dbToVO(total);
        vo.setChartData(excelData);
        return vo;
    }

}
