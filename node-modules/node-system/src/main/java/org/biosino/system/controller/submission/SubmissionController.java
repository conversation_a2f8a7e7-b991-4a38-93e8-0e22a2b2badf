package org.biosino.system.controller.submission;

import org.biosino.common.core.web.controller.BaseController;
import org.biosino.common.core.web.page.TableDataInfo;
import org.biosino.system.dto.dto.SubmissionDTO;
import org.biosino.system.service.SubmissionService;
import org.biosino.system.vo.SubmissionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据提交-数据列表
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/submission")
public class SubmissionController extends BaseController {

    @Autowired
    private SubmissionService submissionService;

    /**
     * 查询当前审核员审核记录
     */
    @GetMapping("/list")
    public TableDataInfo list(SubmissionDTO dto) {
        Page<SubmissionVO> page = submissionService.list(dto);
        return new TableDataInfo(page.getContent(), (int) page.getTotalElements());
    }
}
