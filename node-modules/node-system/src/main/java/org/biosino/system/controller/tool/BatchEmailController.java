package org.biosino.system.controller.tool;

import lombok.RequiredArgsConstructor;
import org.biosino.common.core.web.controller.BaseController;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.log.annotation.Log;
import org.biosino.common.log.enums.BusinessType;
import org.biosino.common.mongo.entity.email.EmailSendLog;
import org.biosino.common.mongo.entity.email.EmailTemplate;
import org.biosino.common.security.annotation.RequiresPermissions;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.system.domain.vo.SendEmailVO;
import org.biosino.system.service.email.IEmailTemplateService;
import org.biosino.system.vo.email.BatchEmailQueryVO;
import org.springframework.data.domain.PageImpl;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * Tools - Batch Sending Email
 * 批量发送邮件 控制层
 *
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/batchEmail")
public class BatchEmailController extends BaseController {
    private final IEmailTemplateService emailTemplateService;

    /**
     * 邮件模版列表
     */
    @RequiresPermissions(value = {"tool:sendingEmail:list"})
    @GetMapping("/templateList")
    public AjaxResult templateList() {
        List<EmailTemplate> list = emailTemplateService.templateList();
        return success(list);
    }

    /**
     * 邮件发送日志列表
     */
    @RequiresPermissions(value = {"tool:sendingEmail:list"})
    @GetMapping("/sendLogList")
    public AjaxResult sendLogList(final BatchEmailQueryVO queryVO) {
        PageImpl<EmailSendLog> page = emailTemplateService.sendLogList(queryVO);
        return success(page);
    }

    /**
     * 上传邮件模版文件
     */
    @RequiresPermissions(value = {"tool:sendingEmail:list"})
    @Log(module1 = "Tools", module2 = "Batch Sending Email", businessType = BusinessType.IMPORT)
    @PostMapping("/uploadEmailTemplate")
    public AjaxResult uploadEmailTemplate(final MultipartFile file, final String id) {
        return success(emailTemplateService.uploadEmailTemplate(file, id));
    }

    /**
     * 保存新增（编辑）的数据
     */
    @RequiresPermissions(value = {"tool:sendingEmail:list"})
    @PostMapping("/save")
    @Log(module1 = "Tools", module2 = "Batch Sending Email", businessType = BusinessType.INSERT)
    public AjaxResult save(@Validated @RequestBody EmailTemplate emailTemplate) {
        return toAjax(emailTemplateService.save(emailTemplate, SecurityUtils.getUsername()));
    }

    /**
     * 预览邮件模版内容
     */
    @RequiresPermissions(value = {"tool:sendingEmail:list"})
    @GetMapping("/preview")
    public AjaxResult preview(String name) {
        return success(emailTemplateService.preview(name));
    }

    /**
     * 发送邮件
     */
    @RequiresPermissions(value = {"tool:sendingEmail:list"})
    @PostMapping("/sendBatchEmail")
    @Log(module1 = "Tools", module2 = "Batch Sending Email", businessType = BusinessType.INSERT)
    public AjaxResult sendBatchEmail(@Validated @RequestBody final SendEmailVO sendEmailVO) {
        emailTemplateService.sendBatchEmail(sendEmailVO, SecurityUtils.getUsername());
        return AjaxResult.success("success");
    }

}
