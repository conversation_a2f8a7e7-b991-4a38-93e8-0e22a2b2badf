package org.biosino.system.controller.statistics;

import lombok.RequiredArgsConstructor;
import org.biosino.common.core.utils.poi.ExcelUtil;
import org.biosino.common.core.web.controller.BaseController;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.log.annotation.Log;
import org.biosino.common.log.enums.BusinessType;
import org.biosino.system.service.statistics.MultipleOmicsStatService;
import org.biosino.system.vo.excel.PrjMultiExpExcel;
import org.biosino.system.vo.excel.SapMultiExpExcel;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 多组学统计页面
 *
 * <AUTHOR>
 * @date 2024/8/1
 */
@RestController
@RequestMapping("/statistics/multiOmics")
@RequiredArgsConstructor
public class MultipleOmicsStatController extends BaseController {
    private final MultipleOmicsStatService multipleOmicsStatService;

    /**
     * 样本基础统计表格数据
     */
    @GetMapping("/statData")
    public AjaxResult statData() {
        return success(multipleOmicsStatService.statData());
    }

    /**
     * 样本基础统计数据导出
     */
    @PostMapping("/export")
    @Log(module1 = "统计", module2 = "多组学项目", businessType = BusinessType.EXPORT)
    public void export(HttpServletResponse response) {
        List<PrjMultiExpExcel> excelList = multipleOmicsStatService.exportPrjOmics();
        ExcelUtil<PrjMultiExpExcel> util = new ExcelUtil<>(PrjMultiExpExcel.class);
        util.exportExcel(response, excelList, "Multiple Omics Project");
    }

    /**
     * 样本基础统计表格数据
     */
    @GetMapping("/sapStatData")
    public AjaxResult sapStatData() {
        return success(multipleOmicsStatService.sapStatData());
    }

    /**
     * 样本基础统计数据导出
     */
    @PostMapping("/exportSap")
    @Log(module1 = "统计", module2 = "多组学样本", businessType = BusinessType.EXPORT)
    public void exportSap(HttpServletResponse response) {
        List<SapMultiExpExcel> excelList = multipleOmicsStatService.exportSapOmics();
        ExcelUtil<SapMultiExpExcel> util = new ExcelUtil<>(SapMultiExpExcel.class);
        util.exportExcel(response, excelList, "Multiple Omics Sample");
    }

}
