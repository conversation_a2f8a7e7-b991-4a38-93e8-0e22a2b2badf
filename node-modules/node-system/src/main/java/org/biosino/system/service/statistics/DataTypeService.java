package org.biosino.system.service.statistics;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.mongo.entity.statistics.StatisticsDataType;
import org.biosino.system.dto.mapper.StatisticsDataTypeMapper;
import org.biosino.system.repository.StatisticsDataTypeRepository;
import org.biosino.system.repository.util.RepositoryUtil;
import org.biosino.system.vo.DataTypeStatVO;
import org.biosino.system.vo.excel.DataTypeExcel;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 数据类型统计
 *
 * <AUTHOR>
 * @date 2024/7/31
 */
@Service
@RequiredArgsConstructor
public class DataTypeService {
    private final StatisticsDataTypeRepository statisticsDataTypeRepository;

    private final MongoTemplate mongoTemplate;

    public List<DataTypeStatVO> statData() {
        final List<StatisticsDataType> all = RepositoryUtil.findLatestMonthData(mongoTemplate, StatisticsDataType.class);
        if (CollUtil.isEmpty(all)) {
            throw new ServiceException("No statistic data");
        }
        return StatisticsDataTypeMapper.INSTANCE.dbToVO(all);
    }

    public List<DataTypeExcel> exportDataType() {
        final List<StatisticsDataType> all = RepositoryUtil.findBaseList(mongoTemplate, true, StatisticsDataType.class);
        if (CollUtil.isEmpty(all)) {
            throw new ServiceException("No statistic data");
        }
        return StatisticsDataTypeMapper.INSTANCE.dbToExcel(all);
    }

}
