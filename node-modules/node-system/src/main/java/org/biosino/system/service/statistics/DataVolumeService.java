package org.biosino.system.service.statistics;

import cn.hutool.core.bean.BeanUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.utils.NodeUtils;
import org.biosino.common.mongo.entity.statistics.StatisticsDataFlow;
import org.biosino.common.mongo.entity.statistics.StatisticsDataVolume;
import org.biosino.job.api.RemoteStatisticsSearchService;
import org.biosino.job.api.vo.statistics.StatDataVO;
import org.biosino.job.api.vo.statistics.StatisticsMetadataVO;
import org.biosino.system.vo.excel.DataExcel;
import org.biosino.system.vo.excel.DataFlowExcel;
import org.biosino.system.vo.excel.MetaDataExcel;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户中的My Data Statistics
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class DataVolumeService {
    private final MongoTemplate mongoTemplate;
    private final RemoteStatisticsSearchService remoteStatisticsSearchService;

    public List<MetaDataExcel> getDataVolumeMetadata() {
        List<StatisticsDataVolume> volumeList = mongoTemplate.findAll(StatisticsDataVolume.class);

        List<MetaDataExcel> metaDataList = new ArrayList<>();

        for (int i = 0; i < volumeList.size(); i++) {
            MetaDataExcel preMetaData = null;
            if (i != 0) {
                preMetaData = metaDataList.get(i - 1);
            }
            StatisticsDataVolume dataVolume = volumeList.get(i);
            MetaDataExcel metaDataExcel = new MetaDataExcel();
            BeanUtil.copyProperties(dataVolume, metaDataExcel);

            metaDataExcel.setProjTotal(dataVolume.getProjAccessible() + dataVolume.getProjUnAccessible());
            metaDataExcel.setProjAccessiblePercentage(NodeUtils.div(metaDataExcel.getProjAccessible(), metaDataExcel.getProjTotal()));
            metaDataExcel.setProjUnAccessiblePercentage(NodeUtils.div(metaDataExcel.getProjUnAccessible(), metaDataExcel.getProjTotal()));

            metaDataExcel.setExpTotal(dataVolume.getExpAccessible() + dataVolume.getExpUnAccessible());
            metaDataExcel.setExpAccessiblePercentage(NodeUtils.div(metaDataExcel.getExpAccessible(), metaDataExcel.getExpTotal()));
            metaDataExcel.setExpUnAccessiblePercentage(NodeUtils.div(metaDataExcel.getExpUnAccessible(), metaDataExcel.getExpTotal()));

            metaDataExcel.setSapTotal(dataVolume.getSapAccessible() + dataVolume.getSapUnAccessible());
            metaDataExcel.setSapAccessiblePercentage(NodeUtils.div(metaDataExcel.getSapAccessible(), metaDataExcel.getSapTotal()));
            metaDataExcel.setSapUnAccessiblePercentage(NodeUtils.div(metaDataExcel.getSapUnAccessible(), metaDataExcel.getSapTotal()));

            metaDataExcel.setRunTotal(dataVolume.getRunAccessible() + dataVolume.getRunUnAccessible());
            metaDataExcel.setRunAccessiblePercentage(NodeUtils.div(metaDataExcel.getRunAccessible(), metaDataExcel.getRunTotal()));
            metaDataExcel.setRunUnAccessiblePercentage(NodeUtils.div(metaDataExcel.getRunUnAccessible(), metaDataExcel.getRunTotal()));

            metaDataExcel.setAnalTotal(dataVolume.getAnalAccessible() + dataVolume.getAnalUnAccessible());
            metaDataExcel.setAnalAccessiblePercentage(NodeUtils.div(metaDataExcel.getAnalAccessible(), metaDataExcel.getAnalTotal()));
            metaDataExcel.setAnalUnAccessiblePercentage(NodeUtils.div(metaDataExcel.getAnalUnAccessible(), metaDataExcel.getAnalTotal()));

            if (preMetaData != null) {
                metaDataExcel.setProjTotalIncrement(metaDataExcel.getProjTotal() - preMetaData.getProjTotal());
                metaDataExcel.setProjAccessibleIncrement(metaDataExcel.getProjAccessible() - preMetaData.getProjAccessible());
                metaDataExcel.setExpTotalIncrement(metaDataExcel.getExpTotal() - preMetaData.getExpTotal());
                metaDataExcel.setExpAccessibleIncrement(metaDataExcel.getExpAccessible() - preMetaData.getExpAccessible());
                metaDataExcel.setSapTotalIncrement(metaDataExcel.getSapTotal() - preMetaData.getSapTotal());
                metaDataExcel.setSapAccessibleIncrement(metaDataExcel.getSapAccessible() - preMetaData.getSapAccessible());
                metaDataExcel.setRunTotalIncrement(metaDataExcel.getRunTotal() - preMetaData.getRunTotal());
                metaDataExcel.setRunAccessibleIncrement(metaDataExcel.getRunAccessible() - preMetaData.getRunAccessible());
                metaDataExcel.setAnalTotalIncrement(metaDataExcel.getAnalTotal() - preMetaData.getAnalTotal());
                metaDataExcel.setAnalAccessibleIncrement(metaDataExcel.getAnalAccessible() - preMetaData.getAnalAccessible());
            }

            metaDataList.add(metaDataExcel);
        }
        return metaDataList;
    }

    public List<DataExcel> getDataStatistics(boolean rawData) {
        final R<StatisticsMetadataVO> dataStatisticsR = remoteStatisticsSearchService.getDataVolumeStatInfo(SecurityConstants.INNER);
        StatisticsMetadataVO statisticsData = dataStatisticsR.getData();

        List<StatDataVO> rawDataList;
        if (rawData) {
            rawDataList = statisticsData.getRawDataList();
        } else {
            rawDataList = statisticsData.getAnalDataList();
        }

        List<DataExcel> dataExcelList = new ArrayList<>();

        for (int i = 0; i < rawDataList.size(); i++) {
            DataExcel preDataExcel = null;
            if (i != 0) {
                preDataExcel = dataExcelList.get(i - 1);
            }
            StatDataVO dataVolume = rawDataList.get(i);
            DataExcel dataExcel = new DataExcel();
            BeanUtil.copyProperties(dataVolume, dataExcel);


            dataExcel.setDataPublicPercentage(NodeUtils.div(dataVolume.getDataPublic(), dataVolume.getDataTotal()));
            dataExcel.setDataRestrictedPercentage(NodeUtils.div(dataVolume.getDataRestricted(), dataVolume.getDataTotal()));
            dataExcel.setDataPrivatePercentage(NodeUtils.div(dataVolume.getDataPrivate(), dataVolume.getDataTotal()));

            Long dataTotalSize = dataVolume.getDataPrivateSize() + dataVolume.getDataRestrictedSize() + dataVolume.getDataPublicSize();
            dataExcel.setDataTotalSize(NodeUtils.convertToTB(dataTotalSize));
            dataExcel.setDataPublicSize(NodeUtils.convertToTB(dataVolume.getDataPublicSize()));
            dataExcel.setDataRestrictedSize(NodeUtils.convertToTB(dataVolume.getDataPublicSize()));
            dataExcel.setDataPrivateSize(NodeUtils.convertToTB(dataVolume.getDataPrivateSize()));

            dataExcel.setDataPublicSizePercentage(NodeUtils.div(dataVolume.getDataPublicSize(), dataTotalSize));
            dataExcel.setDataRestrictedSizePercentage(NodeUtils.div(dataVolume.getDataRestrictedSize(), dataTotalSize));
            dataExcel.setDataPrivateSizePercentage(NodeUtils.div(dataVolume.getDataPrivateSize(), dataTotalSize));

            if (preDataExcel != null) {
                dataExcel.setDataIncrement(dataExcel.getDataTotal() - preDataExcel.getDataTotal());
                dataExcel.setDataPublicIncrement(dataExcel.getDataPublic() - preDataExcel.getDataPublic());
                dataExcel.setDataRestrictedIncrement(dataExcel.getDataRestricted() - preDataExcel.getDataRestricted());
                dataExcel.setDataPrivateIncrement(dataExcel.getDataPrivate() - preDataExcel.getDataPrivate());

                dataExcel.setDataSizeIncrement(dataExcel.getDataTotalSize() - preDataExcel.getDataTotalSize());
                dataExcel.setDataPublicSizeIncrement(dataExcel.getDataPublicSize() - preDataExcel.getDataPublicSize());
                dataExcel.setDataRestrictedSizeIncrement(dataExcel.getDataRestrictedSize() - preDataExcel.getDataRestrictedSize());
                dataExcel.setDataPrivateSizeIncrement(dataExcel.getDataPrivateSize() - preDataExcel.getDataPrivateSize());
            }

            dataExcelList.add(dataExcel);
        }

        return dataExcelList;
    }

    public List<DataFlowExcel> getDataFlow() {
        List<StatisticsDataFlow> dataFlowList = mongoTemplate.findAll(StatisticsDataFlow.class);

        List<DataFlowExcel> excelList = new ArrayList<>();

        for (StatisticsDataFlow dataFlow : dataFlowList) {
            DataFlowExcel excel = new DataFlowExcel();
            BeanUtil.copyProperties(dataFlow, excel);

            excel.setDownloadDataSize(NodeUtils.convertToGB(dataFlow.getDownloadDataSize()));
            excel.setUploadDataSize(NodeUtils.convertToGB(dataFlow.getUploadDataSize()));
            excel.setSubmissionDataSize(NodeUtils.convertToGB(dataFlow.getSubmissionDataSize()));

            excelList.add(excel);
        }

        return excelList;
    }
}
