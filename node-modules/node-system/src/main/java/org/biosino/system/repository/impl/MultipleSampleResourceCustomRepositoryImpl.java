package org.biosino.system.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.enums.sys.DataStatusEnum;
import org.biosino.common.mongo.entity.admin.MultipleSampleResource;
import org.biosino.es.api.vo.fd.MultipleSampleQueryVO;
import org.biosino.system.repository.MultipleSampleResourceCustomRepository;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.*;

import static org.biosino.system.repository.impl.MultipleOmicsResourceCustomRepositoryImpl.like;

/**
 * 多组学资源
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class MultipleSampleResourceCustomRepositoryImpl implements MultipleSampleResourceCustomRepository {
    private final MongoTemplate mongoTemplate;

    @Override
    public Map<String, MultipleSampleResource> findMapByProjIDIn(Collection<String> projIDs) {
        if (CollUtil.isEmpty(projIDs)) {
            return new HashMap<>();
        }
        final List<MultipleSampleResource> list = mongoTemplate.find(new Query(Criteria.where("proj_no").in(projIDs)), MultipleSampleResource.class);
        final Map<String, MultipleSampleResource> map = new HashMap<>();
        if (CollUtil.isNotEmpty(list)) {
            for (MultipleSampleResource item : list) {
                map.put(item.getProjID(), item);
            }
        }
        return map;
    }

    @Override
    public PageImpl<MultipleSampleResource> multSampleList(MultipleSampleQueryVO searchVO) {
        final List<Criteria> conditions = new ArrayList<>();

        final String projID = searchVO.getProjID();
        if (StrUtil.isNotBlank(projID)) {
            conditions.add(Criteria.where("proj_no").regex(like(projID)));
        }

        final String projName = searchVO.getProjName();
        if (StrUtil.isNotBlank(projName)) {
            conditions.add(Criteria.where("projName").regex(like(projName)));
        }

        final String sapType = searchVO.getSapType();
        if (StrUtil.isNotBlank(sapType)) {
            conditions.add(Criteria.where("sapTypes").regex(like(sapType)));
        }

        final Query query = new Query();
        if (CollUtil.isNotEmpty(conditions)) {
            query.addCriteria(new Criteria().andOperator(conditions));
        }
        final long total = mongoTemplate.count(query, MultipleSampleResource.class);
        List<MultipleSampleResource> list = new ArrayList<>();
        final Pageable pageable = searchVO.initPageInfo();
        if (total > 0) {
            query.with(pageable);
            list = mongoTemplate.find(query, MultipleSampleResource.class);
        }
        return new PageImpl<>(list, pageable, total);
    }

    @Override
    public List<MultipleSampleResource> findByNoLike(String searchNo) {
        final List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("status").is(DataStatusEnum.enable.name()));
        if (StrUtil.isNotBlank(searchNo)) {
            condition.add(Criteria.where("proj_no").regex(like(searchNo)));
        }
        final Query query = new Query(new Criteria().andOperator(condition));
        return mongoTemplate.find(query, MultipleSampleResource.class);
    }

}
