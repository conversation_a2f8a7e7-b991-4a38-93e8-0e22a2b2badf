package org.biosino.system.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.enums.AuditEnum;
import org.biosino.common.core.enums.OwnershipEnum;
import org.biosino.common.core.enums.VisibleStatusEnum;
import org.biosino.common.mongo.entity.Experiment;
import org.biosino.common.mongo.iterator.MongoPagingIterator;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.system.dto.dto.MetadataQueryDTO;
import org.biosino.system.repository.ExperimentCustomRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static org.biosino.common.mongo.authorize.IJudgeAuthorize.tempKey;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class ExperimentCustomRepositoryImpl implements ExperimentCustomRepository {

    private final MongoTemplate mongoTemplate;

    public Criteria baseCriteria() {
        return Criteria.where("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus())
                .and("audited").is(AuditEnum.audited.name());
    }

    @Override
    public Experiment findByNo(String expNo) {
        Query query = new Query();
        Criteria criteria = Criteria.where("exp_no").is(expNo);
        criteria.and("ownership").is(OwnershipEnum.self_support.getDesc());
        criteria.and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        query.addCriteria(criteria);
        return mongoTemplate.findOne(query, Experiment.class);
    }

    /**
     * 根据ProjectNo下所有的实验编号
     */
    @Override
    public List<Experiment> findDetailByProjNo(String projNo) {
        Query query = Query.query(Criteria.where("proj_no").is(projNo).and("ownership")
                .is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus())
                .and("audited").is(AuditEnum.audited.name()));
        query.fields().include("exp_no")
                .include("proj_no")
                .include("name")
                .include("description")
                .include("exp_type")
                .include("visible_status");
        return mongoTemplate.find(query, Experiment.class);
    }

    @Override
    public List<Experiment> findDetailByProjNoIn(Collection<String> projNos) {
        Query query = Query.query(Criteria.where("proj_no").in(projNos).and("ownership")
                .is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus())
                .and("audited").is(AuditEnum.audited.name()));
        query.fields().include("exp_no")
                .include("proj_no")
                .include("name")
                .include("description")
                .include("exp_type")
                .include("visible_status");
        return mongoTemplate.find(query, Experiment.class);
    }

    @Override
    public List<Experiment> findAllByProjectNo(String projNo) {
        List<Criteria> condition = new ArrayList<>();
        condition.add(baseCriteria());
        condition.add(Criteria.where("proj_no").is(projNo));

        return mongoTemplate.find(Query.query(new Criteria().andOperator(condition)), Experiment.class);
    }

    @Override
    public List<Experiment> findTempByProjNo(String projNo) {
        Query query = Query.query(Criteria.where(tempKey("proj_no")).is(projNo)
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        return mongoTemplate.find(query, Experiment.class);
    }

    @Override
    public void updateToDeleteAllByExpNoIn(Collection<String> expNos) {
        if (CollUtil.isEmpty(expNos)) {
            return;
        }
        Query query = new Query(Criteria.where("exp_no").in(expNos));
        Update update = new Update().set("visible_status", VisibleStatusEnum.Deleted.name()).set("update_date", new Date());
        mongoTemplate.updateMulti(query, update, Experiment.class);
    }

    @Override
    public void updateCreatorByExpNoIn(Collection<String> expNos, String creator) {
        if (CollUtil.isEmpty(expNos)) {
            return;
        }
        Query query = new Query(Criteria.where("exp_no").in(expNos));
        Update update = new Update().set("creator", creator)
                .set("operator", SecurityUtils.getUserId().toString())
                .set("operation_date", new Date());
        mongoTemplate.updateMulti(query, update, Experiment.class);
    }

    @Override
    public Page<Experiment> findExperimentPage(MetadataQueryDTO queryDTO) {
        Query query = getExperimentQuery(queryDTO);

        // 查询数据量
        long total = mongoTemplate.count(query, Experiment.class);

        // 添加分页和排序
        query.with(queryDTO.getPageable());

        // 查询query
        List<Experiment> content = mongoTemplate.find(query, Experiment.class);

        return new PageImpl<>(content, queryDTO.getPageable(), total);
    }

    private static Query getExperimentQuery(MetadataQueryDTO queryDTO) {
        List<Criteria> criteriaList = new ArrayList<>();
        criteriaList.add(Criteria.where("audited").is(AuditEnum.audited.name()));
        criteriaList.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));

        String name = queryDTO.getName();
        if (StrUtil.isNotBlank(name)) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getName() + ".*$", Pattern.CASE_INSENSITIVE);
            criteriaList.add(Criteria.where("name").regex(pattern));
        }

        if (CollUtil.isNotEmpty(queryDTO.getNos())) {
            criteriaList.add(Criteria.where("exp_no").in(queryDTO.getNos()));
        }

        if (StrUtil.isNotEmpty(queryDTO.getSubmitterEmail())) {
            criteriaList.add(Criteria.where("submitter.email").is(queryDTO.getSubmitterEmail()));
        }

        if (StrUtil.isNotBlank(queryDTO.getSubmitterOrgName())) {
            criteriaList.add(Criteria.where("submitter.org_name").is(queryDTO.getSubmitterOrgName()));
        }
        if (CollUtil.isNotEmpty(queryDTO.getTags())) {
            criteriaList.add(Criteria.where("source_project").in(queryDTO.getTags()));
        }

        if (ObjectUtil.isNotEmpty(queryDTO.getBeginTime()) && ObjectUtil.isNotEmpty(queryDTO.getEndTime())) {
            criteriaList.add(Criteria.where("submission_date").gte(DateUtil.beginOfDay(queryDTO.getBeginTime())).lte(DateUtil.endOfDay(queryDTO.getEndTime())));
        } else if (ObjectUtil.isNotEmpty(queryDTO.getBeginTime())) {
            criteriaList.add(Criteria.where("submission_date").gte(DateUtil.beginOfDay(queryDTO.getBeginTime())));
        } else if (ObjectUtil.isNotEmpty(queryDTO.getEndTime())) {
            criteriaList.add(Criteria.where("submission_date").lte(DateUtil.endOfDay(queryDTO.getEndTime())));
        }
        if (CollUtil.isNotEmpty(queryDTO.getExpTypes())) {
            criteriaList.add(Criteria.where("exp_type").in(queryDTO.getExpTypes()));
        }

        if (StrUtil.isNotBlank(queryDTO.getCreator())) {
            criteriaList.add(Criteria.where("creator").is(queryDTO.getCreator()));
        }

        if (CollUtil.isNotEmpty(queryDTO.getAdvQueryList())) {
            // 添加高级搜索条件
            List<MetadataQueryDTO.AdvQueryDTO> advQueryDTOList = queryDTO.getAdvQueryList().stream()
                    .filter(x -> StrUtil.isNotBlank(x.getQueryField()) && StrUtil.isNotBlank(x.getInputValue()))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(advQueryDTOList)) {
                Criteria criteria = null;
                for (MetadataQueryDTO.AdvQueryDTO advQueryDTO : advQueryDTOList) {
                    Criteria tempCriteria = Criteria.where(advQueryDTO.getQueryField()).is(advQueryDTO.getInputValue());
                    if (StrUtil.containsIgnoreCase(advQueryDTO.getRelation(), "NOT")) {
                        tempCriteria = Criteria.where(advQueryDTO.getQueryField()).ne(advQueryDTO.getInputValue());
                    }
                    if (StrUtil.containsIgnoreCase(advQueryDTO.getRelation(), "LIKE")) {
                        Pattern pattern = Pattern.compile("^.*" + advQueryDTO.getInputValue() + ".*$", Pattern.CASE_INSENSITIVE);
                        tempCriteria = Criteria.where(advQueryDTO.getQueryField()).regex(pattern);
                    }
                    if (criteria == null) {
                        criteria = tempCriteria;
                    } else {
                        switch (advQueryDTO.getRelation().toUpperCase()) {
                            case "AND":
                                criteria = new Criteria().andOperator(criteria, tempCriteria);
                                break;
                            case "OR":
                                criteria = new Criteria().orOperator(criteria, tempCriteria);
                                break;
                            case "AND NOT":
                                criteria = new Criteria().andOperator(criteria, tempCriteria);
                                break;
                            case "AND LIKE":
                                criteria = new Criteria().andOperator(criteria, tempCriteria);
                                break;
                            case "OR NOT":
                                criteria = new Criteria().orOperator(criteria, tempCriteria);
                                break;
                            case "OR LIKE":
                                criteria = new Criteria().orOperator(criteria, tempCriteria);
                                break;
                            default:
                                break;
                        }
                    }
                }
                criteriaList.add(criteria);
            }
        }

        Query query = new Query(new Criteria().andOperator(criteriaList));
        return query;
    }

    @Override
    public List<String> getAuditedExpType() {
        Criteria criteria = baseCriteria();
        Query query = new Query(criteria);
        query.fields().include("exp_type");
        return mongoTemplate.findDistinct(query, "exp_type", Experiment.class, String.class);
    }

    @Override
    public Optional<Experiment> findTopByExpNo(String expNo) {
        if (StrUtil.isBlank(expNo)) {
            return Optional.empty();
        }

        List<Criteria> condition = new ArrayList<>();
        condition.add(baseCriteria());
        condition.add(new Criteria().orOperator(
                Criteria.where("exp_no").is(expNo),
                Criteria.where("used_ids").in(expNo)));

        Query query = new Query(new Criteria().andOperator(condition));
        Experiment exp = mongoTemplate.findOne(query, Experiment.class);
        return Optional.ofNullable(exp);
    }

    @Override
    public List<Experiment> findAllByExpNoIn(List<String> expNos) {
        if (CollUtil.isEmpty(expNos)) {
            return new ArrayList<>();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(baseCriteria());
        condition.add(Criteria.where("exp_no").in(expNos));
        Query query = new Query(new Criteria().andOperator(condition));
        return mongoTemplate.find(query, Experiment.class);
    }

    @Override
    public List<String> getExperimentNos(MetadataQueryDTO queryDTO) {
        Query query = getExperimentQuery(queryDTO);
        return mongoTemplate.findDistinct(query, "exp_no", Experiment.class, String.class);
    }

    @Override
    public MongoPagingIterator<Experiment> getPagingIterator(MetadataQueryDTO queryDTO) {
        return new MongoPagingIterator<>(mongoTemplate, Experiment.class, getExperimentQuery(queryDTO), 5000);
    }
}
