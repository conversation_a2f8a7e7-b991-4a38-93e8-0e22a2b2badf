package org.biosino.system.service.meta;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONWriter;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.enums.AuthorizeType;
import org.biosino.common.core.enums.RouterKeyEnum;
import org.biosino.common.core.enums.SecurityEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.file.MyFileUtils;
import org.biosino.common.mongo.entity.Data;
import org.biosino.common.mongo.entity.Experiment;
import org.biosino.common.mongo.entity.Run;
import org.biosino.common.mongo.entity.Sample;
import org.biosino.common.mongo.iterator.MongoPagingIterator;
import org.biosino.common.rabbitmq.MessageSender;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.es.api.msg.IndexUpdateMsg;
import org.biosino.system.api.dto.MemberDTO;
import org.biosino.system.dto.dto.ExperimentDTO;
import org.biosino.system.dto.dto.MetadataQueryDTO;
import org.biosino.system.dto.dto.export.ExperimentExportDTO;
import org.biosino.system.repository.*;
import org.biosino.system.vo.metadata.ExperimentListVO;
import org.biosino.system.vo.metadata.ExperimentVO;
import org.biosino.system.vo.metadata.PublishVO;
import org.biosino.upload.api.RemoteUploadExperimentService;
import org.biosino.upload.api.vo.DeleteCheckResultVO;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileWriter;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Li
 * @date 2024/4/26
 */
@Service
@RequiredArgsConstructor
public class ExperimentService extends BaseService {

    private final ExperimentRepository experimentRepository;

    private final RunRepository runRepository;

    private final SampleRepository sampleRepository;

    private final DataRepository dataRepository;

    private final PublishRepository publishRepository;

    private final RemoteUploadExperimentService remoteUploadExperimentService;

    private final MessageSender messageSender;


    public Page<ExperimentListVO> listAuditedExperiment(MetadataQueryDTO queryDTO) {
        // 将email转为memberId
        setMetaQueryDTOCreator(queryDTO);

        Page<Experiment> page = experimentRepository.findExperimentPage(queryDTO);

        // 从分页内容中提取实验编号
        List<String> expNos = page.getContent().stream()
                .map(Experiment::getExpNo)
                .collect(Collectors.toList());

        // 批量获取所有实验的运行数据
        List<Run> allRuns = runRepository.findDetailByExpNoIn(expNos);


        // 将运行数据映射到对应的实验编号
        Map<String, List<Run>> expNoToRunsMap = allRuns.stream()
                .collect(Collectors.groupingBy(Run::getExpNo));

        // 批量获取所有运行的样本数据
        List<String> sapNos = allRuns.stream()
                .map(Run::getSapNo)
                .distinct()
                .collect(Collectors.toList());

        List<Sample> allSamples = sampleRepository.findDetailBySapNoIn(sapNos);

        // 将样本数据映射到对应的样本编号
        Map<String, List<Sample>> sapNoToSamplesMap = allSamples.stream()
                .collect(Collectors.groupingBy(Sample::getSapNo));

        // 一次性查询所有相关data
        Map<String, List<Data>> runNoToDatasMap = dataRepository
                .findDetailByRunNoIn(allRuns.stream()
                        .map(Run::getRunNo)
                        .collect(Collectors.toList()))
                .stream().collect(Collectors.groupingBy(Data::getRunNo));

        List<String> creators = page.getContent().stream().map(Experiment::getCreator).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());

        // 获取memberId到email对映射关系
        Map<String, String> memberIdToEmailMap = getMemberIdToEmailMap(creators);

        Page<ExperimentListVO> result = page.map(x -> {
            ExperimentListVO vo = new ExperimentListVO();
            BeanUtil.copyProperties(x, vo);

            // 获取该实验的所有运行数据
            List<Run> runList = expNoToRunsMap.getOrDefault(x.getExpNo(), Collections.emptyList());

            // 获取这些运行的所有样本数据
            List<Sample> sapList = runList.stream()
                    .map(Run::getSapNo)
                    .distinct()
                    .flatMap(sapNo -> sapNoToSamplesMap.getOrDefault(sapNo, Collections.emptyList()).stream())
                    .collect(Collectors.toList());

            vo.setSapTypes(sapList.stream().map(Sample::getOrganism).distinct().collect(Collectors.toList()));
            vo.setSapNum(sapList.size());

            List<Data> dataList = runList.stream()
                    .map(Run::getRunNo)
                    .flatMap(runNo -> runNoToDatasMap.getOrDefault(runNo, Collections.emptyList()).stream())
                    .collect(Collectors.toList());

            Map<String, Long> securityMap = dataList.stream()
                    .collect(Collectors.groupingBy(Data::getSecurity, Collectors.counting()));

            for (String s : SecurityEnum.includeAllSecurity()) {
                securityMap.putIfAbsent(s, 0L);
            }

            vo.setDataCount(securityMap);

            vo.setSubmitter(x.getSubmitter().getFirstName() + " " + x.getSubmitter().getLastName());

            // 设置creatorEmail
            vo.setCreatorEmail(memberIdToEmailMap.get(x.getCreator()));

            return vo;
        });
        return result;
    }

    public ExperimentVO getExpInfoByNo(String expNo) {
        Experiment exp = experimentRepository.findTopByExpNo(expNo).orElseThrow(() -> new ServiceException("Not found Experiment"));
        ExperimentVO result = new ExperimentVO();
        BeanUtil.copyProperties(exp, result);
        List<PublishVO> publishVo = getPublishVo(AuthorizeType.experiment, expNo);
        result.setPublish(publishVo);

        return result;
    }

    public List<String> getAuditedExpType() {
        return experimentRepository.getAuditedExpType().stream().sorted().collect(Collectors.toList());
    }

    public ExperimentVO updateExperiment(ExperimentDTO experimentDTO) {
        Experiment exp = experimentRepository.findTopByExpNo(experimentDTO.getExpNo()).orElseThrow(() -> new ServiceException("No Experiment found"));
        BeanUtil.copyProperties(experimentDTO, exp);
        exp.setOperator(SecurityUtils.getUserId().toString());
        exp.setOperationDate(new Date());
        // 更新publish
        savePublish(experimentDTO.getPublish(), AuthorizeType.experiment, exp.getExpNo(), exp.getCreator());
        experimentRepository.save(exp);
        // 通知es更新索引
        updateEsData(AuthorizeType.experiment.name(), exp.getExpNo());

        return getExpInfoByNo(exp.getExpNo());
    }

    public DeleteCheckResultVO deleteCheck(String expNo) {
        Experiment exp = experimentRepository.findTopByExpNo(expNo).orElseThrow(() -> new ServiceException("No Experiment found"));
        R<DeleteCheckResultVO> r = remoteUploadExperimentService.deleteCheck(exp.getExpNo(), exp.getCreator());
        if (R.isError(r)) {
            throw new ServiceException(r.getMsg());
        }
        return r.getData();
    }

    public void deleteAll(String expNo) {
        Experiment exp = experimentRepository.findTopByExpNo(expNo).orElseThrow(() -> new ServiceException("No Experiment found"));
        R r = remoteUploadExperimentService.deleteAll(exp.getExpNo(), exp.getCreator());
        if (R.isError(r)) {
            throw new ServiceException(r.getMsg());
        }
    }

    public void updateCreator(String expNo, String creator) {
        if (StrUtil.isBlank(creator) || StrUtil.isBlank(expNo)) {
            throw new ServiceException("Parameter cannot be empty");
        }
        Experiment exp = experimentRepository.findTopByExpNo(expNo).orElseThrow(() -> new ServiceException("No Experiment found"));


        MemberDTO data = getMemberInfoByEmail(creator);
        String newCreator = data.getId();
        if (newCreator == null) {
            throw new ServiceException(StrUtil.format("User {} not found", creator));
        }
        DeleteCheckResultVO checkResultVO = deleteCheck(expNo);
        // 删除数据
        if (CollUtil.isNotEmpty(checkResultVO.getErrors())) {
            throw new ServiceException("The experiment cannot change creator because it is associated with other data");
        }
        // 添加修改的日志
        addChangeCreatorLog(expNo, AuthorizeType.experiment.name(), exp.getCreator(), newCreator, checkResultVO);

        // 修改experiment以及关联数据的creator
        experimentRepository.updateCreatorByExpNoIn(checkResultVO.getExpNos(), newCreator);
        runRepository.updateCreatorByRunNoIn(checkResultVO.getRunNos(), newCreator);
        sampleRepository.updateCreatorBySapNoIn(checkResultVO.getSapNos(), newCreator);
        dataRepository.updateCreatorByDatNoIn(checkResultVO.getDataNos(), newCreator);

        // 修改数据相关publish的creator
        publishRepository.updateCreatorByTypeAndTypeId(AuthorizeType.experiment.name(), checkResultVO.getExpNos(), newCreator);
        publishRepository.updateCreatorByTypeAndTypeId(AuthorizeType.sample.name(), checkResultVO.getSapNos(), newCreator);

        // 通知更新索引
        if (CollUtil.isNotEmpty(checkResultVO.getDataNos())) {
            messageSender.sendDelayMsg(RouterKeyEnum.es_index_update_key.name(), new IndexUpdateMsg(AuthorizeType.data.name(), checkResultVO.getDataNos()));
        }
    }

    public List<String> getExperimentNos(MetadataQueryDTO queryDTO) {
        queryDTO.setPageSize(-1);
        return experimentRepository.getExperimentNos(queryDTO);
    }

    public File exportExperiment(MetadataQueryDTO queryDTO) {
        // 将email转为memberId
        setMetaQueryDTOCreator(queryDTO);
        MongoPagingIterator<Experiment> iterator = experimentRepository.getPagingIterator(queryDTO);
        File tempDir = MyFileUtils.getTempDir();
        File resultFile = FileUtil.file(tempDir, "experiment.json");
        FileUtil.touch(resultFile);
        try (FileWriter fileWriter = new FileWriter(resultFile);
             JSONWriter jsonWriter = new JSONWriter(fileWriter)) {
            jsonWriter.config(SerializerFeature.PrettyFormat, true);
            jsonWriter.config(SerializerFeature.WriteDateUseDateFormat, true);
            jsonWriter.startArray();
            while (iterator.hasNext()) {
                List<Experiment> next = iterator.next();
                if (CollUtil.isEmpty(next)) {
                    break;
                }
                for (Experiment experiment : next) {
                    ExperimentExportDTO item = BeanUtil.copyProperties(experiment, ExperimentExportDTO.class);
                    jsonWriter.writeObject(item);
                }
            }
            jsonWriter.endArray();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultFile;
    }
}
