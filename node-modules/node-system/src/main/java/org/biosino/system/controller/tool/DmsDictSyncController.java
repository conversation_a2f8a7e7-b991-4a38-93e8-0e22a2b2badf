package org.biosino.system.controller.tool;

import lombok.RequiredArgsConstructor;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.system.service.DmsDictSyncService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> @date 2024/10/10
 */
@RequestMapping("/dmsDictSync")
@RestController
@RequiredArgsConstructor
public class DmsDictSyncController {
    private final DmsDictSyncService dmsDictSyncService;

    @GetMapping("/list")
    private AjaxResult list() {
        return AjaxResult.success(dmsDictSyncService.list());
    }

    @GetMapping("/syncDictToDb")
    private AjaxResult syncDictToDb(String dictName) {
        dmsDictSyncService.syncDictToDb(dictName);
        return AjaxResult.success();
    }

    @GetMapping("/syncDictToEs")
    private AjaxResult syncDictToEs(String dictName) {
        dmsDictSyncService.syncDictToEs(dictName);
        return AjaxResult.success();
    }
}
