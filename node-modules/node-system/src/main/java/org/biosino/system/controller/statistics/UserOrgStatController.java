package org.biosino.system.controller.statistics;

import lombok.RequiredArgsConstructor;
import org.biosino.common.core.utils.poi.ExcelUtil;
import org.biosino.common.log.annotation.Log;
import org.biosino.common.log.enums.BusinessType;
import org.biosino.system.service.statistics.UserOrgStatService;
import org.biosino.system.vo.excel.UserOrgStatExcel;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 用户组织统计
 *
 * <AUTHOR>
 * @date 2024/8/5
 */
@RestController
@RequestMapping("/statistics/userOrg")
@RequiredArgsConstructor
public class UserOrgStatController {
    private final UserOrgStatService userOrgStatService;

    /**
     * 用户组织统计数据导出
     */
    @PostMapping("/export")
    @Log(module1 = "统计", module2 = "用户组织", businessType = BusinessType.EXPORT)
    public void export(HttpServletResponse response) {
        List<UserOrgStatExcel> excelList = userOrgStatService.export();
        ExcelUtil<UserOrgStatExcel> util = new ExcelUtil<>(UserOrgStatExcel.class);
        util.exportExcel(response, excelList, "User Org Statistic");
    }

}
