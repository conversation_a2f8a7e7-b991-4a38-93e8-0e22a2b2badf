package org.biosino.system.controller.standmg;

import lombok.RequiredArgsConstructor;
import org.biosino.common.core.constant.DirConstants;
import org.biosino.common.core.utils.DownloadUtils;
import org.biosino.common.core.utils.file.MyFileUtils;
import org.biosino.common.core.utils.poi.ExcelUtil;
import org.biosino.common.core.web.controller.BaseController;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.log.annotation.Log;
import org.biosino.common.log.enums.BusinessType;
import org.biosino.common.mongo.entity.ExpSampleType;
import org.biosino.common.security.annotation.Logical;
import org.biosino.common.security.annotation.RequiresPermissions;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.system.domain.dto.StandMgDataDTO;
import org.biosino.system.dto.dto.standmg.StandMgAttrDTO;
import org.biosino.system.dto.dto.standmg.StandMgQueryDTO;
import org.biosino.system.service.standmg.IStandMgExpService;
import org.biosino.system.vo.standmg.StandMgAttrVO;
import org.biosino.system.vo.standmg.StandMgExpVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;

/**
 * Standard Mgmt - Experiment/Sample 控制器
 *
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/stdMg")
public class StandMgExpController extends BaseController {
    private final IStandMgExpService standMgExpService;

    /**
     * 列表数据
     */
    @RequiresPermissions(value = {"standard:experiment", "standard:sample"}, logical = Logical.OR)
    @GetMapping("/list")
    public AjaxResult list(StandMgQueryDTO search) {
        StandMgExpVO vo = standMgExpService.list(search);
        return success(vo);
    }

    /**
     * 修改数据状态
     */
    @RequiresPermissions(value = {"standard:experiment", "standard:sample"}, logical = Logical.OR)
    @Log(module1 = "Standard Mgmt", module2 = "Experiment/Sample", businessType = BusinessType.UPDATE)
    @PostMapping("/updateStatus")
    public AjaxResult updateStatus(@RequestBody final StandMgDataDTO standMgDataDTO) {
        return success(standMgExpService.updateStatus(standMgDataDTO));
    }

    /**
     * 上传（样例）模版文件
     */
    @RequiresPermissions(value = {"standard:experiment", "standard:sample"}, logical = Logical.OR)
    @Log(module1 = "Standard Mgmt", module2 = "Experiment/Sample", businessType = BusinessType.IMPORT)
    @PostMapping("/uploadTemplate")
    public AjaxResult uploadTemplate(final MultipartFile file, final String name, final String version, final String type,
                                     final String parentName, final Boolean isExample, final String id) {
        return success(standMgExpService.uploadTemplate(file, name, version, type, parentName, isExample, id));
    }

    /**
     * 下载（样例模板）文件
     */
    @RequiresPermissions(value = {"standard:experiment", "standard:sample"}, logical = Logical.OR)
    @Log(module1 = "Standard Mgmt", module2 = "Experiment/Sample", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadTemplate")
    public void downloadTemplate(HttpServletRequest request, HttpServletResponse response, String fileName) throws IOException {
        File file = MyFileUtils.expSampleTemplate(DirConstants.DATA_HOME, fileName);
        DownloadUtils.download(request, response, file, fileName);
    }

    /**
     * 保存新增（编辑）的数据
     */
    @RequiresPermissions(value = {"standard:experiment", "standard:sample"}, logical = Logical.OR)
    @Log(module1 = "Standard Mgmt", module2 = "Experiment/Sample", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public AjaxResult save(@Validated @RequestBody ExpSampleType expSampleType) {
        return toAjax(standMgExpService.saveExpSampleType(expSampleType, SecurityUtils.getUsername()));
    }

    /**
     * 组学(样本)类型字典数据导出
     */
    @RequiresPermissions(value = {"standard:experiment", "standard:sample"}, logical = Logical.OR)
    @Log(module1 = "Standard Mgmt", module2 = "Experiment/Sample", businessType = BusinessType.EXPORT)
    @PostMapping("/exportStand")
    public void exportStand(HttpServletRequest request, HttpServletResponse response, String standType) {
        standMgExpService.exportStand(request, response, standType);
    }

    /**
     * 属性详情列表
     */
    @GetMapping("/attrDetail/{standType}/{standId}")
    public AjaxResult attrDetail(@PathVariable("standType") String standType, @PathVariable("standId") String standId) {
        StandMgAttrVO vo = standMgExpService.attrDetail(standType, standId);
        return success(vo);
    }

    /**
     * 保存属性数据
     */
    @RequiresPermissions(value = {"standard:experiment", "standard:sample"}, logical = Logical.OR)
    @Log(module1 = "Standard Mgmt", module2 = "Experiment/Sample", module3 = "Attribute", businessType = BusinessType.INSERT)
    @PostMapping("/saveAttr")
    public AjaxResult saveAttr(@Validated @RequestBody StandMgAttrDTO dto) {
        return success(standMgExpService.saveAttr(dto, SecurityUtils.getUsername()));
    }

    /**
     * 删除属性
     */
    @RequiresPermissions(value = {"standard:experiment", "standard:sample"}, logical = Logical.OR)
    @Log(module1 = "Standard Mgmt", module2 = "Experiment/Sample", module3 = "Attribute", businessType = BusinessType.DELETE)
    @DeleteMapping("/delAttr/{standId}/{id}")
    public AjaxResult delAttr(@PathVariable("standId") String standId, @PathVariable("id") String id) {
        return success(standMgExpService.delAttr(standId, id));
    }


    /**
     * 修改属性数据状态
     */
    @RequiresPermissions(value = {"standard:experiment", "standard:sample"}, logical = Logical.OR)
    @Log(module1 = "Standard Mgmt", module2 = "Experiment/Sample", module3 = "Attribute", businessType = BusinessType.UPDATE)
    @PostMapping("/updateAttrStatus")
    public AjaxResult updateAttrStatus(@RequestBody final StandMgDataDTO standMgDataDTO) {
        return success(standMgExpService.updateAttrStatus(standMgDataDTO));
    }

    /**
     * 属性导入模版下载
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<StandMgAttrDTO> util = new ExcelUtil<>(StandMgAttrDTO.class);
        util.importTemplateExcel(response, "attributes data");
    }

    /**
     * 属性数据导出
     */
    @RequiresPermissions(value = {"standard:experiment", "standard:sample"}, logical = Logical.OR)
    @Log(module1 = "Standard Mgmt", module2 = "Experiment/Sample", module3 = "Attribute", businessType = BusinessType.EXPORT)
    @PostMapping("/exportAttr")
    public void exportAttr(HttpServletResponse response, StandMgDataDTO standMgDataDTO) {
        standMgExpService.exportAttr(response, standMgDataDTO);
    }

    /**
     * 属性数据批量导入
     */
    @RequiresPermissions(value = {"standard:experiment", "standard:sample"}, logical = Logical.OR)
    @Log(module1 = "Standard Mgmt", module2 = "Experiment/Sample", module3 = "Attribute", businessType = BusinessType.IMPORT)
    @PostMapping("/importAttrData")
    public AjaxResult importAttrData(MultipartFile file, final String type, final Boolean deleteOld, final String standId) {
        return success(standMgExpService.importAttrData(file, SecurityUtils.getUsername(), type, deleteOld, standId));
    }

    /**
     * 获取项目中所有的模板名称
     */
    @GetMapping("/getAllNames")
    public AjaxResult getAllNames() {
        return success(standMgExpService.getAllNames());
    }

}
