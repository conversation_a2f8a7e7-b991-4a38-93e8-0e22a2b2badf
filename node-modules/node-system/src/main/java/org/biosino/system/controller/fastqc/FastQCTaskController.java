package org.biosino.system.controller.fastqc;

import com.alibaba.fastjson2.JSON;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.utils.DownloadUtils;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.core.web.page.TableDataInfo;
import org.biosino.common.log.annotation.Log;
import org.biosino.common.log.enums.BusinessType;
import org.biosino.common.mongo.entity.FastQCTask;
import org.biosino.common.security.annotation.RequiresPermissions;
import org.biosino.system.dto.dto.FastQCTaskQueryDTO;
import org.biosino.system.service.fastqc.FastQCTaskService;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2024/5/22
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/fastqcTask")
public class FastQCTaskController {

    private final FastQCTaskService fastQCTaskService;

    /**
     * 任务列表
     */
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody FastQCTaskQueryDTO queryDTO) {
        Page<FastQCTask> page = fastQCTaskService.list(queryDTO);
        return new TableDataInfo(page.getContent(), (int) page.getTotalElements());
    }

    /**
     * 获取任务详细信息
     */
    @RequestMapping("/getByNo/{no}")
    public AjaxResult getByNo(@PathVariable String no) {
        FastQCTask fastQCTask = fastQCTaskService.findByTaskNo(no);
        return AjaxResult.success(fastQCTask);
    }

    /**
     * 重试任务
     */
    @Log(module1 = "FastQC Mgmt", module2 = "Retry", businessType = BusinessType.UPDATE)
    @RequestMapping("/retry")
    public AjaxResult retry(String[] dataNos) {
        fastQCTaskService.retry(Arrays.asList(dataNos));
        return AjaxResult.success();
    }

    /**
     * 修改任务优先级
     */
    @Log(module1 = "FastQC Mgmt", module2 = "Change Priority", businessType = BusinessType.UPDATE)
    @RequestMapping("/changePriority")
    public AjaxResult changePriority(String[] dataNos, Integer priority) {
        fastQCTaskService.changePriority(Arrays.asList(dataNos), priority);
        return AjaxResult.success();
    }

    /**
     * 导出Fastqc数据
     */
    @Log(module1 = "FastQC Mgmt", module2 = "Export", businessType = BusinessType.EXPORT)
    @RequestMapping("/exportData")
    @RequiresPermissions("fastqctask:export")
    public void exportData(String query, HttpServletRequest request, HttpServletResponse response) throws IOException {
        FastQCTaskQueryDTO queryDTO = JSON.parseObject(query, FastQCTaskQueryDTO.class);
        queryDTO.checkNonQuery();
        File file = fastQCTaskService.exportFastqcTask(queryDTO);
        DownloadUtils.download(request, response, file, "FastQCTask.json");
    }
}
