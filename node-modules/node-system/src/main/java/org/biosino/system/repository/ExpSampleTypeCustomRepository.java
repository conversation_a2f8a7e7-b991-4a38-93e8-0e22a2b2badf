package org.biosino.system.repository;

import org.biosino.common.core.enums.sys.ExpSampleTypeEnum;
import org.biosino.common.mongo.entity.ExpSampleType;
import org.biosino.system.dto.dto.standmg.StandMgQueryDTO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface ExpSampleTypeCustomRepository {

    List<String> findLvl1NameByType(ExpSampleTypeEnum typeEnum);

    List<ExpSampleType> list(StandMgQueryDTO search);

    List<ExpSampleType> findByNameAndType(String name, String type);

    List<ExpSampleType> listSampleType();

    List<ExpSampleType> listExpType();

    List<ExpSampleType> findByType(ExpSampleTypeEnum typeEnum);

}
