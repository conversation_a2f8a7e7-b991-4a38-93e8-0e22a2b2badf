package org.biosino.system.repository;

import org.biosino.common.mongo.entity.admin.HumanResource;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface HumanResourceCustomRepository {

    PageImpl<HumanResource> search(HumanResource search, Pageable pageable);

    List<HumanResource> allEnable(Pageable pageable);

}
