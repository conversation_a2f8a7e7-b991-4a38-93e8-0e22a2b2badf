package org.biosino.system.controller.metadata;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.DownloadUtils;
import org.biosino.common.core.utils.poi.ExcelUtil;
import org.biosino.common.core.web.controller.BaseController;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.core.web.page.TableDataInfo;
import org.biosino.common.log.annotation.Log;
import org.biosino.common.log.enums.BusinessType;
import org.biosino.common.security.annotation.RequiresPermissions;
import org.biosino.system.dto.dto.DataQueryDTO;
import org.biosino.system.dto.dto.FtpFileLogQueryDTO;
import org.biosino.system.dto.dto.ModifyIntegrityDTO;
import org.biosino.system.service.FtpFileLogService;
import org.biosino.system.service.IFtpHomeFileService;
import org.biosino.system.service.meta.DataService;
import org.biosino.system.vo.metadata.*;
import org.springframework.data.domain.Page;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> Li
 * @date 2024/5/6
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/metadata/data")
public class DataController extends BaseController {

    private final DataService dataService;

    private final FtpFileLogService ftpFileLogService;

    private final IFtpHomeFileService ftpHomeFileService;

    /**
     * 查询未归档的Data
     */
    @RequestMapping("/listUnarchived")
    public TableDataInfo listUnarchived(DataQueryDTO dto) {
        Page<DataListVO> result = dataService.listUnarchived(dto);
        return new TableDataInfo(result.getContent(), (int) result.getTotalElements());
    }

    /**
     * 导出未归档的数据
     */
    @Log(module1 = "Data Mgmt", module2 = "Unarchived Data", businessType = BusinessType.EXPORT)
    @RequestMapping("/exportUnarchived")
    public void exportUnarchived(DataQueryDTO dto, HttpServletResponse response) {
        dto.setPageNum(1);
        dto.setPageSize(5000);
        Page<DataListVO> result = dataService.listUnarchived(dto);

        List<UnarchivedDataExportVO> content = BeanUtil.copyToList(result.getContent(), UnarchivedDataExportVO.class);

        ExcelUtil<UnarchivedDataExportVO> util = new ExcelUtil<>(UnarchivedDataExportVO.class);
        util.exportExcel(response, content, "sheet1");
    }

    /**
     * 列出归档到RawData的Data
     */
    @PostMapping("/listRawData")
    public TableDataInfo listRawData(@RequestBody DataQueryDTO dto) {
        Page<DataListVO> result = dataService.listRawData(dto);
        return new TableDataInfo(result.getContent(), (int) result.getTotalElements());
    }

    /**
     * 列出归档到Analysis的Data
     */
    @PostMapping("/listAnalysisData")
    public TableDataInfo listAnalysisData(@RequestBody DataQueryDTO dto) {
        Page<DataListVO> result = dataService.listAnalysisData(dto);
        return new TableDataInfo(result.getContent(), (int) result.getTotalElements());
    }

    /**
     * 导出RawData
     */
    @Log(module1 = "Data Mgmt", module2 = "RawData", module3 = "Export Data", businessType = BusinessType.EXPORT)
    @RequestMapping("/exportRawData")
    @RequiresPermissions("metadata:rawdata:export")
    public void exportRawData(String query, HttpServletRequest request, HttpServletResponse response) throws IOException {
        DataQueryDTO queryDTO = JSON.parseObject(query, DataQueryDTO.class);
        if (CollUtil.isEmpty(queryDTO.getDataNos())
                && StrUtil.isBlank(queryDTO.getName())
                && CollUtil.isEmpty(queryDTO.getExpNos())
                && CollUtil.isEmpty(queryDTO.getSapNos())
                && StrUtil.isBlank(queryDTO.getCreatorEmail())
                && queryDTO.getBeginTime() == null
                && queryDTO.getEndTime() == null) {
            throw new ServiceException("Please enter at least one query condition");
        }
        File file = dataService.exportRawData(queryDTO);
        DownloadUtils.download(request, response, file, "data.json");
    }

    /**
     * 导出Analysis data
     */
    @Log(module1 = "Data Mgmt", module2 = "AnalysisData", module3 = "Export Data", businessType = BusinessType.EXPORT)
    @RequestMapping("/exportAnalysisData")
    @RequiresPermissions("metadata:analysisdata:export")
    public void exportAnalysisData(String query, HttpServletRequest request, HttpServletResponse response) throws IOException {
        DataQueryDTO queryDTO = JSON.parseObject(query, DataQueryDTO.class);
        if (CollUtil.isEmpty(queryDTO.getDataNos())
                && StrUtil.isBlank(queryDTO.getName())
                && CollUtil.isEmpty(queryDTO.getAnalNos())
                && StrUtil.isBlank(queryDTO.getCreatorEmail())
                && queryDTO.getBeginTime() == null
                && queryDTO.getEndTime() == null) {
            throw new ServiceException("Please enter at least one query condition");
        }
        File file = dataService.exportAnalysisData(queryDTO);
        DownloadUtils.download(request, response, file, "data.json");
    }


    /**
     * 删除Data
     */
    @Log(module1 = "Data Mgmt", module2 = "Data", businessType = BusinessType.DELETE)
    @RequestMapping("/deleteByDataNos/{dataNos}")
    public AjaxResult deleteByDataNos(@PathVariable String[] dataNos) {
        dataService.deleteByDataNos(Arrays.asList(dataNos));
        return AjaxResult.success();
    }

    /**
     * 检查文件
     */
    @RequestMapping("/allocDataNo4Path/checkPath")
    public AjaxResult checkPath4AllocDataNoPath(String path) {
        String msg = dataService.checkPath4AllocDataNoPath(path);
        return AjaxResult.success(null, msg);
    }

    /**
     * 检查email
     */
    @RequestMapping("/allocDataNo4Path/checkEmail")
    public AjaxResult checkEmail4AllocDataNoPath(String email) {
        dataService.getMemberInfoByEmail(email);
        return AjaxResult.success();
    }

    /**
     * 给Data分配DataNo
     */
    @Log(module1 = "Tools", module2 = "Alloc DataNo", businessType = BusinessType.OTHER)
    @RequestMapping("/allocDataNo4Path/startAlloc")
    public AjaxResult startAlloc(String path, String email) {
        List<DataListVO> result = dataService.startAlloc(path, email);
        return AjaxResult.success(result);
    }

    /**
     * 获取用户目录下文件的数量
     */
    @RequestMapping("/ftpPreLoadFileSync/getFtpFileRecordCount")
    public AjaxResult getFtpFileRecordCount(String email, String path) {
        if (path.startsWith("/")) {
            path = path.substring(1);
        }
        long result = ftpFileLogService.getFtpFileLogNum(email, path);
        return AjaxResult.success(result);
    }

    /**
     * 校验用户目录下核数据库记录的数量是否一样
     */
    @RequestMapping("/ftpPreLoadFileSync/checkFtpFileLog")
    public AjaxResult checkFtpFileLog(String email, String path) {
        if (path.startsWith("/")) {
            path = path.substring(1);
        }
        long result = ftpFileLogService.checkFtpFileLog(email, path);
        return AjaxResult.success(result);
    }

    /**
     * 同步文件上传记录
     */
    @Log(module1 = "Tools", module2 = "Temporary Data Check", module3 = "Sync Ftp File Log", businessType = BusinessType.OTHER)
    @RequestMapping("/ftpPreLoadFileSync/startSync")
    public AjaxResult startSync(String email, String path, boolean readMd5) {
        if (path.startsWith("/")) {
            path = path.substring(1);
        }
        List<FtpFileLogVO> ftpFileLogs = ftpFileLogService.startSync(email, path, readMd5);
        return AjaxResult.success(ftpFileLogs);
    }

    @RequestMapping("/ftpPreLoad/list")
    public TableDataInfo list(FtpFileLogQueryDTO queryDTO) {
        startPage();
        return ftpFileLogService.selectFtpFileLogList(queryDTO);
    }

    @Log(module1 = "Data Mgmt", module2 = "Preload File List", businessType = BusinessType.DELETE)
    @RequestMapping("/ftpPreLoad/delete")
    public AjaxResult deleteFtpFile(@RequestBody String[] ids) {
        ftpFileLogService.deleteFtpFileByIds(Arrays.asList(ids));

        return AjaxResult.success();
    }

    /**
     * 列出已归档未公开的Data
     */
    @RequestMapping("/listPrivateData")
    public TableDataInfo listPrivateData(DataQueryDTO dto) {
        Page<DataListVO> result = dataService.listPrivateData(dto);
        return new TableDataInfo(result.getContent(), (int) result.getTotalElements());
    }

    /**
     * 导出已归档未公开的Data、
     */
    @Log(module1 = "Tools", module2 = "Temporary Data Check", module3 = "Private Data", businessType = BusinessType.EXPORT)
    @RequestMapping("/exportPrivateData")
    public void exportPrivateData(DataQueryDTO dto, HttpServletResponse response) {
        dto.setPageNum(1);
        dto.setPageSize(5000);
        Page<DataListVO> result = dataService.listPrivateData(dto);

        List<PrivateDataExportVO> content = BeanUtil.copyToList(result.getContent(), PrivateDataExportVO.class);

        ExcelUtil<PrivateDataExportVO> util = new ExcelUtil<>(PrivateDataExportVO.class);
        util.exportExcel(response, content, "sheet1");
    }

    /**
     * 校验前预检查
     */
    @RequestMapping("/ftpFile/checkFtpFile")
    public AjaxResult checkFtpFile(@RequestBody String[] ids) {
        List<String> failCheckFileList = dataService.checkFtpFile(Arrays.asList(ids));
        return CollUtil.isEmpty(failCheckFileList) ? AjaxResult.success() : AjaxResult.success(null, CollUtil.join(failCheckFileList, "\n"));
    }

    /**
     * 校验文件md5是否一致
     */
    @Log(module1 = "Data Mgmt", module2 = "Verify Ftp File MD5", businessType = BusinessType.OTHER)
    @RequestMapping("/ftpFile/verify")
    public AjaxResult verifyFtpFile(@RequestBody String[] ids) {
        List<String> failVerifyFileList = dataService.verifyFtpFile(Arrays.asList(ids));
        return CollUtil.isEmpty(failVerifyFileList) ? AjaxResult.success() : AjaxResult.success(null, CollUtil.join(failVerifyFileList, "\n"));
    }

    /**
     * 同步生成结果
     */
    @Log(module1 = "Tools", module2 = "Temporary Data Check", module3 = "Ftp Home Data - Scan Ftp Home File", businessType = BusinessType.OTHER)
    @RequestMapping("/ftpHomeFile/scanFtpHomeFile")
    public AjaxResult scanFtpHomeFile() {
        ftpHomeFileService.scanFtpHomeFile();
        return AjaxResult.success();
    }

    /**
     * FtpHomeFile分页查询
     */
    @RequestMapping("/ftpHomeFile/list")
    public TableDataInfo listFtpHomeFile(FtpFileLogQueryDTO queryDTO) {
        startPage();
        return ftpHomeFileService.selectListPage(queryDTO);
    }

    /**
     * FtpHomeFile导出查询的前5000条
     */
    @Log(module1 = "Tools", module2 = "Temporary Data Check", module3 = "Ftp Home Data", businessType = BusinessType.EXPORT)
    @RequestMapping("/ftpHomeFile/export")
    public void exportFtpHomeFile(FtpFileLogQueryDTO queryDTO, HttpServletResponse response) {
        startPage();
        TableDataInfo tableDataInfo = ftpHomeFileService.selectListPage(queryDTO);

        List<?> rows = tableDataInfo.getRows();
        ExcelUtil<FtpHomeFileVO> util = new ExcelUtil<>(FtpHomeFileVO.class);
        util.exportExcel(response, (List<FtpHomeFileVO>) rows, "sheet1");
    }

    /**
     * batch modify source
     */
    @Log(module1 = "Tools", module2 = "Batch Modify Integrity", businessType = BusinessType.UPDATE)
    @RequestMapping("/batchModifyIntegrity")
    public AjaxResult batchModifyIntegrity(@RequestBody @Validated ModifyIntegrityDTO modifyIntegrityDTO) {
        dataService.batchModifyIntegrity(modifyIntegrityDTO);
        return AjaxResult.success();
    }

    /**
     * 获取用户的ftp_home的绝对路径
     */
    @RequestMapping("/getMemberFtpHomePath")
    public AjaxResult getMemberFtpHomePath(String creatorEmail) {
        return AjaxResult.success(dataService.getMemberFtpHomePath(creatorEmail));
    }
}
