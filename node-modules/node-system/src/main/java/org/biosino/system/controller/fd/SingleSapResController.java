package org.biosino.system.controller.fd;

import lombok.RequiredArgsConstructor;
import org.biosino.common.core.web.controller.BaseController;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.log.annotation.Log;
import org.biosino.common.log.enums.BusinessType;
import org.biosino.common.security.annotation.RequiresPermissions;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.es.api.vo.fd.SingleSapQueryVO;
import org.biosino.system.domain.dto.FeatureDataDTO;
import org.biosino.system.service.fd.ISingleSapResService;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 特殊数据集 单样本多组学 Single Sample Multiple Omics Resource
 *
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/fdSingleSap")
public class SingleSapResController extends BaseController {
    private final ISingleSapResService singleSapResService;


    /**
     * 特殊数据集--单样本多组学资源--列表
     */
    @RequiresPermissions("fd:singleSapRes:list")
    @GetMapping("/list")
    public AjaxResult list(SingleSapQueryVO searchVO) {
        return success(singleSapResService.list(searchVO));
    }

    /**
     * 特殊数据集--单样本多组学资源--弹窗列表
     */
    @RequiresPermissions("fd:singleSapRes:list")
    @GetMapping("/dialogList")
    public AjaxResult dialogList(SingleSapQueryVO searchVO) {
        return success(singleSapResService.dialogList(searchVO));
    }


    /**
     * 保存单样本多组学资源
     */
    @RequiresPermissions("fd:singleSapRes:list")
    @Log(module1 = "Feature Data", module2 = "Single Sample Multiple Omics", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public AjaxResult save(@RequestBody List<String> prjIds) {
        return success(singleSapResService.save(prjIds, SecurityUtils.getUsername()));
    }

    /**
     * 单样本多组学资源数据导出
     */
    @RequiresPermissions("fd:singleSapRes:list")
    @Log(module1 = "Feature Data", module2 = "Single Sample Multiple Omics", businessType = BusinessType.EXPORT)
    @PostMapping("/download")
    public void download(HttpServletRequest request, HttpServletResponse response) {
        singleSapResService.download(request, response);
    }


    /**
     * 批量修改单样本多组学资源状态
     */
    @RequiresPermissions("fd:singleSapRes:list")
    @Log(module1 = "Feature Data", module2 = "Single Sample Multiple Omics", businessType = BusinessType.UPDATE)
    @PostMapping("/batchUpdateStatus")
    public AjaxResult batchUpdateStatus(@RequestBody final FeatureDataDTO featureDataDTO) {
        return success(singleSapResService.batchUpdateStatus(featureDataDTO));
    }

    /**
     * 批量添加单样本多组学资源数据
     */
    @RequiresPermissions("fd:singleSapRes:list")
    @Log(module1 = "Feature Data", module2 = "Single Sample Multiple Omics", businessType = BusinessType.IMPORT)
    @PostMapping("/batchImport")
    public AjaxResult batchImport(@RequestBody final FeatureDataDTO featureDataDTO) {
        return success(singleSapResService.batchImport(featureDataDTO, SecurityUtils.getUsername()));
    }

    /**
     * 批量删除单样本多组学资源数据
     */
    @RequiresPermissions("fd:singleSapRes:list")
    @Log(module1 = "Feature Data", module2 = "Single Sample Multiple Omics", businessType = BusinessType.DELETE)
    @PostMapping("/batchDelete")
    public AjaxResult batchDelete(@RequestBody final FeatureDataDTO featureDataDTO) {
        return success(singleSapResService.batchDelete(featureDataDTO));
    }
}
