package org.biosino.system.service.samtool;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSONWriter;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.file.MyFileUtils;
import org.biosino.common.mongo.entity.SamToolTask;
import org.biosino.common.mongo.iterator.MongoPagingIterator;
import org.biosino.system.dto.dto.SamToolTaskQueryDTO;
import org.biosino.system.repository.SamToolTaskRepository;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileWriter;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/22
 */
@Component
@RequiredArgsConstructor
public class SamToolTaskService {

    private final SamToolTaskRepository samToolTaskRepository;

    public Page<SamToolTask> list(SamToolTaskQueryDTO queryDTO) {
        Page<SamToolTask> page = samToolTaskRepository.findPage(queryDTO);
        return page;
    }

    public SamToolTask findByTaskNo(String no) {
        SamToolTask samToolTask = samToolTaskRepository.findFirstByDataNo(no).orElseThrow(() -> new ServiceException("SamToolTask is not found!"));
        return samToolTask;
    }

    public void changePriority(List<String> dataNos, Integer priority) {
        if (priority == null || CollUtil.isEmpty(dataNos)) {
            throw new ServiceException("参数不能为空！");
        }
        samToolTaskRepository.updatePriority(dataNos, priority);
    }

    public void retry(List<String> dataNos) {
        samToolTaskRepository.retryTask(dataNos);
    }

    public File exportSamToolTask(SamToolTaskQueryDTO queryDTO) {
        MongoPagingIterator<SamToolTask> iterator = samToolTaskRepository.getPagingIterator(queryDTO);
        File tempDir = MyFileUtils.getTempDir();
        File resultFile = FileUtil.file(tempDir, "SamToolTask.json");
        FileUtil.touch(resultFile);
        try (FileWriter fileWriter = new FileWriter(resultFile);
             JSONWriter jsonWriter = new JSONWriter(fileWriter)) {
            jsonWriter.config(SerializerFeature.PrettyFormat, true);
            jsonWriter.config(SerializerFeature.WriteDateUseDateFormat, true);
            jsonWriter.startArray();
            while (iterator.hasNext()) {
                List<SamToolTask> next = iterator.next();
                if (CollUtil.isEmpty(next)) {
                    break;
                }
                for (SamToolTask samToolTask : next) {
                    jsonWriter.writeObject(samToolTask);
                }
            }
            jsonWriter.endArray();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultFile;
    }
}
