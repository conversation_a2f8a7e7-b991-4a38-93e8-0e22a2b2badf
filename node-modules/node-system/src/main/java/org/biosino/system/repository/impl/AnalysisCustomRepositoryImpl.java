package org.biosino.system.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.enums.AuditEnum;
import org.biosino.common.core.enums.OwnershipEnum;
import org.biosino.common.core.enums.VisibleStatusEnum;
import org.biosino.common.mongo.entity.Analysis;
import org.biosino.common.mongo.iterator.MongoPagingIterator;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.system.dto.dto.MetadataQueryDTO;
import org.biosino.system.repository.AnalysisCustomRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.*;
import java.util.regex.Pattern;

/**
 * <AUTHOR> Li
 * @date 2024/1/13
 */
@RequiredArgsConstructor
public class AnalysisCustomRepositoryImpl implements AnalysisCustomRepository {

    private final MongoTemplate mongoTemplate;

    public Criteria baseCriteria() {
        return Criteria.where("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus())
                .and("audited").is(AuditEnum.audited.name());
    }

    @Override
    public Analysis findByNo(String analNo) {
        Query query = new Query();
        Criteria criteria = Criteria.where("anal_no").is(analNo);
        criteria.and("ownership").is(OwnershipEnum.self_support.getDesc());
        criteria.and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        query.addCriteria(criteria);
        return mongoTemplate.findOne(query, Analysis.class);
    }

    @Override
    public Page<Analysis> findAnalysisPage(MetadataQueryDTO queryDTO) {
        Query query = getAnalysisQuery(queryDTO);

        // 查询数据量
        long total = mongoTemplate.count(query, Analysis.class);

        // 添加分页和排序
        query.with(queryDTO.getPageable());

        // 查询query
        List<Analysis> content = mongoTemplate.find(query, Analysis.class);

        return new PageImpl<>(content, queryDTO.getPageable(), total);
    }

    private static Query getAnalysisQuery(MetadataQueryDTO queryDTO) {
        List<Criteria> criteriaList = new ArrayList<>();
        criteriaList.add(Criteria.where("audited").is(AuditEnum.audited.name()));
        criteriaList.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        String name = queryDTO.getName();
        if (StrUtil.isNotBlank(name)) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getName() + ".*$", Pattern.CASE_INSENSITIVE);
            criteriaList.add(Criteria.where("name").regex(pattern));
        }

        if (CollUtil.isNotEmpty(queryDTO.getNos())) {
            criteriaList.add(Criteria.where("anal_no").in(queryDTO.getNos()));
        }

        if (StrUtil.isNotEmpty(queryDTO.getSubmitterEmail())) {
            criteriaList.add(Criteria.where("submitter.email").is(queryDTO.getSubmitterEmail()));
        }

        if (StrUtil.isNotBlank(queryDTO.getSubmitterOrgName())) {
            criteriaList.add(Criteria.where("submitter.org_name").is(queryDTO.getSubmitterOrgName()));
        }
        if (CollUtil.isNotEmpty(queryDTO.getTags())) {
            criteriaList.add(Criteria.where("source_project").in(queryDTO.getTags()));
        }

        if (CollUtil.isNotEmpty(queryDTO.getTargetNos())) {
            criteriaList.add(Criteria.where("target.nos").in(queryDTO.getTargetNos()));
        }

        if (ObjectUtil.isNotEmpty(queryDTO.getBeginTime()) && ObjectUtil.isNotEmpty(queryDTO.getEndTime())) {
            criteriaList.add(Criteria.where("submission_date").gte(DateUtil.beginOfDay(queryDTO.getBeginTime())).lte(DateUtil.endOfDay(queryDTO.getEndTime())));
        } else if (ObjectUtil.isNotEmpty(queryDTO.getBeginTime())) {
            criteriaList.add(Criteria.where("submission_date").gte(DateUtil.beginOfDay(queryDTO.getBeginTime())));
        } else if (ObjectUtil.isNotEmpty(queryDTO.getEndTime())) {
            criteriaList.add(Criteria.where("submission_date").lte(DateUtil.endOfDay(queryDTO.getEndTime())));
        }
        if (CollUtil.isNotEmpty(queryDTO.getAnalTypes())) {
            criteriaList.add(Criteria.where("analysis_type").in(queryDTO.getAnalTypes()));
        }

        if (StrUtil.isNotBlank(queryDTO.getCreator())) {
            criteriaList.add(Criteria.where("creator").is(queryDTO.getCreator()));
        }


        Query query = new Query(new Criteria().andOperator(criteriaList));
        return query;
    }

    @Override
    public List<String> findAuditedAnalType() {
        Criteria criteria = baseCriteria();
        Query query = new Query(criteria);
        query.fields().include("analysis_type");
        return mongoTemplate.findDistinct(query, "analysis_type", Analysis.class, String.class);
    }

    @Override
    public Optional<Analysis> findTopByAnalNo(String analNo) {
        if (StrUtil.isBlank(analNo)) {
            return Optional.empty();
        }

        List<Criteria> condition = new ArrayList<>();
        condition.add(baseCriteria());
        condition.add(new Criteria().orOperator(
                Criteria.where("anal_no").is(analNo),
                Criteria.where("used_ids").in(analNo)));

        Query query = new Query(new Criteria().andOperator(condition));
        Analysis analysis = mongoTemplate.findOne(query, Analysis.class);
        return Optional.ofNullable(analysis);
    }

    @Override
    public void updateCreatorByAnalNo(List<String> analNos, String creator) {
        if (CollUtil.isEmpty(analNos)) {
            return;
        }
        Query query = new Query(Criteria.where("anal_no").in(analNos));
        Update update = new Update().set("creator", creator)
                .set("operator", SecurityUtils.getUserId().toString())
                .set("operation_date", new Date());
        mongoTemplate.updateMulti(query, update, Analysis.class);
    }

    @Override
    public List<Analysis> findAllByAnalNoIn(Collection<String> analysisNos) {
        if (CollUtil.isEmpty(analysisNos)) {
            return new ArrayList<>();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(baseCriteria());
        condition.add(new Criteria().orOperator(
                Criteria.where("anal_no").in(analysisNos),
                Criteria.where("used_ids").in(analysisNos)));

        Query query = new Query(new Criteria().andOperator(condition));
        return mongoTemplate.find(query, Analysis.class);
    }

    @Override
    public MongoPagingIterator<Analysis> getPagingIterator(MetadataQueryDTO queryDTO) {
        Query query = getAnalysisQuery(queryDTO);
        return new MongoPagingIterator<>(mongoTemplate, Analysis.class, query, 1000);
    }
}
