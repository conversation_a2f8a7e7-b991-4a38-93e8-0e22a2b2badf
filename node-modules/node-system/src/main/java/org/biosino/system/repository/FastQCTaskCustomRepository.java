package org.biosino.system.repository;

import org.biosino.common.mongo.entity.FastQCTask;
import org.biosino.common.mongo.iterator.MongoPagingIterator;
import org.biosino.system.dto.dto.FastQCTaskQueryDTO;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Repository;

import java.util.Collection;

@Repository
public interface FastQCTaskCustomRepository {

    Page<FastQCTask> findPage(FastQCTaskQueryDTO queryDTO);

    void updatePriority(Collection<String> taskNos, Integer priority);

    void retryTask(Collection<String> dataNos);

    MongoPagingIterator<FastQCTask> getPagingIterator(FastQCTaskQueryDTO queryDTO);
}
