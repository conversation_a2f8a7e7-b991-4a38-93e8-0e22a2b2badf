package org.biosino.system.repository;

import org.biosino.common.mongo.entity.admin.MultipleSampleResource;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public interface MultipleSampleResourceRepository extends MongoRepository<MultipleSampleResource, String>, MultipleSampleResourceCustomRepository {
    List<MultipleSampleResource> findAllByIdIn(Collection<String> ids);

    void deleteByIdIn(Collection<String> ids);
}
