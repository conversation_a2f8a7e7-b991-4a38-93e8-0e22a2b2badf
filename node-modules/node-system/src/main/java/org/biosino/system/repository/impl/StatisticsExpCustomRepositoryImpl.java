package org.biosino.system.repository.impl;

import lombok.RequiredArgsConstructor;
import org.biosino.common.mongo.entity.statistics.StatisticsExp;
import org.biosino.system.repository.StatisticsExpCustomRepository;
import org.biosino.system.repository.util.RepositoryUtil;
import org.springframework.data.mongodb.core.MongoTemplate;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/19
 */
@RequiredArgsConstructor
public class StatisticsExpCustomRepositoryImpl implements StatisticsExpCustomRepository {
    private final MongoTemplate mongoTemplate;

    /*@Override
    public List<StatisticsExp> findBaseList(boolean all) {
        return RepositoryUtil.findBaseList(mongoTemplate, all, getClz());
    }*/

}
