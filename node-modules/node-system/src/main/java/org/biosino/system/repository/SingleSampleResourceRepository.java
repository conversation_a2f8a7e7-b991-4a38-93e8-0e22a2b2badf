package org.biosino.system.repository;

import org.biosino.common.mongo.entity.admin.SingleSampleResource;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public interface SingleSampleResourceRepository extends MongoRepository<SingleSampleResource, String>, SingleSampleResourceCustomRepository {
    List<SingleSampleResource> findAllByIdIn(Collection<String> ids);

    void deleteByIdIn(Collection<String> ids);
}
