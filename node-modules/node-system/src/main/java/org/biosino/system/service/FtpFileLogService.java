package org.biosino.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.common.core.web.page.TableDataInfo;
import org.biosino.system.api.domain.sftp.FtpFileLog;
import org.biosino.system.dto.dto.FtpFileLogQueryDTO;
import org.biosino.system.vo.metadata.FtpFileLogVO;

import java.util.List;

public interface FtpFileLogService extends IService<FtpFileLog> {
    long countFtpFileLogNum(String creator, String path);

    void deleteByCreatorAndPath(String creator, String path);

    long checkFtpFileLog(String email, String path);

    long getFtpFileLogNum(String email, String path);

    long getFtpFileCount(String email, String path);

    List<FtpFileLogVO> startSync(String email, String path, boolean readMd5);

    TableDataInfo selectFtpFileLogList(FtpFileLogQueryDTO queryDTO);

    void deleteFtpFileByIds(List<String> ids);
}
