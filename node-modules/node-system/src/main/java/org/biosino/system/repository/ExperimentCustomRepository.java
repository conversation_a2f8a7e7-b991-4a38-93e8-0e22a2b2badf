package org.biosino.system.repository;

import org.biosino.common.mongo.entity.Experiment;
import org.biosino.common.mongo.iterator.MongoPagingIterator;
import org.biosino.system.dto.dto.MetadataQueryDTO;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;


@Repository
public interface ExperimentCustomRepository {

    Experiment findByNo(String expNo);

    List<Experiment> findDetailByProjNo(String projNo);

    List<Experiment> findDetailByProjNoIn(Collection<String> projNos);

    List<Experiment> findAllByProjectNo(String projNo);

    List<Experiment> findTempByProjNo(String projNo);

    void updateToDeleteAllByExpNoIn(Collection<String> expNos);

    void updateCreatorByExpNoIn(Collection<String> expNos, String creator);

    Page<Experiment> findExperimentPage(MetadataQueryDTO queryDTO);

    List<String> getAuditedExpType();

    Optional<Experiment> findTopByExpNo(String expNo);

    List<Experiment> findAllByExpNoIn(List<String> expNos);

    List<String> getExperimentNos(MetadataQueryDTO queryDTO);

    MongoPagingIterator<Experiment> getPagingIterator(MetadataQueryDTO queryDTO);
}
