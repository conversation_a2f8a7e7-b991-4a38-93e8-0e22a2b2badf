package org.biosino.system.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.enums.sys.DataStatusEnum;
import org.biosino.common.core.enums.sys.ExpSampleTypeEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.mongo.entity.ExpSampleType;
import org.biosino.system.dto.dto.standmg.StandMgQueryDTO;
import org.biosino.system.repository.ExpSampleTypeCustomRepository;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class ExpSampleTypeCustomRepositoryImpl implements ExpSampleTypeCustomRepository {
    private final MongoTemplate mongoTemplate;


    @Override
    public List<String> findLvl1NameByType(final ExpSampleTypeEnum typeEnum) {
//        final List<String> allSampleName = ExpSampleTokenUtils.getAllSampleName();
        final Query query = new Query(
                new Criteria().andOperator(
                        Criteria.where("type").is(typeEnum.name()).and("status").is(DataStatusEnum.enable.name()),
                        new Criteria().orOperator(Criteria.where("parentName").exists(false), Criteria.where("parentName").isNull())
                )
        );
        query.fields().include("name");
        final List<ExpSampleType> humanResources = mongoTemplate.find(query, ExpSampleType.class);
        return humanResources.stream().map(ExpSampleType::getName).collect(Collectors.toList());
    }

    @Override
    public List<ExpSampleType> list(final StandMgQueryDTO search) {
        final List<Criteria> conditions = new ArrayList<>();
        final ExpSampleTypeEnum expSampleTypeEnum = ExpSampleTypeEnum.findByName(search.getType()).orElseThrow(() -> new ServiceException("Invalid type"));
        conditions.add(Criteria.where("type").is(expSampleTypeEnum.name()));

        final String name = search.getName();
        if (StrUtil.isNotBlank(name)) {
            conditions.add(Criteria.where("name").regex(MultipleOmicsResourceCustomRepositoryImpl.like(name)));
        }

        final String status = search.getStatus();
        if (StrUtil.isNotBlank(status)) {
            conditions.add(Criteria.where("status").is(status));
        }

        final Date createDateStart = search.getCreateDateStart();
        if (createDateStart != null) {
            conditions.add(Criteria.where("createTime").gte(DateUtil.beginOfDay(createDateStart)));
        }

        final Date createDateEnd = search.getCreateDateEnd();
        if (createDateEnd != null) {
            conditions.add(Criteria.where("createTime").lte(DateUtil.endOfDay(createDateEnd)));
        }

        final String notId = search.getNotId();
        if (notId != null) {
            conditions.add(Criteria.where("_id").ne(new ObjectId(notId)));
        }

        final String eqName = search.getEqName();
        if (eqName != null) {
            conditions.add(Criteria.where("name").is(eqName));
        }

        final Query query = new Query();
        if (CollUtil.isNotEmpty(conditions)) {
            query.addCriteria(new Criteria().andOperator(conditions));
        }

        // query.fields().exclude("attributes");
        query.with(Sort.by(Sort.Direction.ASC, "sort"));
        return mongoTemplate.find(query, ExpSampleType.class);
    }

    @Override
    public List<ExpSampleType> findByNameAndType(final String name, final String type) {
        final List<Criteria> conditions = new ArrayList<>();
//        conditions.add(Criteria.where("name").is(name));
        // 查询名称，不区分大小写
        conditions.add(Criteria.where("name").regex("^" + ReUtil.escape(name) + "$", "i"));
        conditions.add(Criteria.where("type").is(type));
        return mongoTemplate.find(new Query(new Criteria().andOperator(conditions)), ExpSampleType.class);
    }

    @Override
    public List<ExpSampleType> listSampleType() {
        Query query = new Query(
                Criteria.where("type").is(ExpSampleTypeEnum.sample.name()).and("status").is(DataStatusEnum.enable.name())
        );
        List<ExpSampleType> list = mongoTemplate.find(query, ExpSampleType.class);
        return list;
    }

    @Override
    public List<ExpSampleType> listExpType() {
        Query query = new Query(
                Criteria.where("type").is(ExpSampleTypeEnum.experiment.name()).and("status").is(DataStatusEnum.enable.name())
        );
        List<ExpSampleType> list = mongoTemplate.find(query, ExpSampleType.class);
        return list;
    }

    @Override
    public List<ExpSampleType> findByType(final ExpSampleTypeEnum typeEnum) {
        final Criteria criteria = Criteria.where("type").is(typeEnum.name()).and("status").is(DataStatusEnum.enable.name());
        return mongoTemplate.find(new Query(criteria), ExpSampleType.class);
    }

}
