package org.biosino.system.service.statistics;

import cn.hutool.core.collection.CollUtil;
import lombok.AllArgsConstructor;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.NodeUtils;
import org.biosino.common.mongo.entity.statistics.StatisticsDataVolume;
import org.biosino.common.mongo.entity.statistics.StatisticsExp;
import org.biosino.common.mongo.entity.statistics.StatisticsSample;
import org.biosino.system.dto.mapper.StatisticsExpMapper;
import org.biosino.system.repository.StatisticsDataVolumeRepository;
import org.biosino.system.repository.StatisticsExpRepository;
import org.biosino.system.repository.util.RepositoryUtil;
import org.biosino.system.vo.ExpStatVO;
import org.biosino.system.vo.excel.ExperimentExcel;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.biosino.system.service.statistics.SampleStatService.rawDataTotalSize;

/**
 * <AUTHOR>
 * @date 2024/7/18
 */
@Service
@AllArgsConstructor
public class ExperimentStatService {
    private final StatisticsExpRepository statisticsExpRepository;
    private final StatisticsDataVolumeRepository statisticsDataVolumeRepository;

    private final MongoTemplate mongoTemplate;

    public List<ExpStatVO> statData() {
        List<StatisticsExp> all = RepositoryUtil.findLatestMonthData(mongoTemplate, StatisticsExp.class);
        if (CollUtil.isEmpty(all)) {
            throw new ServiceException("No statistic data");
        }

        // all = all.stream().filter(x -> x.getTotal() > 0).collect(Collectors.toList());

        return StatisticsExpMapper.INSTANCE.dbToVO(all);
    }

    public List<ExperimentExcel> exportExp() {
        final List<StatisticsExp> all = RepositoryUtil.findBaseList(mongoTemplate, true, StatisticsExp.class);
        if (CollUtil.isEmpty(all)) {
            throw new ServiceException("No statistic data");
        }

        final List<ExperimentExcel> excelData = new ArrayList<>();
        for (StatisticsExp item : all) {
            final ExperimentExcel excel = StatisticsExpMapper.INSTANCE.dbToExcel(item);
            final Optional<StatisticsDataVolume> dataVolumeOptional = statisticsDataVolumeRepository.findFirstByMonth(excel.getMonth());
            if (dataVolumeOptional.isPresent()) {
                final StatisticsDataVolume dataVolume = dataVolumeOptional.get();
                excel.setTotalFileSizeRate(NodeUtils.div(item.getTotalFileSize(), rawDataTotalSize(dataVolume)));
                excel.setTotalRate(NodeUtils.div(item.getTotal(), expTotal(dataVolume)));
            }
            excelData.add(excel);
        }
        return excelData;
    }


    private long expTotal(final StatisticsDataVolume dataVolume) {
        return dataVolume.getExpAccessible() + dataVolume.getExpUnAccessible();
    }

}
