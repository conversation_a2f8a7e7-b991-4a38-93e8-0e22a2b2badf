package org.biosino.system.controller.statistics;

import lombok.RequiredArgsConstructor;
import org.biosino.common.core.utils.poi.ExcelUtil;
import org.biosino.common.core.web.controller.BaseController;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.log.annotation.Log;
import org.biosino.common.log.enums.BusinessType;
import org.biosino.system.service.statistics.SampleStatService;
import org.biosino.system.vo.excel.SampleExcel;
import org.biosino.system.vo.excel.SampleExpExcel;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 统计模块 - Sample样本统计页面
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/statistics/sap")
@RequiredArgsConstructor
public class SampleStatController extends BaseController {

    private final SampleStatService sampleStatService;

    /**
     * 样本基础统计表格数据
     */
    @GetMapping("/statData")
    public AjaxResult statData() {
        return success(sampleStatService.statData());
    }

    /**
     * 样本基础统计数据导出
     */
    @PostMapping("/export")
    @Log(module1 = "统计", module2 = "样本", businessType = BusinessType.EXPORT)
    public void exportSap(HttpServletResponse response) {
        List<SampleExcel> excelList = sampleStatService.exportSap();
        ExcelUtil<SampleExcel> util = new ExcelUtil<>(SampleExcel.class);
        util.exportExcel(response, excelList, "Sample");
    }

    /**
     * 样本-实验组合统计表格数据
     */
    @GetMapping("/sapExpStatData")
    public AjaxResult sapExpStatData() {
        return success(sampleStatService.sapExpStatData());
    }

    /**
     * 样本-实验组合统计数据导出
     */
    @PostMapping("/exportSapExp")
    @Log(module1 = "统计", module2 = "样本-实验组合", businessType = BusinessType.EXPORT)
    public void exportSapExp(HttpServletResponse response) {
        List<SampleExpExcel> excelList = sampleStatService.exportSapExp();
        ExcelUtil<SampleExpExcel> util = new ExcelUtil<>(SampleExpExcel.class);
        util.exportExcel(response, excelList, "Sample-Experiment");
    }

}
