package org.biosino.system.repository.impl;

import lombok.RequiredArgsConstructor;
import org.biosino.common.mongo.entity.statistics.StatisticsSampleExp;
import org.biosino.system.repository.StatisticsSampleExpCustomRepository;
import org.biosino.system.repository.util.RepositoryUtil;
import org.springframework.data.mongodb.core.MongoTemplate;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/24
 */
@RequiredArgsConstructor
public class StatisticsSampleExpCustomRepositoryImpl implements StatisticsSampleExpCustomRepository {
    private final MongoTemplate mongoTemplate;

    /*@Override
    public List<StatisticsSampleExp> findLatestMonthData() {
        return RepositoryUtil.findBaseList(mongoTemplate, getClz());
    }*/

}
