package org.biosino.system.service.statistics;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson2.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.constant.CacheConstants;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.DownloadUtils;
import org.biosino.common.core.utils.NodeUtils;
import org.biosino.common.core.utils.file.MyFileUtils;
import org.biosino.common.mongo.entity.statistics.*;
import org.biosino.common.mongo.enums.PopularDataType;
import org.biosino.common.redis.service.RedisService;
import org.biosino.job.api.RemoteStatisticsSearchService;
import org.biosino.job.api.vo.statistics.PopularDataVO;
import org.biosino.system.dto.mapper.StatisticsTempDataMapper;
import org.biosino.system.dto.mapper.StatisticsTempFtpDataMapper;
import org.biosino.system.vo.excel.DownloadExcel;
import org.biosino.system.vo.excel.TagExcel;
import org.biosino.system.vo.excel.TempDataExcel;
import org.biosino.system.vo.excel.TempFtpDataExcel;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 用户中的My Data Statistics
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DataReportService {
    private final MongoTemplate mongoTemplate;

    private final RedisService redisService;

    private final RemoteStatisticsSearchService remoteStatisticsSearchService;

    public List<StatisticsSubmission> getSubmissionData() {
        List<StatisticsSubmission> all = mongoTemplate.findAll(StatisticsSubmission.class);
        if (CollUtil.isNotEmpty(all)) {
            for (StatisticsSubmission statisticsPublish : all) {
                statisticsPublish.setSubmissionDataSizeExport(NodeUtils.convertToTB(statisticsPublish.getSubmissionDataSize()));
            }
        }
        return all;
    }

    public List<StatisticsPublish> getPublication() {
        return mongoTemplate.findAll(StatisticsPublish.class);
    }

    /**
     * 热门数据导出
     */
    public void exportPopularData(final String statType, HttpServletRequest request, HttpServletResponse response) {
        if (!CollUtil.toList("Visits", "Download", "Requested").contains(statType)) {
            throw new ServiceException("Unknown type");
        }
        final String runningKey = popularDataKey(statType, null);
        final String startFlag = "start_flag_";
        if (startFlag.equalsIgnoreCase(redisService.getCacheObject(runningKey))) {
            throw new ServiceException("The Excel file is being generated");
        }
        redisService.setCacheObject(runningKey, startFlag, 6L, TimeUnit.MINUTES);

        ExcelWriter writer = null;
        String key = null;
        try {
            final R<List<PopularDataVO>> popularDataApiR = remoteStatisticsSearchService.getPopularDataApi(statType, true, SecurityConstants.INNER);
            if (R.isError(popularDataApiR)) {
                throw new ServiceException("Data not found");
            }

            final List<PopularDataVO> data = popularDataApiR.getData();
            if (CollUtil.isNotEmpty(data)) {
                key = popularDataKey(statType, data);
                final String excelFileStr = redisService.getCacheObject(key);

                File excelFile = null;
                if (StrUtil.isNotBlank(excelFileStr)) {
                    excelFile = new File(excelFileStr);
                }
                if (excelFile == null || !excelFile.exists() || !excelFile.isFile()) {

//                    excelFile = Files.createTempFile(statType, ".xlsx").toFile();
                    excelFile = new File(MyFileUtils.getTempDir(), IdUtil.fastSimpleUUID() + ".xlsx");
                    if (excelFile.exists()) {
                        excelFile.delete();
                    }

                    writer = ExcelUtil.getBigWriter(excelFile);
                    for (int i = 0; i < data.size(); i++) {
                        final PopularDataVO item = data.get(i);

                        final List<PopularDataVO.PopularDataStat> listData = item.getListData();
                        if (CollUtil.isEmpty(listData)) {
                            continue;
                        }

                        writer.setSheet(i);
                        final String type = item.getType();
                        if (type == null) {
                            continue;
                        }
                        writer.renameSheet(type);

                        final List<Map<String, Object>> rows = new ArrayList<>();
                        for (PopularDataVO.PopularDataStat rowsData : listData) {
                            final Map<String, Object> row = new LinkedHashMap<>();
                            row.put("Id", rowsData.getNo());
                            row.put("Name", rowsData.getName());

                            final List<Set<String>> tagsGroup = rowsData.getTagsGroup();
                            if (type.equals(PopularDataType.Projects.name()) || type.equals(PopularDataType.Experiments.name()) || type.equals("Data")) {
                                row.put("Experiment Type", join(tagsGroup, 0));
                                row.put("Sample Type", join(tagsGroup, 1));
                            } else if (type.equals(PopularDataType.Samples.name())) {
                                row.put("Sample Type", join(tagsGroup, 0));
                            } else if (type.equals(PopularDataType.Analysis.name())) {
                                row.put("Analysis Type", join(tagsGroup, 0));
                            }

                            row.put("Quantity", rowsData.getNum());
                            rows.add(row);
                        }
                        writer.write(rows, true);
                    }

                    writer.close();
                    redisService.setCacheObject(key, excelFile.getAbsolutePath(), 7L, TimeUnit.DAYS);
                }

                DownloadUtils.download(request, response, excelFile);
            } else {
                throw new ServiceException("Data not found");
            }
        } catch (ServiceException e) {
            if (key != null) {
                redisService.deleteObject(key);
            }
            throw e;
        } catch (Exception e) {
            if (key != null) {
                redisService.deleteObject(key);
            }
            log.error("热门数据导出出错", e);
        } finally {
            redisService.deleteObject(runningKey);
            IoUtil.close(writer);
        }
    }

    private String popularDataKey(final String statType, final List<PopularDataVO> data) {
        return CacheConstants.POPULAR_DATA_EXPORT_KEY + statType + "_"
                + (data == null ? StrUtil.EMPTY : SecureUtil.md5(JSON.toJSONString(data)));
    }

    private String join(List<Set<String>> tagsGroup, int index) {
        try {
            final Set<String> set = tagsGroup.get(index);
            return CollUtil.isEmpty(set) ? StrUtil.EMPTY : CollUtil.join(set, ", ");
        } catch (Exception e) {
            return StrUtil.EMPTY;
        }
    }

    /**
     * 临时数据统计
     */
    public List<TempDataExcel> getTempData() {
        final List<StatisticsTempData> list = mongoTemplate.findAll(StatisticsTempData.class);
        final List<TempDataExcel> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {
            for (StatisticsTempData statisticsTempData : list) {
                result.add(StatisticsTempDataMapper.INSTANCE.dbToExcel(statisticsTempData));
            }
        }
        return result;
    }

    public List<TempFtpDataExcel> getTempFtpData() {
        final List<StatisticsTempFtpData> list = mongoTemplate.findAll(StatisticsTempFtpData.class);
        final List<TempFtpDataExcel> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {
            for (StatisticsTempFtpData statisticsTempFtpData : list) {
                result.add(StatisticsTempFtpDataMapper.INSTANCE.dbToExcel(statisticsTempFtpData));
            }
        }
        return result;
    }

    public List<DownloadExcel> getDownloadLong() {
        List<StatisticsDownload> downloadList = mongoTemplate.findAll(StatisticsDownload.class);
        if (CollUtil.isEmpty(downloadList)) {
            return null;
        }

        Map<String, List<StatisticsDownload>> downloadMap = downloadList.stream()
                .collect(Collectors.groupingBy(StatisticsDownload::getMonth));

        Map<String, List<StatisticsDownload>> listMap = new TreeMap<>(downloadMap);

        List<DownloadExcel> result = new ArrayList<>();
        listMap.forEach((key, value) -> {
            DownloadExcel download = new DownloadExcel();
            download.setMonth(key);
            download.setProject(value.stream().mapToLong(StatisticsDownload::getProjectCount).sum());
            download.setExperiment(value.stream().mapToLong(StatisticsDownload::getExperimentCount).sum());
            download.setSample(value.stream().mapToLong(StatisticsDownload::getSampleCount).sum());
            download.setAnalysis(value.stream().mapToLong(StatisticsDownload::getAnalysisCount).sum());

            download.setFtpPrivateData(value.stream().mapToLong(StatisticsDownload::getFtpPrivateData).sum());
            long ftpPrivateDataSize = value.stream().mapToLong(StatisticsDownload::getFtpPrivateDataSize).sum();
            download.setFtpPrivateDataSize(NodeUtils.convertToGB(ftpPrivateDataSize));
            download.setFtpRestrictedData(value.stream().mapToLong(StatisticsDownload::getFtpRestrictedData).sum());
            long ftpRestrictedDataSize = value.stream().mapToLong(StatisticsDownload::getFtpRestrictedDataSize).sum();
            download.setFtpRestrictedDataSize(NodeUtils.convertToGB(ftpRestrictedDataSize));
            download.setFtpPublicData(value.stream().mapToLong(StatisticsDownload::getFtpPublicData).sum());
            long ftpPublicDataSize = value.stream().mapToLong(StatisticsDownload::getFtpPublicDataSize).sum();
            download.setFtpPublicDataSize(NodeUtils.convertToGB(ftpPublicDataSize));
            download.setFtpDataTotal(download.getFtpPrivateData() + download.getFtpRestrictedData() + download.getFtpPublicData());
            download.setFtpTotalDataSize(NodeUtils.convertToGB(ftpPrivateDataSize + ftpRestrictedDataSize + ftpPublicDataSize));

            download.setHttpPrivateData(value.stream().mapToLong(StatisticsDownload::getHttpPrivateData).sum());
            long httpPrivateDataSize = value.stream().mapToLong(StatisticsDownload::getHttpPrivateDataSize).sum();
            download.setHttpPrivateDataSize(NodeUtils.convertToGB(httpPrivateDataSize));
            download.setHttpRestrictedData(value.stream().mapToLong(StatisticsDownload::getHttpRestrictedData).sum());
            long httpRestrictedDataSize = value.stream().mapToLong(StatisticsDownload::getHttpRestrictedDataSize).sum();
            download.setHttpRestrictedDataSize(NodeUtils.convertToGB(httpRestrictedDataSize));
            download.setHttpPublicData(value.stream().mapToLong(StatisticsDownload::getHttpPublicData).sum());
            long httpPublicDataSize = value.stream().mapToLong(StatisticsDownload::getHttpPublicDataSize).sum();
            download.setHttpPublicDataSize(NodeUtils.convertToGB(httpPublicDataSize));
            download.setHttpDataTotal(download.getHttpPrivateData() + download.getHttpRestrictedData() + download.getHttpPublicData());
            download.setHttpTotalDataSize(NodeUtils.convertToGB(httpPrivateDataSize + httpRestrictedDataSize + httpPublicDataSize));

            result.add(download);
        });
        return result;
    }

    public List<StatisticsSubMethod> subMethod() {
        List<StatisticsSubMethod> subMethodList = mongoTemplate.findAll(StatisticsSubMethod.class);
        for (StatisticsSubMethod subMethod : subMethodList) {
            subMethod.setHttpDataSize(NodeUtils.convertToTB(subMethod.getHttpSize()));
            subMethod.setFtpDataSize(NodeUtils.convertToTB(subMethod.getFtpSize()));
        }
        return subMethodList;
    }

    public List<TagExcel> getTagExcelList() {
        List<StatisticsTag> all = mongoTemplate.findAll(StatisticsTag.class);
        List<TagExcel> result = all.stream().map(x -> {
            TagExcel tagExcel = new TagExcel();
            BeanUtil.copyProperties(x, tagExcel);
            tagExcel.setDataFileSizeGB(NodeUtils.convertToGB(x.getDataFileSize()));
            tagExcel.setDataPublicFileSizeGB(NodeUtils.convertToGB(x.getDataPublicFileSize()));
            return tagExcel;
        }).collect(Collectors.toList());
        return result;
    }

    public List<StatisticsNodeMember> getNodeMemberList() {
        List<StatisticsNodeMember> all = mongoTemplate.findAll(StatisticsNodeMember.class);
        return all;
    }

    public File getExpSapTypeAttrReportFile(String type) {
        // 查询当前type下有多少模板类型的数据
        List<String> subjectTypes = mongoTemplate.findDistinct(new Query(Criteria.where("metadata_type").is(type)), "subject_type", StatisticsExpSapTypeAttr.class, String.class);
        List<String> months = mongoTemplate.findDistinct(new Query(), "month", StatisticsExpSapTypeAttr.class, String.class);

        // 生成临时文件
        File tempDir = MyFileUtils.getTempDir();
        File tempFile = FileUtil.file(tempDir, "expSapTypeAttr.xlsx");
        FileUtil.mkParentDirs(tempFile);
        for (String subjectType : subjectTypes) {
            ExcelWriter writer = ExcelUtil.getWriter(tempFile, subjectType);

            List<String> dateCol = new ArrayList<>();
            List<String> tagCol = new ArrayList<>();
            List<String> countNameCol = new ArrayList<>();
            Map<String, List<String>> fieldToValueMap = new HashMap<>();
            LinkedHashMap<String, List<String>> sheetData = new LinkedHashMap<>();
            for (String month : months) {
                // 查询统计数据
                List<StatisticsExpSapTypeAttr> v = mongoTemplate.find(new Query(Criteria.where("metadata_type").is(type).and("subject_type").is(subjectType).and("month").is(month)), StatisticsExpSapTypeAttr.class);
                if (CollUtil.isEmpty(v)) {
                    continue;
                }

                List<String> fields = v.stream().map(StatisticsExpSapTypeAttr::getAttrField).distinct().collect(Collectors.toList());
                List<String> tags = v.stream().map(StatisticsExpSapTypeAttr::getTag).distinct().collect(Collectors.toList());
                Map<String, StatisticsExpSapTypeAttr> keyToItemMap = v.stream().collect(Collectors.toMap(x -> x.getTag() + x.getAttrField(), x -> x));

                for (String tag : tags) {
                    tagCol.add(tag);
                    tagCol.add(tag);
                    tagCol.add(tag);
                    tagCol.add(tag);
                    countNameCol.add("accessible");
                    countNameCol.add("total_accessible");
                    countNameCol.add("unaccessible");
                    countNameCol.add("total_unaccessible");
                    dateCol.add(month);
                    dateCol.add(month);
                    dateCol.add(month);
                    dateCol.add(month);

                    for (String field : fields) {
                        StatisticsExpSapTypeAttr item = keyToItemMap.get(tag + field);
                        if (item != null) {
                            fieldToValueMap.computeIfAbsent(field, x -> new ArrayList<>()).add(item.getAccessible().toString());
                            fieldToValueMap.computeIfAbsent(field, x -> new ArrayList<>()).add(item.getTotalAccessible().toString());
                            fieldToValueMap.computeIfAbsent(field, x -> new ArrayList<>()).add(item.getUnaccessible().toString());
                            fieldToValueMap.computeIfAbsent(field, x -> new ArrayList<>()).add(item.getTotalUnaccessible().toString());
                        }
                    }
                }
            }


            sheetData.put("month", dateCol);
            sheetData.put("tag", tagCol);
            sheetData.put("count_type", countNameCol);
            sheetData.putAll(fieldToValueMap);
            writer.writeCol(sheetData, true);
            writer.flush();
        }
        return tempFile;
    }
}
