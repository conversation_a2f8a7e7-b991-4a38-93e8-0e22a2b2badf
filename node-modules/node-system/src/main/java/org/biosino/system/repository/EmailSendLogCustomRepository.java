package org.biosino.system.repository;

import org.biosino.common.mongo.entity.email.EmailSendLog;
import org.biosino.system.vo.email.BatchEmailQueryVO;
import org.springframework.data.domain.PageImpl;

public interface EmailSendLogCustomRepository {

    default Class<EmailSendLog> clz() {
        return EmailSendLog.class;
    }

    PageImpl<EmailSendLog> sendLogList(BatchEmailQueryVO queryVO);

}
