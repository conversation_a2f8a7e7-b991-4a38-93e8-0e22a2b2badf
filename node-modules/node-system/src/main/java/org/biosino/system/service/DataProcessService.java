package org.biosino.system.service;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.io.LineHandler;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.mongo.entity.Biome;
import org.biosino.common.mongo.entity.ExpSampleType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.List;

import static org.biosino.common.core.constant.CacheConstants.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataProcessService {
    @Autowired
    private MongoTemplate mongoTemplate;

    public long processAllBiome() {
        mongoTemplate.remove(new Query(), Biome.class);
        processNonHostBiome();
        processHostBiome();
        processEnvBiome();

        refreshBiomeFiled();
        return mongoTemplate.count(new Query(), Biome.class);
    }

    public void processNonHostBiome() {
        try (InputStream inputStream = DataProcessService.class.getResourceAsStream("/files/non_host_biome.txt")) {
            IoUtil.readUtf8Lines(inputStream, (LineHandler) line -> {
                if (StrUtil.isNotBlank(line)) {
                    String bimoe = line.trim();
                    Biome biome = new Biome();
                    biome.setType(NON_HOST_BIOME);
                    biome.setValue(bimoe);
                    mongoTemplate.insert(biome);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void processHostBiome() {
        try (InputStream inputStream = DataProcessService.class.getResourceAsStream("/files/host_biome.txt")) {
            IoUtil.readUtf8Lines(inputStream, (LineHandler) line -> {
                if (StrUtil.isNotBlank(line)) {
                    String bimoe = line.trim();
                    Biome biome = new Biome();
                    biome.setType(HOST_BIOME);
                    biome.setValue(bimoe);
                    mongoTemplate.insert(biome);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void processEnvBiome() {
        try (InputStream inputStream = DataProcessService.class.getResourceAsStream("/files/env_biome.txt")) {
            IoUtil.readUtf8Lines(inputStream, (LineHandler) line -> {
                if (StrUtil.isNotBlank(line)) {
                    String bimoe = line.trim();
                    String[] strings1 = bimoe.split("\t");
                    String id = strings1[0];
                    String name = strings1[1];
                    String value = name + " [" + id + "]";
                    Biome biome = new Biome();
                    biome.setType(ENV_BIOME);
                    biome.setValue(value);
                    mongoTemplate.insert(biome);

                    if (id.startsWith("ENVO")) {
                        Biome biome2 = new Biome();
                        biome2.setType(ENV_BIOME_WATER);
                        biome2.setValue(value);
                        mongoTemplate.insert(biome2);
                    }
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // 临时代码，修改Environment non-host中env_biome,env_material和env_feature的取值来源
    public void refreshBiomeFiled() {
        Query query = Query.query(Criteria.where("type").is("sample"));
        List<ExpSampleType> all = mongoTemplate.find(query, ExpSampleType.class);
        for (ExpSampleType expSampleType : all) {
            if (!"sample".equals(expSampleType.getType())) {
                continue;
            }
            String parentName = expSampleType.getParentName();
            if ("Environment non-host".equals(parentName) || "Environment non-host".equals(expSampleType.getName())) {
                List<ExpSampleType.Attributes> attributes = expSampleType.getAttributes();
                for (ExpSampleType.Attributes attribute : attributes) {
                    String attributesField = attribute.getAttributesField();
                    if ("env_biome".equals(attributesField) || "env_material".equals(attributesField) || "env_feature".equals(attributesField)) {
                        attribute.setDataSource("env_biome_water");
                    }
                }
                mongoTemplate.save(expSampleType);
            }
        }
    }
}
