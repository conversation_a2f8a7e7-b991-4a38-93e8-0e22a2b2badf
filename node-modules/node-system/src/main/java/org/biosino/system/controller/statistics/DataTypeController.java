package org.biosino.system.controller.statistics;

import lombok.RequiredArgsConstructor;
import org.biosino.common.core.utils.poi.ExcelUtil;
import org.biosino.common.core.web.controller.BaseController;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.log.annotation.Log;
import org.biosino.common.log.enums.BusinessType;
import org.biosino.system.service.statistics.DataTypeService;
import org.biosino.system.vo.excel.DataTypeExcel;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 数据类型统计
 *
 * <AUTHOR>
 * @date 2024/7/31
 */
@RestController
@RequestMapping("/statistics/dataType")
@RequiredArgsConstructor
public class DataTypeController extends BaseController {
    private final DataTypeService dataTypeService;


    /**
     * 数据共享统计数据
     */
    @GetMapping("/statData")
    public AjaxResult statData() {
        return success(dataTypeService.statData());
    }

    /**
     * 数据类型统计数据导出
     */
    @PostMapping("/export")
    @Log(module1 = "统计", module2 = "数据类型", businessType = BusinessType.EXPORT)
    public void exportDataType(HttpServletResponse response) {
        List<DataTypeExcel> excelList = dataTypeService.exportDataType();
        ExcelUtil<DataTypeExcel> util = new ExcelUtil<>(DataTypeExcel.class);
        util.exportExcel(response, excelList, "Data Type");
    }

}
