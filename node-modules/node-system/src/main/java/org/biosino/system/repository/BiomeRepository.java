package org.biosino.system.repository;

import org.biosino.common.mongo.entity.Biome;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;

@Repository
public interface BiomeRepository extends MongoRepository<Biome, String> {

    void deleteAllByTypeIn(Collection<String> types);

    long countByTypeIn(Collection<String> types);

}
