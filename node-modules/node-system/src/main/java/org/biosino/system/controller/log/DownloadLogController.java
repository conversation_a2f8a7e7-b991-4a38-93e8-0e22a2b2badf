package org.biosino.system.controller.log;

import lombok.RequiredArgsConstructor;
import org.biosino.common.core.web.page.TableDataInfo;
import org.biosino.system.dto.dto.LogQueryDTO;
import org.biosino.system.service.impl.DownloadLogService;
import org.biosino.system.vo.DownloadLogVO;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/5/20
 */
@RestController
@RequestMapping("/downloadLog")
@RequiredArgsConstructor
public class DownloadLogController {

    private final DownloadLogService downloadLogService;

    @GetMapping("/list")
    public TableDataInfo list(LogQueryDTO queryDTO) {
        Page<DownloadLogVO> result = downloadLogService.list(queryDTO);
        return new TableDataInfo(result.getContent(), (int) result.getTotalElements());
    }

}
