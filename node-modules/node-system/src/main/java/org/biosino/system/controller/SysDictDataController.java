package org.biosino.system.controller;

import cn.hutool.core.collection.CollUtil;
import org.biosino.common.core.utils.StringUtils;
import org.biosino.common.core.utils.poi.ExcelUtil;
import org.biosino.common.core.web.controller.BaseController;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.core.web.page.TableDataInfo;
import org.biosino.common.log.annotation.Log;
import org.biosino.common.log.enums.BusinessType;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.system.api.domain.SysDictData;
import org.biosino.system.dto.dto.OrganizationDTO;
import org.biosino.system.service.ISysDictDataService;
import org.biosino.system.service.ISysDictTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 数据字典信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dict/data")
public class SysDictDataController extends BaseController {
    @Autowired
    private ISysDictDataService dictDataService;

    @Autowired
    private ISysDictTypeService dictTypeService;

    //    @RequiresPermissions("system:dict:list")
    @GetMapping("/list")
    public TableDataInfo list(SysDictData dictData) {
        startPage();
        List<SysDictData> list = dictDataService.selectDictDataList(dictData);
        return getDataTable(list);
    }

    @Log(module1 = "字典数据", businessType = BusinessType.EXPORT)
//    @RequiresPermissions("system:dict:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysDictData dictData) {
        List<SysDictData> list = dictDataService.selectDictDataList(dictData);
        if (CollUtil.isNotEmpty(list)) {
            for (SysDictData sysDictData : list) {
                sysDictData.setDescription(sysDictData.getRemark());
            }
        }
        ExcelUtil<SysDictData> util = new ExcelUtil<SysDictData>(SysDictData.class);
        util.exportExcel(response, list, "字典数据");
    }

    /**
     * 查询字典数据详细
     */
//    @RequiresPermissions("system:dict:query")
    @GetMapping(value = "/{dictCode}")
    public AjaxResult getInfo(@PathVariable Long dictCode) {
        return success(dictDataService.selectDictDataById(dictCode));
    }

    /**
     * 根据字典类型查询字典数据信息
     */
    @GetMapping(value = "/type/{dictType}")
    public AjaxResult dictType(@PathVariable String dictType) {
        List<SysDictData> data = dictTypeService.selectDictDataByType(dictType);
        if (StringUtils.isNull(data)) {
            data = new ArrayList<SysDictData>();
        }
        return success(data);
    }

    /**
     * 新增字典类型
     */
//    @RequiresPermissions("system:dict:add")
    @Log(module1 = "字典数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysDictData dict) {
        dict.setCreateBy(SecurityUtils.getUsername());
        return toAjax(dictDataService.insertDictData(dict));
    }

    /**
     * 修改保存字典类型
     */
//    @RequiresPermissions("system:dict:edit")
    @Log(module1 = "字典数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysDictData dict) {
        dict.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(dictDataService.updateDictData(dict));
    }

    /**
     * 删除字典类型
     */
//    @RequiresPermissions("system:dict:remove")
    @Log(module1 = "字典类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{dictCodes}")
    public AjaxResult remove(@PathVariable Long[] dictCodes) {
        dictDataService.deleteDictDataByIds(dictCodes);
        return success();
    }

    /**
     * 字典数据导入
     */
    @Log(module1 = "字典数据", businessType = BusinessType.IMPORT)
    @PostMapping("/importOrganizationData")
    public AjaxResult importOrganizationData(MultipartFile file) throws Exception {
        // 读取excel文件
        ExcelUtil<OrganizationDTO> util = new ExcelUtil<>(OrganizationDTO.class);
        List<OrganizationDTO> dictDatas = util.importExcel(file.getInputStream());
        String operName = SecurityUtils.getUsername();
        dictDataService.importOrganizationData(dictDatas, operName);
        return success();
    }

    @PostMapping("/importOrganizationTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<OrganizationDTO> util = new ExcelUtil<>(OrganizationDTO.class);
        util.importTemplateExcel(response, "sheet1");
    }
}
