package org.biosino.system.controller.fd;

import lombok.RequiredArgsConstructor;
import org.biosino.common.core.web.controller.BaseController;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.log.annotation.Log;
import org.biosino.common.log.enums.BusinessType;
import org.biosino.common.security.annotation.RequiresPermissions;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.es.api.vo.fd.MultipleSampleQueryVO;
import org.biosino.system.domain.dto.FeatureDataDTO;
import org.biosino.system.service.fd.IMultSampleResService;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 特殊数据集 多样本 Multiple Sample Resource
 *
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/fdMultSample")
public class MultSampleResController extends BaseController {
    private final IMultSampleResService multSapResService;


    /**
     * 特殊数据集--多样本资源--列表
     */
    @RequiresPermissions("fd:multSapRes:list")
    @GetMapping("/list")
    public AjaxResult list(MultipleSampleQueryVO searchVO) {
        return success(multSapResService.list(searchVO));
    }

    /**
     * 特殊数据集--多样本资源--弹窗列表
     */
    @RequiresPermissions("fd:multSapRes:list")
    @GetMapping("/dialogList")
    public AjaxResult dialogList(MultipleSampleQueryVO searchVO) {
        return success(multSapResService.dialogList(searchVO));
    }


    /**
     * 保存多样本资源
     */
    @RequiresPermissions("fd:multSapRes:list")
    @Log(module1 = "Feature Data", module2 = "Multiple Sample Resource", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public AjaxResult save(@RequestBody List<String> prjIds) {
        return success(multSapResService.save(prjIds, SecurityUtils.getUsername()));
    }

    /**
     * 多样本资源数据导出
     */
    @RequiresPermissions("fd:multSapRes:list")
    @Log(module1 = "Feature Data", module2 = "Multiple Sample Resource", businessType = BusinessType.EXPORT)
    @PostMapping("/download")
    public void download(HttpServletRequest request, HttpServletResponse response) {
        multSapResService.download(request, response);
    }


    /**
     * 批量修改多样本资源状态
     */
    @RequiresPermissions("fd:multSapRes:list")
    @Log(module1 = "Feature Data", module2 = "Multiple Sample Resource", businessType = BusinessType.UPDATE)
    @PostMapping("/batchUpdateStatus")
    public AjaxResult batchUpdateStatus(@RequestBody final FeatureDataDTO featureDataDTO) {
        return success(multSapResService.batchUpdateStatus(featureDataDTO));
    }

    /**
     * 批量添加多样本资源数据
     */
    @RequiresPermissions("fd:multSapRes:list")
    @Log(module1 = "Feature Data", module2 = "Multiple Sample Resource", businessType = BusinessType.IMPORT)
    @PostMapping("/batchImport")
    public AjaxResult batchImport(@RequestBody final FeatureDataDTO featureDataDTO) {
        return success(multSapResService.batchImport(featureDataDTO, SecurityUtils.getUsername()));
    }

    /**
     * 批量删除多样本资源数据
     */
    @RequiresPermissions("fd:multSapRes:list")
    @Log(module1 = "Feature Data", module2 = "Multiple Sample Resource", businessType = BusinessType.DELETE)
    @PostMapping("/batchDelete")
    public AjaxResult batchDelete(@RequestBody final FeatureDataDTO featureDataDTO) {
        return success(multSapResService.batchDelete(featureDataDTO));
    }
}
