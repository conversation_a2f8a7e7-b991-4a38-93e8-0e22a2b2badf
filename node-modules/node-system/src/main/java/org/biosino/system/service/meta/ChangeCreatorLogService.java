package org.biosino.system.service.meta;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.utils.ip.IpUtils;
import org.biosino.common.mongo.entity.ChangeCreatorLog;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.system.repository.ChangeCreatorLogRepository;
import org.biosino.upload.api.vo.DeleteCheckResultVO;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/6/17
 */
@Service
@RequiredArgsConstructor
public class ChangeCreatorLogService {

    private final ChangeCreatorLogRepository changeCreatorLogRepository;

    public void addChangeCreatorLog(String no, String type, String sourceCreator, String sourceEmail, String targetCreator, String targetEmail, DeleteCheckResultVO checkResultVO) {
        ChangeCreatorLog log = new ChangeCreatorLog();
        log.setOperator(SecurityUtils.getUserId().toString());
        log.setOperateTime(new Date());
        log.setIp(IpUtils.getIpAddr());
        log.setType(type);
        log.setTypeId(no);
        log.setSourceCreator(sourceCreator);
        log.setTargetCreator(targetCreator);
        log.setSourceEmail(sourceEmail);
        log.setTargetEmail(targetEmail);
        if (CollUtil.isNotEmpty(checkResultVO.getProjNos())) {
            log.setProjNos(checkResultVO.getProjNos());
        }
        if (CollUtil.isNotEmpty(checkResultVO.getExpNos())) {
            log.setExpNos(checkResultVO.getExpNos());

        }
        if (CollUtil.isNotEmpty(checkResultVO.getRunNos())) {
            log.setRunNos(checkResultVO.getRunNos());

        }
        if (CollUtil.isNotEmpty(checkResultVO.getSapNos())) {
            log.setSapNos(checkResultVO.getSapNos());
        }
        if (CollUtil.isNotEmpty(checkResultVO.getAnalNos())) {
            log.setAnalNos(checkResultVO.getAnalNos());
        }
        if (CollUtil.isNotEmpty(checkResultVO.getDataNos())) {
            log.setDataNos(checkResultVO.getDataNos());
        }

        changeCreatorLogRepository.save(log);
    }
}
