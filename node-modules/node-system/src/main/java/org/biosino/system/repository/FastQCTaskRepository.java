package org.biosino.system.repository;

import org.biosino.common.mongo.entity.FastQCTask;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface FastQCTaskRepository extends MongoRepository<FastQCTask, String>, FastQCTaskCustomRepository {
    Optional<FastQCTask> findFirstByDataNo(String dataNo);
}
