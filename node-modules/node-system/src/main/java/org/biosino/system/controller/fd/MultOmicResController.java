package org.biosino.system.controller.fd;

import lombok.RequiredArgsConstructor;
import org.biosino.common.core.web.controller.BaseController;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.log.annotation.Log;
import org.biosino.common.log.enums.BusinessType;
import org.biosino.common.security.annotation.RequiresPermissions;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.es.api.vo.fd.MultipleOmicsQueryVO;
import org.biosino.system.domain.dto.FeatureDataDTO;
import org.biosino.system.service.fd.IMultOmicResService;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 特殊数据集 多组学 Multiple Omics Resource
 *
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/fdMultOmic")
public class MultOmicResController extends BaseController {

    private final IMultOmicResService multOmicResService;

    /**
     * 特殊数据集--多组学资源--列表
     */
    @RequiresPermissions("fd:multOmicRes:list")
    @GetMapping("/list")
    public AjaxResult list(MultipleOmicsQueryVO searchVO) {
        return success(multOmicResService.list(searchVO));
    }

    /**
     * 特殊数据集--多组学资源--弹窗列表
     */
    @RequiresPermissions("fd:multOmicRes:list")
    @GetMapping("/dialogList")
    public AjaxResult dialogList(MultipleOmicsQueryVO searchVO) {
        return success(multOmicResService.dialogList(searchVO));
    }

    /**
     * 保存多组学资源
     */
    @RequiresPermissions("fd:multOmicRes:list")
    @Log(module1 = "Feature Data", module2 = "Multiple Omics Resource", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public AjaxResult save(@RequestBody List<String> prjIds) {
        return success(multOmicResService.save(prjIds, SecurityUtils.getUsername()));
    }

    /**
     * 多组学资源数据导出
     */
    @RequiresPermissions("fd:multOmicRes:list")
    @Log(module1 = "Feature Data", module2 = "Multiple Omics Resource", businessType = BusinessType.EXPORT)
    @PostMapping("/download")
    public void download(HttpServletRequest request, HttpServletResponse response) {
        multOmicResService.download(request, response);
    }

    /**
     * 批量修改多组学资源状态
     */
    @RequiresPermissions("fd:multOmicRes:list")
    @Log(module1 = "Feature Data", module2 = "Multiple Omics Resource", businessType = BusinessType.UPDATE)
    @PostMapping("/batchUpdateStatus")
    public AjaxResult batchUpdateStatus(@RequestBody final FeatureDataDTO featureDataDTO) {
        return success(multOmicResService.batchUpdateStatus(featureDataDTO));
    }

    /**
     * 批量添加多组学资源数据
     */
    @RequiresPermissions("fd:multOmicRes:list")
    @Log(module1 = "Feature Data", module2 = "Multiple Omics Resource", businessType = BusinessType.IMPORT)
    @PostMapping("/batchImport")
    public AjaxResult batchImport(@RequestBody final FeatureDataDTO featureDataDTO) {
        return success(multOmicResService.batchImport(featureDataDTO, SecurityUtils.getUsername()));
    }

    /**
     * 批量删除多组学资源数据
     */
    @RequiresPermissions("fd:multOmicRes:list")
    @Log(module1 = "Feature Data", module2 = "Multiple Omics Resource", businessType = BusinessType.DELETE)
    @PostMapping("/batchDelete")
    public AjaxResult batchDelete(@RequestBody final FeatureDataDTO featureDataDTO) {
        return success(multOmicResService.batchDelete(featureDataDTO));
    }
}
