package org.biosino.system.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.enums.sys.DataStatusEnum;
import org.biosino.common.mongo.entity.admin.MultipleOmicsResource;
import org.biosino.es.api.vo.fd.MultipleOmicsQueryVO;
import org.biosino.system.repository.MultipleOmicsResourceCustomRepository;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.*;
import java.util.regex.Pattern;

/**
 * 多组学资源
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class MultipleOmicsResourceCustomRepositoryImpl implements MultipleOmicsResourceCustomRepository {
    private final MongoTemplate mongoTemplate;

    @Override
    public Map<String, MultipleOmicsResource> findMapByProjIDIn(Collection<String> projIDs) {
        if (CollUtil.isEmpty(projIDs)) {
            return new HashMap<>();
        }
        final List<MultipleOmicsResource> list = mongoTemplate.find(new Query(Criteria.where("proj_no").in(projIDs)), MultipleOmicsResource.class);
        final Map<String, MultipleOmicsResource> map = new HashMap<>();
        if (CollUtil.isNotEmpty(list)) {
            for (MultipleOmicsResource item : list) {
                map.put(item.getProjID(), item);
            }
        }
        return map;
    }

    @Override
    public PageImpl<MultipleOmicsResource> multOmicList(MultipleOmicsQueryVO searchVO) {
        final List<Criteria> conditions = new ArrayList<>();

        final String projID = searchVO.getProjID();
        if (StrUtil.isNotBlank(projID)) {
            conditions.add(Criteria.where("proj_no").regex(like(projID)));
        }

        final String projName = searchVO.getProjName();
        if (StrUtil.isNotBlank(projName)) {
            conditions.add(Criteria.where("projName").regex(like(projName)));
        }

        final String expType = searchVO.getExpType();
        if (StrUtil.isNotBlank(expType)) {
            conditions.add(Criteria.where("expTypes").regex(like(expType)));
        }

        final Query query = new Query();
        if (CollUtil.isNotEmpty(conditions)) {
            query.addCriteria(new Criteria().andOperator(conditions));
        }
        final long total = mongoTemplate.count(query, MultipleOmicsResource.class);
        List<MultipleOmicsResource> list = new ArrayList<>();
        final Pageable pageable = searchVO.initPageInfo();
        if (total > 0) {
            query.with(pageable);
            list = mongoTemplate.find(query, MultipleOmicsResource.class);
        }
        return new PageImpl<>(list, pageable, total);
    }

    @Override
    public List<MultipleOmicsResource> findByNoLike(String no) {
        final List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("status").is(DataStatusEnum.enable.name()));
        if (StrUtil.isNotBlank(no)) {
            condition.add(Criteria.where("proj_no").regex(like(no)));
        }
        final Query query = new Query(new Criteria().andOperator(condition));
        return mongoTemplate.find(query, MultipleOmicsResource.class);
    }

    public static Pattern like(String val) {
        return Pattern.compile("^.*" + ReUtil.escape(val) + ".*$", Pattern.CASE_INSENSITIVE);
    }

}
