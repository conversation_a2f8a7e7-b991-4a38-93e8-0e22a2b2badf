package org.biosino.system.repository;

import org.biosino.common.mongo.entity.admin.MultipleOmicsResource;
import org.biosino.es.api.vo.fd.MultipleOmicsQueryVO;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Map;

@Repository
public interface MultipleOmicsResourceCustomRepository {

    Map<String, MultipleOmicsResource> findMapByProjIDIn(Collection<String> projIDs);

    PageImpl<MultipleOmicsResource> multOmicList(MultipleOmicsQueryVO searchVO);

    List<MultipleOmicsResource> findByNoLike(String no);

}
