package org.biosino.system.service.statistics;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.mongo.entity.statistics.StatisticsUserOrg;
import org.biosino.system.dto.mapper.StatisticsUserOrgMapper;
import org.biosino.system.repository.util.RepositoryUtil;
import org.biosino.system.vo.excel.UserOrgStatExcel;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/5
 */
@Service
@RequiredArgsConstructor
public class UserOrgStatService {
    private final MongoTemplate mongoTemplate;

    public List<UserOrgStatExcel> export() {
        final List<StatisticsUserOrg> all = RepositoryUtil.findBaseList(mongoTemplate, true, StatisticsUserOrg.class);
        if (CollUtil.isEmpty(all)) {
            throw new ServiceException("No statistic data");
        }

        return StatisticsUserOrgMapper.INSTANCE.dbToExcel(all);
    }
}
