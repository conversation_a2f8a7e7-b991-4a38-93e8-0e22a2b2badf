package org.biosino.system.controller.publish;

import org.biosino.common.core.web.controller.BaseController;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.core.web.page.TableDataInfo;
import org.biosino.common.log.annotation.Log;
import org.biosino.common.log.enums.BusinessType;
import org.biosino.system.dto.dto.PublishSearchDTO;
import org.biosino.system.service.meta.PublishService;
import org.biosino.system.vo.metadata.PublishVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * 文献管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/publish")
public class PublishController extends BaseController {

    @Autowired
    private PublishService publishService;

    /**
     * 查询当前审核员审核记录
     */
    @GetMapping("/list")
    public TableDataInfo list(PublishSearchDTO dto) {
        Page<PublishVO> page = publishService.list(dto);
        return new TableDataInfo(page.getContent(), (int) page.getTotalElements());
    }

    /**
     * 查询当前审核员审核记录
     */
    @PostMapping("/add")
    @Log(module1 = "Publish Mgmt", businessType = BusinessType.INSERT)
    public AjaxResult add(@RequestBody PublishSearchDTO dto) {
        publishService.save(dto);
        return AjaxResult.success();
    }

    /**
     * 查询当前审核员审核记录
     */
    @PutMapping("/edit")
    @Log(module1 = "Publish Mgmt", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@RequestBody PublishSearchDTO dto) {
        publishService.save(dto);
        return AjaxResult.success();
    }

    /**
     * 删除文献
     */
    @Log(module1 = "Publish Mgmt", businessType = BusinessType.DELETE)
    @DeleteMapping("/delete/{id}")
    public AjaxResult delete(@PathVariable String id) {
        publishService.delete(id);
        return AjaxResult.success();
    }

    /**
     * 导出所有publish
     */
    @Log(module1 = "Publish Mgmt", businessType = BusinessType.EXPORT)
    @RequestMapping("/export")
    public void exportPublish(HttpServletResponse response) {
        publishService.export(response);
    }
}
