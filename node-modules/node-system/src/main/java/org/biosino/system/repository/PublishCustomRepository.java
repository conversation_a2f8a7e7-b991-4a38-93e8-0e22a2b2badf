package org.biosino.system.repository;

import org.biosino.common.mongo.entity.Publish;
import org.biosino.system.dto.dto.PublishSearchDTO;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public interface PublishCustomRepository {

    /**
     * 根据type和typeID查询所有publish
     */
    List<Publish> findByTypeId(String type, String typeId);

    PageImpl<Publish> findAllPage(PublishSearchDTO dto);

    void updateCreatorByTypeAndTypeId(String type, Collection<String> typIds, String newCreator);
}
