package org.biosino.system.repository;

import org.biosino.common.mongo.entity.admin.MultipleOmicsResource;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public interface MultipleOmicsResourceRepository extends MongoRepository<MultipleOmicsResource, String>, MultipleOmicsResourceCustomRepository {
    List<MultipleOmicsResource> findAllByIdIn(Collection<String> ids);

    void deleteByIdIn(Collection<String> ids);

}
