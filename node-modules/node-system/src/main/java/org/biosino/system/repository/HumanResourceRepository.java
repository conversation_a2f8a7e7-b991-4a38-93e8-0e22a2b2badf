package org.biosino.system.repository;

import org.biosino.common.mongo.entity.admin.HumanResource;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface HumanResourceRepository extends MongoRepository<HumanResource, String>, HumanResourceCustomRepository {

    List<HumanResource> findAllByIdIn(Collection<String> ids);

    List<HumanResource> findAllByMicrobeFlag(final boolean microbeFlag);

    void deleteByIdIn(Collection<String> ids);

    void deleteByMicrobeFlag(final boolean microbeFlag);
}
