package org.biosino.system.repository;

import org.biosino.common.mongo.entity.admin.SingleSampleResource;
import org.biosino.es.api.vo.fd.SingleSapQueryVO;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Map;

@Repository
public interface SingleSampleResourceCustomRepository {

    Map<String, SingleSampleResource> findMapBySapIDIn(Collection<String> ids);

    PageImpl<SingleSampleResource> list(SingleSapQueryVO searchVO);

    List<SingleSampleResource> findByNoLike(String searchNo);
}
