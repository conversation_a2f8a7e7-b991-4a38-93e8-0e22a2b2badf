package org.biosino.system.controller.log;

import lombok.RequiredArgsConstructor;
import org.biosino.common.core.web.controller.BaseController;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.core.web.page.TableDataInfo;
import org.biosino.common.log.annotation.Log;
import org.biosino.common.log.enums.BusinessType;
import org.biosino.system.api.domain.MemberLoginInfo;
import org.biosino.system.dto.dto.LogQueryDTO;
import org.biosino.system.service.IMemberLoginInfoService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/16
 */
@RestController
@RequestMapping("/memberLoginInfo")
@RequiredArgsConstructor
public class MemberLoginInfoController extends BaseController {

    private final IMemberLoginInfoService memberLoginInfoService;

    /**
     * 添加前台登录日志记录
     */
    @PostMapping("/add")
    public AjaxResult saveCasLoginLog(@RequestBody MemberLoginInfo memberLoginInfo) {
        memberLoginInfoService.save(memberLoginInfo);
        // 保存前台登录日志
        return AjaxResult.success();
    }

    /**
     * 查询前台登录日志记录
     */
    @GetMapping("/list")
    public TableDataInfo list(LogQueryDTO queryDTO) {
        startPage();
        List<MemberLoginInfo> list = memberLoginInfoService.selectListPage(queryDTO);
        return getDataTable(list);
    }

}
