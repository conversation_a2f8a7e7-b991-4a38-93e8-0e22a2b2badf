package org.biosino.system.repository;

import org.biosino.common.mongo.entity.Analysis;
import org.biosino.common.mongo.iterator.MongoPagingIterator;
import org.biosino.system.dto.dto.MetadataQueryDTO;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Repository
public interface AnalysisCustomRepository {

    Analysis findByNo(String analNo);

    Page<Analysis> findAnalysisPage(MetadataQueryDTO queryDTO);

    List<String> findAuditedAnalType();

    Optional<Analysis> findTopByAnalNo(String analNo);

    void updateCreatorByAnalNo(List<String> analNos, String newCreator);

    List<Analysis> findAllByAnalNoIn(Collection<String> analysisNos);

    MongoPagingIterator<Analysis> getPagingIterator(MetadataQueryDTO queryDTO);
}
