package org.biosino.system.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.biosino.system.api.domain.MemberLoginInfo;
import org.biosino.system.dto.dto.LogQueryDTO;
import org.biosino.system.mapper.MemberLoginInfoMapper;
import org.biosino.system.service.IMemberLoginInfoService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> <PERSON>
 * @date 2024/5/16
 */
@Service
@RequiredArgsConstructor
public class MemberLoginInfoServiceImpl extends ServiceImpl<MemberLoginInfoMapper, MemberLoginInfo> implements IMemberLoginInfoService {

    @Override
    public List<MemberLoginInfo> selectListPage(LogQueryDTO queryDTO) {
        Wrapper<MemberLoginInfo> qw = Wrappers.<MemberLoginInfo>lambdaQuery()
                .like(StrUtil.isNotBlank(queryDTO.getUserEmail()), MemberLoginInfo::getUserEmail, queryDTO.getUserEmail())
                .like(StrUtil.isNotBlank(queryDTO.getIp()), MemberLoginInfo::getIp, queryDTO.getIp())
                .ge(queryDTO.getBeginTime() != null, MemberLoginInfo::getLoginTime, queryDTO.getBeginTime() != null ? DateUtil.beginOfDay(queryDTO.getBeginTime()) : null)
                .le(queryDTO.getEndTime() != null, MemberLoginInfo::getLoginTime, queryDTO.getEndTime() != null ? DateUtil.endOfDay(queryDTO.getEndTime()) : null);
        return this.list(qw);
    }
}
