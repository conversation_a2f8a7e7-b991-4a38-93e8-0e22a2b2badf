package org.biosino.system.service.meta;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONWriter;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.enums.AuthorizeType;
import org.biosino.common.core.enums.RouterKeyEnum;
import org.biosino.common.core.enums.SecurityEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.file.MyFileUtils;
import org.biosino.common.core.web.page.TableDataInfo;
import org.biosino.common.mongo.entity.Analysis;
import org.biosino.common.mongo.entity.Data;
import org.biosino.common.mongo.iterator.MongoPagingIterator;
import org.biosino.common.rabbitmq.MessageSender;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.es.api.msg.IndexUpdateMsg;
import org.biosino.es.api.vo.detail.DataListSearchVO;
import org.biosino.system.api.dto.MemberDTO;
import org.biosino.system.dto.dto.AnalysisDTO;
import org.biosino.system.dto.dto.AnalysisDataExportDTO;
import org.biosino.system.dto.dto.MetadataQueryDTO;
import org.biosino.system.dto.dto.export.AnalysisExportDTO;
import org.biosino.system.mq.index.IndexUpdateEvent;
import org.biosino.system.repository.AnalysisRepository;
import org.biosino.system.repository.DataRepository;
import org.biosino.system.repository.PublishRepository;
import org.biosino.system.vo.metadata.AnalysisListVO;
import org.biosino.system.vo.metadata.AnalysisVO;
import org.biosino.system.vo.metadata.PublishVO;
import org.biosino.upload.api.RemoteUploadAnalysisService;
import org.biosino.upload.api.dto.RemoteSelectQueryDTO;
import org.biosino.upload.api.vo.DeleteCheckResultVO;
import org.springframework.context.ApplicationContext;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileWriter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Li
 * @date 2024/4/29
 */
@Service
@RequiredArgsConstructor
public class AnalysisService extends BaseService {
    private final AnalysisRepository analysisRepository;
    private final DataRepository dataRepository;
    private final RemoteUploadAnalysisService remoteUploadAnalysisService;
    private final PublishRepository publishRepository;
    private final MessageSender messageSender;
    private final ApplicationContext applicationContext;


    public Page<AnalysisListVO> listAuditedAnalysis(MetadataQueryDTO queryDTO) {
        // 将email转为memberId
        setMetaQueryDTOCreator(queryDTO);

        Page<Analysis> page = analysisRepository.findAnalysisPage(queryDTO);

        List<String> analNos = page.getContent().stream().map(Analysis::getAnalysisNo).distinct().collect(Collectors.toList());

        Map<String, List<Data>> analNoToDatasMap = dataRepository.findDetailByAnalNoIn(analNos).stream().collect(Collectors.groupingBy(Data::getAnalNo));

        List<String> creators = page.getContent().stream().map(Analysis::getCreator).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());

        // 获取memberId到email对映射关系
        Map<String, String> memberIdToEmailMap = getMemberIdToEmailMap(creators);

        Page<AnalysisListVO> result = page.map(x -> {
            AnalysisListVO vo = new AnalysisListVO();
            BeanUtil.copyProperties(x, vo);
            List<Data> dataList = analNoToDatasMap.getOrDefault(x.getAnalysisNo(), new ArrayList<>());
            vo.setDataNum(dataList.size());


            Map<String, Long> securityMap = dataList.stream()
                    .collect(Collectors.groupingBy(Data::getSecurity, Collectors.counting()));

            for (String s : SecurityEnum.includeAllSecurity()) {
                securityMap.putIfAbsent(s, 0L);
            }

            vo.setDataCount(securityMap);

            vo.setSubmitter(x.getSubmitter().getFirstName() + " " + x.getSubmitter().getLastName());

            // 设置creatorEmail
            vo.setCreatorEmail(memberIdToEmailMap.get(x.getCreator()));
            return vo;
        });
        return result;
    }

    public List<String> getAuditedAnalType() {
        return analysisRepository.findAuditedAnalType().stream().sorted().collect(Collectors.toList());
    }


    public TableDataInfo getTargetOptions(RemoteSelectQueryDTO queryDTO) {
        String memberId = queryDTO.getCreator();
        MemberDTO member = getOneMemberByMemberId(memberId);
        queryDTO.setEmail(member.getEmail());

        TableDataInfo result = remoteUploadAnalysisService.getTargetOptions(queryDTO);


        return result;
    }

    public TableDataInfo getPipelineOptions(RemoteSelectQueryDTO queryDTO) {
        String memberId = queryDTO.getCreator();
        MemberDTO member = getOneMemberByMemberId(memberId);

        queryDTO.setEmail(member.getEmail());

        TableDataInfo result = remoteUploadAnalysisService.getPipelineOptions(queryDTO);

        return result;
    }

    public AnalysisVO getAnalInfoByNo(String analNo) {
        Analysis analysis = analysisRepository.findTopByAnalNo(analNo).orElseThrow(() -> new ServiceException("No Analysis found"));
        AnalysisVO result = new AnalysisVO();
        BeanUtil.copyProperties(analysis, result);
        List<PublishVO> publishVo = getPublishVo(AuthorizeType.analysis, analNo);
        result.setPublish(publishVo);
        return result;
    }

    public AnalysisVO updateAnalysis(AnalysisDTO analysisDTO) {
        Analysis analysis = analysisRepository.findTopByAnalNo(analysisDTO.getAnalysisNo()).orElseThrow(() -> new ServiceException("No Analysis found"));
        BeanUtil.copyProperties(analysisDTO, analysis);
        analysis.setOperator(SecurityUtils.getUserId().toString());
        analysis.setOperationDate(new Date());

        // 更新publish
        savePublish(analysisDTO.getPublish(), AuthorizeType.analysis, analysis.getAnalysisNo(), analysis.getCreator());
        analysisRepository.save(analysis);
        // 通知es更新索引
        updateEsData(AuthorizeType.analysis.name(), analysis.getAnalysisNo());

        return getAnalInfoByNo(analysis.getAnalysisNo());
    }

    public DeleteCheckResultVO deleteCheck(String analNo) {
        Analysis analysis = analysisRepository.findTopByAnalNo(analNo).orElseThrow(() -> new ServiceException("No Analysis found"));
        R<DeleteCheckResultVO> r = remoteUploadAnalysisService.deleteCheck(analysis.getAnalysisNo(), analysis.getCreator());
        if (R.isError(r)) {
            throw new ServiceException(r.getMsg());
        }
        return r.getData();
    }

    public void deleteAll(String analNo) {
        Analysis analysis = analysisRepository.findTopByAnalNo(analNo).orElseThrow(() -> new ServiceException("No Analysis found"));
        R r = remoteUploadAnalysisService.deleteAll(analysis.getAnalysisNo(), analysis.getCreator());
        if (R.isError(r)) {
            throw new ServiceException(r.getMsg());
        }
    }

    public void updateCreator(String analNo, String creator) {
        if (StrUtil.isBlank(creator) || StrUtil.isBlank(analNo)) {
            throw new ServiceException("Parameter cannot be empty");
        }
        Analysis analysis = analysisRepository.findTopByAnalNo(analNo).orElseThrow(() -> new ServiceException("No Analysis found"));
        // 查询邮件的用户
        MemberDTO data = getMemberInfoByEmail(creator);
        String newCreator = data.getId();
        if (newCreator == null) {
            throw new ServiceException(StrUtil.format("User {} not found", creator));
        }
        DeleteCheckResultVO checkResultVO = deleteCheck(analNo);
        // 删除数据
        if (CollUtil.isNotEmpty(checkResultVO.getErrors())) {
            throw new ServiceException("The analysis cannot change creator because it is associated with other data");
        }

        // 添加修改的日志
        addChangeCreatorLog(analNo, AuthorizeType.analysis.name(), analysis.getCreator(), newCreator, checkResultVO);

        // 修改analysis以及关联数据的creator
        analysisRepository.updateCreatorByAnalNo(checkResultVO.getAnalNos(), newCreator);
        dataRepository.updateCreatorByDatNoIn(checkResultVO.getDataNos(), newCreator);

        // 修改publish的creator
        publishRepository.updateCreatorByTypeAndTypeId(AuthorizeType.analysis.name(), checkResultVO.getAnalNos(), newCreator);

        //  通知更新索引
        if (CollUtil.isNotEmpty(checkResultVO.getDataNos())) {
            messageSender.sendDelayMsg(RouterKeyEnum.es_index_update_key.name(), new IndexUpdateMsg(AuthorizeType.data.name(), checkResultVO.getDataNos()));
        }

    }

    public void refreshIndex(String analNo) {
        Analysis analysis = analysisRepository.findByNo(analNo);
        if (analysis == null) {
            throw new ServiceException("未找到该分析");
        }
        // 更新浏览页索引
        applicationContext.publishEvent(new IndexUpdateEvent(this, AuthorizeType.analysis, analysis.getAnalysisNo()));

        List<Data> dataList = dataRepository.findDetailByAnalNoIn(CollUtil.newArrayList(analNo));
        if (CollUtil.isEmpty(dataList)) {
            return;
        }
        List<String> dataNos = dataList.stream().map(Data::getDatNo).collect(Collectors.toList());
        // 更新Data索引
        applicationContext.publishEvent(new IndexUpdateEvent(this, AuthorizeType.data, dataNos));
    }

    public File exportAnalysis(MetadataQueryDTO queryDTO) {
        // 将email转为memberId
        setMetaQueryDTOCreator(queryDTO);
        MongoPagingIterator<Analysis> iterator = analysisRepository.getPagingIterator(queryDTO);
        File tempDir = MyFileUtils.getTempDir();
        File resultFile = FileUtil.file(tempDir, "analysis.json");
        FileUtil.touch(resultFile);
        try (FileWriter fileWriter = new FileWriter(resultFile);
             JSONWriter jsonWriter = new JSONWriter(fileWriter)) {
            jsonWriter.config(SerializerFeature.PrettyFormat, true);
            jsonWriter.config(SerializerFeature.WriteDateUseDateFormat, true);
            jsonWriter.startArray();
            while (iterator.hasNext()) {
                List<Analysis> next = iterator.next();
                if (CollUtil.isEmpty(next)) {
                    break;
                }
                for (Analysis analysis : next) {
                    AnalysisExportDTO item = BeanUtil.copyProperties(analysis, AnalysisExportDTO.class);
                    jsonWriter.writeObject(item);
                }
            }
            jsonWriter.endArray();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultFile;
    }

    public List<AnalysisDataExportDTO> listDownloadData(DataListSearchVO searchVO) {
        final String typeNo = searchVO.getTypeNo();
        final Analysis analysis = analysisRepository.findByNo(typeNo);
        if (analysis == null) {
            throw new ServiceException("Analysis not found");
        }

        List<Data> list = dataRepository.findAllByAnalNoIn(CollUtil.newArrayList(searchVO.getTypeNo()));

        if (CollUtil.isEmpty(list)) {
            throw new ServiceException("No Data Export");
        }

        return list.stream().map(x -> {
            AnalysisDataExportDTO dto = new AnalysisDataExportDTO();

            dto.setAnalysisNo(analysis.getAnalysisNo());
            dto.setAnalysisName(analysis.getName());
            dto.setAnalysisType(analysis.getAnalysisType());

            dto.setDataNo(x.getDatNo());
            dto.setDataName(x.getName());
            dto.setDataType(x.getDataType());
            dto.setSecurity(x.getSecurity());
            dto.setMd5(x.getMd5());
            dto.setFileSize(FileUtil.readableFileSize(x.getFileSize()));
            dto.setDataPath(x.getFilePath());

            return dto;
        }).collect(Collectors.toList());


    }
}
