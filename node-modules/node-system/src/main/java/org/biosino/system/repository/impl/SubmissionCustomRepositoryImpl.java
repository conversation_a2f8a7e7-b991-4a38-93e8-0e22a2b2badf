package org.biosino.system.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.enums.SubmissionStatusEnum;
import org.biosino.common.mongo.dto.BaseQuery;
import org.biosino.common.mongo.entity.Submission;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.system.dto.dto.SubmissionDTO;
import org.biosino.system.repository.SubmissionCustomRepository;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class SubmissionCustomRepositoryImpl implements SubmissionCustomRepository {

    private final MongoTemplate mongoTemplate;

    private static void queryDate(BaseQuery dto, List<Criteria> criteriaList, String createTime) {
        if (ObjectUtil.isNotEmpty(dto.getBeginTime()) && ObjectUtil.isNotEmpty(dto.getEndTime())) {
            criteriaList.add(Criteria.where(createTime).gte(DateUtil.beginOfDay(dto.getBeginTime())).lte(DateUtil.endOfDay(dto.getEndTime())));
        } else if (ObjectUtil.isNotEmpty(dto.getBeginTime())) {
            criteriaList.add(Criteria.where(createTime).gte(DateUtil.beginOfDay(dto.getBeginTime())));
        } else if (ObjectUtil.isNotEmpty(dto.getEndTime())) {
            criteriaList.add(Criteria.where(createTime).lte(DateUtil.endOfDay(dto.getEndTime())));
        }
    }

    @Override
    public PageImpl<Submission> findAllPage(SubmissionDTO dto) {

        List<Criteria> criteriaList = new ArrayList<>();

        if (StrUtil.isNotBlank(dto.getSubNo())) {
            criteriaList.add(Criteria.where("sub_no").is(dto.getSubNo().trim()));
        }

        String status = dto.getStatus();
        if (StrUtil.isNotBlank(status) && !"All".equalsIgnoreCase(status)) {
            criteriaList.add(Criteria.where("status").is(status));
        }

        Long auditor = dto.getAuditor();
        if (auditor != null) {
            criteriaList.add(Criteria.where("auditor_id").is(auditor));
        }

        queryDate(dto, criteriaList, "submit_time");

        if (StrUtil.isNotBlank(dto.getCreator())) {
            criteriaList.add(Criteria.where("creator").is(dto.getCreator()));
        }

        Query query = new Query();
        if (CollUtil.isNotEmpty(criteriaList)) {
            query = new Query(new Criteria().andOperator(criteriaList));
        }
        Pageable pageable = dto.getPageable();
        // 加入查询条件
        long total = mongoTemplate.count(query, Submission.class);
        query.with(pageable);

        List<Submission> content = mongoTemplate.find(query, Submission.class);
        return new PageImpl<>(content, dto.getPageable(), total);
    }

}
