package org.biosino.system.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.enums.sys.DataStatusEnum;
import org.biosino.common.mongo.entity.admin.HumanResource;
import org.biosino.system.repository.ExpSampleTypeRepository;
import org.biosino.system.repository.HumanResourceCustomRepository;
import org.biosino.system.service.ISysDictTypeService;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.ArrayList;
import java.util.List;

import static org.biosino.system.service.impl.fd.FeatureDataServiceImpl.handleCat1;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class HumanResourceCustomRepositoryImpl implements HumanResourceCustomRepository {
    private final MongoTemplate mongoTemplate;
    private final ExpSampleTypeRepository expSampleTypeRepository;
    private final ISysDictTypeService sysDictTypeService;

    @Override
    public PageImpl<HumanResource> search(HumanResource search, Pageable pageable) {
        final List<Criteria> list = new ArrayList<>();
        final String category1 = search.getCategory1();
        if (StrUtil.isNotBlank(category1)) {
//            list.add(Criteria.where("category1").regex(".*" + ReUtil.escape(category1) + ".*"));
            list.add(Criteria.where("category1").is(category1));
        }

        final String category2 = search.getCategory2();
        if (StrUtil.isNotBlank(category2)) {
            list.add(Criteria.where("category2").is(category2));
        }

        final String category3 = search.getCategory3();
        if (StrUtil.isNotBlank(category3)) {
            list.add(Criteria.where("category3").is(category3));
        }

        final String projectNo = search.getProjectNo();
        if (StrUtil.isNotBlank(projectNo)) {
            list.add(Criteria.where("proj_no").is(projectNo));
        }

        final String status = search.getStatus();
        if (StrUtil.isNotBlank(status)) {
            list.add(Criteria.where("status").is(status));
        }

        final boolean microbeFlag = search.isMicrobeFlag();
        list.add(Criteria.where("microbeFlag").is(microbeFlag));

        Query query;
        if (CollUtil.isNotEmpty(list)) {
            query = new Query(new Criteria().andOperator(list));
        } else {
            query = new Query();
        }

        final long total = mongoTemplate.count(query, HumanResource.class);
        if (total == 0) {
            return new PageImpl<>(new ArrayList<>(), pageable, total);
        } else {
            query.with(pageable);
            final List<HumanResource> humanResources = mongoTemplate.find(query, HumanResource.class);
            return new PageImpl<>(handleCat1(humanResources, false, microbeFlag, expSampleTypeRepository, sysDictTypeService), pageable, total);
        }
    }

    @Override
    public List<HumanResource> allEnable(Pageable pageable) {
        final Query query = new Query(Criteria.where("status").is(DataStatusEnum.enable.name()));
        query.with(pageable);
        return mongoTemplate.find(query, HumanResource.class);
    }

}
