package org.biosino.system.repository.util;

import org.biosino.common.mongo.entity.statistics.BaseStatistics;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/1
 */
public class RepositoryUtil {

    public static <T extends BaseStatistics> List<T> findBaseList(final MongoTemplate mongoTemplate, final boolean all, Class<T> clz) {
        final Query query = new Query();
        if (all) {
            // query.with(PageRequest.of(0, 20000, Sort.by(Sort.Direction.DESC, "month")));
            query.with(Sort.by(Sort.Direction.DESC, "month"));
        } else {
            // 查询最新一个月的数据
            final Query monthQuery = new Query().with(PageRequest.of(0, 1, Sort.by(Sort.Direction.DESC, "month")));
            monthQuery.fields().include("month");
            final T top = mongoTemplate.findOne(monthQuery, clz);
            if (top == null) {
                return new ArrayList<>();
            }

            query.addCriteria(Criteria.where("month").is(top.getMonth()));
        }
        return mongoTemplate.find(query, clz);
    }

    public static <T extends BaseStatistics> List<T> findLatestMonthData(final MongoTemplate mongoTemplate, Class<T> clz) {
        return findBaseList(mongoTemplate, false, clz);
    }

}
