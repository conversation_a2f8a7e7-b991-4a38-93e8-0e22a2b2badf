import { createApp } from 'vue';
import ElementPlus from 'element-plus';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import 'element-plus/dist/index.css';
import '@/assets/styles/icon.css';
import App from './App.vue';
import router from './router';
import '@/assets/styles/index.scss';
import '@/assets/styles/matertialIcon.scss';
import './assets/main.css';
import ElTableInfiniteScroll from 'el-table-infinite-scroll';
import i18n from './assets/i18n/index';
// svg图标
import 'virtual:svg-icons-register';
import 'element-plus/theme-chalk/display.css';
import SvgIcon from '@/components/SvgIcon/index.vue';

import {
  addDateRange,
  handleTree,
  parseTime,
  resetForm,
  selectDictLabel,
  selectDictLabels,
  textValue,
  bytesToSize,
  formatNumber,
  validateChinese,
} from '@/utils/nodeCommon';

import { HotTable } from '@handsontable/vue3';
import 'handsontable/dist/handsontable.full.css'; //表格样式
import { registerAllModules } from 'handsontable/registry'; // 在线编辑样式
import './permission'; // permission control
// 注册指令
import plugins from './plugins';
import store from './store';
import { download } from '@/utils/request';
// 分页组件
import Pagination from '@/components/Pagination/index.vue';
import { useDict } from '@/utils/dict';
import { getConfigVal } from '@/utils/config';
import _ from 'lodash';

const app = createApp(App);

// 全局方法挂载
app.config.globalProperties.$_ = _;
app.config.globalProperties.$validateChinese = validateChinese;
app.config.globalProperties.$bytesToSize = bytesToSize;
app.config.globalProperties.$text = textValue;
app.config.globalProperties.$formatNumber = formatNumber;
app.config.globalProperties.parseTime = parseTime;
app.config.globalProperties.resetForm = resetForm;
app.config.globalProperties.download = download;
app.config.globalProperties.handleTree = handleTree;
app.config.globalProperties.addDateRange = addDateRange;
app.config.globalProperties.selectDictLabel = selectDictLabel;
app.config.globalProperties.selectDictLabels = selectDictLabels;
app.config.globalProperties.useDict = useDict;
app.config.globalProperties.getConfigVal = getConfigVal;

registerAllModules();
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}
// 全局组件挂载
app.component('SvgIcon', SvgIcon);
app.component('Pagination', Pagination);
app.component('HotTable', HotTable);

app.use(ElTableInfiniteScroll);
app.use(router);
app.use(store);
app.use(i18n);
// app.use(HotTable)
app.use(plugins);
app.use(ElementPlus, {
  // locale: zhCn,
});

app.mount('#app');
