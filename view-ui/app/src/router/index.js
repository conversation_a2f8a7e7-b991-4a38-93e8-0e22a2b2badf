import { createRouter, createWebHistory } from 'vue-router';

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '',
      redirect: '/home',
    },
    {
      path: '/home',
      name: 'Home',
      component: () => import('@/views/home/<USER>'),
    },
    {
      path: '/index',
      name: 'Index',
      component: () => import('@/views/home/<USER>'),
    },
    {
      path: '/esIndex',
      name: 'EsIndex',
      component: () => import('@/views/userCenter/esIndex.vue'),
    },
    {
      path: '/unauthorized',
      name: 'unauthorized',
      component: () => import('@/components/unauthorized.vue'),
    },
    {
      path: '/download',
      name: 'Download',
      component: () => import('@/views/download/download.vue'),
    },
    {
      path: '/statistic',
      name: 'Statistic',
      component: () => import('@/views/statistic/index.vue'),
    },
    {
      path: '/help',
      name: 'Help',
      component: () => import('@/views/help/index.vue'),
    },
    {
      path: '/submit/rawdata',
      name: 'Submit',
      component: () => import('@/views/submit/rawdata/rawData.vue'),
    },
    // Submit metadata
    {
      path: '/submit/metadata',
      name: 'SubmitMetadata',
      component: () => import('@/views/submit/metadata/selectMetaData.vue'),
    },
    {
      path: '/submit/metadata/rawData',
      name: 'AddRawData',
      component: () => import('@/views/submit/metadata/rawData/index.vue'),
    },
    {
      path: '/submit/metadata/rawData/edit/:subNo',
      name: 'EditRawData',
      component: () => import('@/views/submit/metadata/rawData/index.vue'),
    },
    {
      path: '/submit/metadata/analysisData',
      name: 'SubmitAnalysisData',
      component: () => import('@/views/submit/metadata/analysis/index.vue'),
    },
    {
      path: '/submit/metadata/analysisData/edit/:subNo',
      name: 'EditAnalysisData',
      component: () => import('@/views/submit/metadata/analysis/index.vue'),
    },
    // Submission List
    {
      path: '/submit/submission/list',
      name: 'SubmissionList',
      component: () => import('@/views/submit/submission/list.vue'),
    },
    {
      path: '/submit/submission/detail/:subNo',
      name: 'SubmissionDetail',
      component: () => import('@/views/submit/submission/detail/index.vue'),
    },
    // browse 路由
    {
      path: '/browse',
      name: 'Browse',
      component: () => import('@/views/browse/index.vue'),
    },
    // 详情
    {
      path: '/project/detail/:id',
      name: 'ProjDetail',
      component: () => import('@/views/browse/detail/project.vue'),
    },
    {
      path: '/experiment/detail/:id',
      name: 'ExpDetail',
      component: () => import('@/views/browse/detail/experiment.vue'),
    },
    {
      path: '/sample/detail/:id',
      name: 'SampDetail',
      component: () => import('@/views/browse/detail/sample.vue'),
    },
    {
      path: '/analysis/detail/:id',
      name: 'Analysis',
      component: () => import('@/views/browse/detail/analysis.vue'),
    },
    {
      path: '/run/detail/:id',
      name: 'RunDetail',
      component: () => import('@/views/browse/detail/run.vue'),
    },
    // 数据下载
    {
      path: '/download/node/data/:id',
      name: 'download',
      component: () => import('@/components/download.vue'),
    },
    {
      path: '/download/node/data/public/:id',
      name: 'PublicDownload',
      component: () => import('@/components/download.vue'),
    },
    {
      path: '/download/node/review/:reviewId/:code/:dataNo',
      name: 'ReviewDownload',
      component: () => import('@/components/download.vue'),
    },
    // 用户中心
    {
      path: '/userCenter',
      name: 'userCenter',
      component: () => import('@/views/userCenter/index.vue'),
    },
    {
      path: '/userCenter/:personal',
      name: 'Personal',
      component: () => import('@/views/userCenter/personal/index.vue'),
    },
    {
      path: '/review/detail/:reviewNo',
      name: 'ReviewDetail',
      component: () => import('@/views/userCenter/personal/reviewDetail.vue'),
    },
    {
      path: '/submit/metadata/:type/edit/:no',
      name: 'EditProject',
      component: () => import('@/views/userCenter/edit/index.vue'),
    },
    {
      path: '/featureData/:fdType',
      name: 'FeatureData',
      component: () => import('@/views/featureData/index.vue'),
    },
  ],
});

router.beforeEach((to, from, next) => {
  // 在每次导航之前滚动到页面顶部
  window.scrollTo({
    top: 0,
  });
  next();
});

export default router;
