<template>
  <div class="submit-page">
    <div class="container-fluid">
      <Breadcrumb :bread-item="$t('download.title')" />
      <div class="card mt-1">
        <h3 class="text-main-color mb-1">
          {{ $t('download.publicDownloadServer') }}
        </h3>
        <div class="downloadTable">
          <el-tabs v-model="activeName" type="card" class="demo-tabs">
            <el-tab-pane :label="$t('download.rawData')" name="Raw Data">
              <div class="d-flex align-items-center justify-space-between mb-1">
                <div class="w-75">
                  <el-input
                    v-model="rawDataQueryParams.searchName"
                    :placeholder="$t('download.searchPlaceholder')"
                    class="w-50"
                    clearable
                    @keyup.enter="pageRawDataList"
                  />
                  <el-button
                    class="radius-12 ml-1 mr-1"
                    type="primary"
                    @click="pageRawDataList"
                    >{{ $t('download.search') }}
                  </el-button>
                </div>

                <ToolBar
                  :columns="rawDataColumns"
                  :width="340"
                  :checkbox-width="120"
                ></ToolBar>
              </div>
              <el-table
                v-loading="rawDataLoading"
                max-height="550"
                tooltip-effect="dark"
                :data="rawDataTable"
                :header-cell-style="{
                  backgroundColor: '#EDF3FD',
                  color: '#333333',
                  fontWeight: 700,
                }"
                row-key="datNo"
                border
                @sort-change="handleRawDataSortChange"
                @selection-change="handleRawDataSelectionChange"
              >
                <el-table-column
                  type="selection"
                  :reserve-selection="true"
                  width="40"
                />
                <template v-for="(item, index) in rawDataColumns">
                  <el-table-column
                    v-if="item.visible"
                    :key="'table-column' + index"
                    :sortable="!item.notSort ? 'custom' : false"
                    :sort-orders="['ascending', 'descending']"
                    :prop="item.prop"
                    :label="item.label"
                    show-overflow-tooltip
                    :min-width="item.minWidth"
                  >
                    <template v-if="isRouteNo(item.prop)" #default="scope">
                      <router-link
                        :to="`/${routePath[item.prop]}/detail/${
                          scope.row[item.prop]
                        }`"
                        class="text-primary"
                        target="_blank"
                      >
                        {{ scope.row[item.prop] }}
                      </router-link>
                    </template>
                    <template v-if="item.prop === 'fileSize'" #default="scope">
                      {{ scope.row['readableFileSize'] }}
                    </template>
                  </el-table-column>
                </template>
                <el-table-column
                  :label="$t('download.operate')"
                  width="110"
                  fixed="right"
                >
                  <template #default="scope">
                    <div class="download-btn text-center">
                      <el-tooltip
                        :content="$t('download.tooltips.htmlDownload')"
                      >
                        <img
                          src="@/assets/images/btn-ico-h.png"
                          alt=""
                          class="download mr-05"
                          @click="showHttpDownloadModal(scope.row)"
                        />
                      </el-tooltip>
                      <el-tooltip
                        :content="$t('download.tooltips.sftpDownload')"
                      >
                        <img
                          src="@/assets/images/btn-ico-f.png"
                          alt=""
                          class="download"
                          @click="showSftpDownloadModal(scope.row)"
                        />
                      </el-tooltip>
                    </div>
                  </template>
                </el-table-column>
              </el-table>

              <pagination
                v-show="rawDataTotal > 0"
                v-model:page="rawDataQueryParams.pageNum"
                v-model:limit="rawDataQueryParams.pageSize"
                :page-sizes="[5, 10, 20, 50, 100]"
                class="mb-1 mt-2 justify-center"
                :total="rawDataTotal"
                @pagination="pageRawDataList"
              />
            </el-tab-pane>
            <el-tab-pane
              :label="$t('download.analysisData')"
              name="Analysis Data"
            >
              <div class="d-flex align-items-center justify-space-between mb-1">
                <div class="w-75">
                  <el-input
                    v-model="analDataQueryParams.searchName"
                    :placeholder="$t('download.searchPlaceholder')"
                    class="w-50"
                    clearable
                    @keyup.enter="pageAnalDataList"
                  />
                  <el-button
                    class="radius-12 ml-1 mr-1"
                    type="primary"
                    @click="pageAnalDataList"
                    >{{ $t('download.search') }}
                  </el-button>
                </div>

                <ToolBar
                  :columns="analDataColumns"
                  :width="340"
                  :checkbox-width="120"
                ></ToolBar>
              </div>
              <el-table
                v-loading="analDataLoading"
                tooltip-effect="dark"
                :data="analDataTable"
                :header-cell-style="{
                  backgroundColor: '#EDF3FD',
                  color: '#333333',
                  fontWeight: 700,
                }"
                max-height="550"
                row-key="datNo"
                border
                @sort-change="handleAnalDataSortChange"
                @selection-change="handleAnalDataSelectionChange"
              >
                <el-table-column
                  type="selection"
                  :reserve-selection="true"
                  width="40"
                />
                <template v-for="(item, index) in analDataColumns">
                  <el-table-column
                    v-if="item.visible"
                    :key="'table-column' + index"
                    :sortable="!item.notSort ? 'custom' : false"
                    :sort-orders="['ascending', 'descending']"
                    :prop="item.prop"
                    :label="item.label"
                    show-overflow-tooltip
                    :min-width="item.minWidth"
                  >
                    <template v-if="isRouteNo(item.prop)" #default="scope">
                      <router-link
                        :to="`/${routePath[item.prop]}/detail/${
                          scope.row[item.prop]
                        }`"
                        class="text-primary"
                        target="_blank"
                      >
                        {{ scope.row[item.prop] }}
                      </router-link>
                    </template>
                    <template v-if="item.prop === 'fileSize'" #default="scope">
                      {{ scope.row['readableFileSize'] }}
                    </template>
                  </el-table-column>
                </template>
                <el-table-column
                  :label="$t('download.operate')"
                  width="110"
                  fixed="right"
                >
                  <template #default="scope">
                    <div class="download-btn text-center">
                      <el-tooltip
                        :content="$t('download.tooltips.htmlDownload')"
                      >
                        <img
                          src="@/assets/images/btn-ico-h.png"
                          alt=""
                          class="download mr-05"
                          @click="showHttpDownloadModal(scope.row)"
                        />
                      </el-tooltip>
                      <el-tooltip
                        :content="$t('download.tooltips.sftpDownload')"
                      >
                        <img
                          src="@/assets/images/btn-ico-f.png"
                          alt=""
                          class="download"
                          @click="showSftpDownloadModal(scope.row)"
                        />
                      </el-tooltip>
                    </div>
                  </template>
                </el-table-column>
              </el-table>

              <pagination
                v-show="analDataTotal > 0"
                v-model:page="analDataQueryParams.pageNum"
                v-model:limit="analDataQueryParams.pageSize"
                :page-sizes="[10, 20, 50, 100]"
                class="mb-1 mt-2 justify-center"
                :total="analDataTotal"
                @pagination="pageAnalDataList"
              />
            </el-tab-pane>

            <div class="text-align-right">
              <el-button
                type="primary"
                :icon="Download"
                round
                :disabled="
                  rawDataSelectRows.length + analDataSelectRows.length === 0
                "
                @click="exportPublicDataLink"
                >{{ $t('download.exportDataLinks') }}
              </el-button>
            </div>
          </el-tabs>
          <el-divider />
        </div>
      </div>
    </div>

    <http-download-dialog ref="httpDownloadDialog"></http-download-dialog>
    <sftp-download-dialog ref="sftpDownloadDialog"></sftp-download-dialog>
  </div>
</template>

<script setup>
  import { getCurrentInstance, onMounted, reactive, ref, toRefs } from 'vue';
  import { Download } from '@element-plus/icons-vue';
  import { useI18n } from 'vue-i18n';
  import Breadcrumb from '@/components/breadcrumb.vue';
  import ToolBar from '@/components/toolBar.vue';
  import { getPublicDataList } from '@/api/app/browseDetail';
  import SftpDownloadDialog from '@/views/browse/detail/components/SftpDownloadDialog.vue';
  import HttpDownloadDialog from '@/views/browse/detail/components/HttpDownloadDialog.vue';

  const { proxy } = getCurrentInstance();
  const { t } = useI18n();

  onMounted(() => {
    pageRawDataList();
    pageAnalDataList();
  });
  const activeName = ref('Raw Data');

  // prop 可以悬挑
  function isRouteNo(prop) {
    let arr = ['projNo', 'expNo', 'sapNo', 'analNo'];
    return arr.indexOf(prop) !== -1;
  }

  let routePath = {
    projNo: 'project',
    expNo: 'experiment',
    sapNo: 'sample',
    analNo: 'analysis',
  };

  /** RawData  queryParams */
  const rawDataColumns = ref([
    {
      prop: 'projNo',
      minWidth: 120,
      label: t('download.columns.projectId'),
      visible: true,
    },
    {
      prop: 'projName',
      minWidth: 190,
      label: t('download.columns.projectName'),
      visible: false,
    },
    {
      prop: 'expNo',
      minWidth: 140,
      label: t('download.columns.experimentId'),
      visible: true,
    },
    {
      prop: 'expName',
      minWidth: 190,
      label: t('download.columns.experimentName'),
      visible: false,
    },
    {
      prop: 'expType',
      minWidth: 190,
      label: t('download.columns.experimentType'),
      visible: true,
    },
    {
      prop: 'expDesc',
      minWidth: 220,
      label: t('download.columns.experimentDescription'),
      visible: false,
    },
    {
      prop: 'sapNo',
      minWidth: 120,
      label: t('download.columns.sampleId'),
      visible: true,
    },
    {
      prop: 'sapName',
      minWidth: 150,
      label: t('download.columns.sampleName'),
      visible: false,
    },
    {
      prop: 'sapType',
      minWidth: 170,
      label: t('download.columns.sampleType'),
      visible: true,
    },
    {
      prop: 'sapDesc',
      minWidth: 190,
      label: t('download.columns.sampleDescription'),
      visible: false,
    },
    {
      prop: 'runNo',
      minWidth: 120,
      label: t('download.columns.runId'),
      visible: true,
    },
    {
      prop: 'runName',
      minWidth: 170,
      label: t('download.columns.runName'),
      visible: true,
    },
    {
      prop: 'datNo',
      minWidth: 120,
      label: t('download.columns.dataId'),
      visible: true,
    },
    {
      prop: 'name',
      minWidth: 170,
      label: t('download.columns.dataName'),
      visible: true,
    },
    {
      prop: 'fileSize',
      minWidth: 110,
      label: t('download.columns.dataSize'),
      visible: true,
    },
    {
      prop: 'uploadTime',
      minWidth: 170,
      label: t('download.columns.dataUploadTime'),
      visible: true,
    },
  ]);

  const rawData = reactive({
    rawDataTable: [],
    rawDataTotal: 0,
    rawDataQueryParams: {
      pageNum: 1,
      pageSize: 10,
      searchName: '',
      sortKey: 'uploadTime',
      sortType: 'desc',
    },
    rawDataLoading: true,
  });
  let { rawDataTable, rawDataTotal, rawDataQueryParams, rawDataLoading } =
    toRefs(rawData);

  function pageRawDataList() {
    rawDataLoading.value = true;
    let param = {
      type: 'project',
    };
    param = { ...param, ...rawDataQueryParams.value };
    getPublicDataList(param)
      .then(response => {
        let pageInfo = response.data;
        rawDataTotal.value = pageInfo.total;
        rawDataTable.value = pageInfo.list;
      })
      .finally(() => {
        rawDataLoading.value = false;
      });
  }

  function handleRawDataSortChange(column, prop, order) {
    if (column.order) {
      rawDataQueryParams.value.sortKey = column.prop;
      rawDataQueryParams.value.sortType =
        column.order === 'ascending' ? 'asc' : 'desc';
      pageRawDataList();
    }
  }

  let rawDataSelectRows = ref([]);

  function handleRawDataSelectionChange(selection) {
    rawDataSelectRows.value = selection;
  }

  /** analData queryParams */
  const analDataColumns = ref([
    {
      prop: 'analNo',
      minWidth: 120,
      label: t('download.columns.analysisId'),
      visible: true,
    },
    {
      prop: 'analName',
      minWidth: 190,
      label: t('download.columns.analysisName'),
      visible: true,
    },
    {
      prop: 'analType',
      minWidth: 120,
      label: t('download.columns.analysisType'),
      visible: true,
    },
    {
      prop: 'datNo',
      minWidth: 120,
      label: t('download.columns.dataId'),
      visible: true,
    },
    {
      prop: 'name',
      minWidth: 170,
      label: t('download.columns.dataName'),
      visible: true,
    },
    {
      prop: 'dataType',
      minWidth: 120,
      label: t('download.columns.dataType'),
      visible: true,
    },
    {
      prop: 'fileSize',
      minWidth: 110,
      label: t('download.columns.dataSize'),
      visible: true,
    },
    {
      prop: 'uploadTime',
      minWidth: 170,
      label: t('download.columns.dataUploadTime'),
      visible: true,
    },
  ]);
  const analData = reactive({
    analDataTable: [],
    analDataTotal: 0,
    analDataQueryParams: {
      pageNum: 1,
      pageSize: 10,
      searchName: '',
      sortKey: 'uploadTime',
      sortType: 'desc',
    },
    analDataLoading: true,
  });
  let { analDataTable, analDataTotal, analDataQueryParams, analDataLoading } =
    toRefs(analData);

  function pageAnalDataList() {
    analDataLoading.value = true;
    let param = {
      type: 'analysis',
    };
    param = { ...param, ...analDataQueryParams.value };
    getPublicDataList(param)
      .then(response => {
        let pageInfo = response.data;
        analDataTotal.value = pageInfo.total;
        analDataTable.value = pageInfo.list;
      })
      .finally(() => {
        analDataLoading.value = false;
      });
  }

  function handleAnalDataSortChange(column, prop, order) {
    if (column.order) {
      analDataQueryParams.value.sortKey = column.prop;
      analDataQueryParams.value.sortType =
        column.order === 'ascending' ? 'asc' : 'desc';
      pageAnalDataList();
    }
  }

  let analDataSelectRows = ref([]);

  function handleAnalDataSelectionChange(selection) {
    analDataSelectRows.value = selection;
  }

  /** http下载 */
  function showHttpDownloadModal(row) {
    proxy.$refs['httpDownloadDialog'].showHttpDownloadModal(row);
  }

  /** sftp下载 */
  function showSftpDownloadModal(row) {
    proxy.$refs['sftpDownloadDialog'].showSftpDownloadModal(row);
  }

  function exportPublicDataLink() {
    let dataNos = [];
    dataNos.push(...rawDataSelectRows.value.map(it => it.datNo));
    dataNos.push(...analDataSelectRows.value.map(it => it.datNo));
    if (dataNos.length === 0) {
      // 请先勾选Data
      proxy.$modal.alertWarning(t('download.pleaseSelectDataFirst'));
      return;
    }
    proxy.download(
      `/download/node/exportPublicDownloadLink`,
      { dataNos: dataNos },
      `batch_export_data_download_link.zip`,
    );
  }
</script>

<style lang="scss" scoped>
  .el-icon {
    margin-right: 0.5rem;
  }

  :deep(.ftp-download .el-dialog__body) {
    padding: 0 15px !important;
  }

  :deep(.ftp-download .el-dialog__title) {
    font-weight: 600 !important;
  }

  .integrity-body {
    background-color: #fcf8e3;
    border: 1px solid #efe8c5;
    padding: 10px 15px;
    border-radius: 8px;
    color: #8f7443;
    text-align: justify;
    font-size: 14px;

    .note {
      color: #8a6d3b;
    }
  }

  .download {
    width: 20px;
    cursor: pointer;
  }

  :deep(.el-tabs__header .el-tabs__item) {
    margin-left: 3px !important;
  }
</style>
