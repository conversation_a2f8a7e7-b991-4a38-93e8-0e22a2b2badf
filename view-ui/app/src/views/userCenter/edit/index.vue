<template>
  <div class="submit-page">
    <div class="container-fluid">
      <Breadcrumb bread-item="Metadata" />
      <ChooseData :active-menu="activeMenu" class="hidden-xs-only" />
      <div class="d-flex submitData">
        <div v-if="type === 'project'" class="mr-1" style="flex: 0 0 120px">
          <div
            class="item bubble-right"
            :class="{ active: isActive === 'Submitter' }"
            @click="
              isActive = 'Submitter';
              activeMenu = 'metadata';
            "
          >
            <span class="text-danger">*</span>
            <span class="text-primary font-600">{{
              $t('submit.metadata.rawData.navigation.submitter')
            }}</span>
          </div>
          <el-divider />
          <div
            class="bubble-right text-primary font-600"
            :class="{ active: isActive === 'Project' }"
            @click="
              isActive = 'Project';
              activeMenu = 'metadata';
            "
          >
            {{ $t('submit.metadata.rawData.navigation.project') }}
          </div>
        </div>
        <div v-if="type === 'experiment'" class="mr-1" style="flex: 0 0 120px">
          <div
            class="item bubble-right"
            :class="{ active: isActive === 'Submitter' }"
            @click="
              isActive = 'Submitter';
              activeMenu = 'metadata';
            "
          >
            <span class="text-danger">*</span>
            <span class="text-primary font-600">{{
              $t('submit.metadata.rawData.navigation.submitter')
            }}</span>
          </div>
          <el-divider />
          <div
            class="bubble-right text-primary font-600"
            :class="{ active: isActive === 'ExpSingle' }"
            @click="
              isActive = 'ExpSingle';
              activeMenu = 'metadata';
            "
          >
            {{ $t('submit.metadata.rawData.navigation.experiment') }}
          </div>
        </div>
        <div v-if="type === 'sample'" class="mr-1" style="flex: 0 0 120px">
          <div
            class="item bubble-right"
            :class="{ active: isActive === 'Submitter' }"
            @click="
              isActive = 'Submitter';
              activeMenu = 'metadata';
            "
          >
            <span class="text-danger">*</span>
            <span class="text-primary font-600">{{
              $t('submit.metadata.rawData.navigation.submitter')
            }}</span>
          </div>
          <el-divider />
          <div
            class="bubble-right text-primary font-600"
            :class="{ active: isActive === 'SampleSingle' }"
            @click="
              isActive = 'SampleSingle';
              activeMenu = 'metadata';
            "
          >
            {{ $t('submit.metadata.rawData.navigation.sample') }}
          </div>
        </div>
        <div v-if="type === 'analysis'" class="mr-1" style="flex: 0 0 120px">
          <div
            class="item bubble-right"
            :class="{ active: isActive === 'Submitter' }"
            @click="
              isActive = 'Submitter';
              activeMenu = 'metadata';
            "
          >
            <span class="text-danger">*</span>
            <span class="text-primary font-600">{{
              $t('submit.metadata.rawData.navigation.submitter')
            }}</span>
          </div>
          <el-divider />
          <div
            class="bubble-right text-primary font-600"
            :class="{ active: isActive === 'AnalysisSingle' }"
            @click="
              isActive = 'AnalysisSingle';
              activeMenu = 'metadata';
            "
          >
            {{ $t('userCenter.index.dataList.analysis') }}
          </div>
        </div>
        <transition name="animation" mode="out-in">
          <keep-alive>
            <component
              :is="tabs[isActive]"
              @continue-message="handleMessage"
            ></component>
          </keep-alive>
        </transition>
      </div>
    </div>
  </div>
</template>

<script setup>
  import Breadcrumb from '@/components/breadcrumb.vue';
  import ChooseData from '@/views/submit/components/chooseData.vue';
  import { onMounted, ref } from 'vue';
  import { useRoute } from 'vue-router';
  import Submitter from '@/views/userCenter/edit/submitter.vue';
  import Project from '@/views/userCenter/edit/project/project.vue';
  import ExpSingle from '@/views/userCenter/edit/experiment/expSingle.vue';
  import SampleSingle from '@/views/userCenter/edit/sample/sampleSingle.vue';
  import AnalysisSingle from '@/views/userCenter/edit/analysis/analysisSingle.vue';

  const route = useRoute();
  let type = route.params.type;
  let no = route.params.no;
  let tabs = {
    Submitter,
    Project,
  };

  onMounted(() => {
    if (type === 'project') {
      tabs = {
        Submitter,
        Project,
      };
    }
    if (type === 'experiment') {
      tabs = {
        Submitter,
        ExpSingle,
      };
    }
    if (type === 'sample') {
      tabs = {
        Submitter,
        SampleSingle,
      };
    }
    if (type === 'analysis') {
      tabs = {
        Submitter,
        AnalysisSingle,
      };
    }
  });

  const isActive = ref('Submitter');
  const activeMenu = ref('metadata');

  const handleMessage = val => {
    isActive.value = val;
  };
</script>

<style lang="scss" scoped>
  .animation-enter-from,
  .animation-leave-to {
    transform: translateX(20px);
    opacity: 0;
  }

  .animation-enter-to,
  .animation-leave-from {
    opacity: 1;
  }

  .animation-enter-active {
    transition: all 0.7s ease;
  }

  .animation-leave-active {
    transition: all 0.3s cubic-bezier(1, 0.6, 0.6, 1);
  }

  .submitData {
    .before-circle {
      &.active {
        background-color: #fff;
        border-radius: 14px;
        color: #3a78e8;
        /* padding: 0 20px; */
        border: 1px solid #3a78e8;
        padding: 0 15px;
        text-align: center;

        &:before {
          display: none;
          background-color: #3a78e8 !important;
        }
      }

      &:before {
        background-color: #999999 !important;
      }
    }

    .el-form {
      .el-form-item {
        width: 30%;
      }
    }
  }

  .fade-enter-active,
  .fade-leave-active {
    transition:
      opacity 0.3s,
      transform 0.3s;
  }

  .fade-enter {
    opacity: 0;
    //transform: translateX(10px);
  }

  animate__fadeInLeft .fade-leave-to {
    opacity: 0;
    //transform: translateX(-10px);
  }

  @media (max-width: 767px) {
    .submitData {
      margin-top: 1rem;
      flex-direction: column;
    }
  }
</style>
