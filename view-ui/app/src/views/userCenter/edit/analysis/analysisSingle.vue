<template>
  <div class="project w-100">
    <div class="card card-container pt-0">
      <FillTip></FillTip>
      <div class="category-title font-600 text-main-color">
        {{ $t('userCenter.edit.analysis.analysisSingle.generalInfo.title') }}
      </div>
      <div class="plr-20 bg-gray general-info mt-1">
        <el-form
          ref="analForm"
          label-position="top"
          :model="form"
          :inline="true"
          :scroll-to-error="true"
          style="padding-top: 8px"
          :rules="rules"
        >
          <el-form-item
            :label="
              $t(
                'userCenter.edit.analysis.analysisSingle.generalInfo.analysisId',
              )
            "
          >
            <el-input
              disabled
              :placeholder="
                $t(
                  'userCenter.edit.analysis.analysisSingle.generalInfo.analysisIdPlaceholder',
                )
              "
            />
          </el-form-item>
          <el-form-item
            prop="name"
            :label="
              $t(
                'userCenter.edit.analysis.analysisSingle.generalInfo.analysisName',
              )
            "
          >
            <el-input v-model="form.name" />
          </el-form-item>
          <el-form-item
            class="w-100"
            :label="
              $t(
                'userCenter.edit.analysis.analysisSingle.generalInfo.analysisDescription',
              )
            "
            prop="description"
          >
            <el-input v-model="form.description" type="textarea" rows="5" />
          </el-form-item>
        </el-form>
      </div>
      <div class="category-title font-600 text-main-color">
        <span class="text-danger">*</span
        >{{ $t('userCenter.edit.analysis.analysisSingle.analysisType.title') }}
      </div>
      <div class="bg-gray analysis-type p-20">
        <el-radio-group v-model="form.analysisType">
          <el-radio
            v-for="dict in node_analysis_type"
            :key="dict.value"
            :label="dict.value"
            >{{ dict.label }}
          </el-radio>
          <div class="d-flex">
            <el-radio label="Other" style="width: 100px; margin-right: 0.3rem"
              >Other
            </el-radio>
            <el-input
              v-if="form.analysisType === 'Other'"
              v-model="form.customAnalysisType"
              type="text"
            ></el-input>
          </div>
        </el-radio-group>
      </div>
      <div class="category-title font-600 text-main-color">
        <span class="text-danger">*</span
        >{{ $t('userCenter.edit.analysis.analysisSingle.target.title') }}
      </div>
      <div class="plr-20 target d-flex mt-1 flex-wrap">
        <target
          :key="'targetComponent-' + componentKey"
          v-model:target-data="form.target"
        ></target>
        <el-divider class="mb-05 mr-1 mt-1"></el-divider>
        <other-target
          :key="'otherTargetComponent-' + componentKey"
          v-model:custom-target-data="form.customTarget"
        ></other-target>
      </div>

      <div class="category-title font-600 text-main-color">
        {{ $t('userCenter.edit.analysis.analysisSingle.pipeline.title') }}
      </div>
      <pipeline
        :key="'pipelineComponent-' + componentKey"
        v-model:pipeline-data="form.pipeline"
      ></pipeline>
      <Publication
        :key="'publicationComponent-' + componentKey"
        v-model:publish-data="form.publish"
      ></Publication>
      <div class="text-align-right mt-2 pr-20">
        <!--        <el-button
                  type="primary"
                  class="btn-primary btn btn-s btn-shadow"
                  round
                  @click="continueNext"
                  >Continue
                </el-button>-->
        <el-button
          type="primary"
          class="btn-primary btn btn-s btn-shadow"
          round
          @click="previewData"
          >{{
            $t('userCenter.edit.analysis.analysisSingle.buttons.previewSave')
          }}
        </el-button>
        <el-button class="btn-primary btn btn-round" round @click="resetForm"
          >{{ $t('userCenter.edit.analysis.analysisSingle.buttons.reset') }}
        </el-button>
        <!--        <el-button
                  :disabled="!form.analysisNo"
                  type="danger"
                  class="btn"
                  plain
                  round
                  @click="deleteForm"
                  >Delete
                </el-button>-->
      </div>
    </div>
    <el-dialog
      v-model="previewDialog"
      :title="$t('userCenter.edit.analysis.analysisSingle.preview.title')"
      width="70%"
      class="preview-dialog radius-14"
    >
      <el-divider content-position="left"
        ><h3 class="preview-title">
          {{
            $t('userCenter.edit.analysis.analysisSingle.preview.analysisTitle')
          }}
        </h3></el-divider
      >
      <div class="d-flex preview">
        <div>
          <span class="title">{{
            $t('userCenter.edit.analysis.analysisSingle.preview.analysisId')
          }}</span>
          <span class="content">{{
            $t(
              'userCenter.edit.analysis.analysisSingle.preview.analysisIdContent',
            )
          }}</span>
        </div>
        <div>
          <span class="title">{{
            $t('userCenter.edit.analysis.analysisSingle.preview.analysisName')
          }}</span>
          <span class="content">{{ $text(submitData.name) }}</span>
        </div>
        <div>
          <span class="title">{{
            $t('userCenter.edit.analysis.analysisSingle.preview.description')
          }}</span>
          <span class="content">{{ $text(submitData.description) }}</span>
        </div>
        <div>
          <span class="title">{{
            $t('userCenter.edit.analysis.analysisSingle.preview.analysisType')
          }}</span>
          <span v-if="submitData.analysisType !== 'Other'" class="content">{{
            $text(submitData.analysisType)
          }}</span>
          <span v-else>{{ $text(submitData.customAnalysisType) }}</span>
        </div>
      </div>

      <el-divider content-position="left"
        ><h3 class="preview-title">
          {{
            $t('userCenter.edit.analysis.analysisSingle.preview.targetTitle')
          }}
        </h3></el-divider
      >
      <div class="preview">
        <div
          v-if="submitData.target.length !== 0"
          class="w-100 d-flex justify-space-between flex-wrap"
        >
          <div
            v-for="(it, index) in submitData.target"
            :key="'preview-target-target-' + index"
            class="item align-items-center w-50 mt-1"
          >
            <span class="label title">
              <span class="font-600 mr-1">{{
                $t('userCenter.edit.analysis.analysisSingle.preview.target')
              }}</span>
              <span>
                <el-tag round>{{ index + 1 }}</el-tag></span
              >
            </span>
            <div class="bg-gray d-flex content">
              <p
                class="text-secondary-color font-14 font-600 mr-1"
                style="
                  text-transform: capitalize;
                  max-width: 100px;
                  min-width: 100px;
                "
              >
                {{ it.type }}:
              </p>
              <div>
                <el-tag v-for="tag in it.nos" :key="tag" round class="mr-1">
                  <router-link
                    target="_blank"
                    :to="`/${it.type.toLowerCase()}/detail/${tag}`"
                    >{{ tag }}
                  </router-link>
                </el-tag>
              </div>
            </div>
          </div>
        </div>

        <div class="w-100 d-flex justify-space-between flex-wrap">
          <div
            v-for="(it, index) in submitData.customTarget"
            :key="'preview-custom-target-' + index"
            class="item align-items-center"
          >
            <span class="label title">
              <span class="font-600 mr-1">{{
                $t(
                  'userCenter.edit.analysis.analysisSingle.preview.otherTarget',
                )
              }}</span>
              <span>
                <el-tag round>{{ index + 1 }}</el-tag></span
              >
            </span>
            <div class="bg-gray d-flex content">
              <span
                >{{
                  $t('userCenter.edit.analysis.analysisSingle.preview.name')
                }}
              </span>
              <span class="ml-1">{{ $text(it.name) }}</span>
              <span class="ml-2"
                >{{
                  $t('userCenter.edit.analysis.analysisSingle.preview.link')
                }}
              </span>
              <a target="_blank" :href="it.link" class="text-primary ml-1">{{
                $text(it.link)
              }}</a>
            </div>
          </div>
        </div>
      </div>
      <el-divider content-position="left"
        ><h3 class="preview-title">
          {{
            $t('userCenter.edit.analysis.analysisSingle.preview.pipelineTitle')
          }}
        </h3></el-divider
      >
      <div class="d-flex preview">
        <el-table
          class="w-100"
          :data="pipelinePreview"
          border
          tooltip-effect="light"
          :header-cell-style="{
            backgroundColor: '#EDF3FD',
            color: '#333333',
            fontWeight: 700,
          }"
        >
          <el-table-column prop="program" label="" width="70">
            <template #default="scope">
              <el-tag round>{{ scope.row.no }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="program"
            :label="
              $t('userCenter.edit.analysis.analysisSingle.preview.program')
            "
          >
            <template #default="scope">
              {{ $text(scope.row.program) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="link"
            :label="$t('userCenter.edit.analysis.analysisSingle.preview.link')"
          >
            <template #default="scope">
              <a target="_blank" :href="scope.row.link" class="text-primary">{{
                $text(scope.row.link)
              }}</a>
            </template>
          </el-table-column>
          <el-table-column
            prop="version"
            :label="
              $t('userCenter.edit.analysis.analysisSingle.preview.version')
            "
          >
            <template #default="scope">
              {{ $text(scope.row.version) }}
            </template>
          </el-table-column>
          <el-table-column
            :label="
              $t('userCenter.edit.analysis.analysisSingle.preview.output')
            "
          >
            <template #default="scope">
              <div v-if="scope.row.output.length > 0">
                <el-tag
                  v-for="(it, index) in scope.row.output"
                  :key="'ouput-tag-' + index"
                  class="mr-1 cursor-pointer"
                  >{{ it }}
                </el-tag>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="note"
            :label="$t('userCenter.edit.analysis.analysisSingle.preview.notes')"
          >
            <template #default="scope">
              {{ $text(scope.row.note) }}
            </template>
          </el-table-column>
        </el-table>
      </div>

      <PreviewPublish :publish-data="submitData.publish"></PreviewPublish>
      <template #footer>
        <span class="dialog-footer">
          <div class="text-align-center">
            <el-button
              type="primary"
              class="btn-primary btn btn-s btn-shadow"
              round
              @click="saveData"
              >{{
                $t('userCenter.edit.analysis.analysisSingle.preview.save')
              }}</el-button
            >
            <el-button
              round
              class="btn-primary btn btn-round"
              @click="previewDialog = false"
              >{{
                $t('userCenter.edit.analysis.analysisSingle.preview.backEdit')
              }}</el-button
            >
          </div>
        </span>
      </template>
    </el-dialog>

    <ArchivingDialog
      ref="archivingDialogRef"
      :text-tip="
        $t('userCenter.edit.analysis.analysisSingle.messages.editingCompleted')
      "
    ></ArchivingDialog>
  </div>
</template>

<script setup>
  import {
    getCurrentInstance,
    nextTick,
    onActivated,
    onMounted,
    reactive,
    ref,
    toRefs,
  } from 'vue';
  import Publication from '@/views/submit/metadata/rawData/common/Publications.vue';
  import Target from '@/views/submit/metadata/analysis/common/Target.vue';
  import Pipeline from '@/views/submit/metadata/analysis/common/Pipeline.vue';
  import OtherTarget from '@/views/submit/metadata/analysis/common/OtherTarget.vue';
  import { deepClone, isArrEmpty, isStrBlank } from '@/utils';
  import useSubmissionStore from '@/store/modules/metadata';
  import { storeToRefs } from 'pinia';
  import PreviewPublish from '@/views/submit/metadata/rawData/common/PreviewPublish.vue';
  import {
    deleteAnalysis,
    getAnalysisInfo,
    saveEditAnalysis,
    validateAnalysisName,
  } from '@/api/metadata/analysis';
  import { useRoute } from 'vue-router';
  import bus from '@/utils/bus';
  import { BusEnum } from '@/utils/enums';
  import FillTip from '@/views/submit/components/FillTip.vue';
  import ArchivingDialog from '@/views/submit/components/ArchivingDialog.vue';
  import { batchValidateChinese } from '@/utils/nodeCommon';

  let route = useRoute();
  let { proxy } = getCurrentInstance();
  /** 获取字典数据 */
  const { node_analysis_type } = proxy.useDict('node_analysis_type');
  let submissionStore = useSubmissionStore();
  let { subNo, submission } = storeToRefs(submissionStore);
  let emit = defineEmits(['continueMessage']);
  let oldFormStr = ref('');
  const checkAnalysisName = (rule, value, callback) => {
    if (!value) {
      return callback(
        new Error(
          proxy.$t(
            'userCenter.edit.analysis.analysisSingle.validation.analysisNameRequired',
          ),
        ),
      );
    }
    validateAnalysisName({
      analysisNo: form.value.analysisNo,
      name: form.value.name,
    })
      .then(response => {
        if (response && response.msg) {
          callback(new Error(response.msg));
        }
        callback();
      })
      .catch(error => callback(new Error(error)));
  };

  // 切换组件时，自动定位到顶部
  onActivated(() => {
    window.scrollTo(0, 0);
  });

  onMounted(() => {
    initData();
  });

  function initData() {
    const no = route.params.no;
    // 如果存在SUB NO则说明是编辑数据，从数据库回显
    if (no.indexOf('SUB') !== -1) {
      getAnalysisInfo(submission.value.analSingleNo).then(response => {
        let data = response.data;
        if (isArrEmpty(data.target)) {
          data.target = [
            {
              type: 'project',
              nos: [],
            },
          ];
        }
        if (isArrEmpty(data.customTarget)) {
          data.customTarget = [
            {
              name: undefined,
              link: undefined,
            },
          ];
        }
        if (isArrEmpty(data.pipeline)) {
          data.pipeline = [
            {
              program: undefined,
              link: undefined,
              version: undefined,
              note: undefined,
              output: [],
            },
          ];
        }
        componentKey.value++;
        form.value = data;
        stringifyFormData();
      });
    } else if (no) {
      getAnalysisInfo(no).then(response => {
        let data = response.data;
        if (isArrEmpty(data.target)) {
          data.target = [
            {
              type: 'project',
              nos: [],
            },
          ];
        }
        if (isArrEmpty(data.customTarget)) {
          data.customTarget = [
            {
              name: undefined,
              link: undefined,
            },
          ];
        }
        if (isArrEmpty(data.pipeline)) {
          data.pipeline = [
            {
              program: undefined,
              link: undefined,
              version: undefined,
              note: undefined,
              output: [],
            },
          ];
        }
        componentKey.value++;
        form.value = data;
        stringifyFormData();
      });
    } else {
      stringifyFormData();
    }
  }

  let data = reactive({
    form: {
      subNo: subNo.value,
      analysisNo: undefined,
      name: undefined,
      description: undefined,
      analysisType: undefined,
      customAnalysisType: undefined,
      target: [
        {
          type: 'project',
          nos: [],
        },
      ],
      customTarget: [
        {
          name: undefined,
          link: undefined,
        },
      ],
      pipeline: [
        {
          program: undefined,
          link: undefined,
          version: undefined,
          note: undefined,
          output: [],
        },
      ],
      submitter: undefined,
      publish: {
        publication: undefined,
        doi: undefined,
        pmid: undefined,
        reference: undefined,
        articleName: undefined,
      },
    },
    rules: {
      name: [
        {
          required: true,
          validator: checkAnalysisName,
          trigger: 'blur',
        },
        {
          validator: proxy.$validateChinese,
          trigger: 'blur',
        },
      ],
      description: [
        {
          validator: proxy.$validateChinese,
          trigger: 'blur',
        },
      ],
    },
    pipelinePreview: [],
  });
  let { form, rules, pipelinePreview } = toRefs(data);
  let componentKey = ref(0);
  let previewDialog = ref(false);

  function resetForm() {
    proxy.resetForm('analForm');
    // 重置数据
    form.value = {
      subNo: subNo.value,
      analysisNo: undefined,
      name: undefined,
      description: undefined,
      analysisType: undefined,
      customAnalysisType: undefined,
      target: [
        {
          type: 'project',
          nos: [],
          textNos: '',
        },
      ],
      customTarget: [
        {
          name: undefined,
          link: undefined,
        },
      ],
      pipeline: [
        {
          program: undefined,
          link: undefined,
          version: undefined,
          notes: undefined,
          output: [],
        },
      ],
      submitter: undefined,
      publish: {
        publication: undefined,
        doi: undefined,
        pmid: undefined,
        reference: undefined,
        articleName: undefined,
      },
    };
    // 刷新组件
    componentKey.value++;
    // initData();
  }

  /** 校验表单数据是否被更改 */
  function validateSaved() {
    const currentFormStr = JSON.stringify(form.value);
    return currentFormStr === oldFormStr.value;
  }

  /** 继续 */
  function continueNext() {
    if (validateSaved()) {
      sendContinueMessage();
    } else {
      proxy.$modal
        .confirm(
          proxy.$t(
            'userCenter.edit.analysis.analysisSingle.messages.unsavedConfirm',
          ),
        )
        .then(function () {
          sendContinueMessage();
        })
        .catch(() => {});
    }
  }

  const sendContinueMessage = () => {
    emit('continueMessage', 'ArchivingSingle');
  };

  let submitData = reactive({});

  /** 等所有的子组件全部渲染完成后序列化json */
  function stringifyFormData() {
    // 等所有的子组件全部渲染完成后序列化json
    nextTick().then(() => {
      oldFormStr.value = JSON.stringify(form.value);
    });
  }

  /* 预览数据 */
  function previewData() {
    proxy.$refs['analForm'].validate(valid => {
      if (isStrBlank(form.value.name)) {
        proxy.$modal.msgError(
          proxy.$t(
            'userCenter.edit.analysis.analysisSingle.validation.analysisNameRequired',
          ),
        );
        return;
      }
      if (valid) {
        // if (isStrBlank(form.value.name)) {
        //   proxy.$modal.msgError('Please input Analysis Name');
        //   return;
        // }
        if (isStrBlank(form.value.analysisType)) {
          proxy.$modal.msgError(
            proxy.$t(
              'userCenter.edit.analysis.analysisSingle.messages.analysisTypeRequired',
            ),
          );
          return;
        }
        if (
          form.value.analysisType === 'Other' &&
          isStrBlank(form.value.customAnalysisType)
        ) {
          proxy.$modal.msgError(
            proxy.$t(
              'userCenter.edit.analysis.analysisSingle.messages.otherAnalysisTypeRequired',
            ),
          );
          return;
        }
        if (batchValidateChinese(form.value.customAnalysisType)) {
          proxy.$modal.msgError(
            proxy.$t(
              'userCenter.edit.analysis.analysisSingle.messages.otherAnalysisTypeNoChinese',
            ),
          );
          return;
        }
        let targetValidFail = false;
        let targetContainsChinese = false;
        for (let item of form.value.target) {
          if (!isStrBlank(item.textNos) && batchValidateChinese(item.textNos)) {
            targetContainsChinese = true;
          }
          if (isArrEmpty(item.nos) && isStrBlank(item.textNos)) {
            targetValidFail = true;
            break;
          }
        }
        let otherTargetValidFail = false;
        for (let item of form.value.customTarget) {
          if (isStrBlank(item.name) && isStrBlank(item.link)) {
            otherTargetValidFail = true;
            break;
          }
        }
        if (targetValidFail && otherTargetValidFail) {
          proxy.$modal.msgError(
            proxy.$t(
              'userCenter.edit.analysis.analysisSingle.messages.targetRequired',
            ),
          );
          return;
        }
        if (targetContainsChinese) {
          proxy.$modal.msgError(
            proxy.$t(
              'userCenter.edit.analysis.analysisSingle.messages.targetNoChinese',
            ),
          );
          return;
        }
        let otherTargetRequiredFail = false;
        let otherTargetContainsChinese = false;
        if (!otherTargetValidFail) {
          for (let item of form.value.customTarget) {
            if (
              batchValidateChinese(item.name) ||
              batchValidateChinese(item.link)
            ) {
              otherTargetContainsChinese = true;
            }
            if (isStrBlank(item.name) || isStrBlank(item.link)) {
              otherTargetRequiredFail = true;
              break;
            }
          }
        }
        if (otherTargetRequiredFail) {
          proxy.$modal.msgError(
            proxy.$t(
              'userCenter.edit.analysis.analysisSingle.messages.otherTargetRequired',
            ),
          );
          return;
        }
        if (otherTargetContainsChinese) {
          proxy.$modal.msgError(
            proxy.$t(
              'userCenter.edit.analysis.analysisSingle.messages.otherTargetNoChinese',
            ),
          );
          return;
        }
        let pipelineVaildFail = false;
        let pipelineNameVaildFail = false;
        let pipelineContainsChinese = false;
        for (let item of form.value.pipeline) {
          if (isStrBlank(item.program)) {
            pipelineNameVaildFail = true;
          }
          if (
            batchValidateChinese(item.program) ||
            batchValidateChinese(item.link) ||
            batchValidateChinese(item.version) ||
            batchValidateChinese(item.note)
          ) {
            pipelineContainsChinese = true;
          }
          if (
            isStrBlank(item.program) &&
            isStrBlank(item.link) &&
            isStrBlank(item.version) &&
            isStrBlank(item.note) &&
            isArrEmpty(item.output)
          ) {
            pipelineVaildFail = true;
            break;
          }
        }
        if (pipelineVaildFail && form.value.pipeline.length > 1) {
          proxy.$modal.msgError(
            proxy.$t(
              'userCenter.edit.analysis.analysisSingle.messages.pipelineRequired',
            ),
          );
          return;
        }
        if (pipelineNameVaildFail && form.value.pipeline.length > 1) {
          proxy.$modal.msgError(
            proxy.$t(
              'userCenter.edit.analysis.analysisSingle.messages.pipelineNameRequired',
            ),
          );
          return;
        }
        if (pipelineContainsChinese) {
          proxy.$modal.msgError(
            proxy.$t(
              'userCenter.edit.analysis.analysisSingle.messages.pipelineNoChinese',
            ),
          );
          return;
        }

        submitData = deepClone(form.value);
        if (targetValidFail) {
          submitData.target = [];
        } else {
          submitData.target.forEach(item => {
            if (!isStrBlank(item.textNos)) {
              let strings = item.textNos.split('\n');
              // 把每一项两边trims,不为 '' 保留
              strings.forEach(it => {
                if (it.trim() !== '') {
                  item.nos.push(it.trim());
                }
              });
            }
          });
        }
        if (otherTargetValidFail) {
          submitData.customTarget = [];
        }
        if (pipelineVaildFail) {
          submitData.pipeline = [];
        } else {
          pipelinePreview.value = [];
          let no = 1;
          for (let item of form.value.pipeline) {
            if (
              !(
                isStrBlank(item.program) &&
                isStrBlank(item.link) &&
                isStrBlank(item.version) &&
                isStrBlank(item.note) &&
                isArrEmpty(item.output)
              )
            ) {
              let line = deepClone(item);
              line.no = no;
              pipelinePreview.value.push(line);
              no++;
            }
          }
        }

        // 弹出预览框
        previewDialog.value = true;
      }
    });
  }

  /** 提交数据 */
  const saveData = () => {
    proxy.$modal.loading(
      proxy.$t('userCenter.edit.analysis.analysisSingle.messages.saving'),
    );
    if (!subNo.value) {
      // 如果没有subNo代表用户没有保存过Submitter数据，自动保存
      bus.emit(BusEnum.SUBMIT_SUBMITTER, saveForm);
    } else {
      saveForm();
    }
  };

  function saveForm() {
    submitData.subNo = subNo.value;
    saveEditAnalysis(submitData)
      .then(response => {
        proxy.$modal.msgSuccess(
          proxy.$t(
            'userCenter.edit.analysis.analysisSingle.messages.saveSuccess',
          ),
        );
        form.value.analysisNo = response.data.analysisNo;
        stringifyFormData();
        previewDialog.value = false;
        proxy.$refs['archivingDialogRef'].showDialog(subNo.value);
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  /** 删除数据 */
  function deleteForm() {
    proxy.$modal
      .confirm(
        proxy.$t(
          'userCenter.edit.analysis.analysisSingle.messages.deleteConfirm',
        ),
      )
      .then(() => {
        let params = {
          subNo: subNo.value,
          single: true,
        };
        deleteAnalysis(params).then(response => {
          if (response.data) {
            proxy.$refs['deleteLog'].openLog(response.data);
            return;
          }
          proxy.$modal.msgSuccess(
            proxy.$t(
              'userCenter.edit.analysis.analysisSingle.messages.deleteSuccess',
            ),
          );
          resetForm();
          stringifyFormData();
        });
      })
      .catch(() => {});
  }
</script>

<style lang="scss" scoped>
  .project {
    .general-info {
      .el-form-item {
        width: 30%;
      }
    }

    .analysis-type {
      :deep(.el-radio) {
        width: 200px;
      }
    }

    .pipeline,
    .target {
      gap: 10px;

      .pipeline-item,
      .target-item {
        flex: 1;
        margin-right: 1rem;

        :deep(.el-radio__label) {
          font-size: 14px;
          font-weight: 700;
          color: #606266;
        }
      }

      .target-form {
        padding: 0 10px;

        .el-radio {
          width: 110px;
        }

        :deep(.el-form-item__label) {
          width: 27px !important;
          justify-content: flex-start;
        }
      }

      .pipeline-form {
        padding-top: 12px;

        :deep(.el-form-item__label) {
          min-width: 70px;
          justify-content: flex-start;
        }
      }

      .source-name {
        width: 31%;
      }
    }

    :deep(.el-form-item__label) {
      font-weight: 700;
    }

    :deep(.el-radio__label) {
      font-size: 16px;
    }

    :deep(.el-textarea__inner) {
      border-radius: 12px;
    }
  }

  .item {
    margin-top: 0.5rem;
    width: 100%;
    display: flex;

    .label {
      color: #666666;
      font-weight: 600;
      display: inline-block;
      min-width: 150px;
    }

    .el-tag:hover {
      cursor: pointer;
      background-color: #d9e2f6;
    }

    .bg-gray {
      width: 100%;
      padding: 4px 15px;
      border-radius: 6px;
    }

    &.pipeline {
      margin-top: 1rem;
    }

    :deep(.el-step__icon) {
      background-color: #d6e5ff;
      color: #5686dc;
      border: 2px solid #759fea;
    }

    :deep(.el-step__line) {
      background-color: #e8e8e8;
    }
  }

  .target-number {
    background-color: #d6e5ff;
    color: #3a78e8;
    border-radius: 50%;
    padding: 0 2px;
    border: 1px solid #3a78e8;
  }
</style>
