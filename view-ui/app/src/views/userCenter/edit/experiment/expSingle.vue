<template>
  <div class="project w-100">
    <div class="card general-info card-container mt-1 pt-0">
      <FillTip :recommend="true"></FillTip>
      <div class="category-title font-600 text-main-color">
        {{ $t('userCenter.edit.experiment.expSingle.generalInfo.title') }}
      </div>
      <div
        :key="'exp-general-info-' + componentKey"
        class="plr-20 bg-gray mt-1"
      >
        <el-form
          ref="expForm"
          label-position="top"
          :model="form"
          :inline="true"
          :scroll-to-error="true"
          :rules="rules"
          style="padding-top: 8px"
        >
          <el-form-item
            :label="
              $t(
                'userCenter.edit.experiment.expSingle.generalInfo.experimentId',
              )
            "
          >
            <el-input
              v-model="form.projectNo"
              disabled
              :placeholder="
                $t(
                  'userCenter.edit.experiment.expSingle.generalInfo.experimentIdPlaceholder',
                )
              "
            />
          </el-form-item>
          <el-form-item
            :label="
              $t(
                'userCenter.edit.experiment.expSingle.generalInfo.experimentName',
              )
            "
            prop="name"
          >
            <el-input v-model="form.name" />
          </el-form-item>
          <el-form-item
            :label="
              $t('userCenter.edit.experiment.expSingle.generalInfo.projectId')
            "
            prop="projectNo"
            class="w-75"
          >
            <el-select
              v-model="form.projectNo"
              class="w-100 m-2"
              :placeholder="
                $t(
                  'userCenter.edit.experiment.expSingle.generalInfo.projectPlaceholder',
                )
              "
              filterable
              :teleported="false"
            >
              <el-option
                v-for="item in existProjList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item
            v-if="hasDesc !== 'none'"
            class="w-100"
            :required="hasDesc === 'required'"
            :label="
              $t(
                'userCenter.edit.experiment.expSingle.generalInfo.sampleDescription',
              )
            "
            prop="description"
          >
            <template #label>
              <!--推荐填写-->
              <recommend-icon v-if="hasDesc === 'recommend'"></recommend-icon>

              <!--无字段描述信息-->
              <span class="font-bold">{{
                $t(
                  'userCenter.edit.experiment.expSingle.generalInfo.experimentDescription',
                )
              }}</span>
            </template>
            <el-input v-model="form.description" type="textarea" rows="5" />
          </el-form-item>

          <el-form-item
            v-if="hasProtocol !== 'none'"
            class="w-100"
            :required="hasProtocol === 'required'"
            prop="protocol"
          >
            <template #label>
              <!--推荐填写-->
              <recommend-icon
                v-if="hasProtocol === 'recommend'"
              ></recommend-icon>

              <!--无字段描述信息-->
              <span class="font-bold">{{
                $t(
                  'userCenter.edit.experiment.expSingle.generalInfo.experimentProtocol',
                )
              }}</span>
            </template>
            <el-input v-model="form.protocol" type="textarea" rows="5" />
          </el-form-item>

          <RelatedLinks
            :key="'exp-RelatedLinks' + componentKey"
            v-model:relatedLinks="form.relatedLinks"
          ></RelatedLinks>
        </el-form>
      </div>
      <div class="category-title font-600 text-main-color">
        {{ $t('userCenter.edit.experiment.expSingle.experimentInfo.title') }}
      </div>
      <div class="plr-20 new-content mt-1">
        <div class="w-100 exp-type">
          <div class="radius-12 follow-list">
            {{
              $t(
                'userCenter.edit.experiment.expSingle.experimentInfo.description',
                { email: '<EMAIL>' },
              )
            }}
          </div>
          <div class="d-flex flex-column justify-space-between mt-1">
            <p class="font-16 text-main-color font-600 mb-1">
              {{
                $t(
                  'userCenter.edit.experiment.expSingle.experimentInfo.experimentType',
                )
              }}
            </p>
            <el-radio-group
              v-model="form.expType"
              class="exp-radio-group"
              @change="changeExpType"
            >
              <el-radio
                v-for="item in expTypeList"
                :key="item.name"
                :label="item.name"
                >{{ item.name }}
              </el-radio>
            </el-radio-group>
          </div>
        </div>

        <ExpAttr
          :key="'attrExp' + attrComponentKey"
          ref="expAttrRef"
          v-model:attributes="form.attributes"
          v-model:recommend-filled-count="recommendFilledCount"
          :exp-type="form.expType"
        ></ExpAttr>
      </div>

      <Publication
        :key="'exp-Publication' + componentKey"
        v-model:publishData="form.publish"
      ></Publication>

      <div class="text-align-right mt-2 pr-20">
        <!--        <el-button
                  type="primary"
                  class="btn-primary btn btn-s btn-shadow"
                  round
                  @click="continueNext"
                  >Continue
                </el-button>-->
        <el-button
          type="primary"
          class="btn-primary btn btn-s btn-shadow"
          round
          @click="previewData"
          >{{ $t('userCenter.edit.experiment.expSingle.buttons.previewSave') }}
        </el-button>
        <el-button class="btn-primary btn btn-round" round @click="resetForm"
          >{{ $t('userCenter.edit.experiment.expSingle.buttons.reset') }}
        </el-button>
        <!--        <el-button
                  :disabled="!form.expNo"
                  type="danger"
                  class="btn"
                  plain
                  round
                  @click="deleteForm"
                  >Delete
                </el-button>-->
      </div>
    </div>

    <el-dialog
      v-model="previewDialog"
      :title="$t('userCenter.edit.experiment.expSingle.preview.title')"
      width="70%"
      class="preview-dialog radius-14"
    >
      <el-divider content-position="left"
        ><h3 class="preview-title">
          {{
            $t('userCenter.edit.experiment.expSingle.preview.experimentTitle')
          }}
        </h3></el-divider
      >
      <div class="d-flex preview">
        <div>
          <span class="title">{{
            $t('userCenter.edit.experiment.expSingle.preview.experimentId')
          }}</span>
          <span class="content" v-text="form.expNo"></span>
        </div>
        <div>
          <span class="title">{{
            $t('userCenter.edit.experiment.expSingle.preview.experimentType')
          }}</span>
          <span class="content">{{ form.expType }}</span>
        </div>
        <div>
          <span class="title">{{
            $t('userCenter.edit.experiment.expSingle.preview.experimentName')
          }}</span>
          <span class="content">{{ $text(form.name) }}</span>
        </div>
        <div>
          <span class="title">{{
            $t('userCenter.edit.experiment.expSingle.preview.projectId')
          }}</span>
          <span class="content">{{ projName }}</span>
        </div>
        <div>
          <span class="title">{{
            $t('userCenter.edit.experiment.expSingle.preview.description')
          }}</span>
          <span class="content">{{ $text(form.description) }}</span>
        </div>
        <div>
          <span class="title">{{
            $t(
              'userCenter.edit.experiment.expSingle.preview.experimentProtocol',
            )
          }}</span>
          <span class="content">{{ $text(form.protocol) }}</span>
        </div>
        <div>
          <span class="title">{{
            $t('userCenter.edit.experiment.expSingle.preview.relatedLinks')
          }}</span>
          <span
            v-if="!form.relatedLinks || form.relatedLinks.length === 0"
            class="content"
            >-</span
          >
          <div class="d-flex flex-column">
            <p
              v-for="(val, idx) in form.relatedLinks"
              :key="'review-relatedLinks-' + idx"
              class="content"
            >
              {{ $text(val) }}
            </p>
          </div>
        </div>
      </div>

      <PreviewAttribute
        v-if="previewDialog"
        :key="'PreviewAttribute-' + attrComponentKey"
        v-model:attr-data="form.attributes"
        :exp-type="form.expType"
      ></PreviewAttribute>

      <PreviewPublish :publish-data="form.publish"></PreviewPublish>

      <template #footer>
        <span class="dialog-footer">
          <div class="text-align-center">
            <el-button
              type="primary"
              class="btn-primary btn btn-s btn-shadow"
              round
              @click="saveData"
              >{{
                $t('userCenter.edit.experiment.expSingle.preview.save')
              }}</el-button
            >
            <el-button
              round
              class="btn-primary btn btn-round"
              @click="previewDialog = false"
              >{{
                $t('userCenter.edit.experiment.expSingle.preview.backEdit')
              }}</el-button
            >
          </div>
        </span>
      </template>
    </el-dialog>

    <ArchivingDialog
      ref="archivingDialogRef"
      :text-tip="
        $t('userCenter.edit.experiment.expSingle.messages.editingCompleted')
      "
    ></ArchivingDialog>
  </div>
</template>

<script setup>
  import {
    computed,
    getCurrentInstance,
    nextTick,
    onActivated,
    onMounted,
    reactive,
    ref,
    toRefs,
  } from 'vue';
  import { getProjectList } from '@/api/metadata/project';
  import { getExperimentType, getExpSapData } from '@/api/metadata/dict';
  import {
    deleteExperiment,
    getExpInfo,
    saveEditExp,
    validateExpName,
  } from '@/api/metadata/experiment';

  import { storeToRefs } from 'pinia';
  import { useRoute } from 'vue-router';
  import useSubmissionStore from '@/store/modules/metadata';
  import ExpAttr from '@/views/submit/metadata/rawData/experiment/ExpAttr.vue';
  import Publication from '@/views/submit/metadata/rawData/common/Publications.vue';
  import RelatedLinks from '@/views/submit/metadata/rawData/common/RelatedLinks.vue';
  import PreviewPublish from '@/views/submit/metadata/rawData/common/PreviewPublish.vue';
  import PreviewAttribute from '@/views/submit/metadata/rawData/experiment/PreviewAttribute.vue';
  import bus from '@/utils/bus';
  import { BusEnum } from '@/utils/enums';
  import FillTip from '@/views/submit/components/FillTip.vue';
  import RecommendIcon from '@/views/submit/metadata/rawData/common/RecommendIcon.vue';
  import ArchivingDialog from '@/views/submit/components/ArchivingDialog.vue';

  const route = useRoute();
  const { proxy } = getCurrentInstance();
  const submissionStore = useSubmissionStore();
  const { subNo, submission } = storeToRefs(submissionStore);
  const emit = defineEmits(['continueMessage']);

  const oldFormStr = ref('');
  const existProjList = ref([]); // 用户所有项目列表
  const expTypeList = ref([]); // 系统拥有的组学类型列表
  const componentKey = ref(1);
  const attrComponentKey = ref(1);
  const previewDialog = ref(false);

  /** 实验名称查重 */
  const checkExpName = (rule, value, callback) => {
    if (!value) {
      return callback(
        new Error(
          proxy.$t(
            'userCenter.edit.experiment.expSingle.validation.experimentNameRequired',
          ),
        ),
      );
    }
    // 如果没有选择projectNo就先不校验
    if (!form.value.projectNo) {
      callback();
      return;
    }
    validateExpName({
      expNo: form.value.expNo,
      projectNo: form.value.projectNo,
      name: form.value.name,
    })
      .then(response => {
        if (response && response.msg) {
          callback(new Error(response.msg));
        }
        callback();
      })
      .catch(error => callback(new Error(error)));
  };

  const data = reactive({
    form: {
      expNo: undefined,
      projectNo: undefined,
      name: '',
      description: undefined,
      protocol: undefined,
      expType: 'Genomic',
      attributes: {},
      relatedLinks: undefined,
      publish: {
        publication: undefined,
        doi: undefined,
        pmid: undefined,
        reference: undefined,
        articleName: undefined,
      },
    },
    rules: {
      projectNo: [
        {
          required: true,
          message: proxy.$t(
            'userCenter.edit.experiment.expSingle.validation.projectRequired',
          ),
          trigger: 'change',
        },
      ],
      name: [
        {
          required: true,
          validator: checkExpName,
          trigger: 'blur',
        },
        {
          validator: proxy.$validateChinese,
          trigger: 'blur',
        },
      ],
      description: [
        {
          validator: proxy.$validateChinese,
          trigger: 'blur',
        },
      ],
      protocol: [
        {
          validator: proxy.$validateChinese,
          trigger: 'blur',
        },
      ],
      relatedLinks: [
        {
          validator: proxy.$validateChinese,
          trigger: 'blur',
        },
      ],
    },
  });

  const { form, rules } = toRefs(data);

  let resultArr = reactive([]); // 存放子组件的数组
  let errListMsg = ref(''); // 用来存储错误提示

  // 创建Promise 实例，为多个组件校验使用
  const checkForm = formChild => {
    let result = new Promise((resolve, reject) => {
      formChild.validate((valid, fields) => {
        if (valid) {
          resolve();
        } else {
          Object.keys(fields).forEach((v, index) => {
            if (index === 0) {
              // 定位到错误的位置
              // const PropName = fields[v][0].field;
              // formChild.scrollToField(PropName);
              errListMsg.value = fields[v][0].message;
            }
          });
          reject();
        }
      });
    });
    resultArr.push(result);
  };

  let expAttrRef = ref();

  // 必须填写的推荐字段数量
  let mustRecommendNum = ref(0);
  // 自定义表单中填写了推荐字段数量
  let recommendFilledCount = ref(0);

  /** 预览数据 */
  function previewData() {
    let allRecommendFilledCount = recommendFilledCount.value;
    if (hasDesc.value === 'recommend' && form.value.description) {
      allRecommendFilledCount++;
    }
    if (hasProtocol.value === 'recommend' && form.value.protocol) {
      allRecommendFilledCount++;
    }
    if (allRecommendFilledCount < mustRecommendNum.value) {
      // 校验推荐填写的数量是否达标
      proxy.$modal.alertWarning(
        proxy.$t(
          'userCenter.edit.experiment.expSingle.messages.insufficientData',
        ),
      );
      return;
    }

    // 获取该子组件暴露出来的form的ref
    const approvalExpAttrRef = expAttrRef.value.expAttrForm;
    // 调用上面创建好的方法
    checkForm(approvalExpAttrRef);
    checkForm(proxy.$refs['expForm']);

    Promise.all(resultArr)
      .then(() => {
        // 校验通过
        previewDialog.value = true;
      })
      .catch(() => {
        // 校验不通过提示
        proxy.$modal.msgError(errListMsg.value);
        // console.error(errListMsg.value, err);
      });
    resultArr = []; // 每次请求完要清空数组
    errListMsg.value = ''; // 提示也需要清空
  }

  /** 提交数据 */
  const saveData = () => {
    proxy.$modal.loading(
      proxy.$t('userCenter.edit.experiment.expSingle.messages.saving'),
    );
    if (!subNo.value) {
      // 如果没有subNo代表用户没有保存过Submitter数据，自动保存
      bus.emit(BusEnum.SUBMIT_SUBMITTER, saveForm);
    } else {
      saveForm();
    }
  };

  /** 删除数据 */
  const deleteForm = () => {
    proxy.$modal
      .confirm(
        proxy.$t('userCenter.edit.experiment.expSingle.messages.deleteConfirm'),
      )
      .then(() => {
        const params = {
          subNo: subNo.value,
          single: true,
        };
        deleteExperiment(params).then(response => {
          if (response.data) {
            proxy.$refs['deleteLog'].openLog(response.data);
            return;
          }
          proxy.$modal.msgSuccess(
            proxy.$t(
              'userCenter.edit.experiment.expSingle.messages.deleteSuccess',
            ),
          );
          resetForm();
          stringifyFormData();
        });
      })
      .catch(() => {});
  };

  /** 保存表单 */
  const saveForm = () => {
    form.value.subNo = subNo.value;
    saveEditExp(form.value)
      .then(response => {
        form.value = response.data;

        proxy.$modal.msgSuccess(
          proxy.$t('userCenter.edit.experiment.expSingle.messages.saveSuccess'),
        );

        stringifyFormData();
        previewDialog.value = false;
        proxy.$refs['archivingDialogRef'].showDialog(subNo.value);
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  };

  /** 加载系统所拥有的组学类型 */
  function loadExperiment() {
    getExperimentType().then(response => {
      expTypeList.value = response.data;
      changeExpType();
    });
  }

  const hasDesc = ref('optional');
  const hasProtocol = ref('optional');

  /** 查询当前组学类型必填的推荐字段数量 */
  function changeExpType() {
    getExpSapData(form.value.expType).then(response => {
      mustRecommendNum.value = response.data.recommendNum;
      hasDesc.value = response.data.desc;
      hasProtocol.value = response.data.protocol;
    });
  }

  /** 加载用户项目列表 */
  function loadUserProject() {
    getProjectList().then(response => {
      existProjList.value = response.data;
    });
  }

  /** 初始化数据 */
  function initData() {
    const no = route.params.no;

    // 如果存在SUB NO则说明是编辑数据，从数据库回显
    if (no.indexOf('SUB') !== -1) {
      getExpInfo(submission.value.expSingleNo).then(response => {
        form.value = response.data;
        componentKey.value++;
        attrComponentKey.value++;
        loadExperiment();

        stringifyFormData();
      });
    } else if (no) {
      getExpInfo(no).then(response => {
        form.value = response.data;
        componentKey.value++;
        attrComponentKey.value++;
        loadExperiment();

        stringifyFormData();
      });
    } else {
      stringifyFormData();
      loadExperiment();
    }
  }

  /** 等所有的子组件全部渲染完成后序列化json */
  function stringifyFormData() {
    // 等所有的子组件全部渲染完成后序列化json
    nextTick().then(() => {
      oldFormStr.value = JSON.stringify(form.value);
    });
  }

  /** 重置表单 */
  const resetForm = () => {
    proxy.resetForm('expForm');
    // form.value.expNo = undefined;
    // form.value.publish = undefined;
    // form.value.relatedLinks = undefined;
    form.value.protocol = undefined;
    form.value.attributes = {};
    componentKey.value++;
    attrComponentKey.value++;
  };

  /** 继续 */
  function continueNext() {
    if (validateSaved()) {
      sendContinueMessage();
    } else {
      proxy.$modal
        .confirm(
          proxy.$t(
            'userCenter.edit.experiment.expSingle.messages.unsavedConfirm',
          ),
        )
        .then(function () {
          sendContinueMessage();
        })
        .catch(() => {});
    }
  }

  const sendContinueMessage = () => {
    emit('continueMessage', 'SampleSingle');
  };

  /** 校验表单数据是否被更改 */
  function validateSaved() {
    const currentFormStr = JSON.stringify(form.value);
    return currentFormStr === oldFormStr.value;
  }

  /** 用户选择的项目名 */
  const projName = computed(() => {
    const result = existProjList.value.find(
      item => item.value === form.value.projectNo,
    );
    return result ? result.label : null;
  });

  onMounted(() => {
    initData();
  });

  // 切换组件时，自动定位到顶部
  onActivated(() => {
    window.scrollTo(0, 0);
    loadUserProject();
  });
</script>

<style lang="scss" scoped>
  .project {
    .exp-radio-group {
      :deep(.el-radio) {
        width: 17%;
      }
    }

    :deep(.el-radio__label) {
      font-size: 16px;
    }

    .tips {
      font-size: 14px;
      margin-left: 1.3rem;
      //margin-top: .8rem;
    }

    .new-content {
      background-color: #fcfdfe;

      .exp-type {
        padding-bottom: 12px;
        border-bottom: 1px solid #e0e0e0;

        .follow-list {
          background-color: #f4f5f6;
          padding: 8px;
          border: 1px solid #d2d2d2;
        }
      }

      .el-form-item {
        width: 30%;

        .el-select {
          width: 100%;
        }
      }
    }

    .links {
      :deep(.el-form-item__label) {
        font-weight: 700;
      }

      :deep(.el-form-item__content) {
        flex-direction: column;
        align-items: flex-start;

        & + .el-form-item__label {
          font-weight: 700;
        }
      }
    }

    .exist-form {
      width: 100%;
      flex-wrap: wrap;

      .el-form-item {
        width: calc((100% - 100px) / 3) !important;
        margin-right: 10px;

        .el-select {
          width: 100%;
        }
      }
    }

    .general-info {
      .el-form {
        .el-form-item {
          width: 30%;

          .el-select-v2 {
            width: 100%;
          }
        }

        :deep(.el-form-item__label) {
          font-weight: 700;
        }
      }
    }

    :deep(.el-form-item__label) {
      font-weight: 700;
    }

    :deep(.el-select-v2__wrapper) {
      border-radius: 12px;
    }
  }
</style>
