<template>
  <div>
    <h3 class="text-main-color mt-05 mb-1">
      {{ $t('userCenter.personal.myShare.title') }}
    </h3>
    <div
      class="bg-primary mt-05 d-flex justify-space-between align-items-center hidden-xs-only"
    >
      <div class="bg-primary sort d-flex align-items-center">
        <span class="text-secondary-color font-600 mr-1"
          >{{ $t('userCenter.personal.myShare.filters.sort') }}:</span
        >
        <el-button
          v-for="(item, index) in sortBtn"
          :key="`shareSort-${item.field}`"
          plain
          :class="{ active: item.highlighted }"
          @click="toggleSortOrder(index)"
          >{{ item.label }}
          <el-icon v-if="item.highlighted">
            <Bottom v-if="item.sortOrder === 'descending'" />
            <Top v-else />
          </el-icon>
        </el-button>
      </div>

      <div class="d-flex align-items-center search">
        <span class="text-secondary-color font-600 mr-1"
          >{{ $t('userCenter.personal.myShare.filters.resourceType') }}:</span
        >
        <el-select
          v-model="resourceTypeVal"
          :multiple="false"
          clearable
          :placeholder="$t('userCenter.personal.myShare.filters.select')"
          style="width: 250px"
          @change="getDataList"
        >
          <el-option
            v-for="item in resourceType"
            :key="`shareType-${item.value}`"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
      <div class="d-flex align-items-center search">
        <span class="text-secondary-color font-600 mr-1"
          >{{ $t('userCenter.personal.myShare.filters.year') }}:</span
        >
        <el-select
          v-model="year"
          clearable
          :multiple="false"
          :placeholder="$t('userCenter.personal.myShare.filters.select')"
          style="width: 240px"
          @change="getDataList"
        >
          <el-option
            v-for="item in yearOpt"
            :key="`shareYear-${item.value}`"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
    </div>

    <div v-if="shareList && shareList.length !== 0" v-loading="loading">
      <div
        v-for="(share, index) in shareList"
        :key="`shareList-${index}`"
        class="mt-1"
      >
        <div class="d-flex align-items-center justify-space-between">
          <div class="d-flex align-items-center">
            <el-tag type="warning" round class="mr-1"
              >{{ $t('userCenter.personal.myShare.status.share') }}
            </el-tag>
            <el-tag
              v-if="!share.see"
              type="danger"
              effect="dark"
              round
              size="small"
              class="mr-05"
              >{{ $t('userCenter.personal.myShare.status.new') }}
            </el-tag>
            <span class="font-600 text-warning mr-1">
              {{ share.shareId }}
            </span>
            <el-tooltip
              placement="right"
              :width="20"
              trigger="hover"
              :content="$t('userCenter.personal.myShare.actions.dataList')"
            >
              <svg-icon
                icon-class="dataIdList"
                class-name="svg-idList"
                @click="expandDataList(index)"
              ></svg-icon>
            </el-tooltip>
            <el-tag
              v-if="share.status === 'sharing'"
              type="warning"
              round
              class="ml-2"
              effect="plain"
              >{{ $t('userCenter.personal.myShare.status.sharing') }}
            </el-tag>
            <el-tag
              v-if="share.status === 'cancled'"
              type="danger"
              round
              class="ml-2"
              effect="plain"
              >{{ $t('userCenter.personal.myShare.status.canceled') }}
            </el-tag>
          </div>
          <div class="font-600 text-main-color float-right">
            <el-button
              v-if="myData && share.status === 'sharing'"
              type="danger"
              round
              size="small"
              @click="setCancelShareId(share.id)"
            >
              <span class="font-600">{{
                $t('userCenter.personal.myShare.actions.cancel')
              }}</span>
            </el-button>

            <el-button
              round
              size="small"
              type="primary"
              @click="exportDataLink(share.shareId)"
            >
              <span class="font-600">{{
                $t('userCenter.personal.myShare.actions.exportDataLinks')
              }}</span>
            </el-button>
          </div>
        </div>

        <!--Data ID List-->
        <el-collapse-transition>
          <div
            v-if="shareList[index].expand"
            class="radius-12 bg-gray p-15 mt-05 list"
          >
            <el-row v-if="shareList[index]?.expandData?.projNos" :gutter="20">
              <el-col :span="5">
                <span class="text-secondary-color count-type font-600 mr-05">
                  {{ $t('userCenter.personal.myShare.counts.projectCounts') }}
                </span>
                <el-tag type="success" class="tag-success" size="small"
                  >{{ shareList[index]?.expandData?.projNos?.length }}
                </el-tag>
              </el-col>
              <el-col :span="19" class="d-flex row-gap-10 flex-wrap">
                <div
                  v-for="id in shareList[index]?.expandData?.projNos"
                  :key="'projNos-' + id"
                  class="id-list mr-1"
                >
                  <span class="btn-project">P</span>
                  <span>
                    <router-link :to="`/project/detail/${id}`">
                      {{ id }}
                    </router-link>
                  </span>
                </div>
              </el-col>
              <el-divider class="mg-divider"></el-divider>
            </el-row>

            <el-row v-if="shareList[index]?.expandData?.analNos" :gutter="20">
              <el-col :span="5">
                <span class="text-secondary-color count-type font-600 mr-05">
                  {{ $t('userCenter.personal.myShare.counts.analysisCounts') }}
                </span>
                <el-tag type="success" class="tag-success" size="small"
                  >{{ shareList[index]?.expandData?.analNos.length }}
                </el-tag>
              </el-col>
              <el-col :span="19" class="d-flex row-gap-10 flex-wrap">
                <div
                  v-for="id in shareList[index]?.expandData?.analNos"
                  :key="'analNos-' + id"
                  class="id-list mr-1"
                >
                  <span class="btn-project">A</span>
                  <span>
                    <router-link :to="`/analysis/detail/${id}`">
                      {{ id }}
                    </router-link>
                  </span>
                </div>
              </el-col>
              <el-divider class="mg-divider"></el-divider>
            </el-row>

            <el-row v-if="shareList[index]?.expandData?.expNos" :gutter="20">
              <el-col :span="5">
                <span class="text-secondary-color count-type font-600 mr-05">
                  {{
                    $t('userCenter.personal.myShare.counts.experimentCounts')
                  }}
                </span>
                <el-tag type="success" class="tag-success" size="small"
                  >{{ shareList[index]?.expandData?.expNos.length }}
                </el-tag>
              </el-col>
              <el-col :span="19" class="d-flex row-gap-10 flex-wrap">
                <div
                  v-for="id in shareList[index]?.expandData?.expNos"
                  :key="'expNos' + id"
                  class="id-list mr-1"
                >
                  <span class="btn-experiment">X</span>
                  <router-link :to="`/experiment/detail/${id}`">
                    {{ id }}
                  </router-link>
                </div>
              </el-col>
              <el-divider class="mg-divider"></el-divider>
            </el-row>

            <el-row v-if="shareList[index]?.expandData?.sapNos" :gutter="20">
              <el-col :span="5">
                <span class="text-secondary-color count-type font-600 mr-05">
                  {{ $t('userCenter.personal.myShare.counts.sampleCounts') }}
                </span>
                <el-tag type="success" class="tag-success" size="small"
                  >{{ shareList[index]?.expandData?.sapNos.length }}
                </el-tag>
              </el-col>
              <el-col :span="19" class="d-flex row-gap-10 flex-wrap">
                <div
                  v-for="id in shareList[index]?.expandData?.sapNos"
                  :key="'sapNos' + id"
                  class="id-list mr-1"
                >
                  <span class="btn-sample">S</span>
                  <router-link :to="`/sample/detail/${id}`">
                    {{ id }}
                  </router-link>
                </div>
              </el-col>
              <el-divider class="mg-divider"></el-divider>
            </el-row>

            <el-row v-if="shareList[index]?.expandData?.runNos" :gutter="20">
              <el-col :span="5">
                <span class="text-secondary-color count-type font-600 mr-05">
                  {{ $t('userCenter.personal.myShare.counts.runCounts') }}
                </span>
                <el-tag type="success" class="tag-success" size="small"
                  >{{ shareList[index]?.expandData?.runNos.length }}
                </el-tag>
              </el-col>
              <el-col :span="19" class="d-flex row-gap-10 flex-wrap">
                <div
                  v-for="id in shareList[index]?.expandData?.runNos"
                  :key="'runNos' + id"
                  class="id-list mr-1"
                >
                  <span class="btn-run">R</span>
                  <router-link :to="`/run/detail/${id}`">
                    {{ id }}
                  </router-link>
                </div>
              </el-col>
              <el-divider class="mg-divider"></el-divider>
            </el-row>

            <el-row v-if="shareList[index]?.expandData?.dataNos" :gutter="20">
              <el-col :span="5">
                <span class="text-secondary-color count-type font-600 mr-05">
                  {{ $t('userCenter.personal.myShare.counts.dataCounts') }}
                </span>
                <el-tag type="success" class="tag-success" size="small"
                  >{{ shareList[index]?.expandData?.dataNos.length }}
                </el-tag>
              </el-col>
              <el-col :span="19" class="d-flex row-gap-10 flex-wrap">
                <div
                  v-for="id in shareList[index]?.expandData?.dataNos"
                  :key="'dataNos' + id"
                  class="id-list mr-1"
                >
                  <span class="btn-data">D</span>
                  <span>{{ id }}</span>
                </div>
              </el-col>
              <el-divider class="mg-divider"></el-divider>
            </el-row>
          </div>
        </el-collapse-transition>

        <div class="radius-12 bg-gray mt-05 d-flex">
          <div class="d-flex align-items-center mr-2">
            <svg-icon icon-class="request-date" class-name="svg"></svg-icon>
            <span class="text-other-color mr-05 ml-05"
              >{{ $t('userCenter.personal.myShare.dates.shareDate') }}:</span
            >
            <span class="text-other-color">{{ share.shareDate }}</span>
          </div>
          <div class="d-flex align-items-center">
            <svg-icon icon-class="shareEmail" class-name="svg"></svg-icon>
            <template v-if="myData">
              <span class="text-other-color mr-05 ml-05"
                >{{
                  $t('userCenter.personal.myShare.info.shareToEmail')
                }}:</span
              >
              <span class="text-other-color">{{
                share.shareToEmails ? share.shareToEmails.join(', ') : ''
              }}</span>
            </template>
            <template v-else>
              <span class="text-other-color mr-05 ml-05"
                >{{
                  $t('userCenter.personal.myShare.info.shareFromEmail')
                }}:</span
              >
              <span class="text-other-color">{{ share.shareFromEmail }}</span>
            </template>
          </div>
        </div>
        <el-divider></el-divider>
      </div>

      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        class="mb-1"
        @pagination="getDataList"
      />
    </div>

    <div v-else v-loading="loading">
      <el-empty></el-empty>
    </div>

    <el-dialog
      v-model="cancelShareDialog"
      :title="$t('userCenter.personal.myShare.dialog.cancelConfirm')"
      width="400"
      center
      class="round-dialog"
    >
      <template #footer>
        <div>
          <el-button size="small" type="primary" @click="doCancelShare">
            {{ $t('userCenter.personal.myShare.dialog.confirm') }}
          </el-button>
          <el-button size="small" @click="cancelShareDialog = false"
            >{{ $t('userCenter.personal.myShare.dialog.cancel') }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { getCurrentInstance, onMounted, reactive, ref, toRefs } from 'vue';
  import {
    cancelShare,
    getMyShareList,
    getShareDataList,
  } from '@/api/app/share';
  import useUserStore from '@/store/modules/user';
  import { storeToRefs } from 'pinia';

  const userStore = useUserStore();
  const { member } = storeToRefs(userStore);

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    myData: {
      type: Boolean,
      required: true,
      default: true,
    },
  });

  const { myData } = props;

  const cancelShareDialog = ref(false);
  const cancelShareId = ref('');
  const sortBtn = reactive([
    {
      label: proxy.$t('userCenter.personal.myShare.sort.shareDate'),
      field: 'share_date',
      highlighted: true,
      sortOrder: 'descending',
    },
    {
      label: proxy.$t('userCenter.personal.myShare.sort.shareId'),
      field: 'share_id',
      highlighted: false,
      sortOrder: 'descending',
    },
    {
      label: proxy.$t('userCenter.personal.myShare.sort.status'),
      field: 'status',
      highlighted: false,
      sortOrder: 'descending',
    },
  ]);

  /** 响应式数据 */
  const data = reactive({
    total: 0,
    queryParams: {
      pageNum: 1,
      pageSize: 10,
    },
  });

  /** 解构 */
  const { total, queryParams } = toRefs(data);

  // 左上角排序
  const queryPageAndSort = ref({
    sortKey: sortBtn[0].field,
    sortType: sortBtn[0].sortOrder,
  });

  const loading = ref(false);
  const shareList = ref([]);

  const resourceTypeVal = ref([]);
  const resourceType = reactive([
    {
      value: 'project',
      label: proxy.$t('userCenter.personal.myShare.resourceTypes.project'),
    },
    {
      value: 'experiment',
      label: proxy.$t('userCenter.personal.myShare.resourceTypes.experiment'),
    },
    {
      value: 'sample',
      label: proxy.$t('userCenter.personal.myShare.resourceTypes.sample'),
    },
    {
      value: 'analysis',
      label: proxy.$t('userCenter.personal.myShare.resourceTypes.analysis'),
    },
  ]);
  const year = ref([]);
  const yearOpt = ref([]);

  const toggleSortOrder = index => {
    sortBtn.forEach(btn => {
      btn.highlighted = false;
    });
    sortBtn[index].highlighted = true;
    sortBtn[index].sortOrder =
      sortBtn[index].sortOrder === 'ascending' ? 'descending' : 'ascending';

    // 修改排序字段
    doSortSearch(index);
    getDataList();
  };
  const expandDataList = index => {
    let item = shareList.value[index];

    // 已经展开，点击则收起
    if (item?.expand) {
      item.expandData = [];
      item.expand = false;
      return;
    }
    getShareDataList({ id: item.id }).then(response => {
      item.expandData = response.data;
      item.expand = true;
      item.see = true;
    });
  };

  /** 导出页面中data的下载链接 */
  function exportDataLink(id) {
    let type = 'share';
    proxy.download(
      `/download/node/exportDownloadLink/${type}/${id}`,
      null,
      `${type}_${id}_data_download_link.zip`,
    );
  }

  function setCancelShareId(id) {
    if (myData) {
      cancelShareDialog.value = true;
      cancelShareId.value = id;
    }
  }

  function doCancelShare() {
    cancelShare({ id: cancelShareId.value }).then(() => {
      proxy.$modal.msgSuccess(
        proxy.$t('userCenter.personal.myShare.messages.cancelSuccess'),
      );
      cancelShareDialog.value = false;
      getDataList();
    });
  }

  function getRecentYears() {
    let currentYear = new Date().getFullYear();

    for (let i = 0; i < 3; i++) {
      yearOpt.value.push({ value: currentYear - i, label: currentYear - i });
    }

    const lastYear = yearOpt.value[yearOpt.value.length - 1].value;
    yearOpt.value.push({
      value: '<' + lastYear,
      label:
        proxy.$t('userCenter.personal.myShare.filters.before') + ' ' + lastYear,
    });
  }

  function getDataList() {
    const params = {
      sourceMember: member.value.id,
      sourceEmail: member.value.email,
      myRequest: myData,
      resourceType: resourceTypeVal.value,
      year: year.value,
      orderByColumn: queryPageAndSort.value.sortKey,
      isAsc: queryPageAndSort.value.sortType,
      pageNum: queryParams.value.pageNum,
      pageSize: queryParams.value.pageSize,
    };
    loading.value = true;
    getMyShareList(params)
      .then(response => {
        shareList.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  function doSortSearch(index) {
    queryPageAndSort.value.sortKey = sortBtn[index].field;
    queryPageAndSort.value.sortType = sortBtn[index].sortOrder;
  }

  onMounted(() => {
    getRecentYears();
    getDataList();
  });
</script>

<style lang="scss" scoped>
  .list {
    .count-type {
      display: inline-block;
      width: 129px;
    }
  }

  .bg-primary {
    background-color: #f5f8fe;

    .search {
      padding: 6px 10px;
    }
  }

  .tag-success {
    background-color: #cfefed !important;
    color: #07bcb4;
    font-weight: 600;
    border-radius: 8px;
  }

  :deep(.el-tag__content) {
    font-weight: 600;
  }

  :deep(.el-button--small.is-round) {
    padding: 5px 11px !important;
  }

  .svg {
    width: 16px;
    height: 16px;
  }

  .svg-idList {
    width: 13px;
    height: 13px;
    cursor: pointer;

    &:focus {
      outline: none;
    }
  }

  .bg-gray {
    padding: 6px 15px;
  }

  .cancle-share {
    width: 68px;
    background-color: #f4f4f5 !important;
    color: gray !important;
    border-color: gray !important;
  }

  .btn-round-warning:focus {
    background-color: #feeee4;
    color: #fe7f2b;
    border: 1px solid #fe7f2b;
  }

  .round-dialog {
    border-radius: 24px !important;
  }

  :deep(.round-dialog .el-dialog__body) {
    padding: 0 !important;
  }

  :deep(.el-select__wrapper) {
    border-radius: 12px;
  }

  @media (max-width: 767px) {
    .d-flex {
      flex-wrap: wrap;
    }
  }
</style>
