<template>
  <div class="submit-page">
    <div class="container-fluid">
      <Breadcrumb
        :bread-item="$t('userCenter.personal.reviewDetail.breadcrumb')"
      />
      <div v-loading="loading" class="card mt-1">
        <h3 class="text-main-color mb-0">
          {{ $t('userCenter.personal.reviewDetail.title') }}
        </h3>
        <el-result
          v-if="errorMsg"
          icon="error"
          :title="$t('userCenter.personal.reviewDetail.error.title')"
          :sub-title="errorMsg"
        >
          <template #extra>
            <div class="d-flex row-gap-10 flex-wrap">
              <div
                v-for="id in review?.projects"
                :key="'projects-' + id"
                class="id-list mr-1"
              >
                <span class="btn-project">P</span>
                <router-link :to="'/project/detail/' + id.projectNo">
                  {{ id.projectNo }}
                </router-link>
              </div>
              <div
                v-for="id in review?.experiments"
                :key="'experiments-' + id"
                class="id-list mr-1"
              >
                <span class="btn-experiment">E</span>
                <router-link :to="'/experiment/detail/' + id.expNo">
                  {{ id.expNo }}
                </router-link>
              </div>
              <div
                v-for="id in review?.samples"
                :key="'sample-' + id"
                class="id-list mr-1"
              >
                <span class="btn-sample">S</span>
                <router-link :to="'/sample/detail/' + id.sapNo">
                  {{ id.sapNo }}
                </router-link>
              </div>
              <div
                v-for="id in review?.analysis"
                :key="'analysis-' + id"
                class="id-list mr-1"
              >
                <span class="btn-project">A</span>
                <router-link :to="'/analysis/detail/' + id.analNo">
                  {{ id.analNo }}
                </router-link>
              </div>
            </div>
          </template>
        </el-result>
        <div v-for="(it, index) in reviewList" v-else :key="index" class="mt-1">
          <div class="d-flex align-items-center justify-space-between">
            <div class="d-flex align-items-center">
              <el-tag
                type="warning"
                round
                class="mr-05"
                :class="tagClass(it.type)"
                >{{ it?.type?.toUpperCase().substring(0, 4) }}</el-tag
              >
              <span class="font-600 text-warning name mr-05"
                ><router-link
                  :to="`/${it.type}/detail/${it.typeNo}`"
                  class="font-600"
                >
                  {{ it.name }}
                </router-link></span
              >
              <el-tooltip
                effect="light"
                placement="right"
                trigger="hover"
                :content="
                  it.expand
                    ? $t('userCenter.personal.reviewDetail.actions.collapse')
                    : $t('userCenter.personal.reviewDetail.actions.expand')
                "
              >
                <svg-icon
                  v-if="it.expand"
                  icon-class="collapse"
                  class-name="svg-icon"
                  @click="it.expand = !it.expand"
                ></svg-icon>
                <svg-icon
                  v-else
                  icon-class="expand"
                  class-name="svg-icon"
                  @click="it.expand = !it.expand"
                ></svg-icon>
              </el-tooltip>
            </div>
            <span class="font-600 text-main-color float-right"
              ><router-link
                :to="`/${it.type}/detail/${it.typeNo}`"
                class="font-600"
              >
                {{ it.typeNo }}
              </router-link></span
            >
          </div>

          <el-collapse-transition>
            <div
              v-if="it.expand && it?.childList"
              class="bg-gray radius-12 p-15 mt-05"
            >
              <div
                v-for="(child, index2) in it.childList"
                :key="'children' + index2"
              >
                <div
                  class="d-flex mb-05 align-items-center justify-space-between"
                >
                  <div class="d-flex align-items-center">
                    <el-tag
                      type="warning"
                      round
                      class="mr-05"
                      :class="tagClass(child.type)"
                      >{{ child.type.toUpperCase().substring(0, 4) }}</el-tag
                    >
                    <span class="font-600 text-warning"
                      ><router-link
                        :to="`/${child.type}/detail/${child.typeNo}`"
                        class="font-600"
                      >
                        {{ child.name }}
                      </router-link></span
                    >
                  </div>
                  <span class="font-600 text-main-color float-right"
                    ><router-link
                      class="font-600"
                      :to="`/${child.type}/detail/${child.typeNo}`"
                    >
                      {{ child.typeNo }}
                    </router-link></span
                  >
                </div>
                <div class="info radius-12 p-15 bg-white">
                  <div v-if="child.dataType">
                    <span class="title font-600 mr-1"
                      >{{
                        child.type.charAt(0).toUpperCase() + child.type.slice(1)
                      }}
                      Type</span
                    >
                    <span class="content">{{ child.dataType }}</span>
                  </div>
                  <div v-if="child.protocol" class="mt-05">
                    <span class="title font-600 mr-1">{{
                      $t('userCenter.personal.reviewDetail.labels.protocol')
                    }}</span>
                    <span class="content">{{ child.protocol }}</span>
                  </div>
                  <div v-if="child.description" class="mt-05 d-flex">
                    <span class="title font-600 mr-1">{{
                      $t('userCenter.personal.reviewDetail.labels.description')
                    }}</span>
                    <span class="content w-90">{{ child.description }}</span>
                  </div>
                  <div
                    v-if="child.relatedLinks && child.relatedLinks.length !== 0"
                    class="mt-05 d-flex"
                  >
                    <span class="title font-600 mr-1">{{
                      $t('userCenter.personal.reviewDetail.labels.relatedLinks')
                    }}</span>
                    <span class="content"
                      ><div
                        v-for="(link, idx) in child.relatedLinks"
                        :key="'link-' + idx"
                      >
                        <a :href="link" target="_blank" class="text-primary">
                          {{ link }}</a
                        >
                      </div></span
                    >
                  </div>
                  <el-divider></el-divider>
                  <div class="mt-05 d-flex">
                    <span class="title font-600 mr-1" style="width: 340px"
                      >{{
                        $t('userCenter.personal.reviewDetail.labels.dataIdList')
                      }}
                    </span>
                    <div
                      class="data-id-list-container d-flex row-gap-10 flex-wrap"
                    >
                      <div
                        v-for="(data, idx) in child.dataList"
                        :key="'dataList' + idx"
                      >
                        <el-popover
                          :width="400"
                          trigger="hover"
                          content="this is content, this is content, this is content"
                        >
                          <template #reference>
                            <div class="id-list mr-1 cursor-pointer">
                              <span class="btn-data">D</span>
                              <span>{{ data.datNo }}</span>
                            </div>
                          </template>
                          <div>
                            <div v-if="data.expNo" class="id-info text-primary">
                              <span>{{
                                $t(
                                  'userCenter.personal.reviewDetail.popover.experiment',
                                )
                              }}</span>
                              <router-link
                                :to="'/experiment/detail/' + data.expNo"
                              >
                                {{ data.expNo }}
                              </router-link>
                            </div>
                            <div v-if="data.sapNo" class="id-info text-primary">
                              <span>{{
                                $t(
                                  'userCenter.personal.reviewDetail.popover.sample',
                                )
                              }}</span>
                              <router-link :to="'/sample/detail/' + data.sapNo">
                                {{ data.sapNo }}
                              </router-link>
                            </div>
                            <div v-if="data.runNo" class="id-info">
                              <span>{{
                                $t(
                                  'userCenter.personal.reviewDetail.popover.run',
                                )
                              }}</span>
                              <router-link :to="'/run/detail/' + data.runNo">
                                {{ data.runNo }}
                              </router-link>
                            </div>
                            <div v-if="data.analNo" class="id-info">
                              <span>{{
                                $t(
                                  'userCenter.personal.reviewDetail.popover.analysis',
                                )
                              }}</span>
                              <router-link
                                :to="'/analysis/detail/' + data.analNo"
                              >
                                {{ data.analNo }}
                              </router-link>
                            </div>
                            <div v-if="data.datNo" class="id-info">
                              <span>{{
                                $t(
                                  'userCenter.personal.reviewDetail.popover.data',
                                )
                              }}</span>
                              <span>{{ data.datNo }}</span>
                            </div>
                            <div v-if="data.fileName" class="id-info">
                              <span>{{
                                $t(
                                  'userCenter.personal.reviewDetail.popover.fileName',
                                )
                              }}</span>
                              <span>{{ data.fileName }}</span>
                            </div>
                            <div v-if="data.link" class="id-info">
                              <span>{{
                                $t(
                                  'userCenter.personal.reviewDetail.popover.dataLinks',
                                )
                              }}</span>
                              <span>
                                <a
                                  :href="data.link"
                                  target="_blank"
                                  class="text-primary"
                                >
                                  {{ data.link }}</a
                                >
                              </span>
                            </div>
                          </div>
                        </el-popover>
                      </div>
                    </div>
                  </div>
                </div>
                <el-divider
                  v-if="index !== it.childList.length - 1"
                ></el-divider>
              </div>
            </div>
          </el-collapse-transition>

          <el-collapse-transition>
            <div
              v-if="it.expand && it.dataList"
              class="bg-gray radius-12 p-15 mt-05"
            >
              <div class="info radius-12 p-15 bg-white">
                <div class="mt-05 d-flex">
                  <span class="title font-600 mr-1"
                    >{{
                      $t('userCenter.personal.reviewDetail.labels.dataIdList')
                    }}
                  </span>
                  <div
                    class="data-id-list-container d-flex row-gap-10 flex-wrap"
                  >
                    <div
                      v-for="(data, idx) in it.dataList"
                      :key="'dataList' + idx"
                    >
                      <el-popover
                        :width="400"
                        trigger="hover"
                        content="this is content, this is content, this is content"
                      >
                        <template #reference>
                          <div class="id-list mr-1 cursor-pointer">
                            <span class="btn-data">D</span>
                            <span>{{ data.datNo }}</span>
                          </div>
                        </template>
                        <div>
                          <div v-if="data.expNo" class="id-info text-primary">
                            <span>{{
                              $t(
                                'userCenter.personal.reviewDetail.popover.experiment',
                              )
                            }}</span>
                            <router-link
                              :to="'/experiment/detail/' + data.expNo"
                            >
                              {{ data.expNo }}
                            </router-link>
                          </div>
                          <div v-if="data.sapNo" class="id-info text-primary">
                            <span>{{
                              $t(
                                'userCenter.personal.reviewDetail.popover.sample',
                              )
                            }}</span>
                            <router-link :to="'/sample/detail/' + data.sapNo">
                              {{ data.sapNo }}
                            </router-link>
                          </div>
                          <div v-if="data.runNo" class="id-info">
                            <span>{{
                              $t('userCenter.personal.reviewDetail.popover.run')
                            }}</span>
                            <router-link :to="'/run/detail/' + data.runNo">
                              {{ data.runNo }}
                            </router-link>
                          </div>
                          <div v-if="data.analNo" class="id-info">
                            <span>{{
                              $t(
                                'userCenter.personal.reviewDetail.popover.analysis',
                              )
                            }}</span>
                            <router-link
                              :to="'/analysis/detail/' + data.analNo"
                            >
                              {{ data.analNo }}
                            </router-link>
                          </div>
                          <div v-if="data.datNo" class="id-info">
                            <span>{{
                              $t(
                                'userCenter.personal.reviewDetail.popover.data',
                              )
                            }}</span>
                            <span>{{ data.datNo }}</span>
                          </div>
                          <div v-if="data.fileName" class="id-info">
                            <span>{{
                              $t(
                                'userCenter.personal.reviewDetail.popover.fileName',
                              )
                            }}</span>
                            <span>{{ data.fileName }}</span>
                          </div>
                          <div v-if="data.link" class="id-info">
                            <span>{{
                              $t(
                                'userCenter.personal.reviewDetail.popover.dataLinks',
                              )
                            }}</span>
                            <span>
                              <a
                                :href="data.link"
                                target="_blank"
                                class="text-primary"
                              >
                                {{ data.link }}</a
                              >
                            </span>
                          </div>
                        </div>
                      </el-popover>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-collapse-transition>

          <div class="info radius-12 bg-gray p-15 mt-05">
            <div v-if="it.dataType">
              <span class="title font-600 mr-1"
                >{{
                  it.type.charAt(0).toUpperCase() + it.type.slice(1)
                }}
                Type</span
              >
              <span class="content">{{ it.dataType }}</span>
            </div>
            <div v-if="it.protocol" class="mt-05">
              <span class="title font-600 mr-1">{{
                $t('userCenter.personal.reviewDetail.labels.protocol')
              }}</span>
              <span class="content">{{ it.protocol }}</span>
            </div>
            <div v-if="it.description" class="mt-05 d-flex">
              <span class="title font-600 mr-1">{{
                $t('userCenter.personal.reviewDetail.labels.description')
              }}</span>
              <span class="content w-90">{{ it.description }}</span>
            </div>
            <div
              v-if="it.relatedLinks && it.relatedLinks.length !== 0"
              class="mt-05 d-flex"
            >
              <span class="title font-600 mr-1">{{
                $t('userCenter.personal.reviewDetail.labels.relatedLinks')
              }}</span>
              <span class="content"
                ><div
                  v-for="(link, idx) in it.relatedLinks"
                  :key="'link2-' + idx"
                >
                  <a :href="link" target="_blank" class="text-primary">
                    {{ link }}</a
                  >
                </div></span
              >
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import Breadcrumb from '@/components/breadcrumb.vue';
  import { getCurrentInstance, onMounted, ref } from 'vue';
  import { useRoute } from 'vue-router';
  import { getDetailData } from '@/api/app/review';

  const { proxy } = getCurrentInstance();

  const route = useRoute();

  const reviewNo = ref('');
  const code = ref('');

  onMounted(() => {
    code.value = route.query.code;
    reviewNo.value = route.params.reviewNo;

    getData();
  });

  const reviewList = ref([]);
  const review = ref({});
  const errorMsg = ref('');

  const loading = ref(false);

  // 查询外层的请求列表
  function getData() {
    const params = {
      code: code.value,
      reviewNo: reviewNo.value,
    };
    loading.value = true;
    getDetailData(params)
      .then(response => {
        if (response.code === 700) {
          review.value = response.data;
          errorMsg.value = response.msg;
        } else {
          reviewList.value = response.data;
        }
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const tagClass = id => {
    if (!id) {
      return 'tag-proj';
    }
    if (id.includes('project')) {
      return 'tag-proj';
    } else if (id.includes('experiment')) {
      return 'tag-expr';
    } else if (id.includes('sample')) {
      return 'tag-samp';
    } else {
      return 'tag-anal';
    }
  };
</script>

<style lang="scss" scoped>
  :deep(.el-collapse-item__header) {
    padding: 15px 20px;
  }
  :deep(.el-collapse-item__wrap) {
    padding: 0 20px;
  }
  .el-tag {
    font-size: 14px;
    border: none;
    :deep(.el-tag__content) {
      font-weight: 600;
    }
  }
  .info {
    .title {
      display: inline-block;
      color: #333333;
      font-weight: 600;
      margin-right: 20px;
      width: 120px;
    }
    .content {
      color: #606266;
      white-space: normal; /* 允许内容自动换行 */
      word-break: break-all; /* 在单词内部换行 */
    }
  }
  .list {
    gap: 15px;
    .item {
      min-width: 30%;
      margin-right: 1rem;
    }
  }
  .svg-icon {
    cursor: pointer;
    width: 15px;
    height: 15px;
    &:focus {
      outline: none;
    }
  }
  .id-info {
    &:last-child {
      display: flex;
    }
    & > span:first-child {
      display: inline-block;
      width: 100px;
      color: #104eb1;
      font-weight: 600;
    }
    & > span > a {
      display: inline-block;
      width: 300px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      color: #104eb1;
    }
  }

  .data-id-list-container {
    max-height: 300px;
    overflow-y: auto;
    padding-right: 10px;
    flex-wrap: wrap;
    align-content: flex-start;
  }
</style>
