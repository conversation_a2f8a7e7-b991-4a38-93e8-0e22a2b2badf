<template>
  <div>
    <h3 class="text-main-color mt-05 mb-1">
      {{ $t('userCenter.personal.shareFromOther.title') }}
    </h3>
    <div
      class="bg-primary mt-05 d-flex justify-space-between align-items-center"
    >
      <div class="bg-primary sort d-flex align-items-center">
        <span class="text-secondary-color font-600 mr-1"
          >{{ $t('userCenter.personal.shareFromOther.filters.sort') }}:</span
        >
        <el-button
          v-for="(item, index) in sortBtn"
          :key="item"
          plain
          :class="{ active: item.highlighted }"
          @click="toggleSortOrder(index)"
          >{{ item.label }}
          <el-icon v-if="item.highlighted">
            <Bottom v-if="item.sortOrder === 'desc'" />
            <Top v-else />
          </el-icon>
        </el-button>
      </div>

      <div class="d-flex align-items-center search">
        <span class="text-secondary-color font-600 mr-1"
          >{{
            $t('userCenter.personal.shareFromOther.filters.resourceType')
          }}:</span
        >
        <el-select
          v-model="resourceTypeVal"
          multiple
          :placeholder="$t('userCenter.personal.shareFromOther.filters.select')"
          style="width: 240px"
        >
          <el-option
            v-for="item in resourceType"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
      <div class="d-flex align-items-center search">
        <span class="text-secondary-color font-600 mr-1"
          >{{ $t('userCenter.personal.shareFromOther.filters.year') }}:</span
        >
        <el-select
          v-model="year"
          multiple
          :placeholder="$t('userCenter.personal.shareFromOther.filters.select')"
          style="width: 240px"
        >
          <el-option
            v-for="item in yearOpt"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
    </div>
    <div v-for="(share, index) in shareFromList" :key="index" class="mt-1">
      <div class="d-flex align-items-center justify-space-between">
        <div class="d-flex align-items-center">
          <el-tag type="warning" round class="mr-1"
            >{{ $t('userCenter.personal.shareFromOther.status.share') }}
          </el-tag>
          <span class="font-600 text-warning mr-1"> {{ share.id }} </span>
          <el-tooltip
            placement="right"
            :width="20"
            trigger="hover"
            :content="
              $t('userCenter.personal.shareFromOther.actions.dataIdList')
            "
          >
            <svg-icon
              icon-class="dataIdList"
              class-name="svg-idList"
              @click="expandDataList(index)"
            ></svg-icon>
          </el-tooltip>
        </div>
        <div class="font-600 text-main-color float-right">
          <el-button class="btn-round-primary" round size="small"
            >{{
              $t('userCenter.personal.shareFromOther.actions.exportDataLinks')
            }}
          </el-button>
          <el-button class="btn-round-warning" size="small" round
            >{{ $t('userCenter.personal.shareFromOther.status.sharing') }}
          </el-button>
        </div>
      </div>
      <!--data id list-->
      <el-collapse-transition>
        <DataList :data="share"></DataList>
      </el-collapse-transition>
      <div class="radius-12 bg-gray mt-05 d-flex">
        <div class="d-flex align-items-center mr-2">
          <svg-icon icon-class="request-date" class-name="svg"></svg-icon>
          <span class="text-other-color mr-05 ml-05"
            >{{
              $t('userCenter.personal.shareFromOther.dates.shareDate')
            }}:</span
          >
          <span class="text-other-color">{{ share.shareDate }}</span>
        </div>
        <div class="d-flex align-items-center">
          <svg-icon icon-class="shareEmail" class-name="svg"></svg-icon>
          <span class="text-other-color mr-05 ml-05"
            >{{
              $t('userCenter.personal.shareFromOther.info.shareEmail')
            }}:</span
          >
          <span class="text-other-color">{{ share.shareToEmail }}</span>
        </div>
      </div>
      <el-divider></el-divider>
    </div>
  </div>
</template>

<script setup>
  import DataList from '@/views/userCenter/personal/components/dataList.vue';

  import { getCurrentInstance, reactive, ref } from 'vue';

  const { proxy } = getCurrentInstance();
  const sortBtn = reactive([
    {
      label: proxy.$t('userCenter.personal.shareFromOther.sort.shareId'),
      highlighted: false,
      sortOrder: 'desc',
    },
    {
      label: proxy.$t('userCenter.personal.shareFromOther.sort.shareDate'),
      highlighted: false,
      sortOrder: 'desc',
    },
    {
      label: proxy.$t('userCenter.personal.shareFromOther.sort.status'),
      highlighted: false,
      sortOrder: 'desc',
    },
  ]);
  const resourceTypeVal = ref([]);
  const resourceType = reactive([
    {
      value: 'Project',
      label: proxy.$t(
        'userCenter.personal.shareFromOther.resourceTypes.project',
      ),
    },
    {
      value: 'Experiment',
      label: proxy.$t(
        'userCenter.personal.shareFromOther.resourceTypes.experiment',
      ),
    },
    {
      value: 'Sample',
      label: proxy.$t(
        'userCenter.personal.shareFromOther.resourceTypes.sample',
      ),
    },
    {
      value: 'Analysis',
      label: proxy.$t(
        'userCenter.personal.shareFromOther.resourceTypes.analysis',
      ),
    },
  ]);
  const year = ref([]);
  const yearOpt = ref([
    {
      value: '2022',
      label: '2022',
    },
    {
      value: '2023',
      label: '2023',
    },
    {
      value: '2024',
      label: '2024',
    },
  ]);

  const shareFromList = reactive([
    {
      id: 'OEH000471',
      shareDate: '2024.2.27',
      shareToEmail: '<EMAIL>',
      expand: false,
      projectCounts: ['OEP000001'],
      projName: 'Global deep-sea sediment microbiomes',
      des: 'In this study, we collected CSF samples from 152 pediatric patients from 2020 to 2022. By comparing the transcriptomic landscape of AE patients with that of IE patients including viral and bacterial infections, we found gene expression signatures specific to each type of encephalopathy. Finally, we identified markers of AE at the gene expression levels and validated in an additional cohort, thus highlighting the important value of mNGS in the clinical diagnosis of AE.',
    },
    {
      id: 'OEH000472',
      shareDate: '2024.2.27',
      shareToEmail: '<EMAIL>',
      expand: false,
      experimentCounts: ['OEX000001'],
      sampleCounts: [
        'OES000001',
        'OES000002',
        'OES000003',
        'OES000004',
        'OES000005',
      ],
      runCounts: [
        'OER000001',
        'OER000002',
        'OER000003',
        'OER000004',
        'OER000005',
      ],
      dataCounts: [
        'OED000001',
        'OED000002',
        'OED000003',
        'OED000004',
        'OED000005',
        'OED000006',
      ],
    },
    {
      id: 'OEH000474',
      shareDate: '2024.2.27',
      shareToEmail: '<EMAIL>',
      expand: false,
      experimentCounts: ['OEX000001', 'OEX000002'],
      sampleCounts: [
        'OES000001',
        'OES000002',
        'OES000003',
        'OES000004',
        'OES000005',
      ],
      runCounts: [
        'OER000001',
        'OER000002',
        'OER000003',
        'OER000004',
        'OER000005',
      ],
    },
    {
      id: 'OEH000475',
      shareDate: '2024.2.27',
      shareToEmail: '<EMAIL>',
      expand: false,
      experimentCounts: ['OEX000001', 'OEX000002'],
      sampleCounts: ['OES000001', 'OES000002'],
    },
  ]);

  const toggleSortOrder = index => {
    sortBtn.forEach(btn => {
      btn.highlighted = false;
    });
    sortBtn[index].highlighted = true;
    sortBtn[index].sortOrder =
      sortBtn[index].sortOrder === 'asc' ? 'desc' : 'asc';
  };
  const expandDataList = index => {
    shareFromList[index].expand = !shareFromList[index].expand;
  };
</script>

<style lang="scss" scoped>
  .bg-primary {
    background-color: #f5f8fe;
    .search {
      padding: 6px 10px;
    }
  }
  .tag-success {
    background-color: #cfefed !important;
    color: #07bcb4;
    font-weight: 600;
    border-radius: 8px;
  }
  :deep(.el-tag__content) {
    font-weight: 600;
  }
  :deep(.el-button--small.is-round) {
    padding: 5px 11px !important;
  }
  .svg {
    width: 16px;
    height: 16px;
  }
  .svg-idList {
    width: 13px;
    height: 13px;
    cursor: pointer;
    &:focus {
      outline: none;
    }
  }
  .bg-gray {
    padding: 6px 15px;
  }
  .cancle-share {
    width: 68px;
    background-color: #f4f4f5 !important;
    color: gray !important;
    border-color: gray !important;
  }

  .btn-round-warning:focus {
    background-color: #feeee4;
    color: #fe7f2b;
    border: 1px solid #fe7f2b;
  }
  .round-dialog {
    border-radius: 24px !important;
  }
  :deep(.round-dialog .el-dialog__body) {
    padding: 0 !important;
  }
  :deep(.el-select__wrapper) {
    border-radius: 12px;
  }
</style>
