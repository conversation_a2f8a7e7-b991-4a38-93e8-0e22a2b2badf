<template>
  <div>
    <h3 class="text-main-color mt-05 mb-1">
      {{ $t('userCenter.personal.myReview.title') }}
    </h3>

    <div
      class="bg-primary d-flex d-flex-default justify-space-between align-items-center sort hidden-xs-only"
    >
      <div>
        <span class="text-secondary-color font-600 mr-1"
          >{{ $t('userCenter.personal.myReview.filters.sort') }}:</span
        >
        <el-button
          v-for="(item, index) in sortBtn"
          :key="item"
          plain
          class="sort-button"
          :class="{ active: item.highlighted }"
          @click="toggleSortOrder(index)"
          >{{ item.label }}
          <el-icon v-if="item.highlighted">
            <Bottom v-if="item.sortOrder === 'descending'" />
            <Top v-else />
          </el-icon>
        </el-button>
      </div>
      <div class="d-flex align-items-center search">
        <span class="text-secondary-color font-600 mr-1"
          >{{ $t('userCenter.personal.myReview.filters.resourceType') }}:</span
        >
        <el-select
          v-model="resourceTypeVal"
          :placeholder="$t('userCenter.personal.myReview.filters.select')"
          clearable
          style="width: 250px"
          @change="getDataList"
        >
          <el-option
            v-for="item in resourceType"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
      <div class="d-flex align-items-center search">
        <span class="text-secondary-color font-600 mr-1"
          >{{ $t('userCenter.personal.myReview.filters.year') }}:</span
        >
        <el-select
          v-model="year"
          clearable
          :placeholder="$t('userCenter.personal.myReview.filters.select')"
          style="width: 250px"
          @change="getDataList"
        >
          <el-option
            v-for="item in yearOpt"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
    </div>

    <div
      v-if="reviewList && reviewList.length !== 0"
      v-loading="loading"
      class="mt-1"
    >
      <div v-for="(item, index) in reviewList" :key="'reviewList-' + index">
        <div class="d-flex align-items-center justify-space-between">
          <div class="d-flex align-items-center">
            <el-tag type="warning" round class="mr-05"
              >{{ $t('userCenter.personal.myReview.status.review') }}
            </el-tag>
            <el-tag
              v-if="item.see === 'No'"
              type="danger"
              effect="dark"
              round
              size="small"
              class="mr-05"
              >{{ $t('userCenter.personal.myReview.status.new') }}
            </el-tag>
            <span class="font-600 text-warning mr-05">
              {{ item.reviewId }}
            </span>
            <el-tooltip
              :width="20"
              placement="right"
              trigger="hover"
              :content="$t('userCenter.personal.myReview.actions.dataIdList')"
            >
              <svg-icon
                icon-class="dataIdList"
                class-name="svg-idList"
                @click="expandDataList(index)"
              ></svg-icon>
            </el-tooltip>
            <el-tag
              v-if="item.status === 'reviewing'"
              type="warning"
              round
              class="ml-2"
              effect="plain"
              >{{ $t('userCenter.personal.myReview.status.reviewing') }}
            </el-tag>
            <el-tag
              v-if="item.status === 'cancled'"
              type="danger"
              round
              class="ml-2"
              effect="plain"
              >{{ $t('userCenter.personal.myReview.status.canceled') }}
            </el-tag>
          </div>

          <div class="font-600 text-main-color float-right d-flex-default">
            <el-button
              v-if="item.status === 'reviewing'"
              type="danger"
              round
              size="small"
              @click="openCancel(item.id)"
            >
              <span class="font-600">{{
                $t('userCenter.personal.myReview.actions.cancel')
              }}</span>
            </el-button>

            <el-button
              v-if="item.status === 'reviewing'"
              type="warning"
              round
              size="small"
              @click="openExtendDate(item.id)"
            >
              <span class="font-600">{{
                $t('userCenter.personal.myReview.actions.extension')
              }}</span>
            </el-button>

            <el-button
              round
              size="small"
              type="primary"
              @click="exportDataLink(item.reviewId)"
            >
              <span class="font-600">{{
                $t('userCenter.personal.myReview.actions.exportDataLinks')
              }}</span>
            </el-button>
          </div>
        </div>
        <!--data id list-->
        <el-collapse-transition>
          <div
            v-if="reviewList[index].expand"
            class="radius-12 bg-gray p-15 mt-05 list"
          >
            <el-row v-if="reviewList[index]?.expandData?.projNos" :gutter="20">
              <el-col :span="5">
                <span class="text-secondary-color count-type font-600 mr-05">
                  {{ $t('userCenter.personal.myReview.counts.projectCounts') }}
                </span>
                <el-tag type="success" class="tag-success" size="small">{{
                  reviewList[index]?.expandData?.projNos?.length
                }}</el-tag>
              </el-col>
              <el-col :span="19" class="d-flex row-gap-10 flex-wrap">
                <div
                  v-for="id in reviewList[index]?.expandData?.projNos"
                  :key="'projNos-' + id"
                  class="id-list mr-1"
                >
                  <span class="btn-project">P</span>
                  <span>
                    <router-link :to="`/project/detail/${id}`">
                      {{ id }}
                    </router-link>
                  </span>
                </div>
              </el-col>
              <el-divider class="mg-divider"></el-divider>
            </el-row>
            <el-row v-if="reviewList[index]?.expandData?.analNos" :gutter="20">
              <el-col :span="5">
                <span class="text-secondary-color count-type font-600 mr-05">
                  {{ $t('userCenter.personal.myReview.counts.analysisCounts') }}
                </span>
                <el-tag type="success" class="tag-success" size="small">{{
                  reviewList[index]?.expandData?.analNos.length
                }}</el-tag>
              </el-col>
              <el-col :span="19" class="d-flex row-gap-10 flex-wrap">
                <div
                  v-for="id in reviewList[index]?.expandData?.analNos"
                  :key="'analNos-' + id"
                  class="id-list mr-1"
                >
                  <span class="btn-project">A</span>
                  <span>
                    <router-link :to="`/analysis/detail/${id}`">
                      {{ id }}
                    </router-link>
                  </span>
                </div>
              </el-col>
              <el-divider class="mg-divider"></el-divider>
            </el-row>
            <el-row v-if="reviewList[index]?.expandData?.expNos" :gutter="20">
              <el-col :span="5">
                <span class="text-secondary-color count-type font-600 mr-05">
                  {{
                    $t('userCenter.personal.myReview.counts.experimentCounts')
                  }}
                </span>
                <el-tag type="success" class="tag-success" size="small">{{
                  reviewList[index]?.expandData?.expNos.length
                }}</el-tag>
              </el-col>
              <el-col :span="19" class="d-flex row-gap-10 flex-wrap">
                <div
                  v-for="id in reviewList[index]?.expandData?.expNos"
                  :key="'expNos' + id"
                  class="id-list mr-1"
                >
                  <span class="btn-experiment">X</span>
                  <router-link :to="`/experiment/detail/${id}`">
                    {{ id }}
                  </router-link>
                </div>
              </el-col>
              <el-divider class="mg-divider"></el-divider>
            </el-row>
            <el-row v-if="reviewList[index]?.expandData?.sapNos" :gutter="20">
              <el-col :span="5">
                <span class="text-secondary-color count-type font-600 mr-05">
                  {{ $t('userCenter.personal.myReview.counts.sampleCounts') }}
                </span>
                <el-tag type="success" class="tag-success" size="small">{{
                  reviewList[index]?.expandData?.sapNos.length
                }}</el-tag>
              </el-col>
              <el-col :span="19" class="d-flex row-gap-10 flex-wrap">
                <div
                  v-for="id in reviewList[index]?.expandData?.sapNos"
                  :key="'sapNos' + id"
                  class="id-list mr-1"
                >
                  <span class="btn-sample">S</span>
                  <router-link :to="`/sample/detail/${id}`">
                    {{ id }}
                  </router-link>
                </div>
              </el-col>
              <el-divider class="mg-divider"></el-divider>
            </el-row>
            <el-row v-if="reviewList[index]?.expandData?.runNos" :gutter="20">
              <el-col :span="5">
                <span class="text-secondary-color count-type font-600 mr-05">
                  {{ $t('userCenter.personal.myReview.counts.runCounts') }}
                </span>
                <el-tag type="success" class="tag-success" size="small">{{
                  reviewList[index]?.expandData?.runNos.length
                }}</el-tag>
              </el-col>
              <el-col :span="19" class="d-flex row-gap-10 flex-wrap">
                <div
                  v-for="id in reviewList[index]?.expandData?.runNos"
                  :key="'runNos' + id"
                  class="id-list mr-1"
                >
                  <span class="btn-run">R</span>
                  <router-link :to="`/run/detail/${id}`">
                    {{ id }}
                  </router-link>
                </div>
              </el-col>
              <el-divider class="mg-divider"></el-divider>
            </el-row>
            <el-row v-if="reviewList[index]?.expandData?.dataNos" :gutter="20">
              <el-col :span="5">
                <span class="text-secondary-color count-type font-600 mr-05">
                  {{ $t('userCenter.personal.myReview.counts.dataCounts') }}
                </span>
                <el-tag type="success" class="tag-success" size="small">{{
                  reviewList[index]?.expandData?.dataNos.length
                }}</el-tag>
              </el-col>
              <el-col :span="19" class="d-flex row-gap-10 flex-wrap">
                <div
                  v-for="id in reviewList[index]?.expandData?.dataNos"
                  :key="'dataNos' + id"
                  class="id-list mr-1"
                >
                  <span class="btn-data">D</span>
                  <span>{{ id }}</span>
                </div>
              </el-col>
              <el-divider class="mg-divider"></el-divider>
            </el-row>
          </div>
        </el-collapse-transition>

        <div class="radius-12 bg-gray mt-05 d-flex">
          <div class="d-flex align-items-center mr-2">
            <el-tag type="primary" round size="small" class="mr-05"
              >{{ $t('userCenter.personal.myReview.info.entrance') }}
            </el-tag>
            <svg-icon icon-class="link" class-name="svg mr-05"></svg-icon>
            <a :href="item.url" target="_blank">
              {{ item.url }}
            </a>
            <el-tooltip
              effect="dark"
              :content="$t('userCenter.personal.myReview.actions.copy')"
              placement="right"
              popper-class="copy-tooltip"
            >
              <svg-icon
                icon-class="copy"
                class-name="svg-copy"
                @click="copyText(item.url)"
              ></svg-icon>
            </el-tooltip>
          </div>
        </div>

        <div class="radius-12 bg-gray mt-05 d-flex">
          <div class="d-flex align-items-center mr-2">
            <svg-icon icon-class="request-date" class-name="svg"></svg-icon>
            <span class="text-other-color mr-05 ml-05"
              >{{ $t('userCenter.personal.myReview.dates.reviewDate') }}:</span
            >
            <span class="text-other-color">{{ item.reviewDate }}</span>
          </div>
          <div class="d-flex align-items-center mr-2">
            <svg-icon icon-class="request-date" class-name="svg"></svg-icon>
            <span class="text-other-color mr-05 ml-05"
              >{{ $t('userCenter.personal.myReview.dates.expiredDate') }}:</span
            >
            <span class="text-other-color">{{
              parseTime(item.expiredDate, '{y}-{m}-{d}')
            }}</span>
          </div>
          <div v-if="item.reviewToEmail" class="d-flex align-items-center mr-2">
            <svg-icon icon-class="shareEmail" class-name="svg"></svg-icon>
            <span class="text-other-color mr-05 ml-05"
              >{{
                $t('userCenter.personal.myReview.info.reviewToEmail')
              }}:</span
            >
            <span class="text-other-color">{{ item.reviewToEmail }}</span>
          </div>
        </div>
        <el-divider></el-divider>
      </div>
      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        class="mb-1"
        @pagination="getDataList"
      />
    </div>
    <div v-else v-loading="loading">
      <el-empty></el-empty>
    </div>

    <!--取消分享-->
    <el-dialog
      v-model="cancelReviewDialog"
      :title="$t('userCenter.personal.myReview.dialog.cancelConfirm')"
      width="400"
      center
      class="round-dialog"
    >
      <template #footer>
        <div>
          <el-button size="small" type="primary" @click="cancelReview">
            {{ $t('userCenter.personal.myReview.dialog.confirm') }}
          </el-button>
          <el-button size="small" @click="cancelReviewDialog = false"
            >{{ $t('userCenter.personal.myReview.dialog.cancel') }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!--延长过期时间-->
    <el-dialog
      v-model="extensionDialog"
      :title="$t('userCenter.personal.myReview.dialog.extension')"
      width="500"
      center
      class="round-dialog extension-dialog"
    >
      <div class="mb-05">
        <el-alert type="warning" :closable="false">
          <div class="font-14">
            {{ $t('userCenter.personal.myReview.dialog.extensionTips.tip1') }}
          </div>
          <div class="font-14">
            {{ $t('userCenter.personal.myReview.dialog.extensionTips.tip2') }}
          </div>
          <div class="font-14">
            {{ $t('userCenter.personal.myReview.dialog.extensionTips.tip3') }}
          </div>
        </el-alert>
        <div class="mt-1-5 text-center">
          <span class="demonstration mr-1">{{
            $t('userCenter.personal.myReview.dialog.validPeriod')
          }}</span>
          <el-date-picker
            v-model="validPeriod"
            :teleported="false"
            value-format="YYYY-MM-DD"
            type="date"
            :placeholder="$t('userCenter.personal.myReview.dialog.pickDay')"
            size="default"
          />
        </div>
      </div>
      <template #footer>
        <div>
          <el-button size="small" type="primary" @click="extensionDate()">
            {{ $t('userCenter.personal.myReview.dialog.confirm') }}
          </el-button>
          <el-button size="small" @click="extensionDialog = false"
            >{{ $t('userCenter.personal.myReview.dialog.cancel') }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { getCurrentInstance, onMounted, reactive, ref, toRefs } from 'vue';
  import {
    cancel,
    extendDate,
    getMyReviewList,
    getReviewDataList,
  } from '@/api/app/review';
  import { parseTime } from '@/utils/nodeCommon';
  import useUserStore from '@/store/modules/user';
  import { storeToRefs } from 'pinia';

  const { proxy } = getCurrentInstance();
  const userStore = useUserStore();
  const { member } = storeToRefs(userStore);

  const cancelReviewDialog = ref(false);
  const extensionDialog = ref(false);

  const resourceTypeVal = ref([]);
  const resourceType = reactive([
    {
      value: 'project',
      label: proxy.$t('userCenter.personal.myReview.resourceTypes.project'),
    },
    {
      value: 'experiment',
      label: proxy.$t('userCenter.personal.myReview.resourceTypes.experiment'),
    },
    {
      value: 'sample',
      label: proxy.$t('userCenter.personal.myReview.resourceTypes.sample'),
    },
    {
      value: 'analysis',
      label: proxy.$t('userCenter.personal.myReview.resourceTypes.analysis'),
    },
  ]);
  const year = ref([]);
  const yearOpt = ref([]);

  /** 响应式数据 */
  const data = reactive({
    total: 0,
    queryParams: {
      pageNum: 1,
      pageSize: 10,
    },
  });

  /** 解构 */
  const { total, queryParams } = toRefs(data);

  const validPeriod = ref('');

  const sortBtn = reactive([
    {
      label: proxy.$t('userCenter.personal.myReview.sort.reviewDate'),
      field: 'review_date',
      highlighted: true,
      sortOrder: 'descending',
    },
    {
      label: proxy.$t('userCenter.personal.myReview.sort.reviewId'),
      field: 'review_id',
      highlighted: false,
      sortOrder: 'descending',
    },
    {
      label: proxy.$t('userCenter.personal.myReview.sort.status'),
      field: 'status',
      highlighted: false,
      sortOrder: 'descending',
    },
  ]);

  // 左上角排序
  const queryPageAndSort = ref({
    sortKey: sortBtn[0].field,
    sortType: sortBtn[0].sortOrder,
  });

  const loading = ref(false);
  const reviewList = ref([]);

  // 查询外层的请求列表
  function getDataList() {
    const params = {
      sourceMember: member.value.id,
      sourceEmail: member.value.email,
      myRequest: true,
      year: year.value,
      resourceType: resourceTypeVal.value,
      orderByColumn: queryPageAndSort.value.sortKey,
      isAsc: queryPageAndSort.value.sortType,
      pageNum: queryParams.value.pageNum,
      pageSize: queryParams.value.pageSize,
    };
    loading.value = true;
    getMyReviewList(params)
      .then(response => {
        reviewList.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  /** 导出页面中data的下载链接 */
  function exportDataLink(id) {
    let type = 'review';
    proxy.download(
      `/download/node/exportDownloadLink/${type}/${id}`,
      null,
      `${type}_${id}_data_download_link.zip`,
    );
  }

  function doSortSearch(index) {
    queryPageAndSort.value.sortKey = sortBtn[index].field;
    queryPageAndSort.value.sortType = sortBtn[index].sortOrder;
  }

  // 生成最近3年内的查询下拉词
  function getRecentYears() {
    let currentYear = new Date().getFullYear();

    for (let i = 0; i < 3; i++) {
      yearOpt.value.push({ value: currentYear - i, label: currentYear - i });
    }

    const lastYear = yearOpt.value[yearOpt.value.length - 1].value;
    yearOpt.value.push({
      value: '<' + lastYear,
      label:
        proxy.$t('userCenter.personal.myReview.filters.before') +
        ' ' +
        lastYear,
    });
  }

  const expandDataList = index => {
    let item = reviewList.value[index];

    // 已经展开，点击则收起
    if (item?.expand) {
      item.expand = false;
      return;
    }
    getReviewDataList({ reviewId: item.id }).then(response => {
      item.expandData = response.data;
      item.expand = true;
    });
  };

  const currentReviewId = ref('');

  function openCancel(id) {
    currentReviewId.value = id;
    cancelReviewDialog.value = true;
  }

  function openExtendDate(id) {
    currentReviewId.value = id;
    validPeriod.value = '';
    extensionDialog.value = true;
  }

  function cancelReview() {
    cancel({ reviewId: currentReviewId.value }).then(() => {
      proxy.$modal.msgSuccess(
        proxy.$t('userCenter.personal.myReview.messages.cancelSuccess'),
      );
      cancelReviewDialog.value = false;
      getDataList();
    });
  }

  function extensionDate() {
    if (validPeriod.value === '') {
      proxy.$modal.alertError(
        proxy.$t('userCenter.personal.myReview.messages.pickDayRequired'),
      );
      return;
    }
    extendDate({
      reviewId: currentReviewId.value,
      date: validPeriod.value,
    }).then(() => {
      proxy.$modal.msgSuccess(
        proxy.$t('userCenter.personal.myReview.messages.extensionSuccess'),
      );
      extensionDialog.value = false;
      getDataList();
    });
  }

  const toggleSortOrder = index => {
    sortBtn.forEach(btn => {
      btn.highlighted = false;
    });
    sortBtn[index].highlighted = true;
    sortBtn[index].sortOrder =
      sortBtn[index].sortOrder === 'ascending' ? 'descending' : 'ascending';

    // 修改排序字段
    doSortSearch(index);
    getDataList();
  };

  function copyText(text) {
    // 添加一个input元素放置需要的文本内容
    const input = document.createElement('input');
    input.value = text;
    document.body.appendChild(input);
    // 选中并复制文本到剪切板
    input.select();
    document.execCommand('copy');
    // 移除input元素
    document.body.removeChild(input);
    proxy.$modal.msgSuccess(
      proxy.$t('userCenter.personal.myReview.messages.copySuccess'),
    );
  }

  onMounted(() => {
    getRecentYears();
    getDataList();
  });
</script>

<style lang="scss" scoped>
  .list {
    .count-type {
      display: inline-block;
      width: 129px;
    }
  }

  .cancel-review {
    width: 83px;
    background-color: #f4f4f5 !important;
    color: gray !important;
    border-color: gray !important;
    transition: all 0.3s linear;
  }
  .bg-primary {
    background-color: #f5f8fe;
    .search {
      padding: 6px 10px;
    }
  }
  .tag-success {
    background-color: #cfefed !important;
    color: #07bcb4;
    font-weight: 600;
    border-radius: 8px;
  }
  :deep(.el-tag__content) {
    font-weight: 600;
  }
  :deep(.el-button--small.is-round) {
    padding: 5px 11px !important;
  }
  .svg {
    width: 16px;
    height: 16px;
  }
  .svg-idList {
    width: 13px;
    height: 13px;
    cursor: pointer;
    &:focus {
      outline: none;
    }
  }
  .bg-gray {
    padding: 6px 15px;
  }

  .btn-round-success:focus {
    background-color: #f0f9eb;
    color: #67c23a;
    border: 1px solid #67c23a;
  }
  :deep(.el-dialog__body) {
    padding: 0 !important;
  }
  :deep(.extension-dialog .el-dialog__body) {
    padding: 0 15px !important;
  }
  :deep(.el-select__wrapper) {
    border-radius: 12px;
  }
  .svg-copy {
    width: 18px;
    height: 18px;
    margin-left: 0.5rem;
    cursor: pointer;
    &:focus {
      outline: none;
    }
  }
  .copy-tooltip {
    width: 20px;
  }

  @media (max-width: 767px) {
    .d-flex {
      flex-wrap: wrap;
    }
    a {
      word-break: break-all;
    }
    .d-flex-default {
      display: flex;
    }
  }
</style>
