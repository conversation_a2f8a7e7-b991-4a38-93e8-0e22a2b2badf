<template>
  <div class="card-item bg-gray p-10-15 w-100" style="position: relative">
    <div style="width: 100%">
      <div class="text-secondary-color font-600">{{ name }} ({{ count }})</div>
      <div :id="id" style="width: 100%; height: 90px"></div>
    </div>
    <span
      style="position: absolute; top: 12px; right: 20px"
      class="text-primary font-600 font-14"
      >{{ size }}</span
    >
  </div>
</template>

<script setup>
  import * as echarts from 'echarts';
  import { onMounted, nextTick, defineProps } from 'vue';

  const props = defineProps({
    id: {
      type: String,
      required: true,
    },
    name: {
      type: String,
      required: true,
    },
    size: {
      type: String,
    },
    count: {
      type: String,
    },
    series: {
      type: Object,
    },
  });

  const echartsInit = () => {
    const itemCard = echarts.init(document.getElementById(props.id));
    const option = {
      color: ['#7198DE', '#FE7F2B', '#07BCB4'],
      legend: {
        icon: 'circle',
        top: 'bottom',
        left: '',
        selectedMode: false,
        orient: props.name === 'Data' ? 'horizontal' : 'vertical',
        itemGap: 10,
        itemHeight: '6',
      },
      tooltip: {
        formatter: function (params) {
          const dataIndex = params.seriesIndex;
          return (
            '<span class="mr-1 mb-05">' +
            params.seriesName +
            '</span>' +
            '<span class="mr-1">' +
            ((params.value / props.count) * 100).toFixed(0) +
            '%</span>' +
            '<hr>' +
            '<span class="mr-03">' +
            props.series[dataIndex].data[1] +
            '</span>' +
            '<div class="mr-1">' +
            params.value +
            '</div>'
          );
        },
        confine: true,
      },
      grid: {
        left: '0%',
        top: props.name === 'Data' ? '70%' : '37%',
        right: '1%',
      },
      xAxis: {
        type: 'value',
        show: false,
      },
      yAxis: {
        show: false,
        type: 'category',
        data: ['data'],
      },
      series: props.series,
    };
    itemCard.setOption(option);
    window.onresize = function () {
      itemCard.resize();
    };
  };
  onMounted(() => {
    nextTick(() => {
      echartsInit();
    });
  });
</script>

<style lang="scss" scoped>
  .card-item {
    border-radius: 8px;
    position: relative;
    &:before {
      position: absolute;
      left: 0;
      top: 0;
      content: '';
      height: 100%;
      width: 3px;
      border-radius: 45px;
      background: #6089d3;
    }
  }
</style>
