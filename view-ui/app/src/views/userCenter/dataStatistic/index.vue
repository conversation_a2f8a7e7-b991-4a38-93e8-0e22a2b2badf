<template>
  <div v-loading="loading" class="container" style="height: 590px">
    <el-row v-if="!loading" :gutter="20" class="row-gap-20 mt-1">
      <el-col :span="8" :xs="24">
        <ItemCard
          id="project"
          name="Project"
          :count="result.projAccessible + result.projUnAccessible"
          :size="result.rawDataTotalSizeString"
          :series="projSeries"
        ></ItemCard>
      </el-col>
      <el-col :span="8" :xs="24">
        <ItemCard
          id="sample"
          name="Sample"
          :count="result.sapAccessible + result.sapUnAccessible"
          :size="result.rawDataTotalSizeString"
          :series="sapSeries"
        ></ItemCard>
      </el-col>
      <el-col :span="8" :xs="24">
        <ItemCard
          id="analysis"
          name="Analysis"
          :count="result.analAccessible + result.analUnAccessible"
          :size="result.analDataTotalSizeString"
          :series="analSeries"
        ></ItemCard>
      </el-col>
      <el-col :span="8" :xs="24">
        <ItemCard
          id="experiment"
          name="Experiment"
          :count="result.expAccessible + result.expUnAccessible"
          :size="result.rawDataTotalSizeString"
          :series="expSeries"
        ></ItemCard>
      </el-col>
      <el-col :span="24" :xs="24">
        <el-row>
          <el-col :span="16" :xs="24">
            <ItemCard
              id="run"
              name="Run"
              :count="result.runAccessible + result.runUnAccessible"
              :size="result.rawDataTotalSizeString"
              :series="runSeries"
            ></ItemCard>
          </el-col>
        </el-row>
      </el-col>

      <el-col :span="24" :xs="24">
        <ItemCard
          id="data"
          name="Data"
          :count="
            result.rawDataPrivate +
            result.analDataPrivate +
            result.rawDataRestricted +
            result.analDataRestricted +
            result.rawDataPublic +
            result.analDataPublic
          "
          :size="result.dataTotalSizeString"
          :series="dataSeries"
        ></ItemCard>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
  import ItemCard from './itemCard.vue';
  import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
  import { getPublishBaseInfo } from '@/api/statistics';
  const { proxy } = getCurrentInstance();

  onMounted(() => {
    initData();
  });

  const result = ref({});
  const loading = ref(false);

  function initData() {
    loading.value = true;
    getPublishBaseInfo()
      .then(response => {
        result.value = response.data;
        projSeries[0].data[0] = result.value.projAccessible;
        projSeries[0].data[1] = result.value.rawDataAccessibleSizeString;
        projSeries[1].data[0] = result.value.projUnAccessible;
        projSeries[1].data[1] = result.value.rawDataUnAccessibleSizeString;

        expSeries[0].data[0] = result.value.expAccessible;
        expSeries[0].data[1] = result.value.rawDataAccessibleSizeString;
        expSeries[1].data[0] = result.value.expUnAccessible;
        expSeries[1].data[1] = result.value.rawDataUnAccessibleSizeString;

        sapSeries[0].data[0] = result.value.sapAccessible;
        sapSeries[0].data[1] = result.value.rawDataAccessibleSizeString;
        sapSeries[1].data[0] = result.value.sapUnAccessible;
        sapSeries[1].data[1] = result.value.rawDataUnAccessibleSizeString;

        runSeries[0].data[0] = result.value.runAccessible;
        runSeries[0].data[1] = result.value.rawDataAccessibleSizeString;
        runSeries[1].data[0] = result.value.runUnAccessible;
        runSeries[1].data[1] = result.value.rawDataUnAccessibleSizeString;

        analSeries[0].data[0] = result.value.analAccessible;
        analSeries[0].data[1] = result.value.analDataAccessibleSizeString;
        analSeries[1].data[0] = result.value.analUnAccessible;
        analSeries[1].data[1] = result.value.analDataUnAccessibleSizeString;

        dataSeries[0].data[0] =
          result.value.rawDataPrivate + result.value.analDataPrivate;
        dataSeries[0].data[1] = proxy.$bytesToSize(
          result.value.rawDataPrivateSize + result.value.analDataPrivateSize,
        );
        dataSeries[1].data[0] =
          result.value.rawDataRestricted + result.value.analDataRestricted;
        dataSeries[1].data[1] = proxy.$bytesToSize(
          result.value.rawDataRestrictedSize +
            result.value.analDataRestrictedSize,
        );
        dataSeries[2].data[0] =
          result.value.rawDataPublic + result.value.analDataPublic;
        dataSeries[2].data[1] = proxy.$bytesToSize(
          result.value.rawDataPublicSize + result.value.analDataPublicSize,
        );
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const projSeries = reactive([
    {
      data: [130, '121tb'],
      type: 'bar',
      stack: 'a',
      name: 'Accessible',
      barMaxWidth: 16,
      barMinWidth: 16,
    },
    {
      data: [110, '121tb'],
      type: 'bar',
      stack: 'a',
      name: 'UnAccessible',
      barMaxWidth: 16,
      barMinWidth: 16,
    },
  ]);

  const expSeries = reactive([
    {
      data: [67, '121tb'],
      type: 'bar',
      stack: 'a',
      name: 'Accessible',
      barMaxWidth: 16,
      barMinWidth: 16,
    },
    {
      data: [45, '121tb'],
      type: 'bar',
      stack: 'a',
      name: 'UnAccessible',
      barMaxWidth: 16,
      barMinWidth: 16,
    },
  ]);

  const sapSeries = reactive([
    {
      data: [78, '121tb'],
      type: 'bar',
      stack: 'a',
      name: 'Accessible',
      barMaxWidth: 16,
      barMinWidth: 16,
    },
    {
      data: [54, '121tb'],
      type: 'bar',
      stack: 'a',
      name: 'UnAccessible',
      barMaxWidth: 16,
      barMinWidth: 16,
    },
  ]);

  const analSeries = reactive([
    {
      data: [56, '121tb'],
      type: 'bar',
      stack: 'a',
      name: 'Accessible',
      barMaxWidth: 16,
      barMinWidth: 16,
    },
    {
      data: [10, '121tb'],
      type: 'bar',
      stack: 'a',
      name: 'UnAccessible',
      barMaxWidth: 16,
      barMinWidth: 16,
    },
  ]);

  const runSeries = reactive([
    {
      data: [23, '121tb'],
      type: 'bar',
      stack: 'a',
      name: 'Accessible',
      barMaxWidth: 16,
      barMinWidth: 16,
    },
    {
      data: [43, '121tb'],
      type: 'bar',
      stack: 'a',
      name: 'UnAccessible',
      barMaxWidth: 16,
      barMinWidth: 16,
    },
  ]);

  const dataSeries = reactive([
    {
      data: [10, '121tb'],
      type: 'bar',
      stack: 'a',
      name: 'Private',
      barMaxWidth: 16,
      barMinWidth: 16,
    },
    {
      data: [120, '121tb'],
      type: 'bar',
      stack: 'a',
      name: 'Restricted',
      barMaxWidth: 16,
      barMinWidth: 16,
    },
    {
      data: [10, '121tb'],
      type: 'bar',
      stack: 'a',
      barMaxWidth: 16,
      barMinWidth: 16,
      name: 'Public',
    },
  ]);
</script>

<style lang="scss" scoped></style>
