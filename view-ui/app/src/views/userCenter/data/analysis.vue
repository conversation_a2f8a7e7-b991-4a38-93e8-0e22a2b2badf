<template>
  <div class="d-flex align-items-center mb-1">
    <el-input
      v-model="queryParams.name"
      :placeholder="$t('userCenter.data.analysis.searchPlaceholder')"
      class="w-50"
      clearable
      @keydown.enter="getDataList"
    />
    <span class="font-600 text-main-color ml-1 mr-1">{{
      $t('userCenter.data.analysis.period')
    }}</span>
    <el-date-picker
      v-model="dateRange"
      class="hidden-xs-only"
      clearable
      value-format="YYYY-MM-DD"
      type="daterange"
      :range-separator="$t('userCenter.data.analysis.dateTo')"
      :start-placeholder="$t('userCenter.data.analysis.startDate')"
      :end-placeholder="$t('userCenter.data.analysis.endDate')"
    />
    <el-button class="radius-12 ml-1 mr-1" type="primary" @click="getDataList"
      >{{ $t('userCenter.data.analysis.search') }}
    </el-button>
  </div>
  <el-table
    v-loading="loading"
    height="440"
    :data="tableData"
    style="width: 100%; margin-bottom: 20px"
    :header-cell-style="{
      backgroundColor: '#f2f2f2',
      color: '#333333',
      fontWeight: 700,
    }"
    border
    :default-sort="defaultSort"
    @sort-change="handleSortChange"
  >
    <el-table-column
      prop="analysisNo"
      :label="$t('userCenter.data.analysis.columns.id')"
      sortable
      width="115"
    >
      <template #default="scope">
        <router-link :to="`/analysis/detail/${scope.row.analysisNo}`">
          <span class="text-primary">{{ scope.row.analysisNo }}</span>
        </router-link>
      </template>
    </el-table-column>
    <el-table-column
      prop="name"
      :label="$t('userCenter.data.analysis.columns.name')"
      min-width="150"
      sortable
      show-overflow-tooltip
    />
    <el-table-column
      prop="analysisType"
      :label="$t('userCenter.data.analysis.columns.analysisType')"
      min-width="170"
      sortable
      show-overflow-tooltip
    >
      <template #default="scope">
        {{
          scope.row.analysisType === 'Other' && scope.row.customAnalysisType
            ? `${$t('userCenter.data.analysis.other')}(${scope.row.customAnalysisType})`
            : scope.row.analysisType
        }}
      </template>
    </el-table-column>
    <el-table-column
      prop="dataNum"
      :label="$t('userCenter.data.analysis.columns.dataNumber')"
      width="120"
      align="center"
      show-overflow-tooltip
    >
      <template #default="scope">
        <el-tag type="success">{{ scope.row.dataNum }}</el-tag>
      </template>
    </el-table-column>

    <el-table-column
      prop="description"
      :label="$t('userCenter.data.analysis.columns.description')"
      min-width="150"
      show-overflow-tooltip
    />
    <el-table-column
      prop="createDate"
      :label="$t('userCenter.data.analysis.columns.uploadDate')"
      min-width="160"
      sortable
      show-overflow-tooltip
    />
    <el-table-column
      prop="visibleStatus"
      :label="$t('userCenter.data.analysis.columns.status')"
      min-width="130"
      sortable
    >
      <template #default="scope">
        <div class="d-flex align-items-center">
          <el-icon
            v-if="scope.row.visibleStatus === 'Accessible'"
            color="#07BCB4"
          >
            <SuccessFilled />
          </el-icon>
          <el-icon v-else color="#FA8282">
            <CircleCloseFilled />
          </el-icon>
          <div
            class="ml-05"
            :style="{
              color:
                scope.row.visibleStatus === 'Accessible'
                  ? '#07BCB4'
                  : '#FA8282',
            }"
          >
            {{ scope.row.visibleStatus }}
          </div>
        </div>
      </template>
    </el-table-column>
    <el-table-column
      fixed="right"
      :label="$t('userCenter.data.analysis.columns.operate')"
      width="110"
    >
      <template #header>
        {{ $t('userCenter.data.analysis.columns.operate') }}
        <el-tooltip
          effect="light"
          raw-content
          :content="`Batch Modify: <a href='${getOriginUrl()}/submit/metadata/analysisData'>${getOriginUrl()}/submit/metadata/analysisData</a>`"
        >
          <el-icon size="12" color="#3A78E8">
            <InfoFilled />
          </el-icon>
        </el-tooltip>
      </template>
      <template #default="scope">
        <div class="text-center">
          <el-tooltip :content="$t('userCenter.data.analysis.operations.edit')">
            <svg-icon
              v-if="scope.row.visibleStatus === 'Unaccessible'"
              icon-class="usercenterEdit"
              class-name="operate-svg"
              @click="toEdit(scope.row.analysisNo)"
            ></svg-icon>
          </el-tooltip>
          <el-tooltip
            :content="$t('userCenter.data.analysis.operations.delete')"
          >
            <svg-icon
              v-if="scope.row.visibleStatus === 'Unaccessible'"
              icon-class="delete"
              class-name="operate-svg"
              @click="preCheck(scope.row.analysisNo)"
            ></svg-icon>
          </el-tooltip>
        </div>
      </template>
    </el-table-column>
  </el-table>
  <pagination
    v-show="total > 0"
    v-model:page="queryParams.pageNum"
    v-model:limit="queryParams.pageSize"
    :total="total"
    class="mb-1"
    @pagination="getDataList"
  />
  <delete-log
    ref="deleteLog"
    :curr-type="$t('userCenter.data.analysis.deleteLog.type')"
  ></delete-log>

  <delete-confirm
    v-model:password="password"
    v-model:show-dialog="showPwEnterDialog"
    :delete-check-result="deleteCheckResult"
    @delete-method="confirmDelete"
  ></delete-confirm>
</template>
<script setup>
  import { getCurrentInstance, onMounted, reactive, ref, toRefs } from 'vue';
  import { useRouter } from 'vue-router';
  import DeleteLog from '@/views/submit/metadata/rawData/common/DeleteLog.vue';
  import DeleteConfirm from '@/views/userCenter/data/components/DeleteConfirm.vue';
  import {
    analysisDeleteCheck,
    deleteAnalysisAll,
  } from '@/api/metadata/analysis';
  import { listAnalysis } from '@/api/app/analysis';
  import { getOriginUrl } from '@/utils/nodeCommon';

  const { proxy } = getCurrentInstance();
  const router = useRouter();

  onMounted(() => {
    getDataList();
  });

  /** 响应式数据 */
  const data = reactive({
    tableData: [],
    total: 0,
    queryParams: {
      name: '',
      pageNum: 1,
      pageSize: 10,
      orderByColumn: 'createDate',
      isAsc: 'descending',
    },
    dateRange: [],
    loading: true,
    defaultSort: { prop: 'createDate', order: 'descending' },
  });
  /** 解构 */
  const { tableData, total, queryParams, dateRange, loading, defaultSort } =
    toRefs(data);

  /** 查询列表数据*/
  function getDataList() {
    loading.value = true;
    listAnalysis(proxy.addDateRange(queryParams.value, dateRange.value))
      .then(response => {
        // 将结果赋值给tableData
        tableData.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  /** 排序触发事件 */
  function handleSortChange(column, prop, order) {
    if (column.order) {
      queryParams.value.orderByColumn = column.prop;
      queryParams.value.isAsc = column.order;
      getDataList();
    }
  }

  function toEdit(sapNo) {
    router.push({ path: `/submit/metadata/analysis/edit/${sapNo}` });
  }

  let showPwEnterDialog = ref(false);
  let password = ref('');
  let analNo = ref('');
  let deleteCheckResult = ref({});

  /** 删除预检查 */
  function preCheck(no) {
    proxy.$modal.loading(
      proxy.$t('userCenter.data.analysis.messages.openingDeleteDialog'),
    );
    analNo.value = no;
    analysisDeleteCheck(no)
      .then(response => {
        if (response.data.errors && response.data.errors.length !== 0) {
          proxy.$refs['deleteLog'].openLog(response.data.errors);
          return;
        }
        deleteCheckResult.value = response.data;
        showPwEnterDialog.value = true;
        // 验证密码
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  function confirmDelete() {
    proxy.$modal.loading(
      proxy.$t('userCenter.data.analysis.messages.verifyingAndDeleting'),
    );
    deleteAnalysisAll({
      analNo: analNo.value,
      password: password.value,
    })
      .then(() => {
        password.value = '';
        showPwEnterDialog.value = false;
        getDataList();
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }
</script>

<style scoped lang="scss"></style>
