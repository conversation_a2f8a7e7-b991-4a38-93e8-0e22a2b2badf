<template>
  <div>
    <div class="d-flex align-items-center mb-1">
      <el-input
        v-model="queryParams.name"
        :placeholder="$t('userCenter.data.sample.searchPlaceholder')"
        style="width: 300px"
        clearable
        @keydown.enter="getDataList"
      />
      <span class="font-600 text-main-color ml-1 mr-1">{{
        $t('userCenter.data.sample.organism')
      }}</span>
      <el-select
        v-model="queryParams.organisms"
        multiple
        :placeholder="$t('userCenter.data.sample.select')"
        style="width: 300px; border-radius: 12px"
        clearable
      >
        <el-option
          v-for="item in organismOpts"
          :key="item"
          :label="item"
          :value="item"
        />
      </el-select>
      <span class="font-600 text-main-color ml-1 mr-1">{{
        $t('userCenter.data.sample.period')
      }}</span>
      <el-date-picker
        v-model="dateRange"
        class="hidden-xs-only"
        clearable
        value-format="YYYY-MM-DD"
        type="daterange"
        :range-separator="$t('userCenter.data.sample.dateTo')"
        :start-placeholder="$t('userCenter.data.sample.startDate')"
        :end-placeholder="$t('userCenter.data.sample.endDate')"
      />
      <el-button class="radius-12 ml-1 mr-1" type="primary" @click="getDataList"
        >{{ $t('userCenter.data.sample.search') }}
      </el-button>
    </div>
    <div class="status-select bg-gray p-10-15 radius-8 mb-1">
      <el-form-item
        :label="$t('userCenter.data.sample.sampleType')"
        style="margin-bottom: 0"
      >
        <el-checkbox-group v-model="queryParams.sapTypes" @change="getDataList">
          <el-checkbox v-for="item in sampleTypeOpts" :key="item" :label="item"
            >{{ item }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </div>
    <el-table
      v-loading="loading"
      :data="tableData"
      style="width: 100%; margin-bottom: 20px"
      :header-cell-style="{
        backgroundColor: '#f2f2f2',
        color: '#333333',
        fontWeight: 700,
      }"
      border
      height="375"
      :default-sort="defaultSort"
      @sort-change="handleSortChange"
    >
      <el-table-column
        prop="sapNo"
        :label="$t('userCenter.data.sample.columns.id')"
        sortable
        min-width="115"
      >
        <template #default="scope">
          <router-link :to="`/sample/detail/${scope.row.sapNo}`">
            <span class="text-primary">{{ scope.row.sapNo }}</span>
          </router-link>
        </template>
      </el-table-column>
      <el-table-column
        prop="name"
        :label="$t('userCenter.data.sample.columns.name')"
        min-width="150"
        sortable
        show-overflow-tooltip
      />
      <el-table-column
        prop="subjectType"
        :label="$t('userCenter.data.sample.columns.sampleType')"
        width="130"
        sortable
        show-overflow-tooltip
      />
      <el-table-column
        prop="organism"
        :label="$t('userCenter.data.sample.columns.organism')"
        min-width="120"
        sortable
        show-overflow-tooltip
      />
      <el-table-column
        prop="expTypes"
        :label="$t('userCenter.data.sample.columns.experimentType')"
        width="140"
        show-overflow-tooltip
      >
        <template #default="scope">
          {{ scope.row.expTypes.join('; ') }}
        </template>
      </el-table-column>
      <el-table-column
        prop="description"
        :label="$t('userCenter.data.sample.columns.description')"
        width="130"
        show-overflow-tooltip
      />
      <el-table-column
        prop="createDate"
        :label="$t('userCenter.data.sample.columns.uploadDate')"
        min-width="160"
        sortable
        show-overflow-tooltip
      />
      <!--<el-table-column
        prop="submitter"
        label="Submitter"
        width="130"
        sortable
      />-->
      <el-table-column
        prop="visibleStatus"
        :label="$t('userCenter.data.sample.columns.status')"
        min-width="130"
        sortable
      >
        <template #default="scope">
          <div class="d-flex align-items-center">
            <el-icon
              v-if="scope.row.visibleStatus === 'Accessible'"
              color="#07BCB4"
            >
              <SuccessFilled />
            </el-icon>
            <el-icon v-else color="#FA8282">
              <CircleCloseFilled />
            </el-icon>
            <div
              class="ml-05"
              :style="{
                color:
                  scope.row.visibleStatus === 'Accessible'
                    ? '#07BCB4'
                    : '#FA8282',
              }"
            >
              {{ scope.row.visibleStatus }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        :label="$t('userCenter.data.sample.columns.operate')"
        width="110"
      >
        <template #header>
          {{ $t('userCenter.data.sample.columns.operate') }}
          <el-tooltip
            effect="light"
            raw-content
            :content="`Batch Modify: <a href='${getOriginUrl()}/submit/metadata/rawData'>${getOriginUrl()}/submit/metadata/rawData</a>`"
          >
            <el-icon size="12" color="#3A78E8">
              <InfoFilled />
            </el-icon>
          </el-tooltip>
        </template>
        <template #default="scope">
          <div class="text-center">
            <el-tooltip :content="$t('userCenter.data.sample.operations.edit')">
              <svg-icon
                v-if="scope.row.visibleStatus === 'Unaccessible'"
                icon-class="usercenterEdit"
                class-name="operate-svg"
                @click="toEdit(scope.row.sapNo)"
              ></svg-icon>
            </el-tooltip>
            <el-tooltip
              :content="$t('userCenter.data.sample.operations.delete')"
            >
              <svg-icon
                v-if="scope.row.visibleStatus === 'Unaccessible'"
                icon-class="delete"
                class-name="operate-svg"
                @click="preCheck(scope.row.sapNo)"
              ></svg-icon>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      class="mb-1"
      @pagination="getDataList"
    />
    <delete-log
      ref="deleteLog"
      :curr-type="$t('userCenter.data.sample.deleteLog.type')"
    ></delete-log>

    <delete-confirm
      v-model:password="password"
      v-model:show-dialog="showPwEnterDialog"
      :delete-check-result="deleteCheckResult"
      @delete-method="confirmDelete"
    ></delete-confirm>
  </div>
</template>
<script setup>
  import { getCurrentInstance, onMounted, reactive, ref, toRefs } from 'vue';
  import {
    getUserAuditedOrganism,
    getUserAuditedSapType,
    listSample,
  } from '@/api/app/sample';
  import { useRouter } from 'vue-router';
  import { deleteSapAll, sapDeleteCheck } from '@/api/metadata/sample';
  import DeleteConfirm from '@/views/userCenter/data/components/DeleteConfirm.vue';
  import DeleteLog from '@/views/submit/metadata/rawData/common/DeleteLog.vue';
  import { getOriginUrl } from '@/utils/nodeCommon';

  const { proxy } = getCurrentInstance();
  const router = useRouter();

  let organismOpts = ref([]);
  let sampleTypeOpts = ref([]);

  onMounted(() => {
    getUserAuditedOrganism().then(response => {
      organismOpts.value = response.data;
    });
    getUserAuditedSapType().then(response => {
      sampleTypeOpts.value = response.data;
    });
    getDataList();
  });

  /** 响应式数据 */
  const data = reactive({
    tableData: [],
    total: 0,
    queryParams: {
      name: '',
      organisms: [],
      sapTypes: [],
      pageNum: 1,
      pageSize: 10,
      orderByColumn: 'createDate',
      isAsc: 'descending',
    },
    dateRange: [],
    loading: true,
    defaultSort: { prop: 'createDate', order: 'descending' },
  });
  /** 解构 */
  const { tableData, total, queryParams, dateRange, loading, defaultSort } =
    toRefs(data);

  /** 查询列表数据*/
  function getDataList() {
    loading.value = true;
    listSample(proxy.addDateRange(queryParams.value, dateRange.value))
      .then(response => {
        // 将结果赋值给tableData
        tableData.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  /** 排序触发事件 */
  function handleSortChange(column, prop, order) {
    if (column.order) {
      queryParams.value.orderByColumn = column.prop;
      queryParams.value.isAsc = column.order;
      getDataList();
    }
  }

  function toEdit(sapNo) {
    router.push({ path: `/submit/metadata/sample/edit/${sapNo}` });
  }

  let showPwEnterDialog = ref(false);
  let password = ref('');
  let sapNo = ref('');
  let deleteCheckResult = ref({});

  /** 删除预检查 */
  function preCheck(no) {
    proxy.$modal.loading(
      proxy.$t('userCenter.data.sample.messages.openingDeleteDialog'),
    );
    sapNo.value = no;
    sapDeleteCheck(no)
      .then(response => {
        if (response.data.errors && response.data.errors.length !== 0) {
          proxy.$refs['deleteLog'].openLog(response.data.errors);
          return;
        }
        deleteCheckResult.value = response.data;
        showPwEnterDialog.value = true;
        // 验证密码
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  function confirmDelete() {
    proxy.$modal.loading(
      proxy.$t('userCenter.data.sample.messages.verifyingAndDeleting'),
    );
    deleteSapAll({
      sapNo: sapNo.value,
      password: password.value,
    })
      .then(() => {
        password.value = '';
        showPwEnterDialog.value = false;
        getDataList();
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }
</script>

<style scoped lang="scss"></style>
