<template>
  <div class="d-flex align-items-center mb-1">
    <el-input
      v-model="queryParams.name"
      clearable
      :placeholder="$t('userCenter.data.project.searchPlaceholder')"
      class="w-50"
      @keydown.enter="getDataList"
    />
    <span class="font-600 text-main-color ml-1 mr-1">{{
      $t('userCenter.data.project.period')
    }}</span>
    <el-date-picker
      v-model="dateRange"
      class="hidden-xs-only"
      clearable
      value-format="YYYY-MM-DD"
      type="daterange"
      :range-separator="$t('userCenter.data.project.dateTo')"
      :start-placeholder="$t('userCenter.data.project.startDate')"
      :end-placeholder="$t('userCenter.data.project.endDate')"
    />
    <el-button class="radius-12 ml-1 mr-1" type="primary" @click="getDataList"
      >{{ $t('userCenter.data.project.search') }}
    </el-button>
  </div>

  <el-table
    v-loading="loading"
    :data="tableData"
    style="width: 100%; margin-bottom: 20px"
    :header-cell-style="{
      backgroundColor: '#f2f2f2',
      color: '#333333',
      fontWeight: 700,
    }"
    height="443"
    border
    :default-sort="defaultSort"
    @sort-change="handleSortChange"
  >
    <el-table-column
      prop="projectNo"
      :label="$t('userCenter.data.project.columns.id')"
      sortable
      width="115"
    >
      <template #default="scope">
        <router-link :to="`/project/detail/${scope.row.projectNo}`">
          <span class="text-primary">{{ scope.row.projectNo }}</span>
        </router-link>
      </template>
    </el-table-column>
    <el-table-column
      prop="name"
      :label="$t('userCenter.data.project.columns.name')"
      min-width="180"
      sortable
      show-overflow-tooltip
    />
    <el-table-column
      prop="expTypes"
      :label="$t('userCenter.data.project.columns.experimentType')"
      width="140"
      show-overflow-tooltip
    >
      <template #default="scope">
        {{ scope.row.expTypes.join('; ') }}
      </template>
    </el-table-column>
    <el-table-column
      prop="sapTypes"
      :label="$t('userCenter.data.project.columns.sampleType')"
      width="110"
      show-overflow-tooltip
    >
      <template #default="scope">
        {{ scope.row.sapTypes.join('; ') }}
      </template>
    </el-table-column>
    <el-table-column
      prop="description"
      :label="$t('userCenter.data.project.columns.description')"
      min-width="160"
      show-overflow-tooltip
    />
    <el-table-column
      prop="createDate"
      :label="$t('userCenter.data.project.columns.uploadDate')"
      min-width="160"
      sortable
      show-overflow-tooltip
    />

    <el-table-column
      prop="visibleStatus"
      :label="$t('userCenter.data.project.columns.status')"
      min-width="130"
      sortable
    >
      <template #default="scope">
        <div class="d-flex align-items-center">
          <el-icon
            v-if="scope.row.visibleStatus === 'Accessible'"
            color="#07BCB4"
          >
            <SuccessFilled />
          </el-icon>
          <el-icon v-else color="#FA8282">
            <CircleCloseFilled />
          </el-icon>
          <div
            class="ml-05"
            :style="{
              color:
                scope.row.visibleStatus === 'Accessible'
                  ? '#07BCB4'
                  : '#FA8282',
            }"
          >
            {{ scope.row.visibleStatus }}
          </div>
        </div>
      </template>
    </el-table-column>
    <el-table-column
      :label="$t('userCenter.data.project.columns.operate')"
      width="85"
      fixed="right"
    >
      <template #default="scope">
        <div class="text-center">
          <el-tooltip :content="$t('userCenter.data.project.operations.edit')">
            <svg-icon
              v-if="scope.row.visibleStatus === 'Unaccessible'"
              icon-class="usercenterEdit"
              class-name="operate-svg"
              @click="toEdit(scope.row.projectNo)"
            ></svg-icon>
          </el-tooltip>
          <el-tooltip
            :content="$t('userCenter.data.project.operations.delete')"
          >
            <svg-icon
              v-if="scope.row.visibleStatus === 'Unaccessible'"
              icon-class="delete"
              class-name="operate-svg"
              @click="preCheck(scope.row.projectNo)"
            ></svg-icon>
          </el-tooltip>
        </div>
      </template>
    </el-table-column>
  </el-table>
  <pagination
    v-model:page="queryParams.pageNum"
    v-model:limit="queryParams.pageSize"
    :total="total"
    class="mb-1"
    @pagination="getDataList"
  />

  <delete-log
    ref="deleteLog"
    :curr-type="$t('userCenter.data.project.deleteLog.type')"
  ></delete-log>

  <delete-confirm
    v-model:password="password"
    v-model:show-dialog="showPwEnterDialog"
    :delete-check-result="deleteCheckResult"
    @delete-method="confirmDelete"
  ></delete-confirm>
</template>

<script setup>
  import {
    defineProps,
    getCurrentInstance,
    onMounted,
    reactive,
    ref,
    toRefs,
  } from 'vue';
  import { listProject } from '@/api/app/project';
  import { deleteCheck, deleteProjectAll } from '@/api/metadata/project';
  import { useRouter } from 'vue-router';
  import DeleteLog from '@/views/submit/metadata/rawData/common/DeleteLog.vue';
  import DeleteConfirm from '@/views/userCenter/data/components/DeleteConfirm.vue';

  const { proxy } = getCurrentInstance();
  const router = useRouter();

  const props = defineProps({
    hasTip: {
      type: Boolean,
      required: false,
    },
  });

  const { hasTip } = props;

  onMounted(() => {
    getDataList();
  });

  /** 响应式数据 */
  const data = reactive({
    tableData: [],
    total: 0,
    queryParams: {
      name: '',
      pageNum: 1,
      pageSize: 10,
      orderByColumn: 'createDate',
      isAsc: 'descending',
    },
    dateRange: [],
    loading: true,
    defaultSort: { prop: 'createDate', order: 'descending' },
  });

  /** 解构 */
  const { tableData, total, queryParams, dateRange, loading, defaultSort } =
    toRefs(data);

  /** 查询列表数据*/
  function getDataList() {
    loading.value = true;
    listProject(proxy.addDateRange(queryParams.value, dateRange.value))
      .then(response => {
        // 将结果赋值给tableData
        tableData.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  /** 排序触发事件 */
  function handleSortChange(column) {
    if (column.order) {
      queryParams.value.orderByColumn = column.prop;
      queryParams.value.isAsc = column.order;
      getDataList();
    }
  }

  function toEdit(projectNo) {
    router.push({ path: `/submit/metadata/project/edit/${projectNo}` });
  }

  let showPwEnterDialog = ref(false);
  let password = ref('');
  let projNo = ref('');
  let deleteCheckResult = ref({});

  /** 删除预检查 */
  function preCheck(projectNo) {
    proxy.$modal.loading(
      proxy.$t('userCenter.data.project.messages.openingDeleteDialog'),
    );
    projNo.value = projectNo;
    deleteCheck(projectNo)
      .then(response => {
        if (response.data.errors && response.data.errors.length !== 0) {
          proxy.$refs['deleteLog'].openLog(response.data.errors);
          return;
        }
        deleteCheckResult.value = response.data;
        showPwEnterDialog.value = true;
        // 验证密码
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  function confirmDelete() {
    proxy.$modal.loading(
      proxy.$t('userCenter.data.project.messages.verifyingAndDeleting'),
    );
    deleteProjectAll({
      projectNo: projNo.value,
      password: password.value,
    })
      .then(() => {
        password.value = '';
        showPwEnterDialog.value = false;
        getDataList();
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }
</script>

<style scoped lang="scss">
  .el-dropdown-link {
    margin-top: 5px;
    cursor: pointer;
    color: var(--el-color-primary);
    display: flex;
    align-items: center;

    &:focus-visible {
      outline: unset;
    }
  }
</style>
