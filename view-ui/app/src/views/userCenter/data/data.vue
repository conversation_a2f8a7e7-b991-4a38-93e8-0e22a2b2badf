<template>
  <el-tabs v-model="dataActiveTab" type="card" class="data-tab">
    <el-tab-pane
      :label="$t('userCenter.data.data.tabs.unarchivedData')"
      name="Unarchived Data"
    >
      <unarchived-data></unarchived-data>
    </el-tab-pane>
    <el-tab-pane
      :label="$t('userCenter.data.data.tabs.archivedRawData')"
      name="Archived Data"
    >
      <archived-raw-data :show-batch-modify="true"></archived-raw-data>
    </el-tab-pane>
    <el-tab-pane
      :label="$t('userCenter.data.data.tabs.archivedAnalysisData')"
      name="Archived Analysis Data"
    >
      <archived-analysis-data
        :show-batch-modify="true"
      ></archived-analysis-data>
    </el-tab-pane>
  </el-tabs>
</template>
<script setup>
  import { ref } from 'vue';
  import UnarchivedData from '@/views/userCenter/data/components/UnarchivedData.vue';
  import ArchivedRawData from '@/views/userCenter/data/components/ArchivedRawData.vue';
  import ArchivedAnalysisData from '@/views/userCenter/data/components/ArchivedAnalysisData.vue';

  const dataActiveTab = ref('Unarchived Data');
</script>

<style scoped lang="scss"></style>
