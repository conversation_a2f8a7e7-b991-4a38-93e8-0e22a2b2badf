<template>
  <div class="d-flex align-items-center mb-1">
    <el-input
      v-model="queryParams.name"
      :placeholder="$t('userCenter.data.run.searchPlaceholder')"
      clearable
      class="w-50"
      @keydown.enter="getDataList"
    />
    <span class="font-600 text-main-color ml-1 mr-1">{{
      $t('userCenter.data.run.period')
    }}</span>
    <el-date-picker
      v-model="dateRange"
      class="hidden-xs-only"
      clearable
      value-format="YYYY-MM-DD"
      type="daterange"
      :range-separator="$t('userCenter.data.run.dateTo')"
      :start-placeholder="$t('userCenter.data.run.startDate')"
      :end-placeholder="$t('userCenter.data.run.endDate')"
    />
    <el-button class="radius-12 ml-1 mr-1" type="primary" @click="getDataList"
      >{{ $t('userCenter.data.run.search') }}
    </el-button>
  </div>
  <el-table
    v-loading="loading"
    :data="tableData"
    style="width: 100%; margin-bottom: 20px"
    :header-cell-style="{
      backgroundColor: '#f2f2f2',
      color: '#333333',
      fontWeight: 700,
    }"
    border
    height="443"
    :default-sort="defaultSort"
    @sort-change="handleSortChange"
  >
    <el-table-column
      prop="runNo"
      :label="$t('userCenter.data.run.columns.id')"
      sortable
      min-width="115"
    >
    </el-table-column>
    <el-table-column
      prop="name"
      :label="$t('userCenter.data.run.columns.name')"
      min-width="180"
      sortable
      show-overflow-tooltip
    />
    <el-table-column
      prop="expNo"
      :label="$t('userCenter.data.run.columns.experimentId')"
      min-width="140"
      sortable
    >
      <template #default="scope">
        <router-link :to="`/experiment/detail/${scope.row.expNo}`">
          <span class="text-primary">{{ scope.row.expNo }}</span>
        </router-link>
      </template>
    </el-table-column>
    <el-table-column
      prop="sapNo"
      :label="$t('userCenter.data.run.columns.sampleId')"
      min-width="120"
      sortable
    >
      <template #default="scope">
        <router-link :to="`/sample/detail/${scope.row.sapNo}`">
          <span class="text-primary">{{ scope.row.sapNo }}</span>
        </router-link>
      </template>
    </el-table-column>
    <el-table-column
      prop="dataNum"
      :label="$t('userCenter.data.run.columns.dataNumber')"
      width="120"
      align="center"
      show-overflow-tooltip
    >
      <template #default="scope">
        <el-tag type="success">{{ scope.row.dataNum }}</el-tag>
      </template>
    </el-table-column>
    <el-table-column
      prop="description"
      :label="$t('userCenter.data.run.columns.description')"
      min-width="150"
      show-overflow-tooltip
    />
    <el-table-column
      prop="createDate"
      :label="$t('userCenter.data.run.columns.uploadDate')"
      min-width="160"
      sortable
      show-overflow-tooltip
    />
    <el-table-column
      prop="visibleStatus"
      :label="$t('userCenter.data.run.columns.status')"
      min-width="130"
      sortable
    >
      <template #default="scope">
        <div class="d-flex align-items-center">
          <el-icon
            v-if="scope.row.visibleStatus === 'Accessible'"
            color="#07BCB4"
          >
            <SuccessFilled />
          </el-icon>
          <el-icon v-else color="#FA8282">
            <CircleCloseFilled />
          </el-icon>
          <div
            class="ml-05"
            :style="{
              color:
                scope.row.visibleStatus === 'Accessible'
                  ? '#07BCB4'
                  : '#FA8282',
            }"
          >
            {{ scope.row.visibleStatus }}
          </div>
        </div>
      </template>
    </el-table-column>

    <el-table-column
      fixed="right"
      :label="$t('userCenter.data.run.columns.operate')"
      width="110"
    >
      <template #header>
        {{ $t('userCenter.data.run.columns.operate') }}
        <el-tooltip
          effect="light"
          raw-content
          :content="`Batch Modify: <a href='${getOriginUrl()}/submit/metadata/rawData'>${getOriginUrl()}/submit/metadata/rawData</a>`"
        >
          <el-icon size="12" color="#3A78E8">
            <InfoFilled />
          </el-icon>
        </el-tooltip>
      </template>
      <template #default="scope">
        <div class="text-center">
          <el-tooltip :content="$t('userCenter.data.run.operations.delete')">
            <svg-icon
              v-if="scope.row.visibleStatus === 'Unaccessible'"
              icon-class="delete"
              class-name="operate-svg"
              @click="preCheck(scope.row.runNo)"
            ></svg-icon>
          </el-tooltip>
        </div>
      </template>
    </el-table-column>
  </el-table>
  <pagination
    v-model:page="queryParams.pageNum"
    v-model:limit="queryParams.pageSize"
    :total="total"
    class="mb-1"
    @pagination="getDataList"
  />
  <delete-log
    ref="deleteLog"
    :curr-type="$t('userCenter.data.run.deleteLog.type')"
  ></delete-log>

  <delete-confirm
    v-model:password="password"
    v-model:show-dialog="showPwEnterDialog"
    :delete-check-result="deleteCheckResult"
    @delete-method="confirmDelete"
  ></delete-confirm>
</template>
<script setup>
  import { getCurrentInstance, onMounted, reactive, ref, toRefs } from 'vue';
  import DeleteConfirm from '@/views/userCenter/data/components/DeleteConfirm.vue';
  import DeleteLog from '@/views/submit/metadata/rawData/common/DeleteLog.vue';
  import { listRun } from '@/api/app/run';
  import { deleteRunAll, runDeleteCheck } from '@/api/metadata/run';
  import { getOriginUrl } from '@/utils/nodeCommon';

  const { proxy } = getCurrentInstance();

  onMounted(() => {
    getDataList();
  });

  /** 响应式数据 */
  const data = reactive({
    tableData: [],
    total: 0,
    queryParams: {
      name: '',
      pageNum: 1,
      pageSize: 10,
      orderByColumn: 'createDate',
      isAsc: 'descending',
    },
    dateRange: [],
    loading: true,
    defaultSort: { prop: 'createDate', order: 'descending' },
  });
  /** 解构 */
  const { tableData, total, queryParams, dateRange, loading, defaultSort } =
    toRefs(data);

  /** 查询列表数据*/
  function getDataList() {
    loading.value = true;
    listRun(proxy.addDateRange(queryParams.value, dateRange.value))
      .then(response => {
        // 将结果赋值给tableData
        tableData.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  /** 排序触发事件 */
  function handleSortChange(column, prop, order) {
    if (column.order) {
      queryParams.value.orderByColumn = column.prop;
      queryParams.value.isAsc = column.order;
      getDataList();
    }
  }

  let showPwEnterDialog = ref(false);
  let password = ref('');
  let runNo = ref('');
  let deleteCheckResult = ref({});

  /** 删除预检查 */
  function preCheck(no) {
    proxy.$modal.loading(
      proxy.$t('userCenter.data.run.messages.openingDeleteDialog'),
    );
    runNo.value = no;
    runDeleteCheck(no)
      .then(response => {
        if (response.data.errors && response.data.errors.length !== 0) {
          proxy.$refs['deleteLog'].openLog(response.data.errors);
          return;
        }
        deleteCheckResult.value = response.data;
        showPwEnterDialog.value = true;
        // 验证密码
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  function confirmDelete() {
    proxy.$modal.loading(
      proxy.$t('userCenter.data.run.messages.verifyingAndDeleting'),
    );
    deleteRunAll({
      runNo: runNo.value,
      password: password.value,
    })
      .then(() => {
        password.value = '';
        showPwEnterDialog.value = false;
        getDataList();
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }
</script>

<style scoped lang="scss"></style>
