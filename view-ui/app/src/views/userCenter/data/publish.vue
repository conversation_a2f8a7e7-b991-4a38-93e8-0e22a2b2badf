<template>
  <div class="d-flex align-items-center mb-1">
    <el-input
      v-model="queryParams.name"
      :placeholder="$t('userCenter.data.publish.searchPlaceholder')"
      clearable
      class="w-50"
      @keydown.enter="getDataList"
    />

    <span class="font-600 text-main-color ml-1 mr-1">{{
      $t('userCenter.data.publish.period')
    }}</span>
    <el-date-picker
      v-model="queryParams.dateRange"
      class="hidden-xs-only"
      clearable
      value-format="YYYY-MM-DD"
      type="daterange"
      :range-separator="$t('userCenter.data.publish.dateTo')"
      :start-placeholder="$t('userCenter.data.publish.startDate')"
      :end-placeholder="$t('userCenter.data.publish.endDate')"
    />
    <el-button class="radius-12 ml-1 mr-1" type="primary" @click="getDataList"
      >{{ $t('userCenter.data.publish.search') }}
    </el-button>
  </div>
  <el-table
    v-loading="loading"
    :data="tableData"
    style="width: 100%; margin-bottom: 20px"
    :header-cell-style="{
      backgroundColor: '#f2f2f2',
      color: '#333333',
      fontWeight: 700,
    }"
    height="440"
    border
  >
    <el-table-column
      prop="articleName"
      :label="$t('userCenter.data.publish.columns.title')"
      min-width="150"
      sortable
      show-overflow-tooltip
    />

    <el-table-column
      prop="publication"
      :label="$t('userCenter.data.publish.columns.journal')"
      sortable
      width="115"
      show-overflow-tooltip
    >
    </el-table-column>

    <el-table-column
      prop="doi"
      sort-by="doi"
      :label="$t('userCenter.data.publish.columns.doi')"
      width="200"
      sortable
      show-overflow-tooltip
    >
      <template #default="scope">
        <a
          target="_blank"
          :href="`https://doi.org/${scope.row.doi}`"
          class="text-primary cursor-pointer"
          >{{ scope.row.doi }}</a
        >
      </template>
    </el-table-column>
    <el-table-column
      prop="pmid"
      :label="$t('userCenter.data.publish.columns.pmid')"
      width="90"
      sortable
      sort-by="pmid"
      show-overflow-tooltip
    >
      <template #default="scope">
        <a
          target="_blank"
          :href="`https://pubmed.ncbi.nlm.nih.gov/${scope.row.pmid}/`"
          class="text-primary cursor-pointer"
        >
          {{ scope.row.pmid }}</a
        >
      </template>
    </el-table-column>
    <el-table-column
      :label="$t('userCenter.data.publish.columns.relatedItems')"
      width="150"
      show-overflow-tooltip
    >
      <template #default="scope">
        {{ scope.row.relatedId.join('; ') }}
      </template>
    </el-table-column>
    <el-table-column
      prop="reference"
      :label="$t('userCenter.data.publish.columns.reference')"
      sortable
      show-overflow-tooltip
    />

    <el-table-column
      prop="createDate"
      :label="$t('userCenter.data.publish.columns.createDate')"
      width="160"
      sortable
      show-overflow-tooltip
    />

    <el-table-column
      :label="$t('userCenter.data.publish.columns.operate')"
      width="85"
      fixed="right"
    >
      <template #default="scope">
        <div class="text-center">
          <el-tooltip :content="$t('userCenter.data.publish.operations.edit')">
            <svg-icon
              icon-class="usercenterEdit"
              class-name="operate-svg"
              @click="editPublish(scope.row.id)"
            ></svg-icon>
          </el-tooltip>
          <el-tooltip
            :content="$t('userCenter.data.publish.operations.delete')"
          >
            <svg-icon
              icon-class="delete"
              class-name="operate-svg"
              @click="deletePub(scope.row.doi)"
            ></svg-icon>
          </el-tooltip>
        </div>
      </template>
    </el-table-column>
  </el-table>

  <EditPublish ref="editPublishRef"></EditPublish>

  <el-button
    class="btn-round-primary"
    icon="Plus"
    round
    style="float: right"
    @click="addPublish"
    >{{ $t('userCenter.data.publish.add') }}
  </el-button>
  <pagination
    v-model:page="queryParams.pageNum"
    v-model:limit="queryParams.pageSize"
    :total="total"
    class="mb-1"
    :auto-scroll="false"
    @pagination="getDataList"
  />
</template>
<script setup>
  import { getCurrentInstance, onMounted, reactive, toRefs } from 'vue';
  import { deletePublish, listPublish } from '@/api/app/publish';
  import { isStrBlank } from '@/utils';
  import EditPublish from '@/components/Publish/editPublish.vue';

  const { proxy } = getCurrentInstance();

  onMounted(() => {
    initData();
  });

  /** 响应式数据 */
  const data = reactive({
    loading: true,
    dataList: [],
    tableData: [],
    total: 0,
    queryParams: {
      name: '',
      dateRange: [],
      pageNum: 1,
      pageSize: 10,
    },
  });

  /** 解构 */
  const { queryParams, dataList, tableData, total, loading } = toRefs(data);

  /** 查询列表数据*/
  function initData() {
    loading.value = true;
    listPublish()
      .then(response => {
        dataList.value = response.data;
        getDataList();
      })
      .finally(() => {
        loading.value = false;
      });
  }

  function getDataList() {
    let list = dataList.value;
    // 根据关键字过滤
    if (!isStrBlank(queryParams.value.name)) {
      const keyword = queryParams.value.name.toLowerCase().trim();
      list = list.filter(
        item =>
          item.articleName.toLowerCase().indexOf(keyword) !== -1 ||
          item.doi.toLowerCase().indexOf(keyword) !== -1 ||
          item.pmid.toLowerCase().indexOf(keyword) !== -1 ||
          item.relatedId.join().toLowerCase().indexOf(keyword) !== -1 ||
          item.publication.toLowerCase().indexOf(keyword) !== -1,
      );
    }

    // 根据时间过滤
    if (
      queryParams.value.dateRange &&
      queryParams.value.dateRange.length === 2
    ) {
      const dateRange = queryParams.value.dateRange;
      const startDate = new Date(dateRange[0]);
      const endDate = new Date(dateRange[1]);

      list = list.filter(obj => {
        const updateDate = new Date(obj.createDate);
        return updateDate >= startDate && updateDate <= endDate;
      });
    }

    // 对list进行分页
    total.value = list?.length || 0;
    if (total.value > 0) {
      let start = (queryParams.value.pageNum - 1) * queryParams.value.pageSize;
      let end =
        start + queryParams.value.pageNum > total.value
          ? total.value
          : start + queryParams.value.pageSize;
      tableData.value = list.slice(start, end);
    } else {
      tableData.value = [];
    }
  }

  function editPublish(publishId) {
    proxy.$refs['editPublishRef'].editPublish(publishId);
  }

  function addPublish() {
    proxy.$refs['editPublishRef'].addPublish();
  }

  function deletePub(doi) {
    proxy.$modal
      .confirm(proxy.$t('userCenter.data.publish.messages.deleteConfirm'))
      .then(() => {
        proxy.$modal.loading(
          proxy.$t('userCenter.data.publish.messages.deleting'),
        );
        deletePublish({ doi })
          .then(() => {
            initData();
          })
          .finally(() => {
            proxy.$modal.closeLoading();
          });
      })
      .catch(() => {});
  }
</script>

<style scoped lang="scss"></style>
