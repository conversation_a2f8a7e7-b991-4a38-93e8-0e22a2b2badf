<template>
  <div class="d-flex align-items-center mb-1">
    <el-input
      v-model="queryParams.name"
      clearable
      :placeholder="
        $t('userCenter.data.components.unarchivedData.searchPlaceholder')
      "
      class="w-50"
      @keyup.enter="getDataList"
    />
    <span class="font-600 text-main-color ml-1 mr-1">{{
      $t('userCenter.data.components.unarchivedData.period')
    }}</span>
    <el-date-picker
      v-model="dateRange"
      class="hidden-xs-only"
      clearable
      value-format="YYYY-MM-DD"
      type="daterange"
      :range-separator="$t('userCenter.data.components.unarchivedData.dateTo')"
      :start-placeholder="
        $t('userCenter.data.components.unarchivedData.startDate')
      "
      :end-placeholder="$t('userCenter.data.components.unarchivedData.endDate')"
    />
    <el-button class="radius-12 ml-1" type="primary" @click="getDataList"
      >{{ $t('userCenter.data.components.unarchivedData.search') }}
    </el-button>
  </div>
  <el-table
    v-loading="loading"
    :data="tableData"
    :row-key="row => row.id"
    style="width: 100%; margin-bottom: 20px"
    :header-cell-style="{
      backgroundColor: '#f2f2f2',
      color: '#333333',
      fontWeight: 700,
    }"
    border
    height="390"
    :default-sort="defaultSort"
    @sort-change="handleSortChange"
    @selection-change="handleSelectionChange"
  >
    <el-table-column type="selection" :reserve-selection="true" width="40" />
    <el-table-column
      prop="datNo"
      :label="$t('userCenter.data.components.unarchivedData.columns.dataId')"
      min-width="115"
      sortable
    />
    <el-table-column
      prop="name"
      :label="$t('userCenter.data.components.unarchivedData.columns.fileName')"
      show-overflow-tooltip
      min-width="140"
      sortable
    />
    <el-table-column
      prop="dataType"
      :label="$t('userCenter.data.components.unarchivedData.columns.dataType')"
      min-width="120"
      sortable
    />
    <el-table-column
      prop="uploadType"
      :label="
        $t('userCenter.data.components.unarchivedData.columns.uploadType')
      "
      min-width="140"
      sortable
    />
    <el-table-column
      prop="createDate"
      :label="
        $t('userCenter.data.components.unarchivedData.columns.uploadDate')
      "
      min-width="160"
      sortable
    />
    <el-table-column
      prop="fileSize"
      :label="$t('userCenter.data.components.unarchivedData.columns.size')"
      min-width="80"
      sortable
    >
      <template #default="scope">
        {{ scope.row.readableFileSize }}
      </template>
    </el-table-column>
    <el-table-column
      fixed="right"
      :label="$t('userCenter.data.components.unarchivedData.columns.operate')"
      width="85"
    >
      <template #default="scope">
        <div class="text-center">
          <el-tooltip
            :content="
              $t('userCenter.data.components.unarchivedData.operations.delete')
            "
          >
            <svg-icon
              icon-class="delete"
              class-name="operate-svg"
              @click="preCheck(scope.row.datNo)"
            ></svg-icon>
          </el-tooltip>
        </div>
      </template>
    </el-table-column>
  </el-table>
  <el-button
    class="btn-round-primary"
    :icon="Download"
    round
    style="float: right"
    @click="handleExport"
    >{{ $t('userCenter.data.components.unarchivedData.export') }}
  </el-button>
  <pagination
    v-model:page="queryParams.pageNum"
    v-model:limit="queryParams.pageSize"
    :total="total"
    class="mb-1"
    @pagination="getDataList"
  />

  <delete-log
    ref="deleteLog"
    :curr-type="$t('userCenter.data.components.unarchivedData.deleteLog.type')"
  ></delete-log>

  <delete-confirm
    v-model:password="password"
    v-model:show-dialog="showPwEnterDialog"
    :delete-check-result="deleteCheckResult"
    @delete-method="confirmDelete"
  ></delete-confirm>
</template>

<script setup>
  import { getCurrentInstance, onMounted, reactive, ref, toRefs } from 'vue';
  import DeleteConfirm from '@/views/userCenter/data/components/DeleteConfirm.vue';
  import DeleteLog from '@/views/submit/metadata/rawData/common/DeleteLog.vue';
  import {
    dataDeleteCheck,
    deleteDataAll,
    listUnarchived,
  } from '@/api/metadata/data';
  import { Download } from '@element-plus/icons-vue';

  const { proxy } = getCurrentInstance();

  onMounted(() => {
    getDataList();
  });

  /** 响应式数据 */
  const data = reactive({
    tableData: [],
    total: 0,
    queryParams: {
      name: '',
      pageNum: 1,
      pageSize: 10,
      orderByColumn: 'createDate',
      isAsc: 'descending',
    },
    dateRange: [],
    loading: true,
    defaultSort: { prop: 'createDate', order: 'descending' },
  });
  /** 解构 */
  const { tableData, total, queryParams, dateRange, loading, defaultSort } =
    toRefs(data);

  /** 查询列表数据*/
  function getDataList() {
    loading.value = true;
    listUnarchived(proxy.addDateRange(queryParams.value, dateRange.value))
      .then(response => {
        // 将结果赋值给tableData
        tableData.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  /** 排序触发事件 */
  function handleSortChange(column, prop, order) {
    if (column.order) {
      queryParams.value.orderByColumn = column.prop;
      queryParams.value.isAsc = column.order;
      getDataList();
    }
  }

  let showPwEnterDialog = ref(false);
  let password = ref('');
  let dataNo = ref('');
  let deleteCheckResult = ref({});

  /** 删除预检查 */
  function preCheck(no) {
    proxy.$modal.loading(
      proxy.$t(
        'userCenter.data.components.unarchivedData.messages.openingDeleteDialog',
      ),
    );
    dataNo.value = no;
    dataDeleteCheck(no)
      .then(response => {
        if (response.data.errors && response.data.errors.length !== 0) {
          proxy.$refs['deleteLog'].openLog(response.data.errors);
          return;
        }
        deleteCheckResult.value = response.data;
        showPwEnterDialog.value = true;
        // 验证密码
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  function confirmDelete() {
    proxy.$modal.loading(
      proxy.$t(
        'userCenter.data.components.unarchivedData.messages.verifyingAndDeleting',
      ),
    );
    deleteDataAll({
      dataNo: dataNo.value,
      password: password.value,
    })
      .then(() => {
        password.value = '';
        showPwEnterDialog.value = false;
        getDataList();
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  let selectedRows = ref([]);

  function handleSelectionChange(selection) {
    selectedRows.value = selection;
  }

  function handleExport() {
    let b = selectedRows.value.length === 0;
    let content = '';
    if (b) {
      content = proxy.$t(
        'userCenter.data.components.unarchivedData.messages.exportAllConfirm',
      );
    } else {
      content = proxy.$t(
        'userCenter.data.components.unarchivedData.messages.exportSelectedConfirm',
      );
    }

    const downloadParams = b
      ? { ...queryParams.value }
      : {
          ...queryParams.value,
          ids: selectedRows.value.map(it => it.id),
        };

    proxy.$modal.confirm(content).then(() => {
      proxy.download(
        '/upload/data/export/unarchived',
        downloadParams,
        `unarchived_${new Date().getTime()}.xlsx`,
      );
    });
  }
</script>

<style scoped lang="scss"></style>
