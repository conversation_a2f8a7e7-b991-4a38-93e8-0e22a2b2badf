<template>
  <div class="archived-data mb-1">
    <div class="d-flex align-items-center mb-1">
      <el-input
        v-model="queryParams.name"
        clearable
        :placeholder="
          $t('userCenter.data.components.archivedRawData.searchPlaceholder')
        "
        class="w-50"
        @keyup.enter="getDataList"
      />
      <span class="font-600 text-main-color ml-1 mr-1">{{
        $t('userCenter.data.components.archivedRawData.period')
      }}</span>
      <el-date-picker
        v-model="dateRange"
        class="hidden-xs-only"
        clearable
        value-format="YYYY-MM-DD"
        type="daterange"
        :range-separator="
          $t('userCenter.data.components.archivedRawData.dateTo')
        "
        :start-placeholder="
          $t('userCenter.data.components.archivedRawData.startDate')
        "
        :end-placeholder="
          $t('userCenter.data.components.archivedRawData.endDate')
        "
      />
      <el-button class="radius-12 ml-1" type="primary" @click="getDataList"
        >{{ $t('userCenter.data.components.archivedRawData.search') }}
      </el-button>
    </div>
    <el-table
      v-loading="loading"
      :data="tableData"
      style="width: 100%; margin-bottom: 20px"
      :header-cell-style="{
        backgroundColor: '#f2f2f2',
        color: '#333333',
        fontWeight: 700,
      }"
      height="390"
      border
      :default-sort="defaultSort"
      @sort-change="handleSortChange"
    >
      <el-table-column
        prop="datNo"
        :label="$t('userCenter.data.components.archivedRawData.columns.dataId')"
        width="115"
        sortable
      />
      <el-table-column
        prop="name"
        :label="
          $t('userCenter.data.components.archivedRawData.columns.fileName')
        "
        min-width="130"
        show-overflow-tooltip
        sortable
      />
      <el-table-column
        prop="projNo"
        :label="
          $t('userCenter.data.components.archivedRawData.columns.project')
        "
        width="115"
      >
        <template #default="scope">
          <router-link :to="`/project/detail/${scope.row.projNo}`">
            <span class="text-primary">{{ scope.row.projNo }}</span>
          </router-link>
        </template>
      </el-table-column>
      <el-table-column
        prop="expNo"
        :label="
          $t('userCenter.data.components.archivedRawData.columns.experiment')
        "
        width="115"
      >
        <template #default="scope">
          <router-link :to="`/experiment/detail/${scope.row.expNo}`">
            <span class="text-primary">{{ scope.row.expNo }}</span>
          </router-link>
        </template>
      </el-table-column>
      <el-table-column
        prop="sapNo"
        :label="$t('userCenter.data.components.archivedRawData.columns.sample')"
        width="115"
      >
        <template #default="scope">
          <router-link :to="`/sample/detail/${scope.row.sapNo}`">
            <span class="text-primary">{{ scope.row.sapNo }}</span>
          </router-link>
        </template>
      </el-table-column>
      <el-table-column
        sortable
        prop="security"
        :label="
          $t('userCenter.data.components.archivedRawData.columns.security')
        "
        min-width="100"
      >
        <template #default="scope">
          {{ scope.row.security }}
        </template>
      </el-table-column>
      <el-table-column
        prop="fileSize"
        :label="$t('userCenter.data.components.archivedRawData.columns.size')"
        min-width="100"
        sortable
      >
        <template #default="scope">
          {{ scope.row.readableFileSize }}
        </template>
      </el-table-column>
      <el-table-column
        prop="dataType"
        :label="
          $t('userCenter.data.components.archivedRawData.columns.dataType')
        "
        width="120"
        sortable
      />
      <el-table-column
        prop="createDate"
        :label="
          $t('userCenter.data.components.archivedRawData.columns.uploadDate')
        "
        width="155"
        sortable
      />
      <el-table-column
        fixed="right"
        :label="
          $t('userCenter.data.components.archivedRawData.columns.operate')
        "
        width="110"
        align="left"
      >
        <template v-if="props.showBatchModify" #header>
          {{ $t('userCenter.data.components.archivedRawData.columns.operate') }}
          <el-tooltip
            effect="light"
            raw-content
            :content="`Batch Modify: <a href='${getOriginUrl()}/submit/metadata/rawData'>${getOriginUrl()}/submit/metadata/rawData</a>`"
          >
            <el-icon size="12" color="#3A78E8">
              <InfoFilled />
            </el-icon>
          </el-tooltip>
        </template>
        <template #default="scope">
          <div class="text-align-left">
            <el-tooltip
              :content="
                $t(
                  'userCenter.data.components.archivedRawData.operations.htmlDownload',
                )
              "
            >
              <img
                src="@/assets/images/btn-ico-h.png"
                alt=""
                class="operate-svg"
                @click="showHttpDownloadModal(scope.row)"
              />
            </el-tooltip>
            <el-tooltip
              :content="
                $t(
                  'userCenter.data.components.archivedRawData.operations.sftpDownload',
                )
              "
            >
              <img
                src="@/assets/images/btn-ico-f.png"
                alt=""
                class="operate-svg"
                @click="showSftpDownloadModal(scope.row)"
              />
            </el-tooltip>
            <el-tooltip
              :content="
                $t(
                  'userCenter.data.components.archivedRawData.operations.delete',
                )
              "
            >
              <svg-icon
                v-if="scope.row.security === 'Private'"
                icon-class="delete"
                class-name="operate-svg"
                @click="preCheck(scope.row.datNo)"
              ></svg-icon>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      class="mb-1"
      @pagination="getDataList"
    />
    <delete-log
      ref="deleteLog"
      :curr-type="
        $t('userCenter.data.components.archivedRawData.deleteLog.type')
      "
    ></delete-log>

    <delete-confirm
      v-model:password="password"
      v-model:show-dialog="showPwEnterDialog"
      :delete-check-result="deleteCheckResult"
      @delete-method="confirmDelete"
    ></delete-confirm>
    <http-download-dialog ref="httpDownloadDialog"></http-download-dialog>
    <sftp-download-dialog ref="sftpDownloadDialog"></sftp-download-dialog>
  </div>
</template>
<script setup>
  import { getCurrentInstance, onMounted, reactive, ref, toRefs } from 'vue';
  import { listRawData } from '@/api/app/data';
  import { dataDeleteCheck, deleteDataAll } from '@/api/metadata/data';
  import DeleteLog from '@/views/submit/metadata/rawData/common/DeleteLog.vue';
  import DeleteConfirm from '@/views/userCenter/data/components/DeleteConfirm.vue';
  import HttpDownloadDialog from '@/views/browse/detail/components/HttpDownloadDialog.vue';
  import SftpDownloadDialog from '@/views/browse/detail/components/SftpDownloadDialog.vue';
  import { getOriginUrl } from '@/utils/nodeCommon';

  const { proxy } = getCurrentInstance();

  onMounted(() => {
    getDataList();
  });
  const props = defineProps({
    showBatchModify: {
      type: Boolean,
      required: false,
      default: false,
    },
  });

  /** 响应式数据 */
  const data = reactive({
    tableData: [],
    total: 0,
    queryParams: {
      name: '',
      pageNum: 1,
      pageSize: 10,
      orderByColumn: 'createDate',
      isAsc: 'descending',
    },
    dateRange: [],
    loading: true,
    defaultSort: { prop: 'createDate', order: 'descending' },
  });
  /** 解构 */
  const { tableData, total, queryParams, dateRange, loading, defaultSort } =
    toRefs(data);

  /** 查询列表数据*/
  function getDataList() {
    loading.value = true;
    listRawData(proxy.addDateRange(queryParams.value, dateRange.value))
      .then(response => {
        // 将结果赋值给tableData
        tableData.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  /** 排序触发事件 */
  function handleSortChange(column, prop, order) {
    if (column.order) {
      queryParams.value.orderByColumn = column.prop;
      queryParams.value.isAsc = column.order;
      getDataList();
    }
  }

  let showPwEnterDialog = ref(false);
  let password = ref('');
  let dataNo = ref('');
  let deleteCheckResult = ref({});

  /** 删除预检查 */
  function preCheck(no) {
    proxy.$modal.loading(
      proxy.$t(
        'userCenter.data.components.archivedRawData.messages.openingDeleteDialog',
      ),
    );
    dataNo.value = no;
    dataDeleteCheck(no)
      .then(response => {
        if (response.data.errors && response.data.errors.length !== 0) {
          proxy.$refs['deleteLog'].openLog(response.data.errors);
          return;
        }
        deleteCheckResult.value = response.data;
        showPwEnterDialog.value = true;
        // 验证密码
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  function confirmDelete() {
    proxy.$modal.loading(
      proxy.$t(
        'userCenter.data.components.archivedRawData.messages.verifyingAndDeleting',
      ),
    );
    deleteDataAll({
      dataNo: dataNo.value,
      password: password.value,
    })
      .then(() => {
        password.value = '';
        showPwEnterDialog.value = false;
        getDataList();
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  /** http下载 */
  function showHttpDownloadModal(row) {
    proxy.$refs['httpDownloadDialog'].showHttpDownloadModal(row);
  }

  /** sftp下载 */
  function showSftpDownloadModal(row) {
    proxy.$refs['sftpDownloadDialog'].showSftpDownloadModal(row);
  }
</script>

<style scoped lang="scss"></style>
