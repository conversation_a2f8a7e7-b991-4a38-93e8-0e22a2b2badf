<template>
  <div>
    <el-tabs
      v-model="activeTab"
      type="card"
      class="data-tab"
      @tab-change="tabChange"
    >
      <el-tab-pane
        :label="$t('userCenter.dataActivity.tabs.viewsOfData')"
        name="visits"
      >
        <div class="d-flex align-items-center mb-05 mr-1 justify-end">
          <span class="font-600 text-main-color ml-1 mr-1">{{
            $t('userCenter.dataActivity.country')
          }}</span>
          <el-select
            v-model="country"
            clearable
            :placeholder="$t('userCenter.dataActivity.select')"
            style="width: 250px"
          >
            <el-option
              v-for="item in countryOpt"
              :key="`country-${item}`"
              :label="item"
              :value="item"
            />
          </el-select>
        </div>

        <BarChart
          :key="'viewsStackBarKey' + idx"
          stack-bar-i-d="viewsStackBar"
          :country="country"
          :visits="visits"
        ></BarChart>
      </el-tab-pane>

      <el-tab-pane
        :label="$t('userCenter.dataActivity.tabs.downloadOfData')"
        name="download"
      >
        <div class="d-flex align-items-center mb-05 mr-1 justify-end">
          <span class="font-600 text-main-color ml-1 mr-1">{{
            $t('userCenter.dataActivity.country')
          }}</span>
          <el-select
            v-model="country"
            clearable
            :placeholder="$t('userCenter.dataActivity.select')"
            style="width: 250px"
          >
            <el-option
              v-for="item in countryOpt"
              :key="`country-${item}`"
              :label="item"
              :value="item"
            />
          </el-select>
        </div>

        <BarChart
          :key="'downloadStackBarKey' + idx"
          stack-bar-i-d="downloadStackBar"
          :country="country"
          :visits="visits"
        ></BarChart>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
  import { onMounted, ref } from 'vue';
  import { getMyDataVisitsCountry } from '@/api/statistics';
  import BarChart from './chart/barChart.vue';

  const activeTab = ref('visits');

  const visits = ref(activeTab.value === 'visits');
  const idx = ref(1);

  const country = ref('');
  const countryOpt = ref([]);

  onMounted(() => {
    initCountry();
  });

  function tabChange() {
    country.value = '';
    countryOpt.value = [];

    idx.value++;

    visits.value = activeTab.value === 'visits';
    initCountry();
  }

  function initCountry() {
    getMyDataVisitsCountry({
      visits: visits.value,
    }).then(res => {
      if (res.data) {
        countryOpt.value = res.data;
      }
    });
  }
</script>

<style lang="scss" scoped>
  .data-tab {
    :deep(.el-tabs__item) {
      background-color: #ffffff !important;
      border: 1px solid #409eff !important;
      height: 30px;
      border-radius: 0;
      color: #666666;
      &:first-child {
        border-radius: 12px 0 0 12px !important;
        border-right: 0;
      }
      &:last-child {
        border-radius: 0 12px 12px 0 !important;
      }
      &.is-active {
        background-color: #ebf2fd !important;
        color: #409eff !important;
      }
    }
    :deep(.el-tabs__header) {
      margin-bottom: 0px !important;
      margin-top: 8px !important;
    }
  }
  :deep(.el-radio-group .el-radio__label) {
    font-size: 14px !important;
  }
</style>
