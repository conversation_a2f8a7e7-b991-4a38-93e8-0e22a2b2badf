<template>
  <div
    :id="stackBarID"
    v-loading="loading"
    element-loading-text="loading"
    style="width: 100%; height: 502px"
    class="bg-gray radius-12 mb-1 pt-10"
  ></div>
</template>

<script setup>
  import * as echarts from 'echarts';
  import { defineProps, nextTick, onMounted, ref, watch } from 'vue';
  import { getMyDataVisits } from '@/api/statistics';

  const props = defineProps({
    stackBarID: {
      type: String,
      required: true,
    },
    country: {
      type: String,
      required: false,
    },
    visits: {
      type: Boolean,
      required: true,
    },
  });

  const loading = ref(false);

  const echartsInit = () => {
    loading.value = true;
    getMyDataVisits({
      visits: props.visits,
      country: props.country,
    })
      .then(res => {
        if (res.data) {
          renderChart(res.data);
        }
      })
      .finally(() => {
        loading.value = false;
      });
  };

  watch(
    () => props.country,
    () => {
      echartsInit();
    },
  );

  watch(
    () => props.visits,
    () => {
      echartsInit();
    },
  );

  const renderChart = data => {
    const stackBarChart = echarts.init(
      document.getElementById(props.stackBarID),
    );
    const stackBarOpt = {
      color: ['#1981F4', '#37B8F8', '#2ADAC9', '#FFD857', '#FEA52B', '#7287BA'],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        confine: true,
      },
      legend: {
        data: ['Projects', 'Experiments', 'Samples', 'Analysis', 'Total data'],
      },
      grid: {
        top: '12%',
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true,
      },
      dataZoom: [
        {
          show: true,
          realtime: true,
          start: 0,
          end: 100,
        },
        {
          type: 'inside',
          realtime: true,
          start: 0,
          end: 100,
        },
      ],
      xAxis: [
        {
          type: 'category',
          data: data.map(item => item.month),
        },
      ],
      yAxis: [
        {
          type: 'value',
          axisLine: {
            show: true,
          },
          splitLine: {
            show: false,
          },
        },
      ],
      series: [
        {
          name: 'Projects',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          data: data.map(item => item.projects),
        },
        {
          name: 'Experiments',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          data: data.map(item => item.experiments),
        },
        {
          name: 'Samples',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          data: data.map(item => item.samples),
          barMaxWidth: 22,
        },
        {
          name: 'Analysis',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          data: data.map(item => item.analysis),
          barMaxWidth: 22,
        },
        {
          name: 'Total data',
          data: data.map(item => item.total),
          type: 'line',
        },
      ],
    };

    if (!props.visits) {
      stackBarOpt.legend.data = [
        'Projects',
        'Experiments',
        'Samples',
        'Analysis',
        'Data',
        'Total data',
      ];
      stackBarOpt.series.push({
        name: 'Data',
        type: 'bar',
        stack: 'Ad',
        emphasis: {
          focus: 'series',
        },
        data: data.map(item => item.data),
        barMaxWidth: 22,
      });
    }

    stackBarChart.setOption(stackBarOpt);
    stackBarChart.resize();

    window.onresize = function () {
      stackBarChart.resize();
    };
  };

  onMounted(() => {
    nextTick(() => {
      echartsInit();
    });
  });
</script>

<style lang="scss" scoped></style>
