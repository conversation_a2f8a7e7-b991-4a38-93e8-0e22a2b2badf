<template>
  <div class="page">
    <div v-if="showContent" class="container-fluid">
      <Breadcrumb
        :bread-item="
          (userData ? $t('browse.detail.experiment.breadcrumb.my') + ' ' : '') +
          $t('browse.detail.experiment.breadcrumb.title')
        "
      />
      <el-row :gutter="15" class="mt-1 row-gap-15">
        <el-col :span="18" :xs="24" :md="18">
          <div ref="leftHeight">
            <general-info
              :key="'general-info-key-' + generalInfoKey"
              v-loading="generalInfoLoading"
              :general-info="genInfo"
              :creator="creator"
              :visible-status="visibleStatus"
              :publications="publish"
              :related-links="relatedLinks"
              type="experiment"
              :type-id="no"
            ></general-info>
            <attributes
              v-if="attrs.length !== 0"
              v-loading="generalInfoLoading"
              :data="attrs"
            ></attributes>
          </div>
        </el-col>
        <el-col :span="6" :xs="24" :md="6" class="hidden-xs-only">
          <association v-if="showAssociation" :style="rightHeight" />
        </el-col>
      </el-row>
      <stat-detail
        v-loading="statDetailLoading"
        :data="statDetailData"
      ></stat-detail>
      <related-data-list
        ref="dataListRef"
        :key="'exp-data-list-key-' + dataListKey"
        :columns="columns"
        :curr-id="currId"
        :type="'experiment'"
      ></related-data-list>
      <related-data-qc-info-list
        :key="'qc-list-key-' + dataListKey"
        :curr-id="currId"
        :type="'experiment'"
      ></related-data-qc-info-list>
      <related-analysis-list
        v-if="analysisList.length > 0"
        v-loading="analListLoading"
        :data="analysisList"
      />
      <author-info
        v-loading="authorInfoLoading"
        :data="authorData"
      ></author-info>
    </div>
    <div v-if="!showContent" class="container-fluid">
      <div class="card mt-1">
        <h3 class="text-main-color mb-0">
          {{ $t('browse.detail.experiment.error.pageTitle') }}
        </h3>
        <el-result
          icon="error"
          :title="$t('browse.detail.experiment.error.title')"
        >
          <template #sub-title>
            <div class="font-600">
              {{ errorContent }}
            </div>
          </template>
        </el-result>
      </div>
    </div>
  </div>
</template>

<script setup>
  import Breadcrumb from '@/components/breadcrumb.vue';
  import GeneralInfo from '@/views/browse/detail/components/GeneralInfo.vue';
  import StatDetail from '@/views/browse/detail/components/StatDetail.vue';
  import AuthorInfo from '@/views/browse/detail/components/AuthorInfo.vue';
  import RelatedAnalysisList from '@/views/browse/detail/components/RelatedAnalysisList.vue';
  import RelatedDataList from '@/views/browse/detail/components/RelatedDataList.vue';
  import RelatedDataQcInfoList from '@/views/browse/detail/components/RelatedDataQcInfoList.vue';

  import { computed, getCurrentInstance, onMounted, reactive, ref } from 'vue';
  import {
    checkExperimentPermission,
    getAuthorInfo,
    getExperimentGeneralInfo,
    getRelatedAnalysis,
    getStatDetail,
  } from '@/api/app/experiment';
  import Association from '@/views/browse/detail/components/Association.vue';
  import Attributes from '@/views/browse/detail/components/attributes.vue';
  import useUserStore from '@/store/modules/user';

  import { storeToRefs } from 'pinia';
  import { isTokenAccess } from '@/utils/nodeCommon';

  const userStore = useUserStore();
  const { member } = storeToRefs(userStore);

  const { proxy } = getCurrentInstance();

  const rightHeight = ref({
    overflowY: 'auto',
    height: '300px',
  });

  let showContent = ref(true);
  let errorContent = ref('');
  let showAssociation = ref(false);

  let no = proxy.$route.params.id;
  let currId = ref(no);
  onMounted(() => {
    checkExperimentPermission(no).then(response => {
      if (response.msg) {
        errorContent.value = response.msg;
        showContent.value = false;
      } else {
        loadGeneralInfo();
        loadStatDetail();
        // loadDataList();
        loadRelatedAnalysisList();
        loadAuthorInfo();
        showAssociation.value = true;
      }
    });
  });

  const userData = computed(() => {
    return creator.value === member.value.id || isTokenAccess();
  });

  /** 加载基本信息 */
  const genInfo = reactive([]);
  const publish = ref([]);
  const creator = ref('');
  const visibleStatus = ref('');
  const relatedLinks = ref([]);
  const generalInfoKey = ref(0);
  const attrsKey = ref(0);
  const generalInfoLoading = ref(false);
  const attrs = ref([]);

  function loadGeneralInfo() {
    generalInfoLoading.value = true;
    getExperimentGeneralInfo(no)
      .then(response => {
        currId.value = response.data.expNo;
        dataListKey.value++;
        genInfo.push({
          label: proxy.$t('browse.detail.experiment.generalInfo.experimentId'),
          value: response.data.expNo,
        });
        genInfo.push({
          label: proxy.$t(
            'browse.detail.experiment.generalInfo.experimentType',
          ),
          value: response.data.expType,
        });
        genInfo.push({
          label: proxy.$t(
            'browse.detail.experiment.generalInfo.experimentName',
          ),
          value: response.data.name,
        });
        genInfo.push({
          label: proxy.$t('browse.detail.experiment.generalInfo.projectNo'),
          value: response.data.projectNo,
        });
        genInfo.push({
          label: proxy.$t('browse.detail.experiment.generalInfo.description'),
          value: response.data.description,
        });
        // 历史usedIds
        if (response.data?.usedIds) {
          const joinedString = response.data?.usedIds.join(';');
          genInfo.push({
            label: proxy.$t('browse.detail.experiment.generalInfo.usedIds'),
            value: joinedString,
          });
        }
        genInfo.push({
          label: proxy.$t('browse.detail.experiment.generalInfo.protocol'),
          value: response.data.protocol,
        });
        publish.value = response.data.publishes;
        creator.value = response.data.creator;
        visibleStatus.value = response.data.visibleStatus;
        relatedLinks.value = response.data.relatedLinks;
        attrs.value = [response.data.attributes];
      })
      .finally(() => {
        generalInfoKey.value++;
        attrsKey.value++;
        generalInfoLoading.value = false;
        const divHeight = proxy.$refs['leftHeight'].clientHeight;
        rightHeight.value.height = divHeight - 100 + 'px';
      });
  }

  /** 加载统计信息 */
  let statDetailData = reactive([]);
  let statDetailLoading = ref(false);

  function loadStatDetail() {
    statDetailLoading.value = true;
    getStatDetail(no)
      .then(response => {
        statDetailData.push({
          title: proxy.$t('browse.detail.experiment.statDetail.organism'),
          data: response.data.sapStats,
        });
        statDetailData.push({
          title: proxy.$t('browse.detail.experiment.statDetail.fileTypes'),
          data: response.data.dataStats,
        });
      })
      .finally(() => {
        statDetailLoading.value = false;
      });
  }

  /** 加载data list */
  let dataListKey = ref(0);
  // 列显隐信息
  const columns = ref([
    {
      prop: 'projNo',
      minWidth: 120,
      label: proxy.$t('browse.detail.experiment.columns.projectId'),
      visible: true,
    },
    {
      prop: 'projName',
      minWidth: 200,
      label: proxy.$t('browse.detail.experiment.columns.projectName'),
      visible: true,
    },
    {
      prop: 'projDesc',
      minWidth: 170,
      label: proxy.$t('browse.detail.experiment.columns.projectDescription'),
      visible: false,
      notSort: true,
    },
    {
      prop: 'sapNo',
      minWidth: 120,
      label: proxy.$t('browse.detail.experiment.columns.sampleId'),
      visible: true,
    },
    {
      prop: 'sapName',
      minWidth: 150,
      label: proxy.$t('browse.detail.experiment.columns.sampleName'),
      visible: false,
    },
    {
      prop: 'sapType',
      minWidth: 170,
      label: proxy.$t('browse.detail.experiment.columns.sampleType'),
      visible: true,
    },
    {
      prop: 'sapDesc',
      minWidth: 190,
      label: proxy.$t('browse.detail.experiment.columns.sampleDescription'),
      visible: false,
      notSort: true,
    },
    {
      prop: 'runNo',
      minWidth: 120,
      label: proxy.$t('browse.detail.experiment.columns.runId'),
      visible: true,
    },
    {
      prop: 'runName',
      minWidth: 170,
      label: proxy.$t('browse.detail.experiment.columns.runName'),
      visible: true,
    },
    {
      prop: 'datNo',
      minWidth: 120,
      label: proxy.$t('browse.detail.experiment.columns.dataId'),
      visible: true,
    },
    {
      prop: 'name',
      minWidth: 170,
      label: proxy.$t('browse.detail.experiment.columns.dataName'),
      visible: true,
    },
    {
      prop: 'security',
      minWidth: 140,
      label: proxy.$t('browse.detail.experiment.columns.dataSecurity'),
      visible: true,
    },
    {
      prop: 'readableFileSize',
      minWidth: 110,
      label: proxy.$t('browse.detail.experiment.columns.dataSize'),
      visible: false,
    },
    {
      prop: 'uploadTime',
      minWidth: 170,
      label: proxy.$t('browse.detail.experiment.columns.dataUploadTime'),
      visible: false,
    },
  ]);

  /** 加载相关联的analysis */
  let analysisList = reactive([]);
  let analListLoading = ref(false);

  function loadRelatedAnalysisList() {
    analListLoading.value = true;
    getRelatedAnalysis(no)
      .then(response => {
        analysisList.push(...response.data);
      })
      .finally(() => {
        analListLoading.value = false;
      });
  }

  /** 加载用户信息 */
  let authorData = ref({
    createDate: undefined,
    updateDate: undefined,
    submitter: {
      firstName: '',
      middleName: '',
      lastName: '',
      orgName: '',
    },
  });
  let authorInfoLoading = ref(false);

  function loadAuthorInfo() {
    authorInfoLoading.value = true;
    getAuthorInfo(no)
      .then(response => {
        authorData.value = response.data;
      })
      .finally(() => {
        authorInfoLoading.value = false;
      });
  }
</script>

<style lang="scss" scoped>
  :deep(.el-tree-node__content):hover {
    background-color: #f9f9f9;
  }

  :deep(.el-tree-node__content) {
    //justify-content: center;
    border-radius: 12px;
    padding: 16px 0;
  }

  //:deep(.el-tree-node__children) {
  //  .el-tree-node:nth-child(odd) {
  //    .el-tree-node__content {
  //      background-color: #f9f9f9;
  //    }
  //  }
  //}
</style>
