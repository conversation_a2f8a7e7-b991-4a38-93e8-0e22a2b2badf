<template>
  <div class="page">
    <div v-if="showContent" class="container-fluid">
      <Breadcrumb
        :bread-item="
          (userData ? $t('browse.detail.analysis.breadcrumb.my') + ' ' : '') +
          $t('browse.detail.analysis.breadcrumb.title')
        "
      />
      <el-row :gutter="15" class="mt-1 row-gap-15">
        <el-col :span="18" :xs="24" :md="18">
          <general-info
            :key="'analysis-general-info-' + generalInfoKey"
            v-loading="loading"
            style="min-height: 230px"
            :general-info="genInfo"
            :creator="creator"
            :visible-status="visibleStatus"
            :publications="publish"
            type="analysis"
            :type-id="no"
          ></general-info>
        </el-col>
        <el-col :span="6" :xs="24" :md="6">
          <total :data="totalData" :show-tip="userData" />
        </el-col>
      </el-row>
      <div v-if="pipelineData" class="card mt-1">
        <span class="text-main-color font-600 font-16">{{
          $t('browse.detail.analysis.pipeline.title')
        }}</span>
        <el-divider class="mt-05 mb-1"></el-divider>
        <div class="item pipeline">
          <span class="label">{{
            $t('browse.detail.analysis.pipeline.label')
          }}</span>
          <div class="w-100">
            <el-steps direction="vertical" finish-status="success">
              <el-step
                v-for="(item, idx) in pipelineData"
                :key="'pipelineData-' + idx"
              >
                <template #title>
                  <el-table
                    :data="item"
                    :header-cell-style="{
                      backgroundColor: '#EDF3FD',
                      color: '#333333',
                      fontWeight: 700,
                    }"
                    border
                  >
                    <el-table-column
                      prop="program"
                      :label="
                        $t('browse.detail.analysis.pipeline.columns.program')
                      "
                    >
                    </el-table-column>
                    <el-table-column
                      prop="version"
                      :label="
                        $t('browse.detail.analysis.pipeline.columns.version')
                      "
                    />
                    <el-table-column
                      prop="note"
                      :label="
                        $t('browse.detail.analysis.pipeline.columns.notes')
                      "
                    />
                    <el-table-column
                      prop="link"
                      :label="
                        $t('browse.detail.analysis.pipeline.columns.link')
                      "
                    >
                      <template #default="scope">
                        <a
                          :href="scope.row.link"
                          class="text-primary"
                          target="_blank"
                        >
                          {{ scope.row.link }}
                        </a>
                      </template>
                    </el-table-column>
                  </el-table>
                  <h4 class="text-secondary-color">
                    {{ $t('browse.detail.analysis.pipeline.output.title') }}
                  </h4>
                  <el-table
                    :key="'pipelineTable-' + idx"
                    :data="item[0]?.outputData"
                    :header-cell-style="{
                      backgroundColor: '#F7F7F7',
                      color: '#333333',
                      fontWeight: 700,
                    }"
                    max-height="240"
                    tooltip-effect="light"
                    border
                  >
                    <el-table-column
                      prop="datNo"
                      :label="
                        $t(
                          'browse.detail.analysis.pipeline.output.columns.dataId',
                        )
                      "
                      sortable
                      width="120"
                    >
                    </el-table-column>
                    <el-table-column
                      prop="name"
                      :label="
                        $t(
                          'browse.detail.analysis.pipeline.output.columns.dataName',
                        )
                      "
                      sortable
                      show-overflow-tooltip
                    />
                    <el-table-column
                      prop="dataType"
                      :label="
                        $t(
                          'browse.detail.analysis.pipeline.output.columns.dataType',
                        )
                      "
                      width="120"
                      sortable
                    />
                    <el-table-column
                      prop="uploadTime"
                      :label="
                        $t(
                          'browse.detail.analysis.pipeline.output.columns.uploadTime',
                        )
                      "
                      sortable
                      width="185"
                    />

                    <el-table-column
                      prop="security"
                      width="150"
                      :label="
                        $t(
                          'browse.detail.analysis.pipeline.output.columns.security',
                        )
                      "
                    />
                    <el-table-column
                      show-overflow-tooltip
                      prop="md5"
                      :label="
                        $t('browse.detail.analysis.pipeline.output.columns.md5')
                      "
                      width="280"
                    />
                    <el-table-column
                      :label="
                        $t(
                          'browse.detail.analysis.pipeline.output.columns.operate',
                        )
                      "
                      width="110"
                    >
                      <template #default="scope">
                        <div
                          v-if="scope.row.accessible"
                          class="download-btn text-center"
                        >
                          <el-tooltip
                            :content="
                              $t(
                                'browse.detail.analysis.pipeline.output.tooltips.htmlDownload',
                              )
                            "
                          >
                            <img
                              src="@/assets/images/btn-ico-h.png"
                              alt=""
                              class="download mr-05"
                              @click="showHttpDownloadModal(scope.row)"
                            />
                          </el-tooltip>
                          <el-tooltip
                            :content="
                              $t(
                                'browse.detail.analysis.pipeline.output.tooltips.sftpDownload',
                              )
                            "
                          >
                            <img
                              src="@/assets/images/btn-ico-f.png"
                              alt=""
                              class="download"
                              @click="showSftpDownloadModal(scope.row)"
                            />
                          </el-tooltip>
                        </div>
                        <div v-else class="download-btn text-center">
                          <img
                            src="@/assets/images/btn-key.png"
                            alt=""
                            class="download mr-05"
                            @click="openRequestToDialog(scope.row)"
                          />
                        </div>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-divider></el-divider>
                </template>
              </el-step>
            </el-steps>
          </div>
        </div>
      </div>
      <div class="card mt-1">
        <span class="text-main-color font-600 font-16">{{
          $t('browse.detail.analysis.target.title')
        }}</span>
        <el-divider class="mt-05 mb-1"></el-divider>
        <div
          v-for="(it, index) in analysisTargetV1"
          :key="it.type + index"
          class="item align-items-center mt-1"
        >
          <span class="label">
            <span class="font-600 mr-1">{{
              $t('browse.detail.analysis.target.label')
            }}</span>

            <el-tag effect="light" round>
              {{ index + 1 }}
            </el-tag>
          </span>
          <div class="bg-gray d-flex">
            <p class="text-secondary-color font-14 font-600 mr-1">
              {{ it.type.charAt(0).toUpperCase() + it.type.slice(1) }}:
            </p>
            <div v-if="it.type === 'run' || it.type === 'data'">
              <el-tag
                v-for="tag in it.nos"
                :key="tag"
                round
                class="mr-1"
                style="cursor: default"
              >
                {{ tag }}
              </el-tag>
            </div>
            <div v-else>
              <el-tag v-for="tag in it.nos" :key="tag" round class="mr-1">
                <router-link
                  target="_blank"
                  :to="`/${it.type.toLowerCase()}/detail/${tag}`"
                  >{{ tag }}
                </router-link>
              </el-tag>
            </div>
          </div>
        </div>
        <!--自定义Target-->
        <div
          v-for="(it, index) in analysisCustomTarget"
          :key="'analysisCustomTarget' + index"
          class="item align-items-center mt-1"
        >
          <span class="label">
            <span class="font-600 mr-1">{{
              $t('browse.detail.analysis.target.otherTarget')
            }}</span>

            <el-tag effect="light" round>
              {{ index + 1 + analysisTargetV1.length }}
            </el-tag>
          </span>
          <div class="bg-gray d-flex">
            <p class="text-secondary-color font-600 font-14 mr-1">
              {{ $text(it.name) }}:
            </p>
            <div>
              <a target="_blank" :href="it.link" class="text-primary ml-1">{{
                $text(it.link)
              }}</a>
            </div>
          </div>
        </div>
      </div>
      <related-data-list
        ref="dataListRef"
        :key="'ana-data-list-key-' + dataListKey"
        :columns="columns"
        :curr-id="currId"
        :type="'analysis'"
      />
      <related-analysis-list
        v-if="analysisList.length > 0"
        v-loading="analListLoading"
        :data="analysisList"
      />
      <author-info
        v-loading="authorInfoLoading"
        :data="authorData"
      ></author-info>
    </div>
    <div v-if="!showContent" class="container-fluid">
      <div class="card mt-1">
        <el-result
          icon="error"
          :title="$t('browse.detail.analysis.error.title')"
        >
          <template #sub-title>
            <div class="font-600">
              {{ errorContent }}
            </div>
          </template>
        </el-result>
      </div>
    </div>

    <http-download-dialog ref="httpDownloadDialog"></http-download-dialog>
    <sftp-download-dialog ref="sftpDownloadDialog"></sftp-download-dialog>
    <request-to ref="requestToRef"></request-to>
  </div>
</template>

<script setup>
  import Breadcrumb from '@/components/breadcrumb.vue';
  import GeneralInfo from '@/views/browse/detail/components/GeneralInfo.vue';
  import AuthorInfo from '@/views/browse/detail/components/AuthorInfo.vue';
  import Total from '@/views/browse/detail/components/Total.vue';
  import RelatedDataList from '@/views/browse/detail/components/RelatedDataList.vue';
  import {
    checkAnalysisPermission,
    getAnalysisGeneralInfo,
    getAuthorInfo,
    getPipeline,
    getRelatedAnalysis,
    getStatInfo,
  } from '@/api/app/analysis';
  import { computed, getCurrentInstance, onMounted, reactive, ref } from 'vue';
  import RelatedAnalysisList from '@/views/browse/detail/components/RelatedAnalysisList.vue';
  import useUserStore from '@/store/modules/user';
  import HttpDownloadDialog from '@/views/browse/detail/components/HttpDownloadDialog.vue';
  import SftpDownloadDialog from '@/views/browse/detail/components/SftpDownloadDialog.vue';

  import { storeToRefs } from 'pinia';
  import RequestTo from '@/components/DataShare/RequestTo.vue';
  import { isTokenAccess } from '@/utils/nodeCommon';

  const userStore = useUserStore();
  const { member } = storeToRefs(userStore);
  const { proxy } = getCurrentInstance();

  let showContent = ref(true);
  let errorContent = ref('');
  let loading = ref(false);
  let no = proxy.$route.params.id;
  let currId = ref(no);

  onMounted(() => {
    checkAnalysisPermission(no).then(response => {
      if (response.msg) {
        errorContent.value = response.msg;
        showContent.value = false;
      } else {
        loadGeneralInfo();
        loadTotalData();
        loadPipelineData();
        loadRelatedAnalysisList();
        loadAuthorInfo();
      }
    });
  });

  const userData = computed(() => {
    return creator.value === member.value.id || isTokenAccess();
  });

  /** 加载基本信息 */
  const genInfo = reactive([]);
  const creator = ref('');
  const visibleStatus = ref('');
  const publish = ref([]);
  const generalInfoKey = ref(0);
  const totalData = ref([]);
  const pipelineData = ref([]);
  const analysisTargetV1 = ref([]);
  const analysisCustomTarget = ref([]);

  function loadGeneralInfo() {
    loading.value = true;
    getAnalysisGeneralInfo(no)
      .then(response => {
        currId.value = response.data.analysisNo;
        dataListKey.value++;
        genInfo.push({
          label: proxy.$t('browse.detail.analysis.generalInfo.analysisId'),
          value: response.data.analysisNo,
        });
        genInfo.push({
          label: proxy.$t('browse.detail.analysis.generalInfo.analysisName'),
          value: response.data.name,
        });
        genInfo.push({
          label: proxy.$t('browse.detail.analysis.generalInfo.analysisType'),
          value: response.data.analysisType,
        });
        genInfo.push({
          label: proxy.$t('browse.detail.analysis.generalInfo.description'),
          value: response.data.description,
        });
        // 历史usedIds
        if (response.data?.usedIds) {
          const joinedString = response.data?.usedIds.join(';');
          genInfo.push({
            label: proxy.$t('browse.detail.analysis.generalInfo.usedIds'),
            value: joinedString,
          });
        }
        publish.value = response.data.publishes;
        creator.value = response.data.creator;
        visibleStatus.value = response.data.visibleStatus;
        analysisTargetV1.value = response.data.target;
        analysisCustomTarget.value = response.data.customTarget;
      })
      .finally(() => {
        generalInfoKey.value++;
        loading.value = false;
      });
  }

  function loadTotalData() {
    loading.value = true;
    getStatInfo(no)
      .then(response => {
        totalData.value.push({
          name: proxy.$t('browse.detail.analysis.statistics.volume'),
          number: response.data.VOLUME,
        });
        totalData.value.push({
          name: proxy.$t('browse.detail.analysis.statistics.files'),
          number: response.data.FILES,
        });
      })
      .finally(() => {
        generalInfoKey.value++;
        loading.value = false;
      });
  }

  function loadPipelineData() {
    loading.value = true;
    getPipeline(no)
      .then(response => {
        pipelineData.value = response.data;
      })
      .finally(() => {
        generalInfoKey.value++;
        loading.value = false;
      });
  }

  /** 加载data list */
  let dataListKey = ref(0);
  // 列显隐信息
  const columns = ref([
    {
      prop: 'datNo',
      minWidth: 120,
      label: proxy.$t('browse.detail.analysis.columns.dataId'),
      visible: true,
    },
    {
      prop: 'name',
      minWidth: 170,
      label: proxy.$t('browse.detail.analysis.columns.dataName'),
      visible: true,
    },
    {
      prop: 'dataType',
      minWidth: 120,
      label: proxy.$t('browse.detail.analysis.columns.dataType'),
      visible: true,
    },
    {
      prop: 'security',
      minWidth: 135,
      label: proxy.$t('browse.detail.analysis.columns.dataSecurity'),
      visible: true,
    },
    {
      prop: 'readableFileSize',
      minWidth: 110,
      label: proxy.$t('browse.detail.analysis.columns.dataSize'),
      visible: true,
    },
    {
      prop: 'uploadTime',
      minWidth: 170,
      label: proxy.$t('browse.detail.analysis.columns.dataUploadTime'),
      visible: true,
    },
  ]);

  /** 加载相关联的analysis */
  let analysisList = reactive([]);
  let analListLoading = ref(false);

  function loadRelatedAnalysisList() {
    analListLoading.value = true;
    getRelatedAnalysis(no)
      .then(response => {
        analysisList.push(...response.data);
      })
      .finally(() => {
        analListLoading.value = false;
      });
  }

  /** 加载用户信息 */
  let authorData = ref({
    createDate: undefined,
    updateDate: undefined,
    submitter: {
      firstName: '',
      middleName: '',
      lastName: '',
      orgName: '',
    },
  });

  let authorInfoLoading = ref(false);

  /** http下载 */
  function showHttpDownloadModal(row) {
    proxy.$refs['httpDownloadDialog'].showHttpDownloadModal(row);
  }

  /** sftp下载 */
  function showSftpDownloadModal(row) {
    proxy.$refs['sftpDownloadDialog'].showSftpDownloadModal(row);
  }

  const openRequestToDialog = row => {
    proxy.$refs['requestToRef'].init('data', row.datNo);
  };

  function loadAuthorInfo() {
    authorInfoLoading.value = true;
    getAuthorInfo(no)
      .then(response => {
        authorData.value = response.data;
      })
      .finally(() => {
        authorInfoLoading.value = false;
      });
  }
</script>

<style lang="scss" scoped>
  .item {
    margin-top: 0.5rem;
    width: 100%;
    display: flex;

    .label {
      color: #666666;
      font-weight: 600;
      display: inline-block;
      min-width: 150px;
    }

    .el-tag:hover {
      cursor: pointer;
      background-color: #d9e2f6;
    }

    .bg-gray {
      width: 100%;
      padding: 4px 15px;
      border-radius: 6px;
    }

    &.pipeline {
      margin-top: 1rem;
    }

    :deep(.el-step__icon) {
      background-color: #d6e5ff;
      color: #5686dc;
      border: 2px solid #759fea;
    }

    :deep(.el-step__line) {
      background-color: #e8e8e8;
    }
  }

  .target-number {
    background-color: #d6e5ff;
    color: #3a78e8;
    border-radius: 50%;
    padding: 0 2px;
    border: 1px solid #3a78e8;
  }

  .download {
    width: 20px;
    cursor: pointer;
  }
</style>
