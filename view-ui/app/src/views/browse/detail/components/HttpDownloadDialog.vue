<template>
  <el-dialog
    v-model="httpDialogShow"
    :title="$t('download.dialogs.httpDownload.title')"
    width="700"
    class="ftp-download radius-14"
  >
    <div class="mb-05 text-center">
      <span class="text-main-color font-600 mr-05">{{
        $t('download.dialogs.httpDownload.downloadLink')
      }}</span>
      <span class="text-main-color" v-text="downloadLink"></span>
    </div>
    <template #footer>
      <div class="text-center">
        <el-button type="primary" round @click="httpDownload"
          >{{ $t('download.dialogs.httpDownload.localDownload') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
  import { defineExpose, getCurrentInstance, ref } from 'vue';
  import { getWebUrl } from '@/utils/nodeCommon';

  const { proxy } = getCurrentInstance();
  defineExpose({
    showHttpDownloadModal,
  });
  /** http下载 */
  let httpDialogShow = ref(false);

  let downloadLink = ref('');

  function showHttpDownloadModal(row) {
    if (row.fileSize > 200 * 1024 * 1024) {
      proxy.$modal.msgWarning(
        proxy.$t('download.dialogs.httpDownload.fileSizeExceedsLimit'),
      );
      return;
    }
    if (row.security === 'Public') {
      downloadLink.value =
        getWebUrl() + `/download/node/data/public/${row.datNo}`;
    } else {
      downloadLink.value = getWebUrl() + `/download/node/data/${row.datNo}`;
    }

    httpDialogShow.value = true;
  }

  function httpDownload() {
    // proxy.download(downloadLink.value);
    window.open(downloadLink.value, '_blank');
    httpDialogShow.value = false;
  }
</script>

<style scoped lang="scss"></style>
