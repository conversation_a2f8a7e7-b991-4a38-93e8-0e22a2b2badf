<template>
  <div v-if="dataList.length > 0" v-loading="dataListLoading" class="card mt-1">
    <span class="text-main-color font-600 font-16">{{
      $t('browse.detail.components.relatedDataQcInfoList.title')
    }}</span>
    <el-divider class="mb-1 mt-1"></el-divider>
    <el-table
      tooltip-effect="light"
      :data="dataList"
      :header-cell-style="{
        backgroundColor: '#EDF3FD',
        color: '#333333',
        fontWeight: 700,
      }"
      :row-style="{
        position: 'relative',
      }"
      max-height="550"
      class="data-list"
      border
      @sort-change="relTableSortChange"
    >
      <el-table-column
        :label="
          $t('browse.detail.components.relatedDataQcInfoList.table.dataId')
        "
        prop="dataNo"
        min-width="115"
      />
      <el-table-column
        :label="
          $t('browse.detail.components.relatedDataQcInfoList.table.dataName')
        "
        prop="dataFileName"
        min-width="200"
        show-overflow-tooltip
      />
      <el-table-column
        :label="
          $t(
            'browse.detail.components.relatedDataQcInfoList.table.dataSecurity',
          )
        "
        prop="security"
        sortable
        min-width="150"
      >
        <template #default="scope">
          {{ scope.row.dataSecurity }}
        </template>
      </el-table-column>
      <el-table-column
        :label="
          $t('browse.detail.components.relatedDataQcInfoList.table.format')
        "
        prop="seqkitResult.format"
      >
        <template #default="scope">
          {{ scope.row.seqkitResult ? scope.row.seqkitResult.format : '*' }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('browse.detail.components.relatedDataQcInfoList.table.type')"
        prop="seqkitResult.type"
      >
        <template #default="scope">
          {{ scope.row.seqkitResult ? scope.row.seqkitResult.type : '*' }}
        </template>
      </el-table-column>
      <el-table-column
        align="right"
        :label="
          $t('browse.detail.components.relatedDataQcInfoList.table.numSeqs')
        "
        prop="seqkitResult.numSeqs"
        sortable
        min-width="120"
      >
        <template #default="scope">
          {{
            scope.row.seqkitResult
              ? formatNumber(scope.row.seqkitResult.numSeqs)
              : '*'
          }}
        </template>
      </el-table-column>
      <el-table-column
        :label="
          $t('browse.detail.components.relatedDataQcInfoList.table.bases')
        "
        align="right"
        prop="seqkitResult.sumLen"
        sortable
        min-width="120"
      >
        <template #default="scope">
          {{
            scope.row.seqkitResult
              ? formatNumber(scope.row.seqkitResult.sumLen)
              : '*'
          }}
        </template>
      </el-table-column>
      <el-table-column
        :label="
          $t('browse.detail.components.relatedDataQcInfoList.table.minLen')
        "
        align="right"
        prop="seqkitResult.minLen"
        min-width="110"
      >
        <template #default="scope">
          {{
            scope.row.seqkitResult
              ? formatNumber(scope.row.seqkitResult.minLen)
              : '*'
          }}
        </template>
      </el-table-column>
      <el-table-column
        :label="
          $t('browse.detail.components.relatedDataQcInfoList.table.avgLen')
        "
        align="right"
        prop="seqkitResult.avgLen"
        min-width="110"
      >
        <template #default="scope">
          {{
            scope.row.seqkitResult
              ? formatNumber(scope.row.seqkitResult.avgLen)
              : '*'
          }}
        </template>
      </el-table-column>
      <el-table-column
        :label="
          $t('browse.detail.components.relatedDataQcInfoList.table.maxLen')
        "
        align="right"
        prop="seqkitResult.maxLen"
        min-width="110"
      >
        <template #default="scope">
          {{
            scope.row.seqkitResult
              ? formatNumber(scope.row.seqkitResult.maxLen)
              : '*'
          }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('browse.detail.components.relatedDataQcInfoList.table.q20')"
        align="right"
        prop="seqkitResult.q20"
        sortable
        min-width="110"
      >
        <template #default="scope">
          {{ scope.row.seqkitResult ? scope.row.seqkitResult.q20 : '*' }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('browse.detail.components.relatedDataQcInfoList.table.q30')"
        align="right"
        prop="seqkitResult.q30"
        sortable
        min-width="110"
      >
        <template #default="scope">
          {{ scope.row.seqkitResult ? scope.row.seqkitResult.q30 : '*' }}
        </template>
      </el-table-column>
      <el-table-column
        :label="
          $t('browse.detail.components.relatedDataQcInfoList.table.avgQual')
        "
        align="right"
        prop="seqkitResult.avgQual"
        min-width="110"
      >
        <template #default="scope">
          {{ scope.row.seqkitResult ? scope.row.seqkitResult.avgQual : '*' }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('browse.detail.components.relatedDataQcInfoList.table.gc')"
        prop="seqkitResult.gc"
        align="right"
      >
        <template #default="scope">
          {{ scope.row.seqkitResult ? scope.row.seqkitResult.gc : '*' }}
        </template>
      </el-table-column>
      <el-table-column
        :label="
          $t('browse.detail.components.relatedDataQcInfoList.table.operate')
        "
        fixed="right"
        align="center"
        width="110"
      >
        <template #default="scope">
          <el-tooltip
            v-if="scope.row.seqkitResult"
            :content="
              $t(
                'browse.detail.components.relatedDataQcInfoList.tooltips.viewFastqcReport',
              )
            "
          >
            <svg-icon
              icon-class="review"
              class-name="download download-svg"
              @click="toReportPage(scope.row.dataNo)"
            ></svg-icon>
          </el-tooltip>
          <el-tooltip
            v-if="scope.row.seqkitResult"
            :content="
              $t(
                'browse.detail.components.relatedDataQcInfoList.tooltips.downloadFastqcReport',
              )
            "
          >
            <svg-icon
              icon-class="download"
              class-name="download-icon"
              @click="downloadReport(scope.row.dataNo)"
            ></svg-icon>
          </el-tooltip>
          <el-tooltip
            v-if="!scope.row.seqkitResult"
            :content="
              $t(
                'browse.detail.components.relatedDataQcInfoList.tooltips.requestAccessFirst',
              )
            "
          >
            <svg-icon
              icon-class="lock"
              class-name="download download-svg"
              style="width: 20px; height: 20px; cursor: not-allowed"
            ></svg-icon>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="queryPageAndSort.totalCount > 0"
      v-model:page="queryPageAndSort.pageNum"
      v-model:limit="queryPageAndSort.pageSize"
      :page-sizes="[5, 10, 20, 50, 100]"
      class="mb-1 mt-2 justify-center"
      :total="queryPageAndSort.totalCount"
      @pagination="pageDataList"
    />
    <http-download-dialog ref="httpDownloadDialog"></http-download-dialog>
    <sftp-download-dialog ref="sftpDownloadDialog"></sftp-download-dialog>
    <request-to ref="requestToRef"></request-to>
  </div>
</template>
<script setup>
  import {
    defineExpose,
    defineProps,
    getCurrentInstance,
    onMounted,
    reactive,
    ref,
  } from 'vue';
  import { getDataQCInfoList } from '@/api/app/browseDetail';
  import RequestTo from '@/components/DataShare/RequestTo.vue';
  import HttpDownloadDialog from '@/views/browse/detail/components/HttpDownloadDialog.vue';
  import SftpDownloadDialog from '@/views/browse/detail/components/SftpDownloadDialog.vue';
  import { getConfigKey } from '@/api/system/config';
  import { getToken } from '@/utils/auth';
  import { formatNumber } from '@/utils/nodeCommon';

  const { proxy } = getCurrentInstance();

  let props = defineProps({
    currId: {
      type: String,
      required: true,
    },
    type: {
      type: String,
      required: true,
    },
  });

  let enableFastqc = ref('disable');

  onMounted(() => {
    getConfigKey('node.fastqc.status').then(response => {
      enableFastqc.value = response.msg;
      loadDataList();
    });
  });

  const { currId, type } = props;

  const dataListLoading = ref(false);

  const dataList = reactive([]);

  const queryPageAndSort = ref({
    sortKey: '',
    sortType: '',
    pageNum: 1,
    pageSize: 10,
    totalCount: 0,
  });

  /** 数据分页 */
  function pageDataList(pageData) {
    queryPageAndSort.value.pageSize = pageData.limit;
    queryPageAndSort.value.pageNum = pageData.page;
    loadDataList();
  }

  defineExpose({
    pageDataList,
  });

  function relTableSortChange(column) {
    let { prop, order } = column;
    if (order) {
      queryPageAndSort.value.sortKey = prop;
      queryPageAndSort.value.sortType = order === 'ascending' ? 'asc' : 'desc';
      loadDataList();
    }
  }

  function loadDataList() {
    dataListLoading.value = true;
    let param = {
      type: type,
      typeNo: currId,
    };
    let pagePram = queryPageAndSort.value;
    // 请求参数和分页信息
    param = { ...param, ...pagePram };
    getDataQCInfoList(param)
      .then(response => {
        dataList.length = 0;
        let pageInfo = response;
        if (pageInfo) {
          queryPageAndSort.value.totalCount = pageInfo.total;
          dataList.push(...pageInfo.rows);
        } else {
          queryPageAndSort.value.totalCount = 0;
        }
      })
      .finally(() => {
        dataListLoading.value = false;
      });
  }

  function toReportPage(dataNo) {
    const form = document.createElement('form');
    form.action = `${import.meta.env.VITE_APP_BASE_API}/app/fastqc/${dataNo}`;
    form.method = 'POST';
    form.target = '_blank';
    form.style.display = 'none';
    form.enctype = 'application/x-www-form-urlencoded';

    const tokenInput = document.createElement('input');
    tokenInput.type = 'hidden';
    tokenInput.name = 'token';
    tokenInput.value = getToken();

    form.appendChild(tokenInput);
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
  }

  function downloadReport(dataNo) {
    proxy.download(
      `/app/fastqc/downloadReport/${dataNo}`,
      null,
      `${dataNo}_FastQC_Report.zip`,
    );
  }
</script>

<style lang="scss" scoped>
  .downloadTable {
    :deep(.el-table td.el-table__cell div) {
      display: flex;
      align-items: center;
    }
  }

  .download-svg {
    width: 20px;
    height: 20px;
  }

  .el-icon {
    margin-right: 0.5rem;
  }

  .integrity-body {
    background-color: #fcf8e3;
    border: 1px solid #efe8c5;
    padding: 10px 15px;
    border-radius: 8px;
    color: #8f7443;
    text-align: justify;
    font-size: 14px;

    .note {
      color: #8a6d3b;
    }
  }

  :deep(.el-table td.el-table__cell:last-child div) {
    justify-content: center !important;
  }

  .download {
    width: 20px;
    cursor: pointer;
  }

  .download-icon {
    width: 24px;
    height: 24px;
    position: relative;
    top: 2px;
    margin-left: 4px;
    cursor: pointer;
  }
</style>
