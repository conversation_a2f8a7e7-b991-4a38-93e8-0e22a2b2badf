<template>
  <div class="page">
    <div v-if="showContent" class="container-fluid">
      <Breadcrumb
        :bread-item="
          (userData ? $t('browse.detail.project.breadcrumb.my') + ' ' : '') +
          $t('browse.detail.project.breadcrumb.title')
        "
      />
      <el-row :gutter="15" class="mt-1 row-gap-15">
        <el-col :span="18" :xs="24" :md="18">
          <general-info
            :key="'project-general-info-' + generalInfoKey"
            v-loading="loading"
            :general-info="genInfo"
            :creator="creator"
            :visible-status="visibleStatus"
            :publications="publish"
            :related-links="relatedLinks"
            type="project"
            :type-id="no"
          ></general-info>
        </el-col>
        <el-col :span="6" :xs="24" :md="6">
          <total :data="totalData" :show-tip="userData" />
        </el-col>
      </el-row>
      <stat-detail
        v-loading="statDetailLoading"
        :data="statDetailData"
      ></stat-detail>

      <ExpSapTable
        :key="'exp-table'"
        :all-tables="expTablesRef"
        :type="'experiment'"
        :prj-no="currId"
      ></ExpSapTable>
      <ExpSapTable
        :key="'sap-table'"
        :all-tables="sapTablesRef"
        :type="'sample'"
        :prj-no="currId"
      ></ExpSapTable>

      <related-data-list
        ref="dataListRef"
        :key="'prj-data-list-key-' + dataListKey"
        :columns="columns"
        :curr-id="currId"
        :type="'project'"
      />
      <related-data-qc-info-list
        :key="'qc-list-key-' + dataListKey"
        :curr-id="currId"
        :type="'project'"
      ></related-data-qc-info-list>
      <related-analysis-list
        v-if="analysisList.length > 0"
        v-loading="analListLoading"
        :data="analysisList"
      />
      <author-info
        v-loading="authorInfoLoading"
        :data="authorData"
      ></author-info>
    </div>
    <div v-if="!showContent" class="container-fluid">
      <div class="card mt-1">
        <el-result
          icon="error"
          :title="$t('browse.detail.project.error.title')"
        >
          <template #sub-title>
            <div class="font-600">
              {{ errorContent }}
            </div>
          </template>
        </el-result>
      </div>
    </div>
  </div>
</template>

<script setup>
  import Breadcrumb from '@/components/breadcrumb.vue';
  import GeneralInfo from '@/views/browse/detail/components/GeneralInfo.vue';
  import AuthorInfo from '@/views/browse/detail/components/AuthorInfo.vue';
  import Total from '@/views/browse/detail/components/Total.vue';
  import StatDetail from '@/views/browse/detail/components/StatDetail.vue';
  import RelatedDataList from '@/views/browse/detail/components/RelatedDataList.vue';
  import RelatedDataQcInfoList from '@/views/browse/detail/components/RelatedDataQcInfoList.vue';
  import ExpSapTable from '@/views/browse/detail/components/ExpSapTable.vue';

  import RelatedAnalysisList from '@/views/browse/detail/components/RelatedAnalysisList.vue';
  import { computed, getCurrentInstance, onMounted, reactive, ref } from 'vue';
  import {
    checkProjectPermission,
    getAuthorInfo,
    getExpAndSampleTable,
    getProjectGeneralInfo,
    getRelatedAnalysis,
    getStatDetail,
    getStatInfo,
  } from '@/api/app/project';
  import useUserStore from '@/store/modules/user';

  import { storeToRefs } from 'pinia';
  import { isTokenAccess } from '@/utils/nodeCommon';
  import { isArrEmpty } from '@/utils';

  const userStore = useUserStore();
  const { member } = storeToRefs(userStore);
  const { proxy } = getCurrentInstance();

  const expTablesRef = ref([]);
  const sapTablesRef = ref([]);

  let showContent = ref(true);
  let errorContent = ref('');

  let loading = ref(false);
  let no = proxy.$route.params.id;
  const currId = ref(no);
  onMounted(() => {
    checkProjectPermission(no).then(response => {
      if (response.msg) {
        errorContent.value = response.msg;
        showContent.value = false;
      } else {
        loadGeneralInfo();
        loadTotalData();
        loadStatDetail();
        loadRelatedAnalysisList();
        loadAuthorInfo();
        loadExpAndSample();
      }
    });
  });

  const userData = computed(() => {
    return creator.value === member.value.id || isTokenAccess();
  });

  /** 加载基本信息 */
  const genInfo = reactive([]);
  const creator = ref('');
  const visibleStatus = ref('');
  const relatedLinks = ref([]);
  const publish = ref([]);
  const generalInfoKey = ref(0);
  const totalData = ref([]);

  function loadGeneralInfo() {
    loading.value = true;
    getProjectGeneralInfo(no)
      .then(response => {
        currId.value = response.data.projectNo;
        dataListKey.value++;
        genInfo.push({
          label: proxy.$t('browse.detail.project.generalInfo.projectId'),
          value: response.data.projectNo,
        });
        genInfo.push({
          label: proxy.$t('browse.detail.project.generalInfo.projectName'),
          value: response.data.name,
        });
        genInfo.push({
          label: proxy.$t('browse.detail.project.generalInfo.description'),
          value: response.data.description,
        });

        // 历史usedIds
        if (response.data?.usedIds) {
          const joinedString = response.data?.usedIds.join(';');
          genInfo.push({
            label: proxy.$t('browse.detail.project.generalInfo.usedIds'),
            value: joinedString,
          });
        }
        publish.value = response.data.publishes;
        creator.value = response.data.creator;
        visibleStatus.value = response.data.visibleStatus;
        relatedLinks.value = response.data.relatedLinks;
      })
      .finally(() => {
        generalInfoKey.value++;
        loading.value = false;
      });
  }

  function loadTotalData() {
    loading.value = true;
    getStatInfo(no)
      .then(response => {
        totalData.value.push({
          name: proxy.$t('browse.detail.project.statistics.volume'),
          number: response.data.VOLUME,
        });
        totalData.value.push({
          name: proxy.$t('browse.detail.project.statistics.run'),
          number: response.data.RUN,
        });
        totalData.value.push({
          name: proxy.$t('browse.detail.project.statistics.files'),
          number: response.data.FILES,
        });
        totalData.value.push({
          name: proxy.$t('browse.detail.project.statistics.sample'),
          number: response.data.SAMPLE,
        });
      })
      .finally(() => {
        generalInfoKey.value++;
        loading.value = false;
      });
  }

  /** 加载统计信息 */
  let statDetailData = reactive([]);
  let statDetailLoading = ref(false);

  function loadStatDetail() {
    statDetailLoading.value = true;
    getStatDetail(no)
      .then(response => {
        statDetailData.push({
          title: proxy.$t('browse.detail.project.statDetail.experimentalType'),
          data: response.data.expStats,
        });
        statDetailData.push({
          title: proxy.$t('browse.detail.project.statDetail.organism'),
          data: response.data.sapStats,
        });
        statDetailData.push({
          title: proxy.$t('browse.detail.project.statDetail.fileTypes'),
          data: response.data.dataStats,
        });
      })
      .finally(() => {
        statDetailLoading.value = false;
      });
  }

  /** 加载data list */
  let dataListKey = ref(0);
  // 列显隐信息
  const columns = ref([
    {
      prop: 'expNo',
      minWidth: 140,
      label: proxy.$t('browse.detail.project.columns.experimentId'),
      visible: true,
    },
    {
      prop: 'expName',
      minWidth: 190,
      label: proxy.$t('browse.detail.project.columns.experimentName'),
      visible: false,
    },
    {
      prop: 'expType',
      minWidth: 190,
      label: proxy.$t('browse.detail.project.columns.experimentType'),
      visible: true,
    },
    {
      prop: 'expDesc',
      minWidth: 220,
      label: proxy.$t('browse.detail.project.columns.experimentDescription'),
      visible: false,
    },
    {
      prop: 'sapNo',
      minWidth: 120,
      label: proxy.$t('browse.detail.project.columns.sampleId'),
      visible: true,
    },
    {
      prop: 'sapName',
      minWidth: 150,
      label: proxy.$t('browse.detail.project.columns.sampleName'),
      visible: false,
    },
    {
      prop: 'sapType',
      minWidth: 170,
      label: proxy.$t('browse.detail.project.columns.sampleType'),
      visible: true,
    },
    {
      prop: 'sapDesc',
      minWidth: 190,
      label: proxy.$t('browse.detail.project.columns.sampleDescription'),
      visible: false,
    },
    {
      prop: 'runNo',
      minWidth: 120,
      label: proxy.$t('browse.detail.project.columns.runId'),
      visible: true,
    },
    {
      prop: 'runName',
      minWidth: 170,
      label: proxy.$t('browse.detail.project.columns.runName'),
      visible: true,
    },
    {
      prop: 'datNo',
      minWidth: 120,
      label: proxy.$t('browse.detail.project.columns.dataId'),
      visible: true,
    },
    {
      prop: 'name',
      minWidth: 170,
      label: proxy.$t('browse.detail.project.columns.dataName'),
      visible: true,
    },
    {
      prop: 'security',
      minWidth: 135,
      label: proxy.$t('browse.detail.project.columns.dataSecurity'),
      visible: true,
    },
    {
      prop: 'readableFileSize',
      minWidth: 110,
      label: proxy.$t('browse.detail.project.columns.dataSize'),
      visible: true,
    },
    {
      prop: 'uploadTime',
      minWidth: 170,
      label: proxy.$t('browse.detail.project.columns.dataUploadTime'),
      visible: false,
    },
  ]);

  /** 加载相关联的analysis */
  let analysisList = reactive([]);
  let analListLoading = ref(false);

  function loadRelatedAnalysisList() {
    analListLoading.value = true;
    getRelatedAnalysis(no)
      .then(response => {
        analysisList.push(...response.data);
      })
      .finally(() => {
        analListLoading.value = false;
      });
  }

  /** 加载用户信息 */
  let authorData = ref({
    createDate: undefined,
    updateDate: undefined,
    submitter: {
      firstName: '',
      middleName: '',
      lastName: '',
      orgName: '',
    },
  });
  let authorInfoLoading = ref(false);

  function loadAuthorInfo() {
    authorInfoLoading.value = true;
    getAuthorInfo(no)
      .then(response => {
        authorData.value = response.data;
      })
      .finally(() => {
        authorInfoLoading.value = false;
      });
  }

  function loadExpAndSample() {
    expTablesRef.value = [];
    sapTablesRef.value = [];
    getExpAndSampleTable(no).then(response => {
      if (response.code === 200) {
        const data = response.data;
        const { expTables, sapTables } = data;
        expTablesRef.value = isArrEmpty(expTables) ? [] : expTables;
        sapTablesRef.value = isArrEmpty(sapTables) ? [] : sapTables;
      }
    });
  }
</script>

<style lang="scss" scoped></style>
