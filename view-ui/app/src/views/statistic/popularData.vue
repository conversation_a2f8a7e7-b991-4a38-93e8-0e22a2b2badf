<template>
  <div class="card">
    <h3 class="text-main-color mb-1">{{ $t('statistic.popularData.title') }}</h3>
    <div class="downloadTable">
      <el-tabs v-model="activeName" type="card" class="demo-tabs">
        <el-tab-pane :label="$t('statistic.popularData.visits')" name="Visits">
          <PopularDataTabItem
            :key="`pop-da-tab-Visits-${activeTimes}`"
            :tab-name="'Visits'"
          ></PopularDataTabItem>
        </el-tab-pane>

        <el-tab-pane :label="$t('statistic.popularData.download')" name="Download">
          <PopularDataTabItem
            :key="`pop-da-tab-Download-${activeTimes}`"
            :tab-name="'Download'"
          ></PopularDataTabItem>
        </el-tab-pane>

        <el-tab-pane :label="$t('statistic.popularData.requested')" name="Requested">
          <PopularDataTabItem
            :key="`pop-da-tab-Requested-${activeTimes}`"
            :tab-name="'Requested'"
          ></PopularDataTabItem>
        </el-tab-pane>
      </el-tabs>
      <el-divider />
    </div>
  </div>
</template>

<script setup>
  import PopularDataTabItem from '@/views/statistic/common/popularDataTabItem.vue';

  import { onActivated, ref } from 'vue';

  const activeName = ref('Visits');
  const activeTimes = ref(1);

  onActivated(() => {
    activeTimes.value++;
  });

  /*onMounted(() => {
    console.log(22);
  });*/
</script>

<style lang="scss" scoped>
  :deep(.el-tabs__header .el-tabs__item:not(:first-child)) {
    margin-left: 5px !important;
  }
</style>
