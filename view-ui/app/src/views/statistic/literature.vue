<template>
  <div class="card list">
    <el-row :gutter="20">
      <el-col :span="21" :xs="24">
        <h3 class="mb-0 mt-0">{{ $t('statistic.publication.title') }}</h3>
      </el-col>
    </el-row>
    <el-divider></el-divider>
    <div
      id="statistic"
      v-loading="loading"
      class="bg-gray radius-12 mb-1 pt-10"
      style="width: 100%; height: 60vh"
    ></div>
  </div>
</template>

<script setup name="Index">
  import * as echarts from 'echarts';
  import { nextTick, onMounted, ref } from 'vue';
  import { getPublication } from '@/api/statistics';

  const data = ref({});
  const loading = ref(false);

  function loadData() {
    loading.value = true;
    getPublication()
      .then(res => {
        if (res.data) {
          data.value = res.data;
        }
        echartsInit();
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const echartsInit = () => {
    const publishChart = echarts.init(document.getElementById('statistic'));
    const options = {
      color: ['#1981F4', '#2ADAC9', '#FEA52B', '#1981F4'],
      tooltip: {},
      grid: {
        top: '14%',
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true,
      },
      dataZoom: [
        {
          show: true,
          realtime: true,
          start: 0,
          end: 100,
        },
        {
          type: 'inside',
          realtime: true,
          start: 0,
          end: 100,
        },
      ],
      xAxis: [
        {
          type: 'category',
          data: data.value.map(item => item.month),
        },
      ],
      yAxis: [
        {
          type: 'value',
          axisLine: {
            show: true,
          },
          splitLine: {
            show: false,
          },
        },
      ],
      series: [
        {
          name: 'Publish',
          type: 'bar',
          emphasis: {
            focus: 'series',
          },
          barMaxWidth: 25,
          data: data.value.map(item => item.approvedTotal),
        },
        {
          name: 'Total',
          data: data.value.map(item => item.approvedTotal),
          type: 'line',
        },
      ],
    };
    publishChart.setOption(options);

    window.onresize = function () {
      publishChart.resize();
    };
  };

  onMounted(() => {
    nextTick(() => {
      loadData();
    });
  });
</script>

<style scoped lang="scss">
  :deep(.el-form-item__label) {
    color: #858181;
  }
</style>
