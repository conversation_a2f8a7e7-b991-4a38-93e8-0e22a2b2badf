<template>
  <div v-loading="popItemLoading" style="min-height: 500px">
    <PopularItem
      v-for="(item, index) in popItemRef"
      :key="`popular-item-${index}`"
      :meta-data-name="item.type"
      :data="item.listData"
      :tab-name="tabName"
      :show-tags="item.showTags"
    ></PopularItem>
  </div>
</template>

<script setup>
  import { defineProps, onMounted, ref } from 'vue';
  import { getPopularData } from '@/api/statistics';
  import PopularItem from '@/views/statistic/common/popularItem.vue';

  const props = defineProps({
    tabName: {
      type: String,
      required: true,
      default: () => '',
    },
    currActiveName: {
      type: String,
      required: false,
      default: () => '',
    },
  });
  const tabName = ref(props.tabName);
  const popItemLoading = ref(false);
  const popItemRef = ref([]);

  onMounted(() => {
    let param = {
      statType: tabName.value,
    };
    popItemLoading.value = true;
    popItemRef.value = [];
    getPopularData(param)
      .then(res => {
        if (res.code === 200) {
          popItemRef.value = res.data;
        }
      })
      .finally(() => {
        popItemLoading.value = false;
      });
  });
</script>

<style scoped lang="scss"></style>
