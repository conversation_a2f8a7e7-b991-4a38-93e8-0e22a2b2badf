<template>
  <div v-for="(it, index) in dataList" :key="index + 'item'">
    <div v-if="index === 0" class="text-main-color font-16 font-600 mb-1">
      {{ metaDataName }}
    </div>

    <!-- 热门信息-->
    <div class="bg-gray item">
      <el-row :gutter="10">
        <el-col :span="19" class="d-flex align-items-center">
          <div class="d-flex align-items-center">
            <span class="font-16 font-600" :class="computeClass(index)">
              {{ index + 1 }}
            </span>
            <el-divider direction="vertical"></el-divider>
          </div>
          <div class="w-100">
            <!-- id/name-->
            <div class="d-flex align-items-center">
              <el-tag v-if="metaDataName === 'Data'" round class="ml-05 mr-1">
                {{ it.no }}
              </el-tag>
              <a v-else :href="initDetailUrl(it.no)" target="_blank">
                <el-tag round class="ml-05 mr-1">
                  {{ it.no }}
                </el-tag>
              </a>

              <span class="font-16 proj-name hidden-xs-only">
                <a :href="initDetailUrl(it.no)" target="_blank">
                  {{ it.name }}
                </a>
              </span>
            </div>

            <!-- type-tag-->
            <div
              v-if="showTags && !isArrEmpty(it.tagsGroup)"
              class="hidden-xs-only"
            >
              <template
                v-for="(tagGroupItem, tagGroupIndex) in it.tagsGroup"
                :key="`pop-tgroup-${index}-${tagGroupIndex}`"
              >
                <el-tag
                  v-for="(tagItem, tagItemIndex) in tagGroupItem"
                  :key="`pop-tag-${index}-${tagGroupIndex}-${tagItemIndex}`"
                  :type="tagGroupIndex > 0 ? 'success' : 'warning'"
                  class="type-tag"
                  round
                >
                  {{ tagItem }}
                </el-tag>
              </template>
            </div>
          </div>
        </el-col>
        <el-col :span="5" class="d-flex align-items-center justify-end">
          <svg-icon
            v-if="index <= 2"
            icon-class="hot"
            round
            class="hot-svg"
          ></svg-icon>
          <span class="font-16 text-other-color ml-1">{{
            formatNumber(it.num)
          }}</span>
          <span class="font-14 text-other-color ml-1">
            {{ getUnitByTab() }}
          </span>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup name="pieChart">
  import { defineProps, ref } from 'vue';
  import { isArrEmpty } from '@/utils';
  import { formatNumber } from '@/utils/nodeCommon';

  const props = defineProps({
    data: {
      type: Object,
    },
    metaDataName: {
      type: String,
      default: () => '',
    },
    tabName: {
      type: String,
      required: false,
      default: () => '',
    },
    showTags: {
      type: Boolean,
      required: true,
      default: () => false,
    },
  });
  const dataList = ref(props.data);
  const tabName = ref(props.tabName);

  const computeClass = index => {
    if (index <= 2) {
      return `hot-${index}`;
    }
  };

  function initDetailUrl(id) {
    let metaDataName = props.metaDataName;
    if (metaDataName) {
      let publicPath = import.meta.env.VITE_APP_PUBLIC_PATH;
      if (!publicPath) {
        publicPath = '';
      }
      if (metaDataName === 'Projects') {
        publicPath += '/project/detail/' + id;
      } else if (metaDataName === 'Experiments') {
        publicPath += '/experiment/detail/' + id;
      } else if (metaDataName === 'Samples') {
        publicPath += '/sample/detail/' + id;
      } else if (metaDataName === 'Analysis') {
        publicPath += '/analysis/detail/' + id;
      }
      return publicPath;
    }
  }

  function getUnitByTab() {
    let unit = '';
    if (tabName.value === 'Visits') {
      unit = 'visits';
    } else if (tabName.value === 'Download') {
      unit = 'downloads';
    } else if (tabName.value === 'Requested') {
      unit = 'requested';
    }
    return unit;
  }
</script>

<style scoped lang="scss">
  .item {
    padding: 5px 15px;
    margin-bottom: 10px;

    .proj-name {
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 82%;
      margin-right: 15px;
    }
  }

  :deep(.el-tag--warning) {
    .el-tag__content {
      color: #fe7f2b;
      font-weight: 600;
    }
  }

  :deep(.el-tag--primary) {
    .el-tag__content {
      font-weight: 600;
    }
  }

  .type-tag {
    margin: 12px 0.5rem 0.3rem 0.5rem;
  }

  :deep(.el-tag--success) {
    .el-tag__content {
      color: #4aaf4a;
      font-weight: 600;
    }
  }

  .hot-svg {
    width: 32px;
    height: 35px;
    position: relative;
    bottom: -2px;
    margin-right: 1rem;
    cursor: default;
  }

  .download-svg {
    width: 28px;
    height: 28px;
    cursor: pointer;
  }

  .hot-0 {
    color: red;
  }

  .hot-1 {
    color: #f97042;
  }

  .hot-2 {
    color: #ff8e5d;
  }
</style>
