<template>
  <div class="align-items-center">
    <div :id="id" style="width: 100%; height: 200px"></div>

    <div style="margin-left: 30px">
      <div class="text-left" style="width: 270px">
        <span class="before-circle font-600 accessible">{{
          $t('statistic.dataVolume.accessible')
        }}</span>
        <span class="font-600 ml-05"
          >{{
            ((accessible / (accessible + unAccessible)) * 100).toFixed(2)
          }}%</span
        >
        <span class="mr-05 font-16 ml-05">{{ formatNumber(accessible) }}</span>
        <span class="font-14 text-other-color">{{
          $t('statistic.dataVolume.items')
        }}</span>
      </div>
      <div class="text-left" style="width: 270px">
        <span class="before-circle font-600 unaccessible">{{
          $t('statistic.dataVolume.unaccessible')
        }}</span>
        <span class="font-600 ml-05"
          >{{
            ((unAccessible / (accessible + unAccessible)) * 100).toFixed(2)
          }}%</span
        >
        <span class="mr-05 ml-05">{{ formatNumber(unAccessible) }}</span>
        <span class="font-14 text-other-color">{{
          $t('statistic.dataVolume.items')
        }}</span>
      </div>
    </div>
  </div>
</template>

<script setup name="pieChart">
  import * as echarts from 'echarts';
  import { defineProps, nextTick, onMounted } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { formatNumber } from '@/utils/nodeCommon';

  const { t } = useI18n();
  const props = defineProps({
    id: {
      type: String,
      required: true,
    },
    data: {
      type: Array,
    },
    title: {
      type: String,
    },
    totalSize: {
      type: String,
    },
    accessible: {
      type: Number,
    },
    accessibleSize: {
      type: String,
    },
    unAccessible: {
      type: Number,
    },
    unAccessibleSize: {
      type: String,
    },
  });
  const echartInit = () => {
    const pieChart = echarts.init(document.getElementById(props.id));
    const option = {
      color: ['#3A78E8', '#2ADAC9'],
      title: {
        text: props.title,
        subtext: `${props.totalSize}\n${formatNumber(
          props.accessible + props.unAccessible,
        )} ${t('statistic.dataVolume.items')}`,
        textStyle: {
          rich: {
            a: {
              fontSize: 22,
              color: '#FFF',
            },
          },
        },
        subtextStyle: {
          fontSize: 14,
          color: '#999999',
          padding: [5, 10],
        },
        textVerticalAlign: 'top',
        x: 'center',
        y: 'center',
      },
      tooltip: {
        trigger: 'item',
        formatter: function (params) {
          return `<span class="mr-1">${params.name}</span>
<!--                  <span>${params.percent + '%'}</span>-->
                  <hr>
                   <div class="d-flex justify-space-between">
                    <span class="mr-1">${t('statistic.dataVolume.number')}</span>
                    <span>${params.value}</span>
                  </div>
                  <div class="d-flex justify-space-between">
                    <span class="mr-1">${t('statistic.dataVolume.size')}</span>
                    <span>${params.data.size}</span>
                  </div>`;
        },
      },
      series: [
        {
          type: 'pie',
          radius: ['70%', '90%'],
          center: ['50%', '50%'],
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
          data: [
            {
              value: props.accessible,
              name: t('statistic.dataVolume.accessible').replace(':', ''),
              size: props.accessibleSize,
            },
            {
              value: props.unAccessible,
              name: t('statistic.dataVolume.unaccessible').replace(':', ''),
              size: props.unAccessibleSize,
            },
          ],
        },
      ],
    };
    pieChart.setOption(option);
    window.onresize = function () {
      pieChart.resize();
    };
  };
  onMounted(() => {
    nextTick(() => {
      echartInit();
    });
  });
</script>

<style scoped lang="scss">
  .accessible {
    display: inline-block;
    width: 103px;
    text-align: left;
  }
  .accessible:before {
    height: 6px;
    width: 6px;
    background: #3a78e8;
  }
  .unaccessible:before {
    height: 6px;
    width: 6px;
    background: #2adac9;
  }
</style>
