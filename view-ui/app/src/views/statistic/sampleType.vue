<template>
  <div class="card list">
    <el-row :gutter="20">
      <el-col :span="21" :xs="24">
        <h3 class="mb-0 mt-0">Sample Type Statistic</h3>
      </el-col>
      <el-col :span="3" :xs="24" class="text-align-right">
        <el-button plain type="primary" round :icon="Download"
          >Download</el-button
        ></el-col
      >
    </el-row>
    <el-divider></el-divider>
    <div class="bg-gray p-10-15 radius-12 mb-1">
      <el-form-item label="Sample Type:" style="margin-bottom: 0">
        <el-checkbox-group v-model="sampTypes">
          <el-checkbox
            v-for="item in sampTypeOpts"
            :key="item"
            :value="item"
            :label="item"
            >{{ item }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </div>
    <div
      id="typeStatistic"
      style="width: 100%; height: 360px"
      class="bg-gray radius-12 mb-1 pt-10"
    ></div>
  </div>
  <div class="card list mt-1">
    <el-row :gutter="20">
      <el-col :span="21" :xs="24">
        <h3 class="mb-0 mt-0">Experimental Statistics</h3>
      </el-col>
      <el-col :span="3" :xs="24" class="text-align-right">
        <el-button plain type="primary" round :icon="Download"
          >Download</el-button
        ></el-col
      >
    </el-row>
    <el-divider></el-divider>

    <el-row :gutter="10">
      <el-col :span="24" class="bg-gray radius-12">
        <div class="text-align-right p-15 pb-0">
          <el-select v-model="select" style="width: 320px">
            <el-option value="All" label="All" />
            <el-option value="Accessible" label="Accessible" />
          </el-select>
        </div>
        <div id="omicsPropStat" style="width: 100%; height: 360px"></div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="Index">
  import { Download } from '@element-plus/icons-vue';
  import { nextTick, onMounted, ref, reactive } from 'vue';

  import * as echarts from 'echarts';

  const sampTypes = ref([]);
  const sampTypeOpts = [
    'Human',
    'Animalia',
    'Plantae',
    'Cell line',
    'Microbe',
    'Environment host',
    'Environment non-host',
    'Pathogen affecting public health',
  ];
  const select = ref('');

  const statisticData = reactive([
    {
      type: 'Human',
      accessible: 200,
      unaccessible: 130,
      publicNum: 400,
      publicSize: 400,
    },
    {
      type: 'Animalia',
      accessible: 120,
      unaccessible: 120,
    },
    {
      type: 'Plantae',
      accessible: 140,
      unaccessible: 123,
    },
    {
      type: 'Cell line',
      accessible: 213,
      unaccessible: 100,
    },
    {
      type: 'Microbe',
      accessible: 145,
      unaccessible: 150,
    },
    {
      type: 'Environment host',
      accessible: 160,
      unaccessible: 50,
    },
    // {
    //   type: 'Environment non-host',
    //   accessible: 170,
    //   unaccessible: 90,
    // },
  ]);
  const totalData = statisticData.map(it => it.accessible + it.unaccessible);

  const omicsPropData = reactive([
    {
      type: 'Human',
      multiomics: 156,
      singleOmics: 130,
    },
    {
      type: 'Animalia',
      multiomics: 231,
      singleOmics: 130,
    },
    {
      type: 'Plantae',
      multiomics: 200,
      singleOmics: 134,
    },
    {
      type: 'Cell line',
      multiomics: 323,
      singleOmics: 90,
    },
    {
      type: 'Microbe',
      multiomics: 200,
      singleOmics: 130,
    },
    {
      type: 'Environment host',
      multiomics: 200,
      singleOmics: 130,
    },
    // {
    //   type: 'Environment non-host',
    //   accessible: 170,
    //   unaccessible: 90,
    // },
  ]);

  const echartInit = () => {
    const typeStatistic = echarts.init(
      document.getElementById('typeStatistic'),
    );
    const typeStatisticOpt = {
      color: ['#1981F4', '#2ADAC9', '#FEA52B', '#1981F4'],
      tooltip: {
        trigger: 'item',
        confine: true,
        formatter: function (params) {
          if (params.componentSubType === 'line') {
            return `
                 <div><span class="mr-05">${params.name}:</span> ${params.value}</div>
                 <hr>
                 <div><span class="tooltip-label">Public:</span> 155 ( 1012TB )</div>
                 <div><span class="tooltip-label">Restricted:</span> 155 ( 1012TB )</div>
                 <div><span class="tooltip-label">Private:</span> 144 ( 1012TB )</div>`;
          } else {
            return `<div>${params.seriesName}</div>
                 <div>${params.value}</div>
                 `;
          }
        },
      },
      legend: {
        top: '4%',
      },
      grid: {
        top: '12%',
        left: '3%',
        right: '4%',
        bottom: '5%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          data: statisticData.map(it => it.type),
        },
      ],
      yAxis: [
        {
          type: 'value',
          axisLine: {
            show: true,
          },
          splitLine: {
            show: false,
          },
        },
      ],
      series: [
        {
          name: 'Accessible',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          barMaxWidth: 25,
          data: statisticData.map(it => it.accessible),
        },
        {
          name: 'Unaccessible',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          data: statisticData.map(it => it.unaccessible),
        },
        {
          name: 'Total data',
          data: totalData,
          type: 'line',
        },
      ],
    };
    typeStatistic.setOption(typeStatisticOpt);

    const omicsProportion = echarts.init(
      document.getElementById('omicsPropStat'),
    );
    const omicsPropOpt = {
      color: ['#1981F4', '#2ADAC9', '#FEA52B', '#1981F4'],
      tooltip: {
        trigger: 'axis',
        confine: true,
        axisPointer: {
          type: 'shadow',
        },
      },
      legend: {
        top: '4%',
      },
      grid: {
        top: '12%',
        left: '3%',
        right: '4%',
        bottom: '5%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          data: omicsPropData.map(it => it.type),
        },
      ],
      yAxis: [
        {
          type: 'value',
          axisLine: {
            show: true,
          },
          splitLine: {
            show: false,
          },
        },
      ],
      series: [
        {
          name: 'Genomic',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          barMaxWidth: 25,
          data: [90, 142, 76, 75, 36, 78],
        },
        {
          name: 'Transcriptomic',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          data: [69, 64, 64, 35, 64, 0],
        },
        {
          name: 'Transcriptomic sing cell',
          data: [69, 64, 64, 35, 64, 80],
          type: 'bar',
          stack: 'Ad',
        },
        {
          name: 'Total Data',
          data: [230, 270, 200, 140, 160, 160],
          type: 'line',
        },
      ],
    };
    omicsProportion.setOption(omicsPropOpt);

    window.onresize = function () {
      typeStatistic.resize();
      omicsProportion.resize();
    };
  };

  onMounted(() => {
    nextTick(() => {
      echartInit();
    });
  });
</script>

<style scoped lang="scss">
  :deep(.el-form-item__label) {
    color: #858181;
  }
  :deep(.tooltip-label) {
    display: inline-block;
    width: 80px;
  }
  :deep(.tool-label) {
    display: inline-block;
    width: 110px;
  }
  :deep(.el-select__wrapper) {
    border-radius: 12px;
  }
</style>
