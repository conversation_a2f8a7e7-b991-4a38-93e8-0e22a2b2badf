<template>
  <div v-loading="loadingMetadata">
    <!--  MetaData -->
    <div v-if="!loadingMetadata" class="card list">
      <el-row :gutter="20" class="mb-05">
        <el-col :span="21">
          <h3 class="mb-0 mt-0">{{ $t('statistic.dataVolume.metadata') }}</h3>
        </el-col>
      </el-row>
      <el-divider class="mb-1"></el-divider>
      <el-row :gutter="0">
        <el-col :span="6" :xs="24" :md="6">
          <MetadataItem
            v-if="!loadingMetadata"
            id="project"
            :title="$t('statistic.dataVolume.project')"
            :total-size="metadata.rawDataTotalSizeString"
            :accessible="metadata.projAccessible"
            :accessible-size="metadata.rawDataAccessibleSizeString"
            :un-accessible="metadata.projUnAccessible"
            :un-accessible-size="metadata.rawDataUnAccessibleSizeString"
          ></MetadataItem>
        </el-col>
        <el-col :span="6" :xs="24" :md="6">
          <MetadataItem
            v-if="!loadingMetadata"
            id="experiment"
            :title="$t('statistic.dataVolume.experiment')"
            :total-size="metadata.rawDataTotalSizeString"
            :accessible="metadata.expAccessible"
            :accessible-size="metadata.rawDataAccessibleSizeString"
            :un-accessible="metadata.expUnAccessible"
            :un-accessible-size="metadata.rawDataUnAccessibleSizeString"
          ></MetadataItem>
        </el-col>
        <el-col :span="6" :xs="24" :md="6">
          <MetadataItem
            v-if="!loadingMetadata"
            id="sample"
            :title="$t('statistic.dataVolume.sample')"
            :total-size="metadata.rawDataTotalSizeString"
            :accessible="metadata.sapAccessible"
            :accessible-size="metadata.rawDataAccessibleSizeString"
            :un-accessible="metadata.sapUnAccessible"
            :un-accessible-size="metadata.rawDataUnAccessibleSizeString"
          ></MetadataItem>
        </el-col>
        <el-col :span="6" :xs="24" :md="6">
          <MetadataItem
            v-if="!loadingMetadata"
            id="analysis"
            :title="$t('statistic.dataVolume.analysis')"
            :total-size="metadata.analDataTotalSizeString"
            :accessible="metadata.analAccessible"
            :accessible-size="metadata.analDataAccessibleSizeString"
            :un-accessible="metadata.analUnAccessible"
            :un-accessible-size="metadata.analDataUnAccessibleSizeString"
          ></MetadataItem>
        </el-col>
      </el-row>
    </div>

    <!--  raw data -->
    <div class="card list mt-1">
      <el-row :gutter="20" class="mb-05">
        <el-col :span="21" :xs="24">
          <h3 class="mb-0 mt-0">{{ $t('statistic.dataVolume.rawData') }}</h3>
        </el-col>
      </el-row>
      <el-divider></el-divider>
      <el-row :gutter="10">
        <el-col :span="24" class="bg-gray radius-12 mb-1">
          <div id="rawdata" style="width: 100%; height: 400px"></div>
        </el-col>
      </el-row>
    </div>

    <!--  analysis data -->
    <div class="card list mt-1">
      <el-row :gutter="20" class="mb-05">
        <el-col :span="21" :xs="24">
          <h3 class="mb-0 mt-0">
            {{ $t('statistic.dataVolume.analysisData') }}
          </h3>
        </el-col>
      </el-row>
      <el-divider></el-divider>
      <el-row :gutter="10">
        <el-col :span="24" class="bg-gray radius-12 mb-1">
          <div id="analysisData" style="width: 100%; height: 400px"></div>
        </el-col>
      </el-row>
    </div>

    <!--  data flow -->
    <div class="card list mt-1">
      <el-row :gutter="20" class="mb-05">
        <el-col :span="21" :xs="24">
          <h3 class="mb-0 mt-0">{{ $t('statistic.dataVolume.dataFlow') }}</h3>
        </el-col>
      </el-row>
      <el-divider></el-divider>
      <el-row :gutter="10">
        <el-col :span="24" class="bg-gray radius-12 mb-1">
          <div id="dataFlow" style="width: 100%; height: 360px"></div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup name="Index">
  import * as echarts from 'echarts';
  import MetadataItem from '@/views/statistic/common/dataVolumeItem.vue';
  import { nextTick, onMounted, ref } from 'vue';
  import { getDataFlow, getMetadata } from '@/api/statistics';

  const rawDataList = ref('');
  const analDataList = ref('');

  const metadata = ref({});
  const loadingMetadata = ref(false);

  function loadMetaData() {
    loadingMetadata.value = true;
    getMetadata()
      .then(res => {
        if (res.data) {
          metadata.value = res.data.metaData;
          rawDataList.value = res.data.rawDataList;
          analDataList.value = res.data.analDataList;
        }

        loadDataFlow();
      })
      .finally(() => {
        loadingMetadata.value = false;
      });
  }

  const dataFlow = ref({});
  function loadDataFlow() {
    getDataFlow()
      .then(res => {
        if (res.data) {
          dataFlow.value = res.data;
          echartsInit();
        }
      })
      .finally(() => {
        loadingMetadata.value = false;
      });
  }

  const echartsInit = () => {
    // rawdata
    const rawDataChart = echarts.init(document.getElementById('rawdata'));
    const rawDataOpt = {
      color: ['#1981F4', '#2ADAC9', '#FEA52B', '#1981F4'],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        confine: true,
      },
      legend: {
        data: ['Public', 'Restricted', 'Private', 'Total data'],
      },
      grid: {
        top: '12%',
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true,
      },
      dataZoom: [
        {
          show: true,
          realtime: true,
          start: 50,
          end: 100,
        },
        {
          type: 'inside',
          realtime: true,
          start: 50,
          end: 100,
        },
      ],
      xAxis: [
        {
          type: 'category',
          data: rawDataList.value.map(item => item.month),
        },
      ],
      yAxis: [
        {
          type: 'value',
          axisLine: {
            show: true,
          },
          splitLine: {
            show: false,
          },
        },
      ],
      series: [
        {
          name: 'Public',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          barMaxWidth: 25,
          data: rawDataList.value.map(item => item.dataPublic),
        },
        {
          name: 'Restricted',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          data: rawDataList.value.map(item => item.dataRestricted),
        },
        {
          name: 'Private',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          data: rawDataList.value.map(item => item.dataPrivate),
          barMaxWidth: 22,
        },
        {
          smooth: true,
          name: 'Total data',
          data: rawDataList.value.map(item => item.dataTotal),
          type: 'line',
        },
      ],
    };
    rawDataChart.setOption(rawDataOpt);

    //analysis data
    const analysisDataChart = echarts.init(
      document.getElementById('analysisData'),
    );
    const analysisDataOpt = {
      color: ['#1981F4', '#2ADAC9', '#FEA52B', '#1981F4'],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        confine: true,
      },
      legend: {
        data: ['Public', 'Restricted', 'Private', 'Total data'],
      },
      grid: {
        top: '12%',
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true,
      },
      dataZoom: [
        {
          show: true,
          realtime: true,
          start: 50,
          end: 100,
        },
        {
          type: 'inside',
          realtime: true,
          start: 50,
          end: 100,
        },
      ],
      xAxis: [
        {
          type: 'category',
          data: analDataList.value.map(item => item.month),
        },
      ],
      yAxis: [
        {
          type: 'value',
          axisLine: {
            show: true,
          },
          splitLine: {
            show: false,
          },
        },
      ],
      series: [
        {
          name: 'Public',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          barMaxWidth: 25,
          data: analDataList.value.map(item => item.dataPublic),
        },
        {
          name: 'Restricted',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          data: analDataList.value.map(item => item.dataRestricted),
        },
        {
          name: 'Private',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          data: analDataList.value.map(item => item.dataPrivate),
          barMaxWidth: 22,
        },
        {
          // smooth: true,
          name: 'Total data',
          data: analDataList.value.map(item => item.dataTotal),
          type: 'line',
        },
      ],
    };
    analysisDataChart.setOption(analysisDataOpt);

    //data flow
    const dataFlowChart = echarts.init(document.getElementById('dataFlow'));
    const dataFlowOpt = {
      color: ['#1981F4', '#2ADAC9', '#FEA52B'],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        confine: true,
      },
      legend: {},
      grid: {
        top: '12%',
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true,
      },
      dataZoom: [
        {
          show: true,
          realtime: true,
          start: 60,
          end: 100,
        },
        {
          type: 'inside',
          realtime: true,
          start: 60,
          end: 100,
        },
      ],
      xAxis: [
        {
          type: 'category',
          data: dataFlow.value.map(item => item.month),
        },
      ],
      yAxis: [
        {
          type: 'value',
          axisLine: {
            show: true,
          },
          splitLine: {
            show: false,
          },
        },
      ],
      series: [
        {
          name: 'Download',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          barMaxWidth: 25,
          data: dataFlow.value.map(item => item.download),
        },
        {
          name: 'Upload Data',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          data: dataFlow.value.map(item => item.uploadData),
        },
        {
          name: 'Submission',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          data: dataFlow.value.map(item => item.submission),
        },
      ],
    };
    dataFlowChart.setOption(dataFlowOpt);

    window.onresize = function () {
      rawDataChart.resize();
      analysisDataChart.resize();
      dataFlowChart.resize();
    };
  };

  onMounted(() => {
    nextTick(() => {
      loadMetaData();
    });
  });
</script>

<style scoped lang="scss">
  :deep(.el-select__wrapper) {
    border-radius: 12px;
  }
</style>
