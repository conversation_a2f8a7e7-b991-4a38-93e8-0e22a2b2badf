<template>
  <div class="card list">
    <el-row :gutter="20">
      <el-col :span="21" :xs="24">
        <h3 class="mb-0 mt-0">
          Multiple Omics Proportion Statistics (based on the experiment type in
          project)
        </h3>
      </el-col>
      <el-col :span="3" :xs="24" class="text-align-right">
        <el-button plain type="primary" round :icon="Download"
          >Download</el-button
        ></el-col
      >
    </el-row>
    <el-divider></el-divider>
    <el-row :gutter="0" class="bg-gray radius-12 mb-1 pt-10">
      <el-col :span="12">
        <div id="statistic" style="width: 100%; height: 320px"></div>
      </el-col>
      <el-col :span="12">
        <div id="statistic1" style="width: 100%; height: 320px"></div>
      </el-col>
    </el-row>
  </div>
  <div class="card list mt-1">
    <el-row :gutter="20">
      <el-col :span="21" :xs="24">
        <h3 class="mb-0 mt-0">
          Multiple Omics Proportion Statistics (based on the experiment type
          associated with sample)
        </h3>
      </el-col>
      <el-col :span="3" :xs="24" class="text-align-right">
        <el-button plain type="primary" round :icon="Download"
          >Download</el-button
        ></el-col
      >
    </el-row>
    <el-divider></el-divider>
    <div
      id="omicsPropStat"
      style="width: 100%; height: 360px"
      class="bg-gray radius-12 mb-1 pt-10"
    ></div>
  </div>
</template>

<script setup name="Index">
  import { Download } from '@element-plus/icons-vue';
  import { nextTick, onMounted, ref, reactive } from 'vue';

  import * as echarts from 'echarts';

  const pieData = reactive([
    {
      name: 'Two omics',
      value: 60,
      combinations: [
        { name: 'Genomic+Transcriptomic', value: 30 },
        { name: 'Genomic+Proteomic', value: 20 },
        { name: 'Genomic+Transcriptomic sing cell', value: 10 },
      ],
    },
    { name: 'More than two omics', value: 40 },
  ]);

  const omicsPropData = reactive([
    {
      type: 'Human',
      multiomics: 156,
      singleOmics: 130,
    },
    {
      type: 'Animalia',
      multiomics: 231,
      singleOmics: 130,
    },
    {
      type: 'Plantae',
      multiomics: 200,
      singleOmics: 134,
    },
    {
      type: 'Cell line',
      multiomics: 323,
      singleOmics: 90,
    },
    {
      type: 'Microbe',
      multiomics: 200,
      singleOmics: 130,
    },
    {
      type: 'Environment host',
      multiomics: 200,
      singleOmics: 130,
    },
  ]);
  const totalPropData = omicsPropData.map(it => it.multiomics + it.singleOmics);

  const echartInit = () => {
    const omicsChart = echarts.init(document.getElementById('statistic'));
    const option = {
      color: ['#1981F4', '#07BCB4'],
      tooltip: {
        trigger: 'item',
        formatter: function (params) {
          var tooltipContent = `${params.name}: ${params.value} (${params.percent}%)`;
          if (params.data.combinations) {
            tooltipContent += '<hr>';
            params.data.combinations.forEach(function (combination) {
              tooltipContent +=
                combination.name +
                ': ' +
                combination.value +
                ' (' +
                ((combination.value / params.data.value) * 100).toFixed(2) +
                '%)' +
                '<br>';
            });
          }

          return tooltipContent;
        },
      },
      legend: {
        top: '4%',
      },
      series: [
        {
          type: 'pie',
          selectedMode: 'single',
          center: ['50%', '55%'],
          label: {
            formatter: function (params) {
              return `${params.name} ${params.value}( ${params.percent}% )`;
            },
            show: true,
            // position: 'inner',
          },
          itemStyle: {
            borderColor: '#fff',
            borderWidth: 2,
          },
          labelLine: {
            show: true,
          },
          data: [
            { value: 123, name: 'Sing Omics' },
            { value: 300, name: 'Multiple Omics' },
          ],
        },
      ],
    };
    omicsChart.setOption(option);

    const omicsDetailChart = echarts.init(
      document.getElementById('statistic1'),
    );
    const option1 = {
      color: ['#A296B1', '#768FC7'],
      tooltip: {
        trigger: 'item',
        formatter: function (params) {
          var tooltipContent = `${params.name}: ${params.value} (${params.percent}%)`;
          if (params.data.combinations) {
            tooltipContent += '<hr>';
            params.data.combinations.forEach(function (combination) {
              tooltipContent +=
                combination.name +
                ': ' +
                combination.value +
                ' (' +
                ((combination.value / params.data.value) * 100).toFixed(2) +
                '%)' +
                '<br>';
            });
          }

          return tooltipContent;
        },
      },
      legend: {
        top: '4%',
      },
      series: [
        {
          type: 'pie',
          center: ['55%', '55%'],
          label: {
            formatter: function (params) {
              return `${params.name} ${params.value}( ${params.percent}% )`;
            },

            // show: false,
            // backgroundColor: '#F6F8FC',
            // borderColor: '#768FC7',
            // borderWidth: 1,
            // borderRadius: 8,
            // color: '#666666',
            // fontSize: 14,
            // padding: [10, 10, 10, 10],
            // formatter: function (params) {
            //   var tooltipContent = `{title|${params.name}: ${params.value} (${params.percent}%)}`;
            //   if (params.data.combinations) {
            //     tooltipContent += '\n' + '{hr|}' + '\n';
            //     params.data.combinations.forEach(function (combination) {
            //       tooltipContent += `{name|${combination.name}:} ${
            //         combination.value
            //       } {per| (${(
            //         (combination.value / params.data.value) *
            //         100
            //       ).toFixed(2)}% )}\n`;
            //     });
            //   }
            //   return tooltipContent;
            // },
            // rich: {
            //   title: {
            //     color: '#666666',
            //     lineHeight: 22,
            //     align: 'center',
            //     fontSize: 14,
            //   },
            //   name: {
            //     width: 230,
            //     fontSize: 14,
            //     lineHeight: 23,
            //   },
            //   hr: {
            //     borderColor: '#768FC7',
            //     width: '100%',
            //     borderWidth: 1,
            //     height: 0,
            //     lineHeight: 10,
            //   },
            //   per: {
            //     // color: '#fff',
            //     // backgroundColor: '#07BCB4',
            //     fontSize: 14,
            //     padding: [3, 4],
            //     borderRadius: 4,
            //   },
            // },
          },
          labelLine: {},
          data: pieData,
          itemStyle: {
            borderColor: '#fff',
            borderWidth: 2,
          },
        },
      ],
    };
    omicsDetailChart.setOption(option1);

    //统计多组学占比
    const omicsProportion = echarts.init(
      document.getElementById('omicsPropStat'),
    );
    const omicsPropOpt = {
      color: ['#1981F4', '#2ADAC9', '#FEA52B', '#1981F4'],
      tooltip: {
        trigger: 'item',
        confine: true,
        formatter: function (params) {
          if (params.componentSubType === 'line') {
            return `<div><span class="mr-05">${params.name}:</span>${
              params.value
            }</div>
                    <hr>
                    <div>
                       <span class="tooltip-label">Multiple Omics:</span>
                       <span>${omicsPropData[params.dataIndex].multiomics} (
                      ${
                        (
                          (omicsPropData[params.dataIndex].multiomics /
                            totalPropData[params.dataIndex]) *
                          100
                        ).toFixed(2) + '%'
                      })</span>
                   </div>

                    <div>
                       <span class="tooltip-label">Single Omics:</span>
                       <span>${omicsPropData[params.dataIndex].singleOmics} ( ${
              (
                (omicsPropData[params.dataIndex].singleOmics /
                  totalPropData[params.dataIndex]) *
                100
              ).toFixed(2) + '%'
            })</span>
                   </div>
                `;
          } else {
            return `<div>${params.seriesName}</div>
                    <div>${params.value}</div>
                 `;
          }
        },
      },
      legend: {
        top: '4%',
      },
      grid: {
        top: '12%',
        left: '3%',
        right: '4%',
        bottom: '5%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          data: omicsPropData.map(it => it.type),
        },
      ],
      yAxis: [
        {
          type: 'value',
          axisLine: {
            show: true,
          },
          splitLine: {
            show: false,
          },
        },
      ],
      series: [
        {
          name: 'Multiple Omics',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          barMaxWidth: 25,
          data: omicsPropData.map(it => it.multiomics),
        },
        {
          name: 'Single Omics',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          data: omicsPropData.map(it => it.singleOmics),
        },
        {
          name: 'Total data',
          data: totalPropData,
          type: 'line',
        },
      ],
    };
    omicsProportion.setOption(omicsPropOpt);

    window.onresize = function () {
      auditData.resize();
      omicsProportion.resize();
    };
  };

  onMounted(() => {
    nextTick(() => {
      echartInit();
    });
  });
</script>

<style scoped lang="scss">
  :deep(.el-form-item__label) {
    color: #858181;
  }
  :deep(.tooltip-label) {
    display: inline-block;
    width: 110px;
  }
</style>
