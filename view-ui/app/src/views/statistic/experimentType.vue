<template>
  <div class="card list">
    <el-row :gutter="20">
      <el-col :span="21" :xs="24">
        <h3 class="mb-0 mt-0">Experiment Type Statistic</h3>
      </el-col>
      <el-col :span="3" :xs="24" class="text-align-right">
        <el-button plain type="primary" round :icon="Download"
          >Download</el-button
        ></el-col
      >
    </el-row>
    <el-divider></el-divider>
    <div class="bg-gray p-10-15 radius-12 mb-1">
      <el-form-item label="Experiment Type:" style="margin-bottom: 0">
        <el-checkbox-group v-model="expTypes">
          <el-checkbox
            v-for="item in expTypeOpts"
            :key="item"
            :value="item"
            :label="item"
            style="width: 150px"
            >{{ item }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </div>
    <div
      id="typeStatistic"
      style="width: 100%; height: 360px"
      class="bg-gray radius-12 mb-1 pt-10"
    ></div>
  </div>
</template>

<script setup name="Index">
  import { Download } from '@element-plus/icons-vue';
  // import BarChart from '@/views/statistic/common/barChart.vue';
  import { nextTick, onMounted, ref, reactive } from 'vue';

  import * as echarts from 'echarts';

  const expTypes = ref([]);
  const expTypeOpts = [
    'Genomic',
    'Proteomic',
    'Metagenomic',
    'Microarray',
    'Synthetic',
    'Viral RNA',
    'Metabolomic',
    'Transcriptomic',
    'Metatranscriptomic',
    'Genomic single cell',
    'Transcriptomic single cell',
    'Electron microscopy',
    'Flow cytometry',
  ];

  const statisticData = reactive([
    {
      type: 'Genomic',
      accessible: 200,
      unaccessible: 130,
      publicNum: 400,
      publicSize: 400,
    },
    {
      type: 'Proteomic',
      accessible: 120,
      unaccessible: 120,
    },
    {
      type: 'Metagenomic',
      accessible: 140,
      unaccessible: 123,
    },
    {
      type: 'Microarray',
      accessible: 213,
      unaccessible: 100,
    },
    {
      type: 'Synthetic',
      accessible: 145,
      unaccessible: 150,
    },
    {
      type: 'Viral RNA',
      accessible: 160,
      unaccessible: 50,
    },
    {
      type: 'Metatranscriptomic',
      accessible: 170,
      unaccessible: 90,
    },
  ]);

  const totalData = statisticData.map(it => it.accessible + it.unaccessible);

  const echartInit = () => {
    const stackBarChart = echarts.init(
      document.getElementById('typeStatistic'),
    );
    const stackBarOpt = {
      color: ['#1981F4', '#2ADAC9', '#FEA52B', '#1981F4'],
      tooltip: {
        trigger: 'item',
        confine: true,
        formatter: function (params) {
          if (params.componentSubType === 'line') {
            return `
                 <div><span class="mr-05">${params.name}:</span> ${params.value}</div>
                 <hr>
                 <div><span class="tooltip-label">Public:</span> 155 ( 1012TB )</div>
                 <div><span class="tooltip-label">Restricted:</span> 155 ( 1012TB )</div>
                 <div><span class="tooltip-label">Private:</span> 144 ( 1012TB )</div>`;
          } else {
            return `<div>${params.seriesName}</div>
                 <div>${params.value}</div>
                 `;
          }
        },
      },
      legend: {
        top: '4%',
      },
      grid: {
        top: '14%',
        left: '3%',
        right: '4%',
        bottom: '5%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          data: statisticData.map(it => it.type),
        },
      ],
      yAxis: [
        {
          type: 'value',
          axisLine: {
            show: true,
          },
          splitLine: {
            show: false,
          },
        },
      ],
      series: [
        {
          name: 'Accessible',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          barMaxWidth: 25,
          data: statisticData.map(it => it.accessible),
        },
        {
          name: 'Unaccessible',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          data: statisticData.map(it => it.unaccessible),
        },
        {
          name: 'Total data',
          data: totalData,
          type: 'line',
        },
      ],
    };
    stackBarChart.setOption(stackBarOpt);

    window.onresize = function () {
      stackBarChart.resize();
    };
  };

  onMounted(() => {
    nextTick(() => {
      echartInit();
    });
  });
</script>

<style scoped lang="scss">
  :deep(.el-form-item__label) {
    color: #858181;
  }
  :deep(.tooltip-label) {
    display: inline-block;
    width: 80px;
  }
</style>
