<template>
  <div class="card list">
    <el-row :gutter="20">
      <el-col :span="21" :xs="24">
        <h3 class="mb-0 mt-0">Data Type Statistic</h3>
      </el-col>
      <el-col :span="3" :xs="24" class="text-align-right">
        <el-button plain type="primary" round :icon="Download"
          >Download</el-button
        ></el-col
      >
    </el-row>
    <el-divider></el-divider>
    <div
      id="statistic"
      style="width: 100%; height: 360px"
      class="bg-gray radius-12 mb-1 pt-10"
    ></div>
  </div>
</template>

<script setup name="Index">
  import { Download } from '@element-plus/icons-vue';
  import { nextTick, onMounted, ref, reactive } from 'vue';

  import * as echarts from 'echarts';
  const statisticData = reactive([
    {
      type: 'Fastq',
      private: 60,
      public: 40,
      restricted: 56,
    },
    {
      type: 'Fasta',
      private: 60,
      public: 45,
      restricted: 34,
    },
    {
      type: 'Bam',
      private: 60,
      public: 87,
      restricted: 67,
    },
    {
      type: 'Raw',
      private: 60,
      public: 53,
      restricted: 35,
    },
    {
      type: 'Csv',
      private: 60,
      public: 53,
      restricted: 54,
    },
  ]);
  const totalData = statisticData.map(
    it => it.private + it.public + it.restricted,
  );

  const echartInit = () => {
    const statistiChart = echarts.init(document.getElementById('statistic'));
    const options = {
      color: ['#1981F4', '#2ADAC9', '#FEA52B'],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        confine: true,
        formatter: function (params) {
          return `
                 <div><span class="mr-05">${params[0].name}</div>
                 <hr>
                 <div><span class="tooltip-label">Public:</span> 155 files / 1012 TB </div>
                 <div><span class="tooltip-label">Restricted:</span> 155 files / 1012 TB </div>
                 <div><span class="tooltip-label">Private:</span> 155 files / 1012 TB </div>
                 <div><span class="tooltip-label">Total Data:</span> 456 files / 1012 TB </div>
                 `;
        },
      },
      legend: {
        top: '4%',
      },
      grid: {
        top: '14%',
        left: '3%',
        right: '4%',
        bottom: '5%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          data: statisticData.map(it => it.type),
        },
      ],
      yAxis: [
        {
          type: 'value',
          axisLine: {
            show: true,
          },
          splitLine: {
            show: false,
          },
        },
      ],
      series: [
        {
          name: 'Public',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          data: statisticData.map(it => it.public),
        },
        {
          name: 'Restricted',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          barMaxWidth: 25,
          data: statisticData.map(it => it.restricted),
        },
        {
          name: 'Private',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          barMaxWidth: 25,
          data: statisticData.map(it => it.private),
        },

        {
          name: 'Total data',
          data: totalData,
          type: 'line',
        },
      ],
    };
    statistiChart.setOption(options);

    window.onresize = function () {
      statistiChart.resize();
    };
  };

  onMounted(() => {
    nextTick(() => {
      echartInit();
    });
  });
</script>

<style scoped lang="scss">
  :deep(.el-form-item__label) {
    color: #858181;
  }
  :deep(.tooltip-label) {
    display: inline-block;
    width: 80px;
  }
</style>
