<template>
  <div class="submit-page">
    <div class="container-fluid">
      <Breadcrumb :bread-item="$t('featureData.index.breadcrumb')" />
      <el-row :gutter="20" class="mt-1">
        <el-col :span="4" :xs="24" :md="4" class="mb-1">
          <div class="submitData">
            <div
              class="bubble-right text-primary font-600"
              :class="{ active: isActive === 'HumanResource' }"
              @click="toFeatureData('human')"
            >
              {{ $t('featureData.index.navigation.humanResource') }}
            </div>
            <el-divider />
            <div
              class="bubble-right text-primary font-600"
              :class="{ active: isActive === 'MicrobeResource' }"
              @click="toFeatureData('microbe')"
            >
              {{ $t('featureData.index.navigation.microbeResource') }}
            </div>
            <el-divider />
            <div
              class="bubble-right text-primary font-600"
              :class="{ active: isActive === 'OmicsResource' }"
              @click="toFeatureData('omics')"
            >
              {{ $t('featureData.index.navigation.omicsResource') }}
            </div>
            <el-divider />
            <div
              class="bubble-right text-primary font-600"
              :class="{ active: isActive === 'HmdsNsfcDataSet' }"
              @click="toFeatureData('hmds')"
            >
              {{ $t('featureData.index.navigation.hydrosphere') }}
            </div>
          </div>
        </el-col>
        <el-col :span="20" :xs="24" :md="20">
          <div class="card">
            <component :is="tabs[isActive]"></component>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
  import Breadcrumb from '@/components/breadcrumb.vue';
  import HumanResource from '@/views/featureData/humanRes.vue';
  import MicrobeResource from '@/views/featureData/microbeRes.vue';
  import OmicsResource from '@/views/featureData/omics/index.vue';
  import HmdsNsfcDataSet from '@/views/featureData/hmdsNsfcDataSet.vue';

  import { onMounted, ref } from 'vue';
  import { onBeforeRouteUpdate, useRoute, useRouter } from 'vue-router';

  const route = useRoute();
  const router = useRouter();

  const isActive = ref('');

  const tabs = {
    HumanResource,
    MicrobeResource,
    OmicsResource,
    HmdsNsfcDataSet,
  };

  function toFeatureData(type) {
    router.push({
      name: 'FeatureData',
      params: { fdType: type },
    });
  }

  function init(fdType) {
    if (fdType) {
      fdType = fdType.toLowerCase();
      if (fdType === 'human') {
        isActive.value = 'HumanResource';
      } else if (fdType === 'microbe') {
        isActive.value = 'MicrobeResource';
      } else if (fdType === 'omics') {
        isActive.value = 'OmicsResource';
      } else if (fdType === 'hmds') {
        isActive.value = 'HmdsNsfcDataSet';
      }
    }
  }

  /**
   * 监听路由变更
   */
  onBeforeRouteUpdate((to, from) => {
    init(to.params.fdType);
  });

  onMounted(() => {
    init(route.params.fdType);
  });
</script>

<style lang="scss" scoped>
  .submitData {
    .bubble-right {
      padding: 8px 20px !important;
      &.active {
        background-color: #ebf2fd;
        &:after {
          border-color: transparent transparent transparent #ebf2fd !important;
        }
      }
    }
    .before-circle {
      &.active {
        background-color: #d5e4fe;
        border-radius: 14px;
        color: #3a78e8;
        /* padding: 0 20px; */
        border: 1px solid #3a78e8;
        padding: 2px 6px;
        text-align: center;

        &:before {
          display: none;
          background-color: #3a78e8 !important;
        }
      }

      &:before {
        background-color: #999999 !important;
      }
    }

    .el-form {
      .el-form-item {
        width: 30%;
      }
    }
  }

  .drop-down-title {
    & > span {
      font-weight: 600;
    }
    .el-icon {
      position: relative;
      top: 5px;
    }
  }
  .drop-down-content {
    padding: 0 15px;
    :deep(.el-checkbox__label) {
      color: #666666;
      font-size: 14px !important;
    }
    :deep(.el-tree-node__content) {
      background-color: #f4f8fb;
      .el-tree-node__label {
        color: #666666;
        font-weight: 600;
      }
    }
  }
  .drop-down-title {
    font-weight: 600;
    &.active {
      background-color: #feeee4;
      color: #fe7f2b;
      border-color: #fe7f2b;
    }
  }
</style>
