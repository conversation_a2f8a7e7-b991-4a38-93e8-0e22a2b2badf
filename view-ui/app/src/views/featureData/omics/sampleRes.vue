<template>
  <div class="d-flex justify-space-between mb-1">
    <div>
      <el-input
        v-model="searchID"
        :placeholder="$t('featureData.omics.sampleRes.searchPlaceholder')"
        style="width: 350px"
      ></el-input>
      <el-button class="radius-12 ml-1 mr-1" type="primary"
        >{{ $t('featureData.omics.sampleRes.searchButton') }}
      </el-button>
    </div>
    <div>
      <TypeList
        :original-data="originalData"
        :type-name="$t('featureData.omics.sampleRes.filterType')"
        :type-list="colData.slice(1)"
      ></TypeList>

      <ToolBar :columns="colData" :width="300" :checkbox-width="140"></ToolBar>
    </div>
  </div>
  <el-table
    tooltip-effect="dark"
    :data="sampData"
    :header-cell-style="{
      backgroundColor: '#F8F8F8',
      color: '#333333',
      fontWeight: 700,
    }"
    border
  >
    <el-table-column
      v-if="colData[0].visible"
      prop="projID"
      :label="$t('featureData.omics.sampleRes.columns.projectId')"
      width="100"
    >
      <template #default="scope">
        <router-link
          :to="`/project/detail/${scope.row.projID}`"
          class="text-primary"
        >
          {{ scope.row.projID }}</router-link
        >
      </template>
    </el-table-column>
    <el-table-column
      v-if="colData[1].visible"
      prop="human"
      :label="$t('featureData.omics.sampleRes.columns.human')"
      width="90"
    >
      <template #default="scope">
        <el-icon v-if="scope.row.human === 'Y'" color="#3A78E8"
          ><Select
        /></el-icon>
      </template>
    </el-table-column>
    <el-table-column
      v-if="colData[2].visible"
      prop="animalia"
      :label="$t('featureData.omics.sampleRes.columns.animalia')"
      min-width="100"
    >
      <template #default="scope">
        <el-icon v-if="scope.row.animalia === 'Y'" color="#3A78E8"
          ><Select
        /></el-icon>
      </template>
    </el-table-column>
    <el-table-column
      v-if="colData[3].visible"
      prop="plantae"
      :label="$t('featureData.omics.sampleRes.columns.plantae')"
      min-width="100"
    >
      <template #default="scope">
        <el-icon v-if="scope.row.plantae === 'Y'" color="#3A78E8"
          ><Select
        /></el-icon>
      </template>
    </el-table-column>
    <el-table-column
      v-if="colData[4].visible"
      prop="pathogen"
      :label="
        $t('featureData.omics.sampleRes.columns.pathogenAffectingPublicHealth')
      "
      min-width="240"
    >
      <template #default="scope">
        <el-icon v-if="scope.row.pathogen === 'Y'" color="#3A78E8"
          ><Select
        /></el-icon>
      </template>
    </el-table-column>
    <el-table-column
      v-if="colData[5].visible"
      prop="cellLine"
      :label="$t('featureData.omics.sampleRes.columns.cellLine')"
      width="80"
    >
      <template #default="scope">
        <el-icon v-if="scope.row.cellLine === 'Y'" color="#3A78E8"
          ><Select
        /></el-icon>
      </template>
    </el-table-column>
    <el-table-column
      v-if="colData[6].visible"
      prop="environmentHost"
      :label="$t('featureData.omics.sampleRes.columns.environmentHost')"
      min-width="140"
    >
      <template #default="scope">
        <el-icon v-if="scope.row.environmentHost === 'Y'" color="#3A78E8"
          ><Select
        /></el-icon>
      </template>
    </el-table-column>
    <el-table-column
      v-if="colData[7].visible"
      min-width="180"
      prop="environmentNoHost"
      :label="$t('featureData.omics.sampleRes.columns.environmentNonHost')"
    >
      <template #default="scope">
        <el-icon v-if="scope.row.metatranscriptomic === 'Y'" color="#3A78E8"
          ><Select
        /></el-icon>
      </template>
    </el-table-column>
    <el-table-column
      v-if="colData[8].visible"
      prop="microbe"
      :label="$t('featureData.omics.sampleRes.columns.microbe')"
      min-width="80"
    >
      <template #default="scope">
        <el-icon v-if="scope.row.microbe === 'Y'" color="#3A78E8"
          ><Select
        /></el-icon>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup>
  import ToolBar from '@/components/toolBar.vue';
  import TypeList from './typeList.vue';

  import { getCurrentInstance, reactive, ref } from 'vue';

  const { proxy } = getCurrentInstance();

  const searchID = ref('');
  const originalData = reactive([]);
  const sampData = reactive([
    {
      projID: 'OEP000001',
      human: 'Y',
      animalia: 'Y',
      plantae: '',
      pathogen: '',
      cellLine: '',
      environmentHost: '',
      environmentNoHost: '',
      Microbe: '',
    },
    {
      projID: 'OEP000002',
      human: 'Y',
      animalia: 'Y',
      plantae: '',
      pathogen: '',
      cellLine: '',
      environmentHost: '',
      environmentNoHost: '',
      Microbe: '',
    },
    {
      projID: 'OEP000003',
      human: 'Y',
      animalia: '',
      plantae: '',
      pathogen: 'Y',
      cellLine: '',
      environmentHost: '',
      environmentNoHost: '',
      Microbe: '',
    },
    {
      projID: 'OEP000004',
      human: 'Y',
      animalia: 'Y',
      plantae: '',
      pathogen: 'Y',
      cellLine: '',
      environmentHost: '',
      environmentNoHost: '',
      Microbe: '',
    },
    {
      projID: 'OEP000005',
      human: 'Y',
      animalia: 'Y',
      plantae: '',
      pathogen: 'Y',
      cellLine: '',
      environmentHost: '',
      environmentNoHost: '',
      Microbe: '',
    },
    {
      projID: 'OEP000006',
      human: 'Y',
      animalia: 'Y',
      plantae: 'Y',
      pathogen: '',
      cellLine: '',
      environmentHost: '',
      environmentNoHost: '',
      Microbe: '',
    },
    {
      projID: 'OEP000007',
      human: '',
      animalia: 'Y',
      plantae: 'Y',
      pathogen: '',
      cellLine: '',
      environmentHost: '',
      environmentNoHost: '',
      Microbe: '',
    },
    {
      projID: 'OEP000008',
      human: 'Y',
      animalia: '',
      plantae: 'Y',
      pathogen: '',
      cellLine: '',
      environmentHost: '',
      environmentNoHost: '',
      Microbe: '',
    },
  ]);

  const colData = ref([
    {
      label: proxy.$t('featureData.omics.sampleRes.columns.projectId'),
      visible: true,
      show: true,
    },
    {
      label: proxy.$t('featureData.omics.sampleRes.columns.human'),
      visible: true,
      show: true,
    },
    {
      label: proxy.$t('featureData.omics.sampleRes.columns.animalia'),
      visible: true,
      show: true,
    },
    {
      label: proxy.$t('featureData.omics.sampleRes.columns.plantae'),
      visible: true,
      show: true,
    },
    {
      label: proxy.$t(
        'featureData.omics.sampleRes.columns.pathogenAffectingPublicHealth',
      ),
      visible: true,
      show: true,
    },
    {
      label: proxy.$t('featureData.omics.sampleRes.columns.cellLine'),
      visible: true,
      show: true,
    },
    {
      label: proxy.$t('featureData.omics.sampleRes.columns.environmentHost'),
      visible: true,
      show: true,
    },
    {
      label: proxy.$t('featureData.omics.sampleRes.columns.environmentNonHost'),
      visible: true,
      show: true,
    },
    {
      label: proxy.$t('featureData.omics.sampleRes.columns.microbe'),
      visible: true,
      show: true,
    },
  ]);

  function initFdData() {
    console.log('initFdData2');
  }

  defineExpose({
    initFdData,
  });
</script>

<style lang="scss" scoped></style>
