<template>
  <div v-loading="loadingFlag">
    <div class="d-flex justify-space-between mb-1">
      <div class="hidden-xs-only">
        <el-input
          v-model="searchID"
          :placeholder="$t('featureData.omics.omicsRes.searchPlaceholder')"
          style="width: 350px"
          clearable
          @keyup.enter="queryList"
        ></el-input>
        <el-button
          class="radius-12 ml-1 mr-1"
          type="primary"
          @click="queryList"
        >
          {{ $t('featureData.omics.omicsRes.searchButton') }}
        </el-button>
      </div>

      <div class="hidden-xs-only">
        <TypeList
          :key="`${currTabName}_tpl_${currSearchNum}`"
          :original-data="omicsOriginalData"
          :type-name="typeName"
          :type-list="omicsCol.slice(1)"
          @show-select-row="showSelect"
        ></TypeList>
        &nbsp;
        <ToolBar
          :key="`${currTabName}_tob_${currSearchNum}`"
          :columns="omicsCol"
          :width="300"
          :checkbox-width="140"
        ></ToolBar>
      </div>
    </div>

    <el-table
      :key="`${currTabName}_tb`"
      tooltip-effect="dark"
      :data="omicsData"
      :header-cell-style="{
        backgroundColor: '#F8F8F8',
        color: '#333333',
        fontWeight: 700,
      }"
      border
      height="73vh"
    >
      <template
        v-for="(item, index) in omicsCol"
        :key="`${currTabName}_tb_col_${index}`"
      >
        <el-table-column
          v-if="item.label === firstColName && item.visible"
          prop="projID"
          :label="firstColName"
          :width="colWith(item.label)"
        >
          <template #default="scope">
            <router-link
              :to="`${detailUrlName}${scope.row.id}`"
              class="text-primary"
              target="_blank"
            >
              {{ scope.row.id }}
            </router-link>
          </template>
        </el-table-column>

        <el-table-column
          v-if="item.label !== firstColName && item.visible"
          :prop="item.label"
          :label="item.label"
          :width="colWith(item.label)"
        >
          <template #default="scope">
            <el-icon
              v-if="scope.row.types.includes(item.label)"
              color="#3A78E8"
            >
              <Select />
            </el-icon>
          </template>
        </el-table-column>
      </template>
    </el-table>
  </div>
</template>

<script setup>
  import ToolBar from '@/components/toolBar.vue';
  import TypeList from './typeList.vue';
  import {
    computed,
    defineProps,
    getCurrentInstance,
    reactive,
    ref,
  } from 'vue';
  import { fdOmicsDetail } from '@/api/system/featureData';
  import { isArrEmpty, trimStr } from '@/utils';

  const props = defineProps({
    currTabName: {
      type: String,
      required: true,
      default: () => '',
    },
  });

  const { proxy } = getCurrentInstance();

  // 加载中标志位
  const loadingFlag = ref(false);
  // 当前选项卡名称
  const { currTabName } = props;
  // 请求次数，用于配置key来刷新子组件
  const currSearchNum = ref(1);

  // 第一列标题
  const firstColName = computed(() => {
    return currTabName === 'sampleOmics'
      ? proxy.$t('featureData.omics.omicsRes.columns.sampleId')
      : proxy.$t('featureData.omics.omicsRes.columns.projectId');
  });

  // 详情页地址
  const detailUrlName = computed(() => {
    return currTabName === 'sampleOmics'
      ? '/sample/detail/'
      : '/project/detail/';
  });

  // 行筛选按钮名称
  const typeName = computed(() => {
    return currTabName === 'sampleRes'
      ? proxy.$t('featureData.omics.omicsRes.filterTypes.sampleType')
      : proxy.$t('featureData.omics.omicsRes.filterTypes.experimentType');
  });

  // id筛选框值
  const searchID = ref('');

  // 当前表格数据（类型筛选所勾选的数据）
  const omicsData = reactive([]);
  // 当前表格所有后端返回的原始数据
  const omicsOriginalData = reactive([]);
  // 当前表格表头名数组
  const omicsCol = ref([]);

  // 根据表格标题长度计算列宽度
  function colWith(label) {
    label = trimStr(label);
    if (label) {
      let length = label.length;
      if (length > 16) {
        return 100 + length * 5;
      } else if (length > 10) {
        return 100 + length * 3;
      } else {
        return 100 + length * 2;
      }
    } else {
      return 100;
    }
  }

  // 类型筛选器勾选后，更新表格数据
  function showSelect(rows) {
    omicsData.length = 0;
    omicsData.push(...rows);
  }

  // 查询表格数据
  function queryList() {
    loadingFlag.value = true;
    let param = {
      searchNo: trimStr(searchID.value),
      omicsType: currTabName,
    };
    omicsCol.value = [];
    omicsData.length = 0;
    omicsOriginalData.length = 0;
    fdOmicsDetail(param)
      .then(res => {
        addRenderNum();
        loadingFlag.value = false;
        let resData = res.data;
        if (resData) {
          let { allCol, data } = resData;
          if (!isArrEmpty(allCol)) {
            const omicsColVal = [];
            omicsColVal.push({
              label: firstColName,
              visible: true,
              show: true,
            });
            allCol.forEach((item, index) => {
              omicsColVal.push({ label: item, visible: true, show: true });
            });
            omicsCol.value = omicsColVal;
          }

          if (!isArrEmpty(data)) {
            omicsData.push(...data);
            omicsOriginalData.push(...data);
          }
        }
      })
      .catch(() => {
        addRenderNum();
        loadingFlag.value = false;
      });
  }

  // 查询次数计数器
  function addRenderNum() {
    currSearchNum.value = currSearchNum.value + 1;
  }

  // 父组件调用方法
  function initFdData() {
    queryList();
  }

  // 暴露方法
  defineExpose({
    initFdData,
  });
</script>

<style lang="scss" scoped></style>
