<template>
  <div class="d-flex justify-space-between mb-1">
    <div>
      <el-input
        v-model="searchID"
        :placeholder="$t('featureData.omics.sampleOmics.searchPlaceholder')"
        style="width: 350px"
      ></el-input>
      <el-button class="radius-12 ml-1 mr-1" type="primary"
        >{{ $t('featureData.omics.sampleOmics.searchButton') }}
      </el-button>
    </div>
    <div>
      <TypeList
        :original-data="originalData"
        :type-name="$t('featureData.omics.sampleOmics.filterType')"
        :type-list="colData.slice(2)"
      ></TypeList>
      <ToolBar :columns="colData" :width="300" :checkbox-width="140"></ToolBar>
    </div>
  </div>
  <el-table
    tooltip-effect="dark"
    :data="sampleOmicsData"
    :header-cell-style="{
      backgroundColor: '#F8F8F8',
      color: '#333333',
      fontWeight: 700,
    }"
    border
  >
    <el-table-column
      v-if="colData[0].visible"
      prop="sampID"
      :label="$t('featureData.omics.sampleOmics.columns.sampleId')"
      width="100"
    >
      <template #default="scope">
        <router-link
          :to="`/sample/detail/${scope.row.sampID}`"
          class="text-primary"
        >
          {{ scope.row.sampID }}</router-link
        >
      </template>
    </el-table-column>
    <el-table-column
      v-if="colData[1].visible"
      prop="sampType"
      :label="$t('featureData.omics.sampleOmics.columns.sampleType')"
      show-overflow-tooltip
      width="160"
    />
    <el-table-column
      v-if="colData[1].visible"
      prop="genomic"
      :label="$t('featureData.omics.sampleOmics.columns.genomic')"
      width="90"
    >
      <template #default="scope">
        <el-icon v-if="scope.row.genomic === 'Y'" color="#3A78E8"
          ><Select
        /></el-icon>
      </template>
    </el-table-column>
    <el-table-column
      v-if="colData[2].visible"
      prop="transcriptomic"
      :label="$t('featureData.omics.sampleOmics.columns.transcriptomic')"
      min-width="120"
    >
      <template #default="scope">
        <el-icon v-if="scope.row.transcriptomic === 'Y'" color="#3A78E8"
          ><Select
        /></el-icon>
      </template>
    </el-table-column>
    <el-table-column
      v-if="colData[3].visible"
      prop="transcriptomicSingCell"
      :label="
        $t('featureData.omics.sampleOmics.columns.transcriptomicSingleCell')
      "
      min-width="180"
    >
      <template #default="scope">
        <el-icon v-if="scope.row.transcriptomicSingCell === 'Y'" color="#3A78E8"
          ><Select
        /></el-icon>
      </template>
    </el-table-column>
    <el-table-column
      v-if="colData[4].visible"
      prop="microarray"
      :label="$t('featureData.omics.sampleOmics.columns.microarray')"
      min-width="100"
    >
      <template #default="scope">
        <el-icon v-if="scope.row.microarray === 'Y'" color="#3A78E8"
          ><Select
        /></el-icon>
      </template>
    </el-table-column>
    <el-table-column
      v-if="colData[5].visible"
      prop="proteomic"
      :label="$t('featureData.omics.sampleOmics.columns.proteomic')"
      width="100"
    >
      <template #default="scope">
        <el-icon v-if="scope.row.proteomic === 'Y'" color="#3A78E8"
          ><Select
        /></el-icon>
      </template>
    </el-table-column>
    <el-table-column
      v-if="colData[6].visible"
      prop="metagenomic"
      :label="$t('featureData.omics.sampleOmics.columns.metagenomic')"
      min-width="120"
    >
      <template #default="scope">
        <el-icon v-if="scope.row.metagenomic === 'Y'" color="#3A78E8"
          ><Select
        /></el-icon>
      </template>
    </el-table-column>
    <el-table-column
      v-if="colData[7].visible"
      min-width="150"
      prop="metatranscriptomic"
      :label="$t('featureData.omics.sampleOmics.columns.metatranscriptomic')"
    >
      <template #default="scope">
        <el-icon v-if="scope.row.metatranscriptomic === 'Y'" color="#3A78E8"
          ><Select
        /></el-icon>
      </template>
    </el-table-column>
    <el-table-column
      v-if="colData[8].visible"
      prop="metabolomic"
      :label="$t('featureData.omics.sampleOmics.columns.metabolomic')"
      min-width="110"
    >
      <template #default="scope">
        <el-icon v-if="scope.row.metabolomic === 'Y'" color="#3A78E8"
          ><Select
        /></el-icon>
      </template>
    </el-table-column>
    <el-table-column
      v-if="colData[9].visible"
      prop="other"
      :label="$t('featureData.omics.sampleOmics.columns.other')"
      width="80"
    >
      <template #default="scope">
        <el-icon v-if="scope.row.other === 'Y'" color="#3A78E8"
          ><Select
        /></el-icon>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup>
  import ToolBar from '@/components/toolBar.vue';
  import TypeList from './typeList.vue';

  import { getCurrentInstance, reactive, ref } from 'vue';

  const { proxy } = getCurrentInstance();

  const searchID = ref('');

  const originalData = reactive([]);
  const sampleOmicsData = reactive([
    {
      sampID: 'OES000001',
      sampType: 'Cell line',
      genomic: 'Y',
      transcriptomic: 'Y',
      transcriptomicSingCell: '',
      microarray: '',
      proteomic: '',
      metagenomic: '',
      metatranscriptomic: '',
      metabolomic: '',
      other: '',
    },
    {
      sampID: 'OES000002',
      sampType: 'Cell line',
      genomic: 'Y',
      transcriptomic: 'Y',
      transcriptomicSingCell: '',
      microarray: '',
      proteomic: '',
      metagenomic: '',
      metatranscriptomic: '',
      metabolomic: '',
      other: '',
    },
    {
      sampID: 'OES000003',
      sampType: 'Cell line',
      genomic: 'Y',
      transcriptomic: '',
      transcriptomicSingCell: 'Y',
      microarray: '',
      proteomic: '',
      metagenomic: '',
      metatranscriptomic: '',
      metabolomic: '',
      other: '',
    },
    {
      sampID: 'OES000004',
      sampType: 'Cell line',
      genomic: 'Y',
      transcriptomic: '',
      transcriptomicSingCell: 'Y',
      microarray: '',
      proteomic: '',
      metagenomic: '',
      metatranscriptomic: '',
      metabolomic: '',
      other: '',
    },
    {
      sampID: 'OES000005',
      sampType: 'Environment no-host',
      genomic: '',
      transcriptomic: 'Y',
      transcriptomicSingCell: 'Y',
      microarray: '',
      proteomic: '',
      metagenomic: '',
      metatranscriptomic: '',
      metabolomic: '',
      other: '',
    },
    {
      sampID: 'OES000006',
      sampType: 'Environment no-host',
      genomic: '',
      transcriptomic: 'Y',
      transcriptomicSingCell: 'Y',
      microarray: '',
      proteomic: 'Y',
      metagenomic: '',
      metatranscriptomic: '',
      metabolomic: '',
      other: '',
    },
    {
      sampID: 'OES000007',
      sampType: 'Environment no-host',
      genomic: '',
      transcriptomic: 'Y',
      transcriptomicSingCell: 'Y',
      microarray: '',
      proteomic: 'Y',
      metagenomic: '',
      metatranscriptomic: '',
      metabolomic: '',
      other: '',
    },
    {
      sampID: 'OES000008',
      sampType: 'Environment no-host',

      genomic: '',
      transcriptomic: 'Y',
      transcriptomicSingCell: 'Y',
      microarray: '',
      proteomic: 'Y',
      metagenomic: '',
      metatranscriptomic: '',
      metabolomic: '',
      other: '',
    },
  ]);

  const colData = ref([
    {
      label: proxy.$t('featureData.omics.sampleOmics.columns.sampleId'),
      visible: true,
      show: true,
    },
    {
      label: proxy.$t('featureData.omics.sampleOmics.columns.sampleType'),
      visible: true,
      show: true,
    },
    {
      label: proxy.$t('featureData.omics.sampleOmics.columns.genomic'),
      visible: true,
      show: true,
    },
    {
      label: proxy.$t('featureData.omics.sampleOmics.columns.transcriptomic'),
      visible: true,
      show: true,
    },
    {
      label: proxy.$t(
        'featureData.omics.sampleOmics.columns.transcriptomicSingleCell',
      ),
      visible: true,
      show: true,
    },
    {
      label: proxy.$t('featureData.omics.sampleOmics.columns.microarray'),
      visible: true,
      show: true,
    },
    {
      label: proxy.$t('featureData.omics.sampleOmics.columns.proteomic'),
      visible: true,
      show: true,
    },
    {
      label: proxy.$t('featureData.omics.sampleOmics.columns.metagenomic'),
      visible: true,
      show: true,
    },
    {
      label: proxy.$t(
        'featureData.omics.sampleOmics.columns.metatranscriptomic',
      ),
      visible: true,
      show: true,
    },
    {
      label: proxy.$t('featureData.omics.sampleOmics.columns.metabolomic'),
      visible: true,
      show: true,
    },
    {
      label: proxy.$t('featureData.omics.sampleOmics.columns.other'),
      visible: true,
      show: true,
    },
  ]);

  function initFdData() {
    console.log('initFdData3');
  }

  defineExpose({
    initFdData,
  });
</script>

<style lang="scss" scoped></style>
