<template>
  <el-popover
    :teleported="false"
    placement="bottom"
    width="250"
    trigger="click"
  >
    <template #reference>
      <el-button type="primary" class="radius-12" @click="expand = !expand">
        {{ typeName }}
        <el-icon class="ml-05">
          <ArrowDownBold v-if="expand" />
          <ArrowUpBold v-else />
        </el-icon>
      </el-button>
    </template>
    <div class="d-flex">
      <el-checkbox
        v-model="checkAll"
        @change="handleCheckAllChange"
      ></el-checkbox>
      <el-input
        v-model.trim="filterText"
        class="w-85 ml-05 mr-1"
        placeholder="Search"
        clearable
        @clear="clearable"
      />
    </div>

    <el-checkbox-group v-model="checkList" class="d-flex flex-wrap">
      <el-checkbox
        v-for="item in typeList"
        v-show="item.show"
        :key="item.label"
        :label="item.label"
        style="width: 140px"
        @change="changeRadioStatus"
      />
    </el-checkbox-group>
  </el-popover>
</template>

<script setup>
  import { defineEmits, defineProps, ref, toRaw, watch } from 'vue';
  import { arrContainsAny, isArrEmpty } from '@/utils';

  const props = defineProps({
    typeName: {
      type: String,
    },
    typeList: {
      type: Array,
    },
    originalData: {
      type: Array,
    },
  });

  const { typeList, originalData, typeName } = props;

  const emits = defineEmits(['showSelectRow']);

  const expand = ref(false);

  const filterText = ref('');
  const checkAll = ref(true);
  const checkList = ref(toRaw(typeList).map(item => item.label));

  const map = new Map();

  const getShowCol = () => {
    typeList.forEach(item => {
      if (item.show) {
        map.set(item.label, item.label);
      }
    });
  };

  // 单选事件
  const changeRadioStatus = val => {
    getShowCol();
    const allValuesExist = Array.from(map.values()).every(value =>
      checkList.value.includes(value),
    );
    checkAll.value = !!allValuesExist;
    map.clear();
    showSelect();
  };

  // 全选事件
  const handleCheckAllChange = val => {
    getShowCol();
    map.forEach((item, key) => {
      if (val) {
        checkList.value.push(key);
      } else {
        const index = checkList.value.indexOf(key);
        if (index > -1) {
          checkList.value.splice(index, 1);
        }
      }
    });
    checkList.value = [...new Set(checkList.value)];
    map.clear();
    showSelect();
  };

  const clearable = () => {
    checkAll.value = typeList.length === checkList.value.length ? true : false;
  };

  // 勾选后，更新父组件包含指定类型表格数据
  function showSelect() {
    const selectTypes = checkList.value;
    const currTbData = [];
    if (!isArrEmpty(selectTypes)) {
      let length = originalData.length;
      if (length > 0) {
        let originalDataRaw = toRaw(originalData);
        for (let i = 0; i < length; i++) {
          let item = originalDataRaw[i];
          // 保留包含勾选类型的数据
          if (arrContainsAny(item.types, selectTypes)) {
            currTbData.push(item);
          }
        }
      }
    }
    emits('showSelectRow', currTbData);
  }

  // 模糊筛选
  watch(filterText, (newValue, oldValue) => {
    if (newValue) {
      typeList.forEach(ele => {
        if (ele.label.toLowerCase().indexOf(newValue.toLowerCase()) !== -1) {
          ele.show = true;
        } else {
          ele.show = false;
        }
      });
    } else {
      typeList.forEach(ele => {
        ele.show = true;
      });
    }
  });
</script>

<style lang="scss" scoped></style>
