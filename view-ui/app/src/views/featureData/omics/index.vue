<template>
  <div class="d-flex justify-space-between align-items-center"></div>
  <el-tabs v-model="activeTab" type="card" @tab-change="changeOmicsTab">
    <el-tab-pane
      :label="$t('featureData.omics.index.tabs.multipleOmicsResource')"
      name="omicsRes"
    >
      <OmicsRes
        ref="omicsResRef"
        :key="`omicsResTab_${changeTimes}`"
        :curr-tab-name="'omicsRes'"
      ></OmicsRes>
    </el-tab-pane>
    <el-tab-pane
      :label="$t('featureData.omics.index.tabs.multipleSampleResource')"
      name="sampleRes"
    >
      <OmicsRes
        ref="sampleResRef"
        :key="`sampleResTab_${changeTimes}`"
        :curr-tab-name="'sampleRes'"
      ></OmicsRes>
    </el-tab-pane>
    <el-tab-pane
      :label="$t('featureData.omics.index.tabs.singleSampleMultiOmics')"
      name="sampleOmics"
    >
      <OmicsRes
        ref="sampleOmicsRef"
        :key="`sampleOmicsTab_${changeTimes}`"
        :curr-tab-name="'sampleOmics'"
      ></OmicsRes>
    </el-tab-pane>
  </el-tabs>
</template>

<script setup>
  import OmicsRes from '@/views/featureData/omics/omicsRes.vue';

  import { getCurrentInstance, onMounted, ref } from 'vue';

  const { proxy } = getCurrentInstance();

  const activeTab = ref('omicsRes');
  const changeTimes = ref(0);

  // 选项卡切换事件
  function changeOmicsTab(tabName) {
    // changeTimes.value = changeTimes.value + 1;
    const refObj = proxy.$refs[`${tabName}Ref`];
    if (refObj) {
      refObj.initFdData();
    }
  }

  onMounted(() => {
    // 默认显示第一个选项卡数据
    changeOmicsTab(activeTab.value);
  });
</script>

<style lang="scss" scoped></style>
