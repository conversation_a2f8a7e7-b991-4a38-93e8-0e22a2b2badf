<template>
  <div v-loading="loadingFlag.lvl1">
    <h3 class="text-main-color mt-05 mb-0">
      {{
        microbeFlag
          ? $t('featureData.humanRes.titles.microbeResource')
          : $t('featureData.humanRes.titles.humanResource')
      }}
    </h3>
    <el-divider class="mt-05 mb-1"></el-divider>
    <el-row class="row-gap-15 mt-1" :gutter="15">
      <el-col
        v-for="(it, index) in humanResource"
        :key="`fd_hr_${index}`"
        :span="6"
        :xs="24"
        :md="6"
        :class="activeCardName === it.name ? 'active' : ''"
        @click="clickCard(it)"
      >
        <FeatureData :feature-card="it"></FeatureData>
      </el-col>
    </el-row>

    <div v-loading="loadingFlag.lvl2" class="card mt-1">
      <div class="text-secondary-color font-18 font-600">
        {{ activeCardName }}
      </div>
      <div v-if="trimStr(cat1Info?.prjCount)">
        {{ cat1Info?.prjCount }}
        {{ $t('featureData.humanRes.statistics.project') }},
        {{ cat1Info?.sapCount }}
        {{ $t('featureData.humanRes.statistics.samples') }},
        {{ cat1Info?.sapTypes?.length }}
        {{ $t('featureData.humanRes.statistics.sampleTypes') }},
        {{ cat1Info?.expCount }}
        {{ $t('featureData.humanRes.statistics.experiments') }},
        {{ cat1Info?.expTypes?.length }}
        {{ $t('featureData.humanRes.statistics.experimentTypes') }}
      </div>

      <div
        v-if="!isArrEmpty(cat2List)"
        style="max-height: 50vh; overflow-y: auto"
      >
        <!--分类-->
        <template v-for="(it, index) in cat2List" :key="index + 'category'">
          <div class="sortTwo">
            <el-icon color="#3A78E8">
              <CaretRight />
            </el-icon>
            <span @click="clickCat2(it.cateName)">{{ it.cateName }}</span>
          </div>
          <div class="statistic">
            {{ it.statDTO?.prjCount }}
            {{ $t('featureData.humanRes.statistics.project') }},
            {{ it.statDTO?.sapCount }}
            {{ $t('featureData.humanRes.statistics.samples') }},
            {{ it.statDTO?.sapTypes?.length }}
            {{ $t('featureData.humanRes.statistics.sampleTypes') }},
            {{ it.statDTO?.expCount }}
            {{ $t('featureData.humanRes.statistics.experiments') }},
            {{ it.statDTO?.expTypes?.length }}
            {{ $t('featureData.humanRes.statistics.experimentTypes') }}
          </div>
          <div class="text-primary sortThree">
            <span
              v-for="item in it.cateThree"
              :key="item"
              class="before-circle"
            >
              <span @click="clickCat3(item.cateName, it.cateName)">
                {{ item.cateName }}
              </span>
            </span>
          </div>
        </template>
      </div>

      <div v-loading="loadingFlag.lvl3">
        <div class="text-secondary-color font-18 font-600 mb-05 mt-2">
          {{ $t('featureData.humanRes.tables.projectList') }}
        </div>
        <el-table
          tooltip-effect="dark"
          :data="prjTableData.tableData"
          :header-cell-style="{
            backgroundColor: '#F8F8F8',
            color: '#333333',
            fontWeight: 700,
          }"
          :max-height="500"
          border
          @sort-change="tableWebSort"
        >
          <el-table-column
            sortable
            :sort-orders="['ascending', 'descending']"
            prop="projID"
            :label="$t('featureData.humanRes.tables.columns.projectId')"
            width="120"
          >
            <template #default="scope">
              <router-link
                :to="`/project/detail/${scope.row.projID}`"
                class="text-primary"
                target="_blank"
              >
                {{ scope.row.projID }}
              </router-link>
            </template>
          </el-table-column>
          <el-table-column
            sortable
            :sort-orders="['ascending', 'descending']"
            prop="projName"
            :label="$t('featureData.humanRes.tables.columns.projectName')"
            min-width="170"
            show-overflow-tooltip
          />
          <el-table-column
            prop="des"
            :label="$t('featureData.humanRes.tables.columns.description')"
            min-width="170"
            show-overflow-tooltip
          />
          <el-table-column
            sortable
            :sort-orders="['ascending', 'descending']"
            prop="expType"
            :label="$t('featureData.humanRes.tables.columns.experimentType')"
            width="200"
            show-overflow-tooltip
          />
        </el-table>
        <div style="margin-top: 8px">
          <WebPage
            ref="web_page_prj"
            :key="`web_page_${prjTableData.changeNum}`"
            v-model="prjTableData"
          />
        </div>

        <div v-loading="loadingFlag.lvl4">
          <div class="text-secondary-color font-18 font-600 mb-05 mt-1">
            {{ $t('featureData.humanRes.tables.sampleList') }}
          </div>
          <el-table
            tooltip-effect="dark"
            :data="sampData"
            :header-cell-style="{
              backgroundColor: '#F8F8F8',
              color: '#333333',
              fontWeight: 700,
            }"
            :max-height="500"
            border
            @sort-change="sapSortChange"
          >
            <el-table-column
              sortable
              :sort-orders="['ascending', 'descending']"
              prop="sampID"
              :label="$t('featureData.humanRes.tables.columns.sampleId')"
              width="120"
            >
              <template #default="scope">
                <router-link
                  :to="`/sample/detail/${scope.row.sampID}`"
                  class="text-primary"
                  target="_blank"
                >
                  {{ scope.row.sampID }}
                </router-link>
              </template>
            </el-table-column>
            <el-table-column
              sortable
              :sort-orders="['ascending', 'descending']"
              prop="sampName"
              :label="$t('featureData.humanRes.tables.columns.sampleName')"
              min-width="140"
              show-overflow-tooltip
            />
            <el-table-column
              prop="des"
              :label="$t('featureData.humanRes.tables.columns.description')"
              min-width="140"
              show-overflow-tooltip
            />
            <el-table-column
              sortable
              :sort-orders="['ascending', 'descending']"
              prop="sampType"
              :label="$t('featureData.humanRes.tables.columns.sampleType')"
              min-width="110"
              show-overflow-tooltip
            />
            <el-table-column
              sortable
              :sort-orders="['ascending', 'descending']"
              prop="organism"
              :label="$t('featureData.humanRes.tables.columns.organism')"
              min-width="120"
              show-overflow-tooltip
            />
            <el-table-column
              sortable
              :sort-orders="['ascending', 'descending']"
              prop="tissue"
              :label="$t('featureData.humanRes.tables.columns.tissue')"
              min-width="100"
              show-overflow-tooltip
            />
            <el-table-column
              sortable
              :sort-orders="['ascending', 'descending']"
              prop="expType"
              :label="$t('featureData.humanRes.tables.columns.experimentType')"
              min-width="140"
              show-overflow-tooltip
            />
          </el-table>
          <div style="margin-top: 8px">
            <pagination
              v-show="queryParams.total > 0"
              v-model:page="queryParams.pageNum"
              v-model:limit="queryParams.pageSize"
              :total="queryParams.total"
              class="mb-1"
              @pagination="
                () => {
                  initList(4);
                }
              "
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import FeatureData from '@/views/featureData/common/resourceCard.vue';
  import WebPage from '@/components/Pagination/webPage.vue';
  import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
  import { fdResDetail } from '@/api/system/featureData';
  import { isArrEmpty, trimStr } from '@/utils';
  import { useRoute } from 'vue-router';

  const route = useRoute();

  const props = defineProps({
    // 是否为Microbe Resource
    microbeFlag: {
      type: Boolean,
      required: false,
      default() {
        return false;
      },
    },
  });

  const { proxy } = getCurrentInstance();

  const loadingFlag = reactive({
    lvl1: false,
    lvl2: false,
    lvl3: false,
    lvl4: false,
  });

  const { microbeFlag } = props;

  // 查询参数分类1数据
  const queryCat1 = ref('');
  // 顶部分类1数据
  const humanResource = ref([]);
  // 顶部分类1当前选中项
  const activeCardName = ref('');

  // 中间分类1统计数据
  const cat1Info = ref({});
  // 中间分类2数据
  const cat2List = ref([]);

  // 项目列表数据，使用前端分页插件
  const prjTableData = ref({
    tableData: [],
    originalTableData: [],
    changeNum: 0,
  });

  // 初始化项目前端分页数据
  function initPrjTableData() {
    prjTableData.value.tableData = [];
    prjTableData.value.originalTableData = [];
    prjTableData.value.changeNum = 0;
  }

  // 样本列表数据，使用后端分页
  const sampData = reactive([]);

  // 查询条件
  const queryParams = reactive({
    total: 0,
    pageNum: 1,
    pageSize: 10,
    sortKey: '',
    sortType: '',
    category1: '',
    category2: '',
    category3: '',
    microbeFlag,
  });

  // 分类1点击事件
  function clickCard(it) {
    if (it) {
      changeCat1(it);
      queryParams.category2 = '';
      queryParams.category3 = '';
      initList(2);
    }
  }

  // 分类1数据
  function changeCat1(it) {
    activeCardName.value = it.name;
    queryParams.category1 = microbeFlag ? it.name : it.cat1Val;
  }

  // 分类2点击事件
  const clickCat2 = name => {
    queryParams.category2 = name;
    queryParams.category3 = '';
    initList(3);
  };

  // 分类3点击事件
  const clickCat3 = (name, cat2) => {
    queryParams.category2 = cat2;
    queryParams.category3 = name;
    initList(3);
  };

  // 项目数据较少，使用前端分页、排序
  function tableWebSort(column) {
    let refObj = proxy.$refs['web_page_prj'];
    if (refObj) {
      const { prop, order } = column;
      refObj.handleSortChange(prop, order);
    }
  }

  // 样本数据较多，使用后端分页、排序
  function sapSortChange(column) {
    const { prop, order } = column;
    if (order && prop) {
      let sortKey;
      switch (prop) {
        case 'sampID':
          sortKey = 'typeId';
          break;
        case 'sampName':
          sortKey = 'name';
          break;
        case 'sampType':
          sortKey = 'sampleType';
          break;
        case 'organism':
        case 'tissue':
          sortKey = prop;
          break;
        case 'expType':
          sortKey = 'relaExpType';
          break;
      }
      if (sortKey) {
        queryParams.sortKey = sortKey;
        queryParams.sortType = order === 'ascending' ? 'asc' : 'desc';
        initList(4);
      }
    }
  }

  function changeLoadingByLvl(lvl, val) {
    loadingFlag[`lvl${lvl}`] = val;
  }

  // 加载当前页面数据
  function initList(lvl) {
    changeLoadingByLvl(lvl, true);
    if (lvl <= 1) {
      humanResource.value = [];
    }
    if (lvl <= 2) {
      cat1Info.value = {};
      cat2List.value = [];
    }
    if (lvl <= 3) {
      initPrjTableData();
    }
    if (lvl <= 4) {
      sampData.length = 0;
    }
    fdResDetail(queryParams)
      .then(res => {
        changeLoadingByLvl(lvl, false);
        let data = res.data;
        if (data) {
          const {
            currCat1Item,
            statInfo,
            cat2Data,
            cat1Stat,
            projectInfo,
            sampleInfo,
            sampleTotal,
          } = data;
          queryParams.total = sampleTotal;
          // 回显分类1
          changeCat1(currCat1Item);
          if (lvl <= 1) {
            humanResource.value = statInfo;
            // 从首页跳转过来时显示对应分类1
            let cat1 = trimStr(queryCat1.value);
            if (cat1 && statInfo) {
              queryCat1.value = '';
              let len = statInfo.length;
              if (len > 0) {
                let hasQueryType = false;
                for (let i = 0; i < len; i++) {
                  if (statInfo[i].name === cat1) {
                    hasQueryType = true;
                    clickCard(statInfo[i]);
                    break;
                  }
                }
                if (hasQueryType) {
                  return false;
                }
              }
            }
          }
          if (lvl <= 2) {
            cat1Info.value = cat1Stat;
            cat2List.value = cat2Data;
          }
          if (lvl <= 3) {
            prjTableData.value.originalTableData = projectInfo;
            prjTableData.value.changeNum++;
          }
          if (lvl <= 4) {
            if (!isArrEmpty(sampleInfo)) {
              sampData.push(...sampleInfo);
            }
          }
        } else {
          queryParams.total = 0;
        }
      })
      .catch(() => {
        changeLoadingByLvl(lvl, false);
      });
  }

  onMounted(() => {
    // 设置默认第一次打开分类1
    queryCat1.value = trimStr(route.query?.cat1);
    initList(1);
  });
</script>

<style lang="scss" scoped>
  .active .item {
    border-color: #ebf2fd;
    box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
  }

  .sortTwo {
    color: #3a78e8;
    font-size: 17px;
    display: flex;
    align-items: center;
    margin-top: 0.6rem;

    & > span {
      font-weight: 600;
      cursor: pointer;

      &:hover {
        color: #3568cb;
      }
    }
  }

  .statistic {
    margin: 3px 0 3px 3px;
    padding-left: 12px;
    color: #999999;
  }

  .sortThree {
    margin-left: 4px;
    padding-left: 12px;
    cursor: pointer;

    .before-circle {
      color: #3a78e8;
      margin-right: 2rem;
      margin-top: 13px;

      &:before {
        background-color: #3a78e8;
      }
    }
  }
</style>
<style lang="scss">
  .el-popper.is-dark {
    max-width: 400px;
    font-size: 14px;
  }
</style>
