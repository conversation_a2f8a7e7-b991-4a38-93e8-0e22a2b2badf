<template>
  <div class="checkedTable">
    <div class="d-flex align-items-center mb-1">
      <el-input
        v-model="queryParams.name"
        :placeholder="$t('submit.rawdata.checkTable.search.placeholder')"
        clearable
        class="w-50"
        @keydown.enter="getDataList"
      />
      <span class="font-600 text-main-color ml-1 mr-1">{{
        $t('submit.rawdata.checkTable.search.period')
      }}</span>
      <el-date-picker
        v-model="dateRange"
        value-format="YYYY-MM-DD"
        type="daterange"
        :range-separator="$t('submit.rawdata.checkTable.search.rangeSeparator')"
        :start-placeholder="$t('submit.rawdata.checkTable.search.startDate')"
        :end-placeholder="$t('submit.rawdata.checkTable.search.endDate')"
      />
      <el-button class="radius-12 ml-1 mr-1" type="primary" @click="getDataList"
        >{{ $t('submit.rawdata.checkTable.search.searchBtn') }}
      </el-button>
    </div>
    <el-divider />
    <div class="status-select mb-1">
      <span class="font-600 text-main-color mr-2"
        >{{ $t('submit.rawdata.checkTable.filter.status') }}:</span
      >
      <el-radio-group v-model="queryParams.status" @change="getDataList">
        <el-radio label="">{{
          $t('submit.rawdata.checkTable.filter.all')
        }}</el-radio>
        <el-radio label="排队中">{{
          $t('submit.rawdata.checkTable.filter.queuing')
        }}</el-radio>
        <el-radio label="校验中">{{
          $t('submit.rawdata.checkTable.filter.checking')
        }}</el-radio>
        <el-radio label="校验失败">{{
          $t('submit.rawdata.checkTable.filter.failed')
        }}</el-radio>
      </el-radio-group>
    </div>
    <el-table
      v-loading="loading"
      :data="tableData"
      stripe
      style="width: 100%; margin-bottom: 20px"
      :header-cell-style="{
        backgroundColor: '#f2f2f2',
        color: '#333333',
        fontWeight: 700,
      }"
      :row-key="row => row.id"
      border
      default-expand-all
      :default-sort="defaultSort"
      @sort-change="handleSortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :reserve-selection="true" type="selection" width="55" />
      <el-table-column
        prop="name"
        :label="$t('submit.rawdata.checkTable.table.fileName')"
        sortable
      />
      <el-table-column
        prop="createTime"
        :label="$t('submit.rawdata.checkTable.table.uploadDate')"
        sortable
      />
      <el-table-column
        prop="status"
        :label="$t('submit.rawdata.checkTable.table.status')"
        sortable
      >
        <template #default="scope">
          <div class="d-flex align-items-center">
            <el-icon :color="iconColor(scope.row.status)">
              <CircleCheckFilled
                v-if="scope.row.status === '校验成功'"
              ></CircleCheckFilled>
              <CircleCloseFilled v-else-if="scope.row.status === '校验失败'" />
              <RemoveFilled v-else-if="scope.row.status === '排队中'" />
              <svg
                v-else
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                width="16"
                height="16"
                viewBox="0 0 16 16"
                fill="none"
              >
                <g opacity="1" transform="translate(0 0)  rotate(0)">
                  <path
                    id="椭圆 135"
                    fill-rule="evenodd"
                    style="fill: #3a78e8"
                    opacity="1"
                    d="M8 0C3.58 0 0 3.58 0 8C0 12.42 3.58 16 8 16C12.42 16 16 12.42 16 8C16 3.58 12.42 0 8 0Z"
                  ></path>
                  <path
                    id="路径 1377"
                    style="fill: #ffffff; opacity: 1"
                    d="M10.024,4.21101l-4.22998,4.23l-0.70711,-0.7071v-1h5.82999c0.8914,0 1.3374,1.07818 0.7064,1.70785l-4.77002,4.76004c-0.04648,0.04633 -0.09704,0.08773 -0.15167,0.1242c-0.05463,0.0364 -0.11229,0.06713 -0.17298,0.0922c-0.06069,0.02507 -0.12325,0.04397 -0.18766,0.0567c-0.06441,0.01273 -0.12944,0.01907 -0.19511,0.019c-0.06566,-0.00007 -0.13068,-0.00653 -0.19507,-0.0194c-0.06438,-0.01287 -0.12689,-0.0319 -0.18753,-0.0571c-0.06063,-0.0252 -0.11823,-0.05607 -0.17279,-0.0926c-0.05455,-0.03653 -0.10502,-0.07803 -0.1514,-0.1245c-0.04639,-0.04647 -0.08779,-0.09703 -0.12421,-0.1517c-0.03642,-0.0546 -0.06716,-0.11227 -0.09223,-0.173c-0.02506,-0.06067 -0.04396,-0.1232 -0.05671,-0.1876c-0.01274,-0.06447 -0.01908,-0.1295 -0.01901,-0.1951c0.00007,-0.06567 0.00655,-0.1307 0.01942,-0.1951c0.01288,-0.0644 0.03192,-0.1269 0.05711,-0.1875c0.02519,-0.06067 0.05605,-0.11827 0.09259,-0.1728c0.03653,-0.0546 0.07804,-0.10507 0.12452,-0.1514l4.77005,-4.76004l0.7063,0.70785v1h-5.82999c-0.8909,0 -1.33707,-1.07715 -0.7071,-1.70711l4.23,-4.23c0.04643,-0.04643 0.09694,-0.08788 0.15153,-0.12436c0.0546,-0.03648 0.11223,-0.06728 0.17289,-0.09241c0.06066,-0.02513 0.12319,-0.0441 0.18759,-0.05691c0.0644,-0.01281 0.12943,-0.01921 0.19509,-0.01921c0.06566,0 0.13069,0.0064 0.19509,0.01921c0.0644,0.01281 0.12693,0.03178 0.1876,0.05691c0.06066,0.02513 0.11829,0.05593 0.17288,0.09241c0.0546,0.03648 0.10511,0.07793 0.15152,0.12436c0.04647,0.04643 0.08793,0.09694 0.1244,0.15154c0.03647,0.05459 0.06727,0.11222 0.0924,0.17288c0.02513,0.06067 0.0441,0.1232 0.0569,0.1876c0.0128,0.06439 0.0192,0.12942 0.0192,0.19509c0,0.06566 -0.0064,0.13069 -0.0192,0.19509c-0.0128,0.06439 -0.03177,0.12692 -0.0569,0.18759c-0.02513,0.06066 -0.05593,0.11829 -0.0924,0.17289c-0.03647,0.05459 -0.07793,0.1051 -0.1244,0.15153z"
                  ></path>
                </g>
              </svg>
            </el-icon>
            <span
              :style="{ color: iconColor(scope.row.status) }"
              v-text="getStatus(scope.row.status)"
            ></span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="failCause"
        :label="$t('submit.rawdata.checkTable.table.failedCause')"
        sortable
      />
    </el-table>
    <pagination
      v-show="dataTotal > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="dataTotal"
      @pagination="getDataList"
    />
    <el-divider />
    <div class="text-align-right">
      <el-button class="btn-round-primary" round @click="ftpFileBatchVerify">
        <el-icon>
          <Check />
        </el-icon>
        <span class="integrity">{{
          $t('submit.rawdata.checkTable.actions.dataIntegrityCheck')
        }}</span>
      </el-button>
      <el-button
        class="btn-round-primary"
        :icon="Download"
        round
        @click="handleExport"
        >{{ $t('submit.rawdata.checkTable.actions.export') }}
      </el-button>
      <el-button
        class="btn-round-warning"
        round
        :icon="Delete"
        @click="handleDelete"
        >{{ $t('submit.rawdata.checkTable.actions.delete') }}
      </el-button>
    </div>
  </div>
</template>
<script setup>
  import { Delete, Download } from '@element-plus/icons-vue';
  import { getCurrentInstance, onMounted, reactive, toRefs } from 'vue';
  import {
    deleteFtpFile,
    listChecking,
    verifyFtpFile,
  } from '@/api/metadata/data';

  const { proxy } = getCurrentInstance();
  defineExpose({
    getDataList,
  });
  /** 响应式数据 */
  const data = reactive({
    tableData: [],
    dataTotal: 0,
    queryParams: {
      name: '',
      status: '',
      pageNum: 1,
      pageSize: 10,
    },
    dateRange: [],
    checkTableSelected: [],
    defaultSort: { prop: 'createTime', order: 'descending' },
    loading: true,
  });

  const {
    tableData,
    dataTotal,
    queryParams,
    dateRange,
    checkTableSelected,
    defaultSort,
    loading,
  } = toRefs(data);

  onMounted(() => {
    getDataList();
  });

  function getDataList() {
    loading.value = true;
    listChecking(proxy.addDateRange(queryParams.value, dateRange.value))
      .then(response => {
        tableData.value = response.rows;
        dataTotal.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  /** 排序触发事件 */
  function handleSortChange(column) {
    if (column.order) {
      queryParams.value.orderByColumn = column.prop;
      queryParams.value.isAsc = column.order;
      getDataList();
    }
  }

  /** handleSelectionChange */
  function handleSelectionChange(selection) {
    // 过滤出不是dir的文件的path
    checkTableSelected.value = selection;
  }

  /* 批量校验sftp里面的文件 */
  function ftpFileBatchVerify() {
    // 过滤出不是dir且有md5的文件的path
    let checkSelected = checkTableSelected.value.filter(
      item => item.status === '校验失败',
    );
    if (checkSelected.length === 0) {
      proxy.$modal.msgError(
        proxy.$t('submit.rawdata.checkTable.messages.selectFailedFiles'),
      );
      return;
    }
    proxy.$modal
      .confirm(
        proxy.$t('submit.rawdata.checkTable.messages.confirmReVerify', {
          files: checkSelected.map(x => x.relativePath).join('<br>'),
        }),
      )
      .then(() => {
        return verifyFtpFile(checkSelected.map(it => it.id));
      })
      .then(() => {
        getDataList();
      });
  }

  /* 批量删除文件 */
  function handleDelete() {
    let checkSelected = checkTableSelected.value.filter(
      item => item.status === '校验失败',
    );
    if (checkSelected.length === 0) {
      proxy.$modal.msgError(
        proxy.$t('submit.rawdata.checkTable.messages.selectFilesToDelete'),
      );
      return;
    }
    proxy.$modal
      .confirm(
        proxy.$t('submit.rawdata.checkTable.messages.confirmDelete', {
          files: checkSelected.map(x => x.relativePath).join('<br>'),
        }),
      )
      .then(() => {
        return deleteFtpFile(checkSelected.map(it => it.id));
      })
      .then(() => {
        getDataList();
      });
  }

  /* 批量导出 */
  function handleExport() {
    let b = checkTableSelected.value.length === 0;
    let content = '';
    if (b) {
      content = proxy.$t('submit.rawdata.checkTable.messages.exportAll');
    } else {
      content = proxy.$t('submit.rawdata.checkTable.messages.exportSelected');
    }

    const downloadParams = b
      ? { ...queryParams.value }
      : { ids: checkTableSelected.value.map(it => it.id) };

    proxy.$modal.confirm(content).then(() => {
      proxy.download(
        '/upload/data/export/checking',
        downloadParams,
        `checking_${new Date().getTime()}.xlsx`,
      );
    });
  }

  const iconColor = status => {
    if (status === '排队中') {
      return '#07BCB4';
    } else if (status === '校验失败') {
      return '#FF8989';
    } else if (status === '校验成功') {
      return '#999999';
    } else return '#3A78E8';
  };
  const getStatus = status => {
    if (status === '排队中') {
      return ' ' + proxy.$t('submit.rawdata.checkTable.status.queuing');
    } else if (status === '校验失败') {
      return ' ' + proxy.$t('submit.rawdata.checkTable.status.failed');
    } else if (status === '校验中') {
      return ' ' + proxy.$t('submit.rawdata.checkTable.status.checking');
    } else return proxy.$t('submit.rawdata.checkTable.status.checkSuccess');
  };
</script>

<style scoped lang="scss">
  :deep(.el-divider--horizontal) {
    margin: 5px 0;
  }
</style>
