<template>
  <div
    v-loading="loading"
    class="mb-05 d-flex justify-space-between align-items-center select-column pos-relative plr-20 mt-1"
  >
    <el-popover :teleported="false" :width="300">
      <template #reference>
        <el-tag
          effect="light"
          class="font-600 select-col-btn"
          size="large"
          @click="openPopover"
        >
          <el-icon>
            <Menu />
          </el-icon>
          Select Column
        </el-tag>
      </template>
      <div class="text-align-center text-main-color font-600">
        Column visibility
      </div>
      <el-radio-group v-model="colShow" class="ml-4" @change="colVisibility">
        <el-radio label="all" size="large">
          <span class="text-main-color col-radio">All columns</span>
        </el-radio>
        <el-radio label="required" size="large">
          <span class="required-tag text-main-color col-radio"
            >Required columns</span
          >
        </el-radio>
        <el-radio label="recommend" size="large">
          <span class="recommend-tag text-main-color col-radio"
            >Recommended columns</span
          >
        </el-radio>
        <el-radio label="optional" size="large">
          <span class="optional-tag text-main-color col-radio"
            >Optional columns</span
          >
        </el-radio>
      </el-radio-group>
      <el-divider class="mt-05 mb-05"></el-divider>
      <div class="d-flex">
        <el-checkbox
          v-model="checkAll"
          @change="handleCheckAllChange"
        ></el-checkbox>
        <el-input
          v-model.trim="filterText"
          class="w-85 ml-05"
          placeholder="Search column"
          clearable
          @clear="clearable"
        />
      </div>
      <el-checkbox-group
        :key="'rg-key-' + rgkey"
        v-model="checkList"
        class="d-flex flex-wrap"
      >
        <el-checkbox
          v-for="item in hotColumns"
          v-show="item.show"
          :key="item.title"
          :label="item.title"
          @change="changeRadioStatus"
          >{{ item.title }}
        </el-checkbox>
      </el-checkbox-group>
      <div class="d-flex mt-1 justify-center">
        <el-button
          type="primary"
          class="popover-btn"
          :size="'small'"
          @click="selectColumn"
          >Confirm
        </el-button>
      </div>
    </el-popover>
    <div>
      <span class="text-secondary-color font-600 mr-05 color-key"
        >Color key:</span
      >
      <el-tag round class="mr-05 required-tag">Required filed</el-tag>
      <el-tag round class="mr-05 recommend-tag">Recommended filed</el-tag>
      <el-tag round class="mr-05 optional-tag">Optional filed</el-tag>
      <el-tag round class="invalid-tag">Invalid cell</el-tag>
    </div>
  </div>
  <div class="plr-20 mt-05">
    <div id="hotTable" class="pos-relative text-center"></div>
  </div>
</template>
<script setup>
  import { nextTick, onActivated, onMounted, reactive, ref, watch } from 'vue';
  import Handsontable from 'handsontable';
  import { getDicts } from '@/api/system/dict/data';
  import { getMultiAnalysisBySubNo } from '@/api/metadata/analysis';
  import useSubmissionStore from '@/store/modules/metadata';
  import { storeToRefs } from 'pinia';
  import {
    myDropdownValidator,
    myNumericValidator,
    requiredValidator,
  } from '@/utils/ht/validator';
  import { isArraysEqual, trimStr } from '@/utils';

  const submissionStore = useSubmissionStore();
  const { subNo } = storeToRefs(submissionStore);
  let rgkey = ref(0);
  let node_analysis_type = reactive([]);
  let loading = ref(false);
  /** 获取字典数据 */
  onMounted(() => {
    loading.value = true;
    let promise1 = getDicts('node_analysis_type').then(response => {
      node_analysis_type = response.data.map(p => ({
        label: p.dictLabel,
        value: p.dictValue,
        elTagType: p.listClass,
        elTagClass: p.cssClass,
      }));
    });

    let promise2;
    if (subNo.value) {
      promise2 = getMultiAnalysisBySubNo(subNo.value).then(response => {
        hotTableData = response.data;
      });
    } else {
      promise2 = Promise.resolve();
    }

    Promise.all([promise1, promise2])
      .then(() => {
        // 初始化列信息
        initHtColumns();
        // 初始化表格
        initHandsontable();
        stringifyFormData();
        loading.value = false;
      })
      .catch(() => {
        loading.value = false;
      });
  });

  onActivated(() => {
    if (hotInstance.value && hotInstance.value.render) {
      // 解决菜单切换时，表格显示不全bug
      hotInstance.value.render();
    }
  });

  let oldFormStr = ref([]);

  /** 等所有的子组件全部渲染完成后序列化json */
  function stringifyFormData() {
    // 等所有的子组件全部渲染完成后序列化json
    nextTick().then(() => {
      oldFormStr.value = hotInstance.value.getData();
    });
  }

  /** 校验表单数据是否被更改 */
  function validateChanged() {
    return isArraysEqual(oldFormStr.value, hotInstance.value.getData());
  }

  /** excel上传后修改表格数据 */
  function changeTbData(excelData) {
    hotTableData.length = 0;
    excelData.forEach((item, index) => {
      let row = {};
      for (let key2 in item) {
        row[key2] = item[key2]['value'];
      }
      hotTableData.push(row);
    });
    initHandsontable();
  }

  function initHtColumns() {
    let analysisType = node_analysis_type.map(it => it.value);
    analysisType.push('Other');
    hotColumns = [
      {
        title: 'analysis_id',
        type: 'text',
        isRequired: 'optional',
        data: 'analysis_id',
        show: true,
      },
      {
        title: 'analysis_name',
        type: 'text',
        data: 'analysis_name',
        isRequired: 'required',
        validator: requiredValidator,
        des: 'unique name of your analysis',
        show: true,
      },
      {
        title: 'index',
        type: 'numeric',
        data: 'index',
        isRequired: 'optional',
        des: 'If the analysis process has three steps,the index should be 1,2,3',
        validator: myNumericValidator,
        className: 'htCenter',
        show: true,
      },
      {
        title: 'program',
        type: 'text',
        data: 'program',
        isRequired: 'optional',
        des: 'tools used in each step',
        allowInvalid: true,
        show: true,
      },
      {
        title: 'link',
        type: 'text',
        data: 'link',
        des: 'Related link of the tool',
        isRequired: 'optional',
        show: true,
      },
      {
        title: 'version',
        type: 'text',
        data: 'version',
        isRequired: 'optional',
        show: true,
      },
      {
        title: 'note',
        type: 'text',
        data: 'note',
        isRequired: 'optional',
        show: true,
      },
      {
        title: 'output_file',
        type: 'text',
        data: 'output_file',
        isRequired: 'optional',
        show: true,
      },
      {
        title: 'description',
        type: 'text',
        data: 'description',
        isRequired: 'optional',
        show: true,
      },
      {
        title: 'analysis_type',
        data: 'analysis_type',
        isRequired: 'required',
        type: 'dropdown',
        source: analysisType,
        validator: myDropdownValidator,
        className: 'htCenter',
        show: true,
      },
      {
        title: 'other_analysis_type',
        data: 'other_analysis_type',
        isRequired: 'optional',
        des: 'If you select Other for analysis type, custom analysis type needs to be filled in.',
        type: 'text',
        show: true,
      },
      {
        title: 'target_project',
        type: 'text',
        data: 'target_project',
        isRequired: 'recommend',
        show: true,
      },
      {
        title: 'target_experiment',
        type: 'text',
        data: 'target_experiment',
        des: 'Filling the existed experiment id,if the analysis is related to a experiment.Separate multiple associations with semicolons e.g. 0EX000011 ; 0EX000012 ; 0EX000013',
        isRequired: 'recommend',
        show: true,
      },
      {
        title: 'target_sample',
        type: 'text',
        data: 'target_sample',
        isRequired: 'recommend',
        show: true,
      },
      {
        title: 'target_analysis',
        type: 'text',
        data: 'target_analysis',
        isRequired: 'recommend',
        show: true,
      },
      {
        title: 'target_run',
        type: 'text',
        data: 'target_run',
        isRequired: 'recommend',
        show: true,
      },
      {
        title: 'target_data',
        type: 'text',
        data: 'target_data',
        isRequired: 'recommend',
        show: true,
      },
      {
        title: 'target_other_name',
        type: 'text',
        data: 'target_other_name',
        isRequired: 'recommend',
        show: true,
      },
      {
        title: 'target_other_link',
        type: 'text',
        data: 'target_other_link',
        isRequired: 'recommend',
        show: true,
      },
    ];
    rgkey.value++;
  }

  // ht表格初始化
  function initHandsontable() {
    if (hotInstance.value && hotInstance.value.destroy) {
      hotInstance.value.destroy();
    }
    const dataLength = hotTableData.length;
    // 补充空行，防止下拉框无法正常显示
    const minRowCount = 10;
    if (dataLength < minRowCount) {
      for (let i = 0; i < minRowCount - dataLength; i++) {
        hotTableData.push({});
      }
    }
    let tbHeight = 50 + hotTableData.length * 23;
    if (tbHeight < 300) {
      tbHeight = 300;
    } else if (tbHeight > 700) {
      tbHeight = 700;
    }
    const container = document.getElementById('hotTable');
    if (!container) {
      return false;
    }
    hotInstance.value = new Handsontable(container, {
      data: hotTableData,
      colHeaders: true,
      columns: hotColumns,
      maxRows: 5000,
      // height: 'auto', // 自动高度
      height: tbHeight,
      // colWidths: 160, // 固定列宽度，将使autoColumnSize失效
      autoColumnSize: true,
      afterGetColHeader: function (col, th) {
        if (col !== -1) {
          if (hotColumns[col].isRequired === 'required') {
            th.style.backgroundColor = '#e7e5a5';
          } else if (hotColumns[col].isRequired === 'optional') {
            th.style.backgroundColor = '#c8e6cb';
          } else if (hotColumns[col].isRequired === 'recommend') {
            th.style.backgroundColor = 'rgba(99, 144, 224, 0.5)';
          }

          if (hotColumns[col].des) {
            th.classList.add('hoveredTH');
          }
        }
      },
      afterOnCellMouseOver: function (e, coords, th) {
        if (coords.row === -1 && coords.col !== -1) {
          if (hotColumns[coords.col].des) {
            popover = document.createElement('div');
            popover.classList.add('hover-comment');
            popover.innerText = hotColumns[coords.col].des;

            // 设置位置
            const W = popover.offsetWidth / 2;
            const H = popover.offsetHeight / 2;
            popover.style.top = `${event.pageY - H}px`;
            popover.style.left = `${event.pageX - W + 10}px`;
            document.body.appendChild(popover);
          }
        }
      },
      afterOnCellMouseOut: function (e, coords, th) {
        if (
          popover &&
          coords.row === -1 &&
          coords.col !== -1 &&
          hotColumns[coords.col].des
        ) {
          document.body.removeChild(popover);
        }
      },
      comments: true,
      currentRowClassName: 'currentRow', // 突出显示行
      currentColClassName: 'currentCol', // 突出显示列
      rowHeaders: true, // 显示行号
      copyable: true, // 允许复制
      copyPaste: true, //复制粘贴
      filters: true, // 使用过滤功能
      readOnly: false,
      dropdownMenu: true, // 下拉菜单具体功能
      columnSorting: true, // 开启排序
      autoColumnValidation: true,
      // contextMenu: true,
      contextMenu: [
        'row_above',
        'row_below',
        '---------',
        'remove_row',
        'clear_column',
        '---------',
        'undo',
        'redo',
        '---------',
        'make_read_only',
        '---------',
        'alignment',
        '---------',
        'cut',
        'copy',
      ],
      licenseKey: 'non-commercial-and-evaluation', //去除底部非商用声明
      hiddenColumns: {
        columns: [], // 要隐藏的列索引数组
      },
      columnSummary: [],
    });
    hideColPlugin = hotInstance.value.getPlugin('hiddenColumns');
    nextTick(() => {
      if (dataLength !== 0) {
        hotInstance.value.validateCells();
      }
    });
  }

  // 表格数据
  let hotTableData = reactive([]);
  // 表格列配置
  let hotColumns = reactive([]);
  // 表格对象里面的实例变量
  let hotInstance = ref({});
  // 表头描述弹窗
  var popover = null;
  // 表格列隐藏显示插件
  let hideColPlugin = null;
  /**--- 表格列隐藏显示功能 start ---*/
  const visible = ref(false);
  const checkList = ref([]);
  const colShow = ref('');
  const checkAll = ref(false);
  const filterText = ref('');
  const map = new Map();

  const getShowCol = () => {
    hotColumns.forEach(item => {
      if (item.show) {
        map.set(item.title, item.title);
      }
    });
  };

  const selectColumn = () => {
    colShow.value = '';
    const columnIndexes = hotColumns.map((item, index) => index);
    const showIndex = checkList.value.map(item =>
      hotColumns.findIndex(obj => obj.title === item),
    );
    hideColPlugin.hideColumns(columnIndexes);
    hideColPlugin.showColumns(showIndex);
    hotInstance.value.render();
    visible.value = false;
  };
  const handleCheckAllChange = val => {
    getShowCol();
    map.forEach((item, key) => {
      if (val) {
        checkList.value.push(key);
      } else {
        const index = checkList.value.indexOf(key);
        if (index > -1) {
          checkList.value.splice(index, 1);
        }
      }
    });
    checkList.value = [...new Set(checkList.value)];
    map.clear();
  };
  const changeRadioStatus = val => {
    getShowCol();
    const allValuesExist = Array.from(map.values()).every(value =>
      checkList.value.includes(value),
    );
    checkAll.value = !!allValuesExist;
    map.clear();
  };
  const clearable = () => {
    checkAll.value = hotColumns.length === checkList.value.length;
  };
  const colVisibility = () => {
    checkList.value = [];
    const columnIndexes = hotColumns.map((item, index) => index);
    let showIndex;
    if (colShow.value === 'all') {
      showIndex = [...columnIndexes];
    } else {
      showIndex = hotColumns
        .map((it, index) => {
          if (it.isRequired === colShow.value) {
            return index;
          }
        })
        .filter(ele => ele !== undefined);
    }
    hideColPlugin.hideColumns(columnIndexes);
    hideColPlugin.showColumns(showIndex);
    hotInstance.value.render();

    visible.value = false;
  };
  const openPopover = () => {
    visible.value = !visible.value;
    filterText.value = '';
    clearable();
  };

  // 表格列隐藏/显示面板--搜索框字段监听器
  watch(filterText, (newValue, oldValue) => {
    // const keyWord = trim(newVal); // 去除空格;
    let val = trimStr(newValue).toLowerCase();
    if (val) {
      hotColumns.forEach(ele => {
        if (ele.title.toLowerCase().includes(val)) {
          ele.show = true;
        } else {
          ele.show = false;
        }
      });
    } else {
      hotColumns.forEach(ele => {
        ele.show = true;
      });
    }
  });

  // 暴露子组件变量和方法，父组件使用$refs可以调用
  defineExpose({
    hotInstance,
    hotTableData,
    changeTbData,
    validateChanged,
  });
</script>
<style scoped lang="scss">
  .select-column {
    .select-col-btn {
      color: #ffffff;

      :deep(.el-tag__content) {
        display: flex;
        align-items: center;
        font-weight: 600;
        color: #ffffff;
      }

      &:hover {
        cursor: pointer;
      }
    }

    .el-tag {
      color: #333333;
    }

    .required-tag {
      border: none;
      background-color: #e7e5a5;
    }

    .recommend-tag {
      border: none;
      background-color: rgba(143, 169, 218, 0.5);
    }

    .optional-tag {
      border: none;
      background-color: #c8e6cb;
    }

    .invalid-tag {
      border: none;
      background-color: #ffbeba;
    }

    .col-radio {
      padding: 2px 6px;
      border-radius: 12px;
    }

    :deep(.el-radio.el-radio--large .el-radio__label) {
      font-size: 12px !important;
    }

    .color-key {
      font-size: 13px;
    }

    .w-85 {
      width: 85%;
    }

    .popover-btn {
      :deep(span) {
        padding-top: 1px;
      }
    }
  }

  .el-checkbox-group {
    :deep(.el-checkbox) {
      width: 25%;
      margin-right: 65px;
      //&:nth-child(odd) {
      //  margin-right: 70px;
      //}
    }

    :deep(.el-checkbox__label) {
      font-size: 12px;
    }
  }

  :deep(.el-upload-dragger) {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 16px;
    background-color: #eff3f9;

    .el-icon--upload {
      margin-right: 0.5rem;
      font-size: 40px;
      color: #fe7f2b;
      margin-bottom: 0;
    }
  }

  :deep(.el-upload-list) {
    margin: 0;
  }
</style>
