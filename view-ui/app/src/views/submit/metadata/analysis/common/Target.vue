<template>
  <div
    v-for="(item, index) in targetData"
    :key="'target-source' + index"
    class="d-flex bg-gray target-item flex-column"
  >
    <div class="d-flex bg-primary justify-space-between">
      <p class="text-main-color font-600 pl-10">{{ $t('target.title', { index: index + 1 }) }}</p>
      <el-button
        v-if="index === 0"
        class="ml-2"
        circle
        type="primary"
        size="small"
        plain
        @click="addTarget"
      >
        <el-icon>
          <Plus />
        </el-icon>
      </el-button>
      <el-button
        v-else
        class="ml-2"
        circle
        type="warning"
        size="small"
        plain
        @click="removeTarget(index)"
      >
        <el-icon>
          <Minus />
        </el-icon>
      </el-button>
    </div>
    <div class="target-form">
      <el-radio-group
        v-model="targetData[index].type"
        class="ml-4"
        @change="clearNos(index)"
      >
        <el-radio label="project" size="large">{{ $t('target.types.projectId') }}</el-radio>
        <el-radio label="experiment" size="large">{{ $t('target.types.experimentId') }}</el-radio>
        <el-radio label="sample" size="large">{{ $t('target.types.sampleId') }}</el-radio>
        <el-radio label="run" size="large">{{ $t('target.types.runId') }}</el-radio>
        <el-radio label="data" size="large">{{ $t('target.types.dataId') }}</el-radio>
        <el-radio label="analysis" size="large">{{ $t('target.types.analysisId') }}</el-radio>
      </el-radio-group>
      <el-form :model="targetData" label-width="120px">
        <el-form-item :label="$t('target.form.id')">
          <el-select
            v-model="targetData[index].nos"
            class="w-100"
            multiple
            :placeholder="$t('target.placeholders.selectId', { type: getValue(targetData[index].type) })"
            remote
            reverse-keyword
            filterable
            :remote-method="
              keyword => remoteSearch(targetData[index].type, keyword)
            "
            :loading="loading"
            :teleported="false"
            @visible-change="
              visible => handleVisibleChange(targetData[index].type, visible)
            "
          >
            <el-option
              v-for="(it, index) in selOptions"
              :key="'option-' + it.value"
              :label="it.label"
              :value="it.value"
              :disabled="it.value === $t('target.options.noData')"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="  " prop="desc">
          <el-input
            v-model="targetData[index].textNos"
            type="textarea"
            rows="5"
            :placeholder="getTextareaPlaceholder(targetData[index].type)"
          />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script setup>
  import { defineProps, getCurrentInstance, reactive, ref, watch } from 'vue';
  import { getTargetOptions } from '@/api/metadata/analysis';
  import { useI18n } from 'vue-i18n';

  let { proxy } = getCurrentInstance();
  const { t } = useI18n();
  let props = defineProps({
    targetData: {
      type: Array,
      default: () => [],
    },
    isDisabled: {
      type: Boolean,
    },
  });
  let loading = ref(false);

  let getValue = v => {
    const typeMap = {
      project: t('target.types.projectId'),
      experiment: t('target.types.experimentId'),
      sample: t('target.types.sampleId'),
      run: t('target.types.runId'),
      data: t('target.types.dataId'),
      analysis: t('target.types.analysisId'),
    };
    return typeMap[v];
  };

  // 获取文本区域占位符
  const getTextareaPlaceholder = (type) => {
    const examples = {
      project: 'OEP00000001\nOEP00000002\nOEP00000003',
      experiment: 'OEX00000001\nOEX00000002\nOEX00000003',
      sample: 'OES00000001\nOES00000002\nOES00000003',
      run: 'OER00000001\nOER00000002\nOER00000003',
      data: 'OED00000001\nOED00000002\nOED00000003',
      analysis: 'OEZ00000001\nOEZ00000002\nOEZ00000003',
    };
    return t('target.placeholders.textareaExample', { example: examples[type] || '' });
  };

  let targetData = ref(
    props.targetData && props.targetData.length > 0 ? props.targetData : [],
  );

  // 监听用户输入的值，动态修改父组件的值
  watch(
    targetData,
    newVal => {
      proxy.$emit('update:targetData', newVal);
    },
    {
      immediate: true,
      deep: true,
    },
  );

  function clearNos(index) {
    targetData.value[index].nos = [];
    targetData.value[index].textNo = '';
  }

  function addTarget() {
    targetData.value.push({
      type: 'project',
      nos: [],
      textNo: '',
    });
  }

  function removeTarget(index) {
    targetData.value.splice(index, 1);
  }

  let selOptions = ref([]);
  let query = reactive({
    pageNum: 1,
    pageSize: 100,
    name: '',
    type: '',
  });

  /* 远程搜索 */
  function remoteSearch(type, keyword) {
    loading.value = true;
    query.name = keyword;
    query.type = type;

    getTargetOptions(query)
      .then(response => {
        selOptions.value = response.rows;
        if (selOptions.value.length === 0) {
          selOptions.value.push({
            label: t('target.options.noData'),
            value: t('target.options.noData'),
          });
        }
      })
      .finally(() => {
        loading.value = false;
      });
  }

  /* visibleChange 暂时废弃 */
  function handleVisibleChange(type, visible) {
    // if (!visible) {
    //   selOptions.value = [];
    // }
    // if (visible && selOptions.value.length === 0) {
    //   // 第一次显示下拉框时触发远程搜索
    //   remoteSearch(type, '');
    // }
  }
</script>
<style scoped lang="scss">
  .target-item {
    margin-right: 1rem;
    width: 30%;

    :deep(.el-radio__label) {
      font-size: 14px;
      font-weight: 700;
      color: #606266;
    }
  }

  .target-form {
    padding: 0 10px;

    .el-radio {
      width: 110px;
    }

    :deep(.el-form-item__label) {
      width: 27px !important;
      justify-content: flex-start;
    }
  }
</style>
