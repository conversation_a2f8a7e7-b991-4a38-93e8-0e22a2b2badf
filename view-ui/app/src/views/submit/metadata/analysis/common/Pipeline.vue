<template>
  <div class="plr-20 pipeline d-flex mt-1 flex-wrap">
    <div
      v-for="(item, index) in pipelineData"
      :key="item + index"
      class="d-flex pipeline-item flex-column"
    >
      <div class="d-flex bg-primary plr-20 justify-space-between">
        <p class="text-main-color font-600">
          {{ $t('pipeline.step', { step: index + 1 }) }}
        </p>
        <el-button
          v-if="index === 0"
          class="ml-2"
          circle
          type="primary"
          size="small"
          plain
          @click="addPipeline"
        >
          <el-icon>
            <Plus />
          </el-icon>
        </el-button>
        <el-button
          v-else
          class="ml-2"
          circle
          type="warning"
          size="small"
          plain
          @click="removePipeline(index)"
        >
          <el-icon>
            <Minus />
          </el-icon>
        </el-button>
      </div>
      <el-form :model="pipelineData" class="bg-gray plr-20 pipeline-form">
        <el-form-item :label="$t('pipeline.form.program')">
          <el-input v-model="item.program" />
        </el-form-item>
        <el-form-item :label="$t('pipeline.form.link')">
          <el-input v-model="item.link" />
        </el-form-item>
        <el-form-item :label="$t('pipeline.form.version')">
          <el-input v-model="item.version" />
        </el-form-item>
        <el-form-item :label="$t('pipeline.form.output')">
          <el-select
            v-model="item.output"
            class="w-100"
            multiple
            :placeholder="$t('pipeline.placeholders.selectOutput')"
            remote
            reverse-keyword
            filterable
            :remote-method="remoteSearch"
            :loading="loading"
            :teleported="false"
            @visible-change="handleVisibleChange"
          >
            <el-option
              v-for="(it, index) in selOptions"
              :key="'option-' + index"
              :label="it.label"
              :value="it.value"
              :disabled="it.value === $t('pipeline.options.noData')"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('pipeline.form.notes')">
          <el-input v-model="item.note" type="textarea" rows="3" />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script setup>
  import { defineProps, getCurrentInstance, reactive, ref, watch } from 'vue';
  import { getPipelineOptions } from '@/api/metadata/analysis';

  let { proxy } = getCurrentInstance();
  let props = defineProps({
    pipelineData: {
      type: Array,
      default: () => [],
    },
    isDisabled: {
      type: Boolean,
    },
  });
  let pipelineData = ref(
    props.pipelineData && props.pipelineData.length > 0
      ? props.pipelineData
      : [],
  );

  // 监听用户输入的值，动态修改父组件的值
  watch(
    pipelineData,
    newVal => {
      proxy.$emit('update:pipelineData', newVal);
    },
    {
      immediate: true,
      deep: true,
    },
  );

  function addPipeline() {
    pipelineData.value.push({
      program: '',
      link: '',
      version: '',
      notes: '',
      output: [],
    });
  }

  function removePipeline(index) {
    pipelineData.value.splice(index, 1);
  }

  let selOptions = reactive([]);
  let query = reactive({
    pageNum: 1,
    pageSize: 100,
    name: '',
  });
  let loading = ref(false);

  /* 远程搜索 */
  function remoteSearch(keyword) {
    loading.value = true;
    query.name = keyword;
    getPipelineOptions(query)
      .then(response => {
        selOptions = response.rows;
        if (selOptions.length === 0) {
          selOptions.push({
            label: proxy.$t('pipeline.options.noData'),
            value: proxy.$t('pipeline.options.noData'),
          });
        }
      })
      .finally(() => {
        loading.value = false;
      });
  }

  /* visibleChange 暂时废弃 */
  function handleVisibleChange(visible) {
    // if (visible && selOptions.length === 0) {
    //   // 第一次显示下拉框时触发远程搜索
    //   remoteSearch('');
    // }
    // if (!visible) {
    //   selOptions = [];
    // }
  }
</script>
<style lang="scss" scoped>
  .pipeline-item {
    margin-right: 1rem;
    width: 30%;

    :deep(.el-radio__label) {
      font-size: 14px;
      font-weight: 700;
      color: #606266;
    }
  }

  .pipeline-form {
    padding-top: 12px;

    :deep(.el-form-item__label) {
      min-width: 70px;
      justify-content: flex-start;
    }
  }
</style>
