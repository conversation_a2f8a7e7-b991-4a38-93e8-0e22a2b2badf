<template>
  <div class="w-100">
    <div v-loading="isRequesting" class="card card-container">
      <DownloadTemplate
        :key="'downloadTemp' + currStage"
        v-model="isRequesting"
        :curr-stage="currStage"
        :curr-data-type="dataType"
        @change-ht-table-data="changeHtTableData"
      >
      </DownloadTemplate>
      <ht-table
        :key="'anal-ht-table-' + htTableKey"
        ref="analHtTable"
        v-loading="loading"
        :hot-table-data="hotTableData"
        :hot-columns="hotColumns"
      ></ht-table>
      <result-log
        v-if="resultDialogOpen"
        :log-data="resultDialogData"
        :curr-exp-type="
          $t('submit.metadata.analysis.analysisMultiple.resultLog.type')
        "
      >
      </result-log>
      <div class="text-align-right mt-2 pr-20">
        <el-button
          type="primary"
          class="btn-primary btn btn-s btn-shadow"
          round
          @click="continueNext"
          >{{
            $t('submit.metadata.analysis.analysisMultiple.buttons.continue')
          }}
        </el-button>
        <el-button
          type="primary"
          class="btn-primary btn btn-s btn-shadow"
          round
          :disabled="isRequesting"
          @click="saveData"
          >{{
            $t('submit.metadata.analysis.analysisMultiple.buttons.checkSave')
          }}
        </el-button>
        <el-button class="btn-primary btn btn-round" round @click="resetForm"
          >{{ $t('submit.metadata.analysis.analysisMultiple.buttons.reset') }}
        </el-button>
        <el-button
          :disabled="!subNo"
          type="danger"
          class="btn"
          plain
          round
          @click="deleteForm"
          >{{ $t('submit.metadata.analysis.analysisMultiple.buttons.delete') }}
        </el-button>
      </div>
    </div>
    <DeleteLog
      ref="deleteLog"
      :curr-type="
        $t('submit.metadata.analysis.analysisMultiple.deleteLog.type')
      "
    ></DeleteLog>
  </div>
</template>

<script setup>
  import {
    defineEmits,
    getCurrentInstance,
    onMounted,
    reactive,
    ref,
  } from 'vue';
  import { storeToRefs } from 'pinia';
  import useSubmissionStore from '@/store/modules/metadata';
  import { BusEnum } from '@/utils/enums';
  import bus from '@/utils/bus';
  import ResultLog from '@/views/submit/metadata/rawData/common/ResultLog.vue';
  import {
    batchSaveAnalysis,
    deleteAnalysis,
    getMultiAnalysisBySubNo,
  } from '@/api/metadata/analysis';
  import HtTable from '@/components/HtTable/index.vue';
  import { getDicts } from '@/api/system/dict/data';
  import {
    myDropdownValidator,
    myNumericValidator,
    requiredValidator,
  } from '@/utils/ht/validator';
  import DownloadTemplate from '@/views/submit/metadata/rawData/common/DownloadTemplate.vue';
  import DeleteLog from '@/views/submit/metadata/rawData/common/DeleteLog.vue';

  let loading = ref(false);
  let { proxy } = getCurrentInstance();
  // 提交者信息
  const submissionStore = useSubmissionStore();
  // 提交编号
  const { subNo } = storeToRefs(submissionStore);
  const emit = defineEmits(['continueMessage']);
  let htTableKey = ref(0);

  /*********************************************************/
  const isRequesting = ref(false);
  // 当前阶段
  const currStage = ref('analysis');
  let dataType = ref('analysis');

  /**********************************************************/
  function continueNext() {
    let htTbRef = proxy.$refs['analHtTable'];
    if (htTbRef.validateChanged()) {
      sendContinueMessage();
    } else {
      proxy.$modal
        .confirm(
          proxy.$t(
            'submit.metadata.analysis.analysisMultiple.messages.unsavedConfirm',
          ),
        )
        .then(function () {
          sendContinueMessage();
        })
        .catch(() => {});
    }
  }

  function sendContinueMessage() {
    emit('continueMessage', 'ArchivingMultiple');
  }

  /*********************************************************/
  let node_analysis_type = reactive([]);
  let hotTableData = reactive([]);
  let hotColumns = reactive([]);

  onMounted(() => {
    let promise1 = getDicts('node_analysis_type').then(response => {
      node_analysis_type = response.data.map(p => ({
        label: p.dictLabel,
        value: p.dictValue,
        elTagType: p.listClass,
        elTagClass: p.cssClass,
      }));
    });

    let promise2;
    if (subNo.value) {
      loading.value = true;
      promise2 = getMultiAnalysisBySubNo(subNo.value)
        .then(response => {
          // 获取数据
          hotTableData = response.data;
        })
        .finally(() => {
          loading.value = false;
        });
    } else {
      promise2 = Promise.resolve();
    }

    Promise.all([promise1, promise2])
      .then(() => {
        // 初始化列信息
        initHtColumns();
        // 重新渲染组件
        htTableKey.value++;
      })
      .catch(() => {});
  });

  function initHtColumns() {
    let analysisType = node_analysis_type.map(it => it.value);
    analysisType.push('Other');
    hotColumns = [
      {
        title: 'analysis_id',
        type: 'text',
        isRequired: 'optional',
        data: 'analysis_id',
        des: proxy.$t(
          'submit.metadata.analysis.analysisMultiple.columns.analysisId.description',
        ),
        show: true,
      },
      {
        title: 'analysis_name',
        type: 'text',
        data: 'analysis_name',
        isRequired: 'required',
        validator: requiredValidator,
        des: proxy.$t(
          'submit.metadata.analysis.analysisMultiple.columns.analysisName.description',
        ),
        show: true,
      },
      {
        title: 'description',
        type: 'text',
        data: 'description',
        isRequired: 'optional',
        des: proxy.$t(
          'submit.metadata.analysis.analysisMultiple.columns.description.description',
        ),
        show: true,
      },
      {
        title: 'analysis_type',
        data: 'analysis_type',
        isRequired: 'required',
        type: 'dropdown',
        source: analysisType,
        validator: myDropdownValidator,
        className: 'htCenter',
        des: proxy.$t(
          'submit.metadata.analysis.analysisMultiple.columns.analysisType.description',
        ),
        show: true,
      },
      {
        title: 'other_analysis_type',
        data: 'other_analysis_type',
        isRequired: 'optional',
        des: proxy.$t(
          'submit.metadata.analysis.analysisMultiple.columns.otherAnalysisType.description',
        ),
        type: 'text',
        show: true,
      },
      {
        title: proxy.$t(
          'submit.metadata.analysis.analysisMultiple.columns.index.title',
        ),
        type: 'numeric',
        data: 'index',
        isRequired: 'optional',
        des: proxy.$t(
          'submit.metadata.analysis.analysisMultiple.columns.index.description',
        ),
        validator: myNumericValidator,
        className: 'htCenter',
        show: true,
      },
      {
        title: 'program',
        type: 'text',
        data: 'program',
        isRequired: 'optional',
        des: proxy.$t(
          'submit.metadata.analysis.analysisMultiple.columns.program.description',
        ),
        allowInvalid: true,
        show: true,
      },
      {
        title: 'link',
        type: 'text',
        data: 'link',
        des: proxy.$t(
          'submit.metadata.analysis.analysisMultiple.columns.link.description',
        ),
        isRequired: 'optional',
        show: true,
      },
      {
        title: 'version',
        type: 'text',
        data: 'version',
        isRequired: 'optional',
        des: proxy.$t(
          'submit.metadata.analysis.analysisMultiple.columns.version.description',
        ),
        show: true,
      },
      {
        title: 'note',
        type: 'text',
        data: 'note',
        isRequired: 'optional',
        des: proxy.$t(
          'submit.metadata.analysis.analysisMultiple.columns.note.description',
        ),
        show: true,
      },
      {
        title: 'output_file',
        type: 'text',
        data: 'output_file',
        isRequired: 'optional',
        des: proxy.$t(
          'submit.metadata.analysis.analysisMultiple.columns.outputFile.description',
        ),
        show: true,
      },
      {
        title: 'target_project',
        type: 'text',
        data: 'target_project',
        des: proxy.$t(
          'submit.metadata.analysis.analysisMultiple.columns.targetProject.description',
        ),
        isRequired: 'recommend',
        show: true,
      },
      {
        title: 'target_experiment',
        type: 'text',
        data: 'target_experiment',
        des: proxy.$t(
          'submit.metadata.analysis.analysisMultiple.columns.targetExperiment.description',
        ),
        isRequired: 'recommend',
        show: true,
      },
      {
        title: 'target_sample',
        type: 'text',
        data: 'target_sample',
        isRequired: 'recommend',
        des: proxy.$t(
          'submit.metadata.analysis.analysisMultiple.columns.targetSample.description',
        ),
        show: true,
      },
      {
        title: 'target_analysis',
        type: 'text',
        data: 'target_analysis',
        isRequired: 'recommend',
        des: proxy.$t(
          'submit.metadata.analysis.analysisMultiple.columns.targetAnalysis.description',
        ),
        show: true,
      },
      {
        title: 'target_run',
        type: 'text',
        data: 'target_run',
        isRequired: 'recommend',
        des: proxy.$t(
          'submit.metadata.analysis.analysisMultiple.columns.targetRun.description',
        ),
        show: true,
      },
      {
        title: 'target_data',
        type: 'text',
        data: 'target_data',
        isRequired: 'recommend',
        des: proxy.$t(
          'submit.metadata.analysis.analysisMultiple.columns.targetData.description',
        ),
        show: true,
      },
      {
        title: 'target_other_name',
        type: 'text',
        data: 'target_other_name',
        isRequired: 'recommend',
        des: proxy.$t(
          'submit.metadata.analysis.analysisMultiple.columns.targetOtherName.description',
        ),
        show: true,
      },
      {
        title: 'target_other_link',
        type: 'text',
        data: 'target_other_link',
        isRequired: 'recommend',
        des: proxy.$t(
          'submit.metadata.analysis.analysisMultiple.columns.targetOtherLink.description',
        ),
        show: true,
      },
    ];
  }

  /** 上传excel成功后初始化ht表格 */
  function changeHtTableData(excelData) {
    proxy.$refs['analHtTable'].changeTbData(excelData);
  }

  function changeLoadingFlag(flag) {
    isRequesting.value = flag;
  }

  /*********************************************************/
  const resultDialogOpen = ref(false);
  const resultDialogData = ref([]);
  /** 提交数据 */
  const saveData = () => {
    changeLoadingFlag(true);
    proxy.$modal.loading(
      proxy.$t('submit.metadata.analysis.analysisMultiple.messages.saving'),
    );
    if (!subNo.value) {
      // 如果没有subNo代表用户没有保存过Submitter数据，自动保存
      bus.emit(BusEnum.SUBMIT_SUBMITTER, saveForm);
    } else {
      saveForm();
    }
  };

  function saveForm() {
    resultDialogOpen.value = false;
    let htTbRef = proxy.$refs['analHtTable'];
    const { validatePromise, refreshOldData, hotInstance } = htTbRef;
    if (!validatePromise || !refreshOldData) {
      proxy.$modal.closeLoading();
      return;
    }
    validatePromise().then(valid => {
      if (!valid) {
        changeLoadingFlag(false);
        proxy.$modal.closeLoading();
        // 校验失败
        proxy.$modal.msgWarning(
          proxy.$t(
            'submit.metadata.analysis.analysisMultiple.messages.invalidCells',
          ),
        );
      } else {
        let data = hotInstance.getData();
        let titles = hotInstance.getColHeader();
        let paramData = {
          subNo: subNo.value,
          stage: currStage.value,
          datas: data,
          titles: titles,
        };
        batchSaveAnalysis(paramData)
          .then(response => {
            let data = response.data;
            if (data) {
              // 提交失败，展示错误信息
              resultDialogData.value = data;
              resultDialogOpen.value = true;
            } else {
              refreshOldData();
              proxy.$modal.alertSuccess(
                proxy.$t(
                  'submit.metadata.analysis.analysisMultiple.messages.saveSuccess',
                ),
              );
            }
            changeLoadingFlag(false);
          })
          .catch(() => {
            changeLoadingFlag(false);
          })
          .finally(() => {
            proxy.$modal.closeLoading();
          });
      }
    });
  }

  function resetForm() {
    hotTableData.length = 0;
    proxy.$refs['analHtTable'].initHandsontable();
  }

  function deleteForm() {
    proxy.$modal
      .confirm(
        proxy.$t(
          'submit.metadata.analysis.analysisMultiple.messages.deleteConfirm',
        ),
      )
      .then(() => {
        let params = {
          subNo: subNo.value,
          single: false,
        };
        deleteAnalysis(params).then(response => {
          if (response.data) {
            proxy.$refs['deleteLog'].openLog(response.data);
            return;
          }
          proxy.$modal.msgSuccess(
            proxy.$t(
              'submit.metadata.analysis.analysisMultiple.messages.deleteSuccess',
            ),
          );
          resetForm();
        });
      })
      .catch(() => {});
  }
</script>

<style lang="scss" scoped>
  .exp-type {
    margin: 0;
  }

  .select-column {
    .select-col-btn {
      color: #ffffff;

      :deep(.el-tag__content) {
        display: flex;
        align-items: center;
        font-weight: 600;
        color: #ffffff;
      }

      &:hover {
        cursor: pointer;
      }
    }

    .el-tag {
      color: #333333;
    }

    .required-tag {
      border: none;
      background-color: #e7e5a5;
    }

    .recommend-tag {
      border: none;
      background-color: #c8e6cb;
    }

    .invalid-tag {
      border: none;
      background-color: #ffbeba;
    }

    .col-radio {
      padding: 2px 6px;
      border-radius: 12px;
    }

    :deep(.el-radio.el-radio--large .el-radio__label) {
      font-size: 12px !important;
    }

    .color-key {
      font-size: 13px;
    }

    .w-85 {
      width: 85%;
    }

    .popover-btn {
      :deep(span) {
        padding-top: 1px;
      }
    }
  }

  .el-checkbox-group {
    :deep(.el-checkbox) {
      width: 25%;
      margin-right: 65px;
      //&:nth-child(odd) {
      //  margin-right: 70px;
      //}
    }

    :deep(.el-checkbox__label) {
      font-size: 12px;
    }
  }

  :deep(.el-upload-dragger) {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 16px;
    background-color: #eff3f9;

    .el-icon--upload {
      margin-right: 0.5rem;
      font-size: 40px;
      color: #fe7f2b;
      margin-bottom: 0;
    }
  }

  :deep(.el-upload-list) {
    margin: 0;
  }
</style>
