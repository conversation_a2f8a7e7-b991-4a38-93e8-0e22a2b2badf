<template>
  <div class="submit-page">
    <div class="container-fluid">
      <Breadcrumb
        :bread-item="$t('submit.metadata.analysis.index.breadcrumb')"
      />
      <ChooseData :active-menu="activeMenu" class="hidden-xs-only" />
      <div class="d-flex submitData">
        <div class="mr-1" style="flex: 0 0 120px">
          <div
            class="item bubble-right"
            :class="{ active: isActive === 'Submitter' }"
            @click="
              isActive = 'Submitter';
              activeMenu = 'metadata';
            "
          >
            <span class="text-danger">*</span>
            <span class="text-primary font-600">{{
              $t('submit.metadata.analysis.index.submitter')
            }}</span>
          </div>
          <el-divider />
          <div
            class="bubble-right"
            :class="{ active: isActive.includes('Analysis') }"
            @click="activeMenu = 'metadata'"
          >
            <span class="text-primary font-600">{{
              $t('submit.metadata.analysis.index.analysis')
            }}</span>
            <div class="ml-05 d-flex flex-column">
              <span
                class="before-circle"
                :class="{ active: isActive === 'AnalysisMultiple' }"
                @click="isActive = 'AnalysisMultiple'"
                >{{ $t('submit.metadata.analysis.index.multiple') }}</span
              >
              <span
                class="before-circle"
                :class="{ active: isActive === 'AnalysisSingle' }"
                @click="isActive = 'AnalysisSingle'"
                >{{ $t('submit.metadata.analysis.index.single') }}</span
              >
            </div>
          </div>
          <el-divider />

          <div
            class="bubble-right"
            :class="{ active: isActive.includes('Archiving') }"
            @click="activeMenu = 'archiving'"
          >
            <span class="text-danger">*</span>
            <span class="text-primary font-600">{{
              $t('submit.metadata.analysis.index.archiving')
            }}</span>
            <div class="ml-05 d-flex flex-column">
              <span
                class="before-circle"
                :class="{ active: isActive === 'ArchivingMultiple' }"
                @click="isActive = 'ArchivingMultiple'"
                >{{ $t('submit.metadata.analysis.index.multiple') }}</span
              >
              <span
                class="before-circle"
                :class="{ active: isActive === 'ArchivingSingle' }"
                @click="isActive = 'ArchivingSingle'"
                >{{ $t('submit.metadata.analysis.index.single') }}</span
              >
            </div>
          </div>
        </div>

        <transition name="animation" mode="out-in">
          <keep-alive>
            <component
              :is="tabs[isActive]"
              @continue-message="handleMessage"
            ></component>
          </keep-alive>
        </transition>
      </div>
    </div>
  </div>
</template>

<script setup>
  import Breadcrumb from '@/components/breadcrumb.vue';
  import ChooseData from '@/views/submit/components/chooseData.vue';

  import { ref } from 'vue';
  import Submitter from '@/views/submit/metadata/rawData/submitter.vue';
  import AnalysisMultiple from '@/views/submit/metadata/analysis/analysisMultiple.vue';
  import ArchivingSingle from '@/views/submit/metadata/analysis/archivingSingle.vue';
  import ArchivingMultiple from '@/views/submit/metadata/analysis/archivingMultiple.vue';
  import AnalysisSingle from '@/views/submit/metadata/analysis/analysisSingle.vue';

  const isActive = ref('Submitter');
  const activeMenu = ref('metadata');
  const tabs = {
    Submitter,
    AnalysisSingle,
    AnalysisMultiple,
    ArchivingSingle,
    ArchivingMultiple,
  };
  const handleMessage = val => {
    isActive.value = val;
  };
</script>

<style lang="scss" scoped>
  .animation-enter-from,
  .animation-leave-to {
    transform: translateX(20px);
    opacity: 0;
  }

  .animation-enter-to,
  .animation-leave-from {
    opacity: 1;
  }

  .animation-enter-active {
    transition: all 0.7s ease;
  }

  .animation-leave-active {
    transition: all 0.3s cubic-bezier(1, 0.6, 0.6, 1);
  }

  .submitData {
    .before-circle {
      &.active {
        background-color: #fff;
        border-radius: 14px;
        color: #3a78e8;
        /* padding: 0 20px; */
        border: 1px solid #3a78e8;
        padding: 0 15px;
        text-align: center;

        &:before {
          display: none;
          background-color: #3a78e8 !important;
        }
      }

      &:before {
        background-color: #999999 !important;
      }
    }

    .el-form {
      .el-form-item {
        width: 30%;
      }
    }
  }

  .fade-enter-active,
  .fade-leave-active {
    transition:
      opacity 0.3s,
      transform 0.3s;
  }

  .fade-enter {
    opacity: 0;
    //transform: translateX(10px);
  }

  animate__fadeInLeft .fade-leave-to {
    opacity: 0;
    //transform: translateX(-10px);
  }

  @media (max-width: 767px) {
    .submitData {
      margin-top: 1rem;
      flex-direction: column;
    }
  }
</style>
