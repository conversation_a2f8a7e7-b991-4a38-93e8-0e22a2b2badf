<template>
  <div class="archiving w-100">
    <div class="card p-20">
      <h3>
        {{ $t('submit.metadata.rawData.archivingSingle.selectData.title') }}
      </h3>
      <el-tabs v-model="activeName" type="card" class="demo-tabs">
        <el-tab-pane
          :label="
            $t(
              'submit.metadata.rawData.archivingSingle.selectData.unarchivedData',
            )
          "
          name="Unarchived/Archiving Data"
        >
          <pre-archived-table
            ref="unarchivedTableRef"
            v-model:selectedRows="unarchivedSelectRows"
            :show-operate-col="false"
            :show-filter="true"
            table-type="rawData"
            :show-toolbox="false"
            @cancel-archive="handleCancelArchive"
          ></pre-archived-table>
        </el-tab-pane>
        <el-tab-pane
          :label="
            $t(
              'submit.metadata.rawData.archivingSingle.selectData.archivedRawData',
            )
          "
          class="ml5"
        >
          <archived-raw-data-table
            ref="archivedRawDataTable"
            v-model:selected-rows="archivedRawDataSelectRows"
            :show-operate-col="false"
          ></archived-raw-data-table>
        </el-tab-pane>
        <el-tab-pane
          :label="
            $t(
              'submit.metadata.rawData.archivingSingle.selectData.archivedAnalysisData',
            )
          "
          name="Archived Analysis Data"
        >
          <archived-analysis-data-table
            ref="archivedAnalysisDataTable"
            v-model:selected-rows="archivedAnalysisSelectRows"
            :show-operate-col="false"
          ></archived-analysis-data-table>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div class="card card-container mt-1 pt-0">
      <FillTip></FillTip>
      <h3 class="plr-20">
        {{ $t('submit.metadata.rawData.archivingSingle.archiving.title') }}
      </h3>
      <div class="select-item bg-gray d-flex mb-1 p-20">
        <div class="d-flex align-items-center items-selected">
          <el-icon color="#3A78E8" size="20">
            <WarningFilled />
          </el-icon>
          <span class="text-primary ml-1">{{
            unarchivedSelectRows.length +
            archivedAnalysisSelectRows.length +
            archivedRawDataSelectRows.length
          }}</span>
          <span class="ml-05">{{
            $t(
              'submit.metadata.rawData.archivingSingle.archiving.itemsSelected',
            )
          }}</span>
        </div>
        <div class="tag d-flex align-items-center flex-wrap">
          <span
            class="text-primary pointer font-600 mr-1"
            @click="clearAllSelection"
            >{{
              $t('submit.metadata.rawData.archivingSingle.archiving.clear')
            }}</span
          >
          <el-tag
            v-for="(it, index) in unarchivedSelectRows"
            :key="'unarchived-selected-' + index"
            class="mr-05 mx-1"
            effect="light"
            size="large"
            closable
            round
            @close="removeUnarchived(it, index)"
            >{{ it.datNo }}
          </el-tag>
          <el-tag
            v-for="(it, index) in archivedRawDataSelectRows"
            :key="'archived-rawdata-selected-' + index"
            class="mr-05 mx-1"
            effect="light"
            size="large"
            closable
            round
            @close="removeArchivedRawData(it, index)"
            >{{ it.datNo }}
          </el-tag>
          <el-tag
            v-for="(it, index) in archivedAnalysisSelectRows"
            :key="'archived-analysis-selected-' + index"
            class="mr-05 mx-1"
            effect="light"
            size="large"
            closable
            round
            @close="removeArchivedAnalysisData(it, index)"
            >{{ it.datNo }}
          </el-tag>
        </div>
      </div>
      <div class="category-title font-600 text-main-color">
        {{ $t('submit.metadata.rawData.archivingSingle.form.title') }}
      </div>
      <div class="plr-20 bg-gray mt-1">
        <el-form
          ref="runFormRef"
          label-position="top"
          label-width="100px"
          :model="submitForm"
          :inline="true"
          :rules="rules"
          style="padding-top: 8px"
        >
          <el-form-item
            required
            :label="$t('submit.metadata.rawData.archivingSingle.form.project')"
          >
            <el-select
              v-model="submitForm.projectNo"
              :placeholder="
                $t(
                  'submit.metadata.rawData.archivingSingle.form.projectPlaceholder',
                )
              "
              :teleported="false"
              filterable
              clearable
              remote
              reverse-keyword
              :loading="loading"
              :remote-method="keyword => remoteSearch(keyword, 'projectNo')"
              @visible-change="
                visible => handleVisibleChange(visible, 'projectNo')
              "
              @change="handleProjectChange"
            >
              <el-option
                v-for="(it, index) in projectOptions"
                :key="'project-option-' + index"
                :label="it.label"
                :value="it.value"
                :disabled="it.value === 'No data'"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            required
            :label="
              $t('submit.metadata.rawData.archivingSingle.form.experiment')
            "
          >
            <el-select
              v-model="submitForm.expNo"
              :placeholder="
                $t(
                  'submit.metadata.rawData.archivingSingle.form.experimentPlaceholder',
                )
              "
              :teleported="false"
              filterable
              clearable
              remote
              reverse-keyword
              :loading="loading"
              :remote-method="keyword => remoteSearch(keyword, 'expNo')"
              @visible-change="visible => handleVisibleChange(visible, 'expNo')"
              @change="handleExperimentChange"
            >
              <el-option
                v-for="(it, index) in experimentOptions"
                :key="'experiment-option-' + index"
                :label="it.label"
                :value="it.value"
                :disabled="it.value === 'No data'"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            required
            :label="$t('submit.metadata.rawData.archivingSingle.form.sample')"
          >
            <el-select
              v-model="submitForm.sapNo"
              :placeholder="
                $t(
                  'submit.metadata.rawData.archivingSingle.form.samplePlaceholder',
                )
              "
              :teleported="false"
              filterable
              clearable
              remote
              reverse-keyword
              :loading="loading"
              :remote-method="keyword => remoteSearch(keyword, 'sapNo')"
              @visible-change="visible => handleVisibleChange(visible, 'sapNo')"
              @change="handleSampleChange"
            >
              <el-option
                v-for="(it, index) in sampleOptions"
                :key="'sample-option-' + index"
                :label="it.label"
                :value="it.value"
                :disabled="it.value === 'No data'"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            required
            :label="$t('submit.metadata.rawData.archivingSingle.form.run')"
          >
            <el-radio-group v-model="radio">
              <div class="d-flex align-items-center w-100 mb-1">
                <el-radio
                  :label="
                    $t('submit.metadata.rawData.archivingSingle.form.selectRun')
                  "
                  size="large"
                  class="select-run mr-1"
                >
                </el-radio>
                <el-select
                  v-model="submitForm.runNo"
                  style="width: 300px"
                  :teleported="false"
                  :placeholder="
                    $t(
                      'submit.metadata.rawData.archivingSingle.form.runPlaceholder',
                    )
                  "
                  filterable
                  clearable
                  remote
                  reverse-keyword
                  :loading="loading"
                  :remote-method="keyword => remoteSearch(keyword, 'runNo')"
                  @visible-change="
                    visible => handleVisibleChange(visible, 'runNo')
                  "
                  @change="handleRunChange"
                >
                  <el-option
                    v-for="(it, index) in runOptions"
                    :key="'run-option-' + index"
                    :label="it.label"
                    :value="it.value"
                    :disabled="it.value === 'No data'"
                  ></el-option>
                </el-select>
              </div>

              <el-radio
                :label="
                  $t(
                    'submit.metadata.rawData.archivingSingle.form.createNewRun',
                  )
                "
                size="large"
              ></el-radio>
            </el-radio-group>
          </el-form-item>
          <transition name="fade">
            <div v-if="submitForm.createNewRun" class="w-100 newrun">
              <el-form-item
                prop="runName"
                :label="
                  $t(
                    'submit.metadata.rawData.archivingSingle.form.customRunName',
                  )
                "
              >
                <el-input v-model="submitForm.runName" />
              </el-form-item>
              <el-form-item
                class="w-100"
                :label="
                  $t('submit.metadata.rawData.archivingSingle.form.description')
                "
                prop="runDescription"
              >
                <el-input
                  v-model="submitForm.runDescription"
                  type="textarea"
                  rows="5"
                />
              </el-form-item>
            </div>
          </transition>
        </el-form>
      </div>
      <div class="text-align-right mt-2 pr-20">
        <el-button
          type="primary"
          class="btn-primary btn btn-s"
          round
          @click="saveData"
          >{{ $t('submit.metadata.rawData.archivingSingle.buttons.checkSave') }}
        </el-button>
        <el-button
          type="primary"
          class="btn-primary btn btn-s"
          round
          @click="submitSubmission"
          >{{ $t('submit.metadata.rawData.archivingSingle.buttons.submit') }}
        </el-button>
        <el-button class="btn-primary btn btn-round" round @click="resetForm"
          >{{ $t('submit.metadata.rawData.archivingSingle.buttons.reset') }}
        </el-button>
      </div>
    </div>
    <ArchivingDialog ref="archivingDialogRef"></ArchivingDialog>
  </div>
</template>

<script setup>
  import { getCurrentInstance, onActivated, reactive, ref, watch } from 'vue';
  import { getProjectOptionsByPage } from '@/api/metadata/project';
  import { getExperimentOptionsByPage } from '@/api/metadata/experiment';
  import { isStrBlank } from '@/utils';
  import { getSampleOptionsByPage } from '@/api/metadata/sample';
  import { getRunOptionsByPage, validateRunName } from '@/api/metadata/run';
  import { parseTime } from '@/utils/nodeCommon';
  import { cancelArchiving, saveRawDataArchive } from '@/api/metadata/archive';
  import { storeToRefs } from 'pinia';
  import bus from '@/utils/bus';
  import useUserStore from '@/store/modules/user';
  import useSubmissionStore from '@/store/modules/metadata';
  import { BusEnum } from '@/utils/enums';

  import ArchivedAnalysisDataTable from '@/views/submit/rawdata/common/ArchivedAnalysisDataTable.vue';
  import ArchivedRawDataTable from '@/views/submit/rawdata/common/ArchivedRawDataTable.vue';
  import PreArchivedTable from '@/views/submit/rawdata/common/PreArchivedTable.vue';
  import ArchivingDialog from '@/views/submit/components/ArchivingDialog.vue';
  import FillTip from '@/views/submit/components/FillTip.vue';

  let { proxy } = getCurrentInstance();
  const submissionStore = useSubmissionStore();

  const { subNo } = storeToRefs(submissionStore);

  const activeName = ref('Unarchived/Archiving Data');
  let unarchivedSelectRows = ref([]);
  let archivedAnalysisSelectRows = ref([]);
  let archivedRawDataSelectRows = ref([]);
  const { member } = useUserStore();
  const checkRunName = (rule, value, callback) => {
    if (submitForm.createNewRun) {
      if (!value) {
        return callback(
          new Error(
            proxy.$t(
              'submit.metadata.rawData.archivingSingle.validation.runNameRequired',
            ),
          ),
        );
      }
      validateRunName({
        name: submitForm.runName,
      })
        .then(response => {
          if (response && response.msg) {
            callback(new Error(response.msg));
          }
          callback();
        })
        .catch(error => callback(new Error(error)));
    } else {
      callback();
    }
  };

  let rules = reactive({
    runName: [
      {
        required: true,
        validator: checkRunName,
        trigger: 'blur',
      },
      {
        validator: proxy.$validateChinese,
        trigger: 'blur',
      },
    ],
    runDescription: [
      {
        validator: proxy.$validateChinese,
        trigger: 'blur',
      },
    ],
  });

  onActivated(() => {
    generateCustomRunName();
  });

  let projectOptions = ref([]);
  let experimentOptions = ref([]);
  let sampleOptions = ref([]);
  let runOptions = ref([]);

  function generateCustomRunName() {
    //OER_{姓}_{yyMMddhhmm}
    let dateFormat = parseTime(new Date(), '{y}{m}{d}{h}{i}');
    dateFormat = dateFormat.slice(2);
    submitForm.runName = 'OER_' + member.firstName + '_' + dateFormat;
  }

  /** 删除选择的未归档的 */
  function removeUnarchived(row, index) {
    unarchivedSelectRows.value.splice(index, 1);
    proxy.$refs['unarchivedTableRef'].removeSelection(row);
  }

  /** 删除选择的归档的analysis data */
  function removeArchivedAnalysisData(row, index) {
    archivedAnalysisSelectRows.value.splice(index, 1);
    proxy.$refs['archivedAnalysisDataTable'].removeSelection(row);
  }

  /** 删除选择的归档的analysis data */
  function removeArchivedRawData(row, index) {
    archivedRawDataSelectRows.value.splice(index, 1);
    proxy.$refs['archivedRawDataTable'].removeSelection(row);
  }

  /** 删除所有选中的 */
  function clearAllSelection() {
    unarchivedSelectRows.value.forEach(item => {
      proxy.$refs['unarchivedTableRef'].removeSelection(item);
    });
    unarchivedSelectRows.value = [];
    archivedAnalysisSelectRows.value.forEach(item => {
      proxy.$refs['archivedAnalysisDataTable'].removeSelection(item);
    });
    archivedAnalysisSelectRows.value = [];
    archivedRawDataSelectRows.value.forEach(item => {
      proxy.$refs['archivedRawDataTable'].removeSelection(item);
    });
    archivedRawDataSelectRows.value = [];
  }

  let query = reactive({
    pageNum: 1,
    pageSize: 100,
    name: '',
    projectNo: '',
    expNo: '',
    sapNo: '',
    runNo: '',
  });
  let loading = ref(false);

  function remoteSearch(keyword, type) {
    loading.value = true;
    // 重置检索条件
    query.name = '';
    query.projectNo = '';
    query.expNo = '';
    query.sapNo = '';
    if (type === 'projectNo') {
      query.name = keyword;
      projectOptions.value = [];
      getProjectOptionsByPage(query)
        .then(response => {
          projectOptions.value = response.rows;
          if (projectOptions.value.length === 0) {
            projectOptions.value.push({
              label: 'No data',
              value: 'No data',
            });
          }
        })
        .finally(() => {
          loading.value = false;
        });
    }
    if (type === 'expNo') {
      query.name = keyword;
      query.projectNo = submitForm.projectNo;
      experimentOptions.value = [];
      getExperimentOptionsByPage(query)
        .then(response => {
          experimentOptions.value = response.rows;
          if (experimentOptions.value.length === 0) {
            experimentOptions.value.push({
              label: 'No data',
              value: 'No data',
            });
          }
        })
        .finally(() => {
          loading.value = false;
        });
    }
    if (type === 'sapNo') {
      query.name = keyword;
      sampleOptions.value = [];
      getSampleOptionsByPage(query)
        .then(response => {
          sampleOptions.value = response.rows;
          if (sampleOptions.value.length === 0) {
            sampleOptions.value.push({
              label: 'No data',
              value: 'No data',
            });
          }
        })
        .finally(() => {
          loading.value = false;
        });
    }
    if (type === 'runNo') {
      query.name = keyword;
      query.sapNo = submitForm.sapNo;
      query.expNo = submitForm.expNo;
      runOptions.value = [];
      getRunOptionsByPage(query)
        .then(response => {
          runOptions.value = response.rows;
          if (runOptions.value.length === 0) {
            runOptions.value.push({
              label: 'No data',
              value: 'No data',
            });
          }
        })
        .finally(() => {
          loading.value = false;
        });
    }
  }

  /** 暂时废弃 */
  function handleVisibleChange(visible, type) {}

  /** handleProjectChange */
  function handleProjectChange() {
    submitForm.expNo = '';
    submitForm.runNo = '';
  }

  /** handleExperimentChange */
  function handleExperimentChange(val) {
    submitForm.runNo = '';
    experimentOptions.value.forEach(item => {
      if (item.value === val) {
        projectOptions.value.push(item.params['project']);
        submitForm.projectNo = item.params['project'].value;
      }
    });
  }

  /** handleSampleChange */
  function handleSampleChange() {
    submitForm.runNo = '';
  }

  /** handleRunChange */
  function handleRunChange(val) {
    runOptions.value.forEach(item => {
      if (item.value === val) {
        projectOptions.value.push(item.params['project']);
        submitForm.projectNo = item.params['project'].value;
        experimentOptions.value.push(item.params['experiment']);
        submitForm.expNo = item.params['experiment'].value;
        sampleOptions.value.push(item.params['sample']);
        submitForm.sapNo = item.params['sample'].value;
      }
    });
  }

  let submitForm = reactive({
    subNo: '',
    projectNo: '',
    expNo: '',
    sapNo: '',
    createNewRun: false,
    runNo: '',
    datNos: [],
    runName: '',
    runDescription: '',
  });

  let radio = ref(
    proxy.$t('submit.metadata.rawData.archivingSingle.form.selectRun'),
  );
  watch(
    radio,
    newVal => {
      submitForm.createNewRun =
        newVal !==
        proxy.$t('submit.metadata.rawData.archivingSingle.form.selectRun');
    },
    {
      immediate: true,
      deep: true,
    },
  );

  function saveData() {
    proxy.$refs['runFormRef'].validate(valid => {
      if (valid) {
        if (
          unarchivedSelectRows.value.length === 0 &&
          archivedAnalysisSelectRows.value.length === 0 &&
          archivedRawDataSelectRows.value.length === 0
        ) {
          proxy.$modal.alertError(
            proxy.$t(
              'submit.metadata.rawData.archivingSingle.messages.selectDataRequired',
            ),
          );
          return;
        }
        if (isStrBlank(submitForm.projectNo)) {
          proxy.$modal.alertError(
            proxy.$t(
              'submit.metadata.rawData.archivingSingle.messages.projectRequired',
            ),
          );
          return;
        }
        if (isStrBlank(submitForm.expNo)) {
          proxy.$modal.alertError(
            proxy.$t(
              'submit.metadata.rawData.archivingSingle.messages.experimentRequired',
            ),
          );
          return;
        }
        if (isStrBlank(submitForm.sapNo)) {
          proxy.$modal.alertError(
            proxy.$t(
              'submit.metadata.rawData.archivingSingle.messages.sampleRequired',
            ),
          );
          return;
        }
        if (submitForm.createNewRun && isStrBlank(submitForm.runName)) {
          proxy.$modal.alertError(
            proxy.$t(
              'submit.metadata.rawData.archivingSingle.messages.runNameRequired',
            ),
          );
          return;
        }
        if (!submitForm.createNewRun && isStrBlank(submitForm.runNo)) {
          proxy.$modal.alertError(
            proxy.$t(
              'submit.metadata.rawData.archivingSingle.messages.runRequired',
            ),
          );
          return;
        }
        submitForm.datNos = [];
        submitForm.datNos.push(
          ...unarchivedSelectRows.value.map(it => it.datNo),
        );
        submitForm.datNos.push(
          ...archivedAnalysisSelectRows.value.map(it => it.datNo),
        );
        submitForm.datNos.push(
          ...archivedRawDataSelectRows.value.map(it => it.datNo),
        );
        proxy.$modal.loading(
          proxy.$t('submit.metadata.rawData.archivingSingle.messages.saving'),
        );
        if (!subNo.value) {
          bus.emit(BusEnum.SUBMIT_SUBMITTER, saveForm);
        } else {
          saveForm();
        }
      }
    });
  }

  function submitSubmission() {
    if (isStrBlank(subNo.value)) {
      proxy.$modal.alertError(
        proxy.$t(
          'submit.metadata.rawData.archivingSingle.messages.submissionIncomplete',
        ),
      );
      return;
    }
    proxy.$refs['archivingDialogRef'].showDialog(subNo.value);
  }

  /** 保存表单 */
  function saveForm() {
    submitForm.subNo = subNo.value;
    saveRawDataArchive(submitForm)
      .then(() => {
        proxy.$refs['unarchivedTableRef'].getDataList();
        proxy.$refs['archivedAnalysisDataTable'].getDataList();
        proxy.$refs['archivedRawDataTable'].getDataList();
        proxy.$refs['archivingDialogRef'].showDialog(subNo.value);
        resetForm();
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  /** 重置表格 */
  function resetForm() {
    proxy.resetForm('runFormRef');
    submitForm.projectNo = '';
    submitForm.expNo = '';
    submitForm.sapNo = '';
    radio.value = proxy.$t(
      'submit.metadata.rawData.archivingSingle.form.selectRun',
    );
    submitForm.runNo = '';
    submitForm.datNos = [];
    submitForm.runDescription = '';
    generateCustomRunName();
    clearAllSelection();
  }

  /** 取消归档 */
  function handleCancelArchive() {
    let rows = unarchivedSelectRows.value.filter(it => !isStrBlank(it.runNo));
    if (rows.length === 0) {
      proxy.$modal.alertError(
        proxy.$t(
          'submit.metadata.rawData.archivingSingle.messages.selectDataRequired',
        ),
      );
    }
    let data = {
      subNo: subNo.value,
      dataNos: rows.map(it => it.datNo),
      type: 'rawData',
    };
    cancelArchiving(data).then(() => {
      proxy.$refs['unarchivedTableRef'].getDataList();
      proxy.$refs['archivedAnalysisDataTable'].getDataList();
      proxy.$refs['archivedRawDataTable'].getDataList();
      clearAllSelection();
    });
  }
</script>

<style lang="scss" scoped>
  .select-run {
    width: 100px !important;
  }

  :deep(.el-select__wrapper) {
    border-radius: 12px;
  }

  .archiving {
    //table tab
    :deep(.el-tabs--card > .el-tabs__header) {
      border-bottom: none;

      .el-tabs__nav {
        border: none;
      }

      .el-tabs__item {
        margin-left: 3px;
        border-radius: 4px;
        background-color: #edf4fe;
      }

      .el-tabs__item.is-active {
        background-color: #3a78e8;
        color: #fff;
        border-radius: 4px;
      }
    }

    .archived-data,
    .unarchived-data {
      :deep(.el-input__wrapper) {
        border-radius: 12px;
      }

      .date-to {
        margin: 0 0.5rem;
      }

      :deep(.el-table__header .cell) {
        font-weight: 600;
      }

      :deep(.el-radio__input.is-checked) {
        .el-radio__inner {
          border: none;
          background-color: #fe7f2b;
        }

        & + .el-radio__label {
          color: #333333;
        }
      }

      :deep(.el-table td.el-table__cell div) {
        display: flex;
        align-items: center;
      }
    }

    //tag
    .select-item {
      & span {
        font-weight: 600;
        font-size: 16px;
      }
    }

    .newrun {
      transition: all 0.3s linear;

      .links {
        :deep(.el-form-item__label) {
          font-weight: 700;
        }

        :deep(.el-form-item__content) {
          flex-direction: column;
          align-items: flex-start;

          & + .el-form-item__label {
            font-weight: 700;
          }
        }
      }
    }

    .el-form {
      .el-form-item {
        width: 30%;

        .el-select {
          width: 100%;
        }

        .el-radio {
          width: 100%;
          margin-right: 0;

          :deep(.el-radio__label) {
            width: 100%;
          }
        }
      }

      :deep(.el-form-item__label) {
        font-weight: 700;
      }
    }

    :deep(.dialog .el-dialog__body) {
      text-align: center;
      padding: 10px 15px 0 15px;

      a:hover {
        color: #3a78e8;
      }
    }
  }
</style>
