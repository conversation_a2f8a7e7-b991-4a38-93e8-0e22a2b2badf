<template>
  <div class="d-flex align-items-center ml-1">
    <el-icon size="14" color="#3A78E8"><InfoFilled /></el-icon>
    &nbsp;&nbsp;{{ t('submit.recommendTip.prefix') }}&nbsp;
    <strong>{{
      recommendNum === 1 ? t('submit.recommendTip.one') : recommendNum
    }}</strong
    >&nbsp; {{ t('submit.recommendTip.suffix') }} {{ recommendTip }}
  </div>
</template>

<script setup>
  import { useI18n } from 'vue-i18n';

  const { t } = useI18n();

  defineProps({
    recommendNum: {
      type: Number,
      required: true,
      default: 0,
    },
    recommendTip: {
      type: String,
      required: false,
    },
  });
</script>
