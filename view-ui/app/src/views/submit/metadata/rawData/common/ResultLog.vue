<template>
  <el-dialog
    v-model="visible"
    :close-on-click-modal="false"
    :title="t('submit.resultLog.title')"
    width="1000px"
    class="preview-dialog radius-14"
  >
    <div class="ht-parent">
      <div id="resultLogTable"></div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button
          type="primary"
          class="btn-primary btn btn-s btn-shadow"
          round
          :icon="Download"
          @click="exportTableData"
          >{{ t('submit.resultLog.export') }}</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
  import { defineProps, nextTick, onMounted, reactive, ref } from 'vue';
  import { useI18n } from 'vue-i18n';
  import Handsontable from 'handsontable';
  import { Download } from '@element-plus/icons-vue';

  const { t } = useI18n();
  let visible = true;
  let hdTableObj = null;
  const props = defineProps({
    logData: {
      type: Array,
      required: true,
    },
    currExpType: {
      type: String,
      required: false,
    },
  });

  const hotColumns = reactive([
    {
      title: 'Row',
      type: 'text',
      data: 'row',
      cellStyle: 'custom-err-cell',
    },
    {
      title: 'Column',
      type: 'text',
      data: 'column',
    },
    {
      title: 'Value',
      type: 'text',
      data: 'value',
    },
    {
      title: 'Error Message',
      type: 'text',
      data: 'message',
    },
  ]);

  const logData = ref(props.logData);
  const currExpType = ref(props.currExpType);

  function initResultDialog() {
    nextTick().then(() => {
      if (hdTableObj) {
        hdTableObj.destroy();
      }
      let domObj = document.getElementById('resultLogTable');
      hdTableObj = new Handsontable(domObj, {
        data: logData.value,
        colHeaders: true,
        columns: hotColumns,
        comments: true,
        currentRowClassName: 'currentRow', // 突出显示行
        currentColClassName: 'currentCol', // 突出显示列
        height: 500,
        stretchH: 'last',
        width: '100%',
        autoColumnSize: true,
        rowHeaders: true, // 显示行号
        filters: false, // 使用过滤功能
        columnSorting: true, // 开启排序
        licenseKey: 'non-commercial-and-evaluation', //去除底部非商用声明
        autoWrapRow: true,
        autoWrapCol: true,
        className: 'custom-err-table',
      });
    });
  }

  function exportTableData() {
    if (hdTableObj) {
      const exportPlugin = hdTableObj.getPlugin('exportFile');
      exportPlugin.downloadFile('csv', {
        bom: false,
        columnDelimiter: '\t',
        columnHeaders: true,
        exportHiddenColumns: true,
        exportHiddenRows: true,
        fileExtension: 'tsv',
        filename: currExpType.value
          ? `${currExpType.value}-Error-log_[YYYY]-[MM]-[DD]`
          : `error-log_[YYYY]-[MM]-[DD]`,
        mimeType: 'text/tab-separated-values',
        rowDelimiter: '\r\n',
        rowHeaders: false,
      });
    }
  }

  onMounted(() => {
    initResultDialog();
  });
</script>

<style lang="scss">
  .custom-err-cell {
    vertical-align: middle !important;
    color: #000 !important;
    min-width: 100px !important;
  }

  .custom-err-table thead th {
    background-color: #f56c6c1a !important;
  }
</style>
