<template>
  <div class="mt-1">
    <div class="category-title font-600 text-main-color">
      {{ $t('submit.metadata.rawData.common.publications.title') }}
    </div>

    <div
      v-for="(item, idx) in publishArray"
      :key="'Publications-' + idx"
      class="plr-20 bg-gray pos-relative mt-1"
    >
      <!--添加-->
      <el-button
        v-if="idx === 0"
        type="primary"
        circle
        plain
        class="add-btn"
        @click="addPublish"
      >
        <el-icon>
          <Plus />
        </el-icon>
      </el-button>
      <!--删除-->
      <el-button
        v-if="idx !== 0"
        type="warning"
        circle
        plain
        class="add-btn"
        @click="removePublish(idx)"
      >
        <el-icon>
          <Minus />
        </el-icon>
      </el-button>
      <el-form
        label-position="top"
        label-width="100px"
        :rules="rules"
        :model="publishArray[idx]"
        :inline="true"
        style="padding-top: 8px"
      >
        <el-form-item v-show="false" hidden prop="id">
          <el-input v-model="item.id" />
        </el-form-item>
        <el-form-item
          :label="
            $t('submit.metadata.rawData.common.publications.form.journal')
          "
          prop="publication"
        >
          <el-autocomplete
            v-model="item.publication"
            :teleported="false"
            class="w-100"
            :fetch-suggestions="queryJournalSearch"
            clearable
          />
        </el-form-item>
        <el-form-item
          :label="$t('submit.metadata.rawData.common.publications.form.doi')"
          prop="doi"
        >
          <el-input v-model="item.doi" style="width: 90%" />
          <el-tooltip
            :content="
              $t(
                'submit.metadata.rawData.common.publications.form.autoFillTooltip',
              )
            "
          >
            <img
              src="@/assets/images/plosp.png"
              alt=""
              style="margin-left: 5px; width: 20px; cursor: pointer"
              @click="getFromPlosp(idx, item.doi)"
            />
          </el-tooltip>
        </el-form-item>
        <el-form-item
          class="pmid"
          :label="$t('submit.metadata.rawData.common.publications.form.pmid')"
          prop="pmid"
        >
          <el-input v-model="item.pmid" />
        </el-form-item>
        <el-form-item
          :label="$t('submit.metadata.rawData.common.publications.form.title')"
          class="w-100"
          prop="articleName"
        >
          <el-input v-model="item.articleName" />
        </el-form-item>
        <el-form-item
          class="w-100"
          :label="
            $t('submit.metadata.rawData.common.publications.form.reference')
          "
          prop="reference"
        >
          <el-input v-model="item.reference" />
        </el-form-item>
      </el-form>
    </div>
  </div>
  <el-dialog
    v-model="previewDialog"
    :title="$t('submit.metadata.rawData.common.publications.preview.title')"
    class="preview-dialog radius-14"
  >
    <div class="d-flex preview">
      <div class="w-100">
        <span class="title">{{
          $t('submit.metadata.rawData.common.publications.preview.journal')
        }}</span>
        <span class="content" v-text="plospInfo.publication"></span>
      </div>
      <div class="w-100">
        <span class="title">{{
          $t('submit.metadata.rawData.common.publications.preview.doi')
        }}</span>
        <span class="content" v-text="plospInfo.doi"></span>
      </div>
      <div class="w-100">
        <span class="title">{{
          $t('submit.metadata.rawData.common.publications.preview.pmid')
        }}</span>
        <span class="content" v-text="plospInfo.pmid"></span>
      </div>
      <div class="w-100">
        <span class="title">{{
          $t('submit.metadata.rawData.common.publications.preview.articleTitle')
        }}</span>
        <span class="content" v-text="plospInfo.articleName"></span>
      </div>
      <div class="w-100">
        <span class="title">{{
          $t('submit.metadata.rawData.common.publications.preview.reference')
        }}</span>
        <span class="content" v-text="plospInfo.reference"></span>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <div class="text-align-center">
          <el-button
            type="primary"
            class="btn-primary btn btn-s btn-shadow"
            round
            @click="fillIn"
            >{{
              $t('submit.metadata.rawData.common.publications.preview.fillIn')
            }}</el-button
          >
          <el-button
            round
            class="btn-primary btn btn-round"
            @click="previewDialog = false"
            >{{
              $t('submit.metadata.rawData.common.publications.preview.cancel')
            }}</el-button
          >
        </div>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
  import { defineProps, getCurrentInstance, ref, watch } from 'vue';
  import { getPubInfoFromPlosp } from '@/api/app/publish';
  import { isStrBlank } from '@/utils';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    publishData: {
      type: Object,
      default() {
        return [
          {
            id: undefined, // ID
            publication: undefined, // Journal
            doi: undefined,
            pmid: undefined,
            articleName: undefined, // Title
            reference: undefined,
          },
        ];
      },
    },
  });

  let plospInfo = ref({
    id: undefined, // ID
    publication: undefined, // Journal
    doi: undefined,
    pmid: undefined,
    articleName: undefined, // Title
    reference: undefined,
  });

  let previewDialog = ref(false);
  let editIdx = ref(0);

  const rules = ref({
    doi: [
      {
        pattern: /10\.\d{4,}\/\S+/,
        message: proxy.$t(
          'submit.metadata.rawData.common.publications.validation.doiFormat',
        ),
        trigger: 'blur',
      },
      {
        validator: proxy.$validateChinese,
        trigger: 'blur',
      },
    ],
    pmid: [
      {
        pattern: /\b\d{8,9}\b/,
        message: proxy.$t(
          'submit.metadata.rawData.common.publications.validation.pmidFormat',
        ),
        trigger: 'blur',
      },
    ],
    publication: [
      {
        validator: proxy.$validateChinese,
        trigger: 'blur',
      },
    ],
    articleName: [
      {
        validator: proxy.$validateChinese,
        trigger: 'blur',
      },
    ],
    reference: [
      {
        validator: proxy.$validateChinese,
        trigger: 'blur',
      },
    ],
  });

  const publishArray = ref(
    props.publishData && props.publishData.length > 0
      ? props.publishData
      : [
          {
            id: undefined,
            publication: undefined,
            doi: undefined,
            pmid: undefined,
            articleName: undefined,
            reference: undefined,
          },
        ],
  );

  const addPublish = () => {
    publishArray.value.push({
      id: undefined,
      publication: undefined,
      doi: undefined,
      pmid: undefined,
      articleName: undefined,
      reference: undefined,
    });
  };

  const removePublish = index => {
    publishArray.value.splice(index, 1);
  };

  /** 期刊 自动补全提醒 */
  const queryJournalSearch = (queryString, cb) => {
    const results = queryString
      ? node_journal.value.filter(createFilter(queryString))
      : node_journal.value;
    cb(results);
  };
  const createFilter = queryString => {
    return node_journal => {
      return (
        node_journal.value.toLowerCase().indexOf(queryString.toLowerCase()) ===
        0
      );
    };
  };

  watch(
    () => props.publishData,
    newValue => {
      // 使用深拷贝更新第一个输入框的值
      publishArray.value[0] = JSON.parse(
        JSON.stringify(
          newValue && newValue.length > 0
            ? newValue[0]
            : {
                id: undefined,
                publication: undefined,
                doi: undefined,
                pmid: undefined,
                articleName: undefined,
                reference: undefined,
              },
        ),
      );
    },
  );

  watch(
    publishArray,
    newVal => {
      proxy.$emit('update:publishData', newVal);
    },
    {
      immediate: true,
      deep: true,
    },
  );

  function getFromPlosp(idx, doi) {
    if (isStrBlank(doi) || !/10\.\d{4,}\/\S+/.test(doi)) {
      proxy.$modal.msgError(
        proxy.$t(
          'submit.metadata.rawData.common.publications.messages.doiRequired',
        ),
      );
      return;
    }

    editIdx.value = idx;
    proxy.$modal.loading(
      proxy.$t('submit.metadata.rawData.common.publications.messages.loading'),
    );
    getPubInfoFromPlosp(doi)
      .then(response => {
        if (!response.data) {
          proxy.$modal.alertError(
            proxy.$t(
              'submit.metadata.rawData.common.publications.messages.notFound',
            ),
          );
          return;
        }
        plospInfo.value = response.data;
        previewDialog.value = true;
      })
      .catch(e => {
        console.log(e);
        proxy.$modal.alertError(e);
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  function fillIn() {
    proxy.$modal.msgSuccess(
      proxy.$t(
        'submit.metadata.rawData.common.publications.messages.fillSuccess',
      ),
    );

    let idx = editIdx.value;
    publishArray.value[idx].publication = plospInfo.value.publication;
    publishArray.value[idx].pmid = plospInfo.value.pmid;
    publishArray.value[idx].doi = plospInfo.value.doi;
    publishArray.value[idx].articleName = plospInfo.value.articleName;
    publishArray.value[idx].reference = plospInfo.value.reference;

    previewDialog.value = false;
  }

  const { node_journal } = proxy.useDict('node_journal');
</script>

<style lang="scss" scoped>
  .el-form {
    .el-form-item {
      width: calc((100% - 99px) / 3);
    }

    :deep(.el-form-item__label) {
      font-weight: 700;
    }
  }

  .add-btn {
    position: absolute;
    right: 0;
    margin-top: 8px;
    margin-right: 8px;
  }
</style>
