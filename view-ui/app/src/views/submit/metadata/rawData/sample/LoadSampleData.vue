<template>
  <el-dialog
    v-model="dialogVisible"
    width="70%"
    align-center
    :title="'Select existing ' + sampleType + ' Samples'"
    class="dialog radius-14"
  >
    <div class="d-flex align-items-center mb-1">
      <el-form
        ref="existSampleFormRef"
        :model="queryParams"
        :inline="true"
        style="padding-top: 8px"
        class="exist-form"
        label-width="120px"
      >
        <el-form-item label="Sample ID" prop="sampleNo" class="mb-1 mr-1">
          <el-input v-model="queryParams.sampleNo" clearable />
        </el-form-item>
        <el-form-item label="Sample Name" prop="sampleName" class="mb-1 mr-1">
          <el-input v-model="queryParams.sampleName" clearable />
        </el-form-item>
        <el-form-item label="Organism" prop="organism" class="mb-1 mr-1">
          <el-select
            v-model="queryParams.organism"
            :teleported="false"
            filterable
            clearable
          >
            <el-option
              v-for="val in organismList"
              :key="'exist-organism' + val"
              :label="val"
              :value="val"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Tissue" prop="tissue" class="mb-1 mr-1">
          <el-input v-model="queryParams.tissue" clearable />
        </el-form-item>
        <el-form-item label="Attribute" class="mb-1 mr-1">
          <div class="attr-input">
            <el-select
              v-model="queryParams.attrName"
              placeholder="Select name"
              :teleported="false"
              filterable
              clearable
            >
              <el-option
                v-for="val in attrNameList"
                :key="'existAttr' + val"
                :label="val.label"
                :value="val.value"
              />
            </el-select>
            <el-input
              v-model="queryParams.attrValue"
              clearable
              placeholder="attribute value"
            />
          </div>
        </el-form-item>

        <el-form-item label="Create Date" class="mb-1 mr-1">
          <el-date-picker
            v-model="dateRange"
            :teleported="false"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="To"
            start-placeholder="Start date"
            end-placeholder="End date"
          />
        </el-form-item>
        <div class="float-right">
          <el-button class="radius-12 mr-1" type="primary" @click="getDataList"
            >Search
          </el-button>
          <el-button class="radius-12 mr-3" @click="resetForm">Reset</el-button>
        </div>
      </el-form>
    </div>
    <el-table
      ref="sapTableRef"
      v-loading="loading"
      height="450"
      class="mt-1"
      :data="tableData"
      stripe
      style="width: 100%; margin-bottom: 20px"
      :header-cell-style="{
        backgroundColor: '#f2f2f2',
        color: '#333333',
        fontWeight: 700,
      }"
      :row-key="row => row.sapNo"
      border
      default-expand-all
      :default-sort="queryParams"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" :reserve-selection="true" width="40" />
      <el-table-column prop="sapNo" label="Sample ID" width="120" sortable />
      <el-table-column prop="name" label="Sample Name" sortable />
      <el-table-column prop="organism" label="Organism" sortable />
      <el-table-column prop="tissue" label="Tissue" sortable />
      <el-table-column
        prop="description"
        label="Description"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        prop="createDate"
        label="Create Date"
        width="155"
        sortable
      />
    </el-table>

    <pagination
      v-show="tableDataTotal > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="tableDataTotal"
      @pagination="getDataList"
    />

    <div class="select-item bg-gray d-flex mb-1 mt-1 p-20">
      <div class="d-flex align-items-center mr-1" style="min-width: 220px">
        <el-icon color="#3A78E8" size="20">
          <WarningFilled />
        </el-icon>
        <span class="text-primary ml-1">{{ sampleSelection.length }}</span>
        <span class="ml-05">items are selected</span>
        <span
          class="text-primary pointer font-600 ml-1"
          @click="clearAllSelection"
          >Clear</span
        >
      </div>

      <div class="tag d-flex align-items-center flex-wrap">
        <el-tag
          v-for="(item, index) in sampleSelection"
          :key="'sapNo-selected-' + index"
          class="mr-05 mx-1"
          effect="light"
          size="large"
          closable
          round
          @close="removeSapNo(item, index)"
          >{{ item.sapNo }}
        </el-tag>
      </div>
    </div>

    <template #footer>
      <div class="text-align-center dialog-footer">
        <el-button
          type="primary"
          class="btn-primary btn btn-s btn-shadow"
          round
          @click="loadToExcel"
          >Load to Excel
        </el-button>
        <el-button
          class="btn-primary btn btn-round"
          round
          @click="dialogVisible = false"
          >Cancel
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
  import {
    defineEmits,
    defineExpose,
    getCurrentInstance,
    reactive,
    ref,
    toRefs,
  } from 'vue';
  import {
    getExistSampleAttr,
    getExistSampleData,
    getExistSampleOrganism,
  } from '@/api/metadata/sample';

  let { proxy } = getCurrentInstance();

  const organismList = ref([]);
  const attrNameList = ref([]);

  const sampleType = ref();
  const sampleSelection = ref([]);

  const emits = defineEmits(['loadExistData']);

  const loadToExcel = () => {
    const sapNos = sampleSelection.value.map(item => item.sapNo);
    const max = 5000;
    if (sapNos && sapNos.length > max) {
      proxy.$modal.alertWarning(`The selected data cannot exceed ${max}`);
      return false;
    }
    emits('loadExistData', sapNos);
  };

  const initData = sapType => {
    sampleType.value = sapType;
    clearAllSelection();

    dialogVisible.value = true;

    getExistSampleOrganism({ sampleType: sampleType.value }).then(response => {
      organismList.value = response.data;
    });

    getExistSampleAttr({ sampleType: sampleType.value }).then(response => {
      attrNameList.value = response.data;
    });

    getDataList();
  };

  function getDataList() {
    loading.value = true;
    queryParams.value.sampleType = sampleType.value;
    getExistSampleData(proxy.addDateRange(queryParams.value, dateRange.value))
      .then(response => {
        tableData.value = response.rows;
        tableDataTotal.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const data = reactive({
    tableData: [],
    tableDataTotal: 0,
    queryParams: {
      sampleNo: '',
      sampleType: '',
      organism: '',
      tissue: '',
      attrName: '',
      attrValue: '',
      sampleName: '',
      orderByColumn: 'submission_date',
      isAsc: 'descending',
      pageNum: 1,
      pageSize: 10,
    },
    dateRange: [],
    loading: true,
  });

  const { tableData, tableDataTotal, queryParams, dateRange, loading } =
    toRefs(data);

  const dialogVisible = ref(false);

  function removeSapNo(item, index) {
    sampleSelection.value.splice(index, 1);
    proxy.$refs['sapTableRef'].toggleRowSelection(item, false);
  }

  function clearAllSelection() {
    sampleSelection.value.forEach(item => {
      proxy.$refs['sapTableRef'].toggleRowSelection(item, false);
    });
  }

  function resetForm() {
    dateRange.value = [];
    queryParams.value.attrName = '';
    queryParams.value.attrValue = '';
    proxy.$refs['existSampleFormRef'].resetFields();
    getDataList();
  }

  function handleSelectionChange(selection) {
    sampleSelection.value = selection;
  }

  function closeDialog() {
    dialogVisible.value = false;
  }

  defineExpose({
    initData,
    closeDialog,
  });
</script>

<style lang="scss" scoped>
  .exist-form {
    width: 100%;
    flex-wrap: wrap;

    .el-form-item {
      width: calc((100% - 100px) / 3) !important;
      margin-right: 10px;

      .el-select {
        width: 100%;
      }
    }
  }

  .attr-input {
    display: flex;
    width: 100%;
    gap: 10px;
  }

  :deep(.el-dialog__body) {
    padding: 0 15px !important;
  }

  :deep(.el-dialog__title) {
    font-weight: 600 !important;
  }
</style>
