<template>
  <el-divider content-position="left"
    ><h3 class="preview-title">Attributes</h3></el-divider
  >
  <div
    v-for="(group, idx) in attrFiled"
    :key="'sapAttrFiledGroup-' + idx"
    class="bg-gray group"
  >
    <div class="d-flex preview">
      <template v-for="(item, idx2) in group" :key="'sapAttrFiled-' + idx2">
        <div v-if="attributes[item.attributesField]">
          <span class="title">{{ item.attributesName }}</span>
          <span class="content">{{
            $text(attributes[item.attributesField])
          }}</span>
        </div>
      </template>
    </div>
  </div>

  <div class="bg-gray group">
    <div
      v-if="customAttrShow"
      key="sapCustomAttrFiled"
      class="d-flex preview mt-1"
    >
      <div
        v-for="(item, idx) in customAttrData"
        :key="'sapCustomAttrFiled-' + idx"
      >
        <span class="title">{{ item.attr }}</span>
        <span class="content">{{ $text(item.value) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { computed, onMounted, ref } from 'vue';
  import { getExpSapData } from '@/api/metadata/dict';

  const props = defineProps({
    attrData: {
      type: Object,
      default() {
        return {};
      },
    },
    customAttrData: {
      type: Object,
      default() {
        return {};
      },
    },
    sapType: {
      type: String,
      required: true,
    },
  });

  const attributes = ref(props.attrData);
  const customAttrData = ref(props.customAttrData);
  const sapType = ref(props.sapType);
  const attrFiled = ref([]);

  /** 加载当前系统所拥有的组学类型数据字典 */
  function loadExpSapData(type) {
    getExpSapData(type).then(response => {
      attrFiled.value = convertValueRange(response.data.attributes);
    });
  }

  function convertValueRange(jsonData) {
    const groupedData = {};
    for (const item of jsonData) {
      const group = item.group;
      if (!groupedData[group]) {
        groupedData[group] = [];
      }
      groupedData[group].push(item);
    }

    // 过滤掉在attributes中不存在的字段及分组
    const filteredData = Object.entries(groupedData).reduce(
      (acc, [group, items]) => {
        const filteredItems = items.filter(item =>
          // eslint-disable-next-line no-prototype-builtins
          attributes.value.hasOwnProperty(item.attributesField),
        );
        if (filteredItems.length > 0) {
          acc[group] = filteredItems;
        }
        return acc;
      },
      {},
    );

    return Object.values(filteredData);
  }

  const customAttrShow = computed(() => {
    if (!customAttrData.value) {
      return false;
    }
    if (customAttrData.value[0].attr === '') {
      return false;
    }
    return true;
  });

  onMounted(() => {
    loadExpSapData(sapType.value);
  });
</script>
<style lang="scss" scoped>
  .group {
    margin-top: 12px;
  }
</style>
