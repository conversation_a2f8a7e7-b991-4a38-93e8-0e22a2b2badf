<template>
  <div class="project w-100">
    <div class="card w-100 card-container pt-0">
      <FillTip></FillTip>
      <div class="category-title font-600 text-main-color">
        {{ $t('submit.metadata.rawData.project.title') }}
      </div>
      <div class="plr-20 bg-gray mt-1">
        <el-form
          ref="projectForm"
          label-position="top"
          label-width="100px"
          :model="form"
          :inline="true"
          :rules="rules"
          style="padding-top: 8px"
        >
          <el-form-item
            :label="$t('submit.metadata.rawData.project.form.projectId')"
            class="w-50"
          >
            <div class="d-flex align-items-center w-100">
              <el-input
                class="w-100"
                disabled
                :placeholder="
                  $t(
                    'submit.metadata.rawData.project.form.projectIdPlaceholder',
                  )
                "
              />
            </div>
          </el-form-item>
          <el-form-item
            :label="$t('submit.metadata.rawData.project.form.projectName')"
            class="w-40"
            prop="name"
          >
            <el-input v-model="form.name" />
          </el-form-item>
          <el-form-item
            class="w-100"
            :label="
              $t('submit.metadata.rawData.project.form.projectDescription')
            "
            prop="description"
          >
            <el-input v-model="form.description" type="textarea" rows="5" />
          </el-form-item>
          <RelatedLinks
            :key="'project-RelatedLinks' + componentKey"
            v-model:relatedLinks="form.relatedLinks"
          ></RelatedLinks>
        </el-form>
      </div>
      <Publication
        :key="'prj-Publication' + componentKey"
        v-model:publishData="form.publish"
      ></Publication>
      <div class="text-align-right mt-2 pr-20">
        <el-button
          type="primary"
          class="btn-primary btn btn-s btn-shadow"
          round
          @click="continueNext()"
          >{{ $t('submit.metadata.rawData.project.buttons.continue') }}
        </el-button>
        <el-button
          type="primary"
          class="btn-primary btn btn-s btn-shadow"
          round
          @click="previewData"
          >{{ $t('submit.metadata.rawData.project.buttons.previewSave') }}
        </el-button>
        <el-button class="btn-primary btn btn-round" round @click="resetForm"
          >{{ $t('submit.metadata.rawData.project.buttons.reset') }}
        </el-button>
        <el-button
          :disabled="!form.projectNo"
          type="danger"
          class="btn"
          plain
          round
          @click="deleteForm"
          >{{ $t('submit.metadata.rawData.project.buttons.delete') }}
        </el-button>
      </div>
    </div>
    <el-dialog
      v-model="previewDialog"
      :title="$t('submit.metadata.rawData.project.preview.title')"
      width="70%"
      class="preview-dialog radius-14"
    >
      <el-divider content-position="left"
        ><h3 class="preview-title">
          {{ $t('submit.metadata.rawData.project.preview.projectTitle') }}
        </h3></el-divider
      >
      <div class="d-flex preview">
        <div>
          <span class="title">{{
            $t('submit.metadata.rawData.project.preview.projectId')
          }}</span>
          <span class="content">{{
            $t('submit.metadata.rawData.project.preview.projectIdContent')
          }}</span>
        </div>
        <div>
          <span class="title">{{
            $t('submit.metadata.rawData.project.preview.projectName')
          }}</span>
          <span class="content">{{ $text(form.name) }}</span>
        </div>
        <div>
          <span class="title">{{
            $t('submit.metadata.rawData.project.preview.description')
          }}</span>
          <span class="content">{{ $text(form.description) }}</span>
        </div>
        <div>
          <span class="title">{{
            $t('submit.metadata.rawData.project.preview.relatedLinks')
          }}</span>
          <span
            v-if="!form.relatedLinks || form.relatedLinks.length === 0"
            class="content"
            >-</span
          >
          <div class="d-flex flex-column">
            <p
              v-for="(val, idx) in form.relatedLinks"
              :key="'review-relatedLinks-' + idx"
              class="content"
            >
              {{ $text(val) }}
            </p>
          </div>
        </div>
      </div>
      <PreviewPublish v-model:publish-data="form.publish"></PreviewPublish>
      <template #footer>
        <span class="dialog-footer">
          <div class="text-align-center">
            <el-button
              type="primary"
              class="btn-primary btn btn-s btn-shadow"
              round
              @click="saveData"
              >{{
                $t('submit.metadata.rawData.project.preview.save')
              }}</el-button
            >
            <el-button
              round
              class="btn-primary btn btn-round"
              @click="previewDialog = false"
              >{{
                $t('submit.metadata.rawData.project.preview.backEdit')
              }}</el-button
            >
          </div>
        </span>
      </template>
    </el-dialog>

    <DeleteLog
      ref="deleteLog"
      :curr-type="$t('submit.metadata.rawData.project.deleteLog.type')"
    ></DeleteLog>
  </div>
</template>

<script setup>
  import {
    getCurrentInstance,
    nextTick,
    onMounted,
    reactive,
    ref,
    toRefs,
  } from 'vue';
  import { storeToRefs } from 'pinia';
  import { useRoute } from 'vue-router';
  import Publication from './common/Publications.vue';
  import RelatedLinks from './common/RelatedLinks.vue';
  import PreviewPublish from './common/PreviewPublish.vue';
  import useSubmissionStore from '@/store/modules/metadata';
  import {
    deleteProject,
    getProjInfo,
    saveProject,
    validateProjectName,
  } from '@/api/metadata/project';
  import bus from '@/utils/bus';
  import { BusEnum } from '@/utils/enums';
  import DeleteLog from '@/views/submit/metadata/rawData/common/DeleteLog.vue';
  import FillTip from '@/views/submit/components/FillTip.vue';

  const route = useRoute();
  const { proxy } = getCurrentInstance();
  const submissionStore = useSubmissionStore();
  const { subNo, submission } = storeToRefs(submissionStore);
  const emit = defineEmits(['continueMessage']);

  const oldFormStr = ref('');
  const componentKey = ref(1);
  const previewDialog = ref(false);

  /** 项目名查重 */
  const checkProjectName = (rule, value, callback) => {
    if (!value) {
      return callback(
        new Error(
          proxy.$t(
            'submit.metadata.rawData.project.validation.projectNameRequired',
          ),
        ),
      );
    }
    validateProjectName({
      projectNo: form.value.projectNo,
      name: form.value.name,
    })
      .then(response => {
        if (response && response.msg) {
          callback(new Error(response.msg));
        }
        callback();
      })
      .catch(error => callback(new Error(error)));
  };

  const data = reactive({
    form: {
      projectNo: undefined,
      name: '',
      description: undefined,
      relatedLinks: undefined,
      publish: [
        {
          id: undefined,
          publication: undefined,
          doi: undefined,
          pmid: undefined,
          reference: undefined,
          articleName: undefined,
        },
      ],
    },
    rules: {
      name: [
        {
          required: true,
          validator: checkProjectName,
          trigger: 'blur',
        },
        {
          validator: proxy.$validateChinese,
          trigger: 'blur',
        },
      ],
      description: [
        {
          validator: proxy.$validateChinese,
          trigger: 'blur',
        },
      ],
      relatedLinks: [
        {
          validator: proxy.$validateChinese,
          trigger: 'blur',
        },
      ],
    },
  });

  const { form, rules } = toRefs(data);

  /** 初始化数据 */
  function initData() {
    const subNo = route.params.subNo;
    // 如果存在SUB NO则说明是编辑数据，从数据库回显
    if (subNo && submission.value.projNo) {
      getProjInfo(submission.value.projNo).then(response => {
        form.value = response.data;
        componentKey.value++;
        stringifyFormData();
      });
    } else {
      stringifyFormData();
    }
  }

  /** 等所有的子组件全部渲染完成后序列化json */
  function stringifyFormData() {
    // 等所有的子组件全部渲染完成后序列化json
    nextTick().then(() => {
      oldFormStr.value = JSON.stringify(form.value);
    });
  }

  /** 预览数据 */
  function previewData() {
    proxy.$refs['projectForm'].validate(valid => {
      if (valid) {
        previewDialog.value = true;
      }
    });
  }

  /** 提交数据 */
  const saveData = () => {
    proxy.$modal.loading(
      proxy.$t('submit.metadata.rawData.project.messages.saving'),
    );
    if (!subNo.value) {
      // 如果没有subNo代表用户没有保存过Submitter数据，自动保存
      bus.emit(BusEnum.SUBMIT_SUBMITTER, saveForm);
    } else {
      saveForm();
    }
  };

  function saveForm() {
    form.value.subNo = subNo.value;
    saveProject(form.value)
      .then(response => {
        form.value = response.data;

        previewDialog.value = false;
        proxy.$modal.alertSuccess(
          proxy.$t('submit.metadata.rawData.project.messages.saveSuccess'),
        );

        stringifyFormData();
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  /** 重置表单 */
  const resetForm = () => {
    proxy.resetForm('projectForm');
    form.value.projectNo = undefined;
    form.value.publish = undefined;
    form.value.relatedLinks = undefined;
    componentKey.value++;
  };

  /** 删除数据 */
  const deleteForm = () => {
    proxy.$modal
      .confirm(
        proxy.$t('submit.metadata.rawData.project.messages.deleteConfirm'),
      )
      .then(() => {
        deleteProject(subNo.value).then(response => {
          if (response.data) {
            proxy.$refs['deleteLog'].openLog(response.data);
            return;
          }
          proxy.$modal.msgSuccess(
            proxy.$t('submit.metadata.rawData.project.messages.deleteSuccess'),
          );
          resetForm();
        });
      })
      .catch(() => {});
  };

  /** 继续 */
  function continueNext() {
    if (validateSaved()) {
      sendContinueMessage();
    } else {
      proxy.$modal
        .confirm(
          proxy.$t('submit.metadata.rawData.project.messages.unsavedConfirm'),
        )
        .then(function () {
          sendContinueMessage();
        })
        .catch(() => {});
    }
  }

  /** 校验表单数据是否被更改 */
  function validateSaved() {
    const currentFormStr = JSON.stringify(form.value);
    return currentFormStr === oldFormStr.value;
  }

  onMounted(() => {
    initData();
  });

  const sendContinueMessage = () => {
    emit('continueMessage', 'ExpMultiple');
  };
</script>

<style lang="scss" scoped>
  .project {
    :deep(.el-radio__label) {
      font-weight: 600;
      color: #333333;
      font-size: 16px;
    }

    .tips {
      font-size: 14px;
      margin-left: 1.3rem;
      //margin-top: .8rem;
    }

    .general-info {
      .w-45 {
        width: 45%;
      }

      .used-id {
        width: 220px;
        margin-left: 0.5rem;
      }
    }

    .links {
      .el-button {
        padding: 2px 8px;
        border-radius: 50%;
      }

      :deep(.el-form-item__label) {
        font-weight: 700;
      }

      :deep(.el-form-item__content) {
        flex-direction: column;
        align-items: flex-start;

        & + .el-form-item__label {
          font-weight: 700;
        }
      }
    }
  }
</style>
