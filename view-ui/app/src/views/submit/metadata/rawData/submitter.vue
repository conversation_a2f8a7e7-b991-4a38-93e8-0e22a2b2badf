<template>
  <div class="d-flex submitData w-100">
    <div class="card w-100 card-container pt-0">
      <FillTip></FillTip>
      <div class="category-title font-600 text-main-color">
        {{ $t('submit.metadata.rawData.submitter.title') }}
      </div>
      <div>
        <el-form
          ref="submitterForm"
          label-position="top"
          label-width="100px"
          :model="form"
          :rules="rules"
          style="padding-top: 8px"
        >
          <div class="d-flex bg-gray p-20">
            <el-form-item
              :label="$t('submit.metadata.rawData.submitter.form.firstName')"
              class="mr-1"
              prop="firstName"
            >
              <el-input v-model="form.firstName" maxlength="20" />
            </el-form-item>
            <el-form-item
              :label="$t('submit.metadata.rawData.submitter.form.middleName')"
              class="mr-1"
              prop="middleName"
            >
              <el-input v-model="form.middleName" maxlength="20" />
            </el-form-item>
            <el-form-item
              :label="$t('submit.metadata.rawData.submitter.form.lastName')"
              prop="lastName"
            >
              <el-input v-model="form.lastName" maxlength="20" />
            </el-form-item>
          </div>
          <div class="d-flex bg-gray mt-1 p-20">
            <el-form-item
              class="mr-1"
              :label="$t('submit.metadata.rawData.submitter.form.organization')"
              prop="orgName"
            >
              <el-autocomplete
                v-model="form.orgName"
                :teleported="false"
                class="w-100"
                :fit-input-width="true"
                :fetch-suggestions="queryOrganizationSearch"
                clearable
              >
                <template #default="{ item }">
                  <span :title="item.label" v-html="item.label"></span>
                </template>
              </el-autocomplete>
            </el-form-item>
            <el-form-item
              :label="$t('submit.metadata.rawData.submitter.form.department')"
              class="mr-1"
              prop="deptName"
            >
              <el-input v-model="form.deptName" />
            </el-form-item>
            <el-form-item
              :label="$t('submit.metadata.rawData.submitter.form.piName')"
              prop="piName"
            >
              <el-input v-model="form.piName" />
            </el-form-item>
          </div>
          <div class="d-flex bg-gray mt-1 p-20">
            <el-form-item
              :label="$t('submit.metadata.rawData.submitter.form.email')"
              class="mr-1"
              prop="email"
            >
              <el-input v-model="form.email" />
            </el-form-item>
            <el-form-item
              :label="$t('submit.metadata.rawData.submitter.form.phone')"
              class="mr-1"
              prop="phone"
            >
              <el-input v-model="form.phone" maxlength="20" />
            </el-form-item>
            <el-form-item
              :label="$t('submit.metadata.rawData.submitter.form.fax')"
              prop="fax"
            >
              <el-input v-model="form.fax" />
            </el-form-item>
          </div>
          <div class="bg-gray mt-1 p-20">
            <div class="d-flex mt-1">
              <el-form-item
                :label="$t('submit.metadata.rawData.submitter.form.street')"
                class="mr-1"
                prop="street"
              >
                <el-input v-model="form.street" />
              </el-form-item>
              <el-form-item
                :label="$t('submit.metadata.rawData.submitter.form.city')"
                class="mr-1"
                prop="city"
              >
                <el-input v-model="form.city" />
              </el-form-item>
              <el-form-item
                :label="
                  $t('submit.metadata.rawData.submitter.form.stateProvince')
                "
                prop="stateProvince"
              >
                <el-input v-model="form.stateProvince" />
              </el-form-item>
            </div>
            <div class="d-flex mt-1">
              <el-form-item
                :label="$t('submit.metadata.rawData.submitter.form.postalCode')"
                class="mr-1"
                prop="postalCode"
              >
                <el-input v-model="form.postalCode" />
              </el-form-item>
              <el-form-item
                :label="
                  $t('submit.metadata.rawData.submitter.form.countryRegion')
                "
                prop="countryRegion"
              >
                <el-select
                  v-model="form.countryRegion"
                  :teleported="false"
                  filterable
                  :placeholder="
                    $t(
                      'submit.metadata.rawData.submitter.form.countryPlaceholder',
                    )
                  "
                >
                  <el-option
                    v-for="(val, idx) in node_country"
                    :key="'country-' + idx"
                    :label="val.label"
                    :value="val.value"
                  />
                </el-select>
              </el-form-item>
            </div>
          </div>
        </el-form>
      </div>
      <div class="text-align-right mt-2 pr-20">
        <el-button
          type="primary"
          class="btn-primary btn btn-s btn-shadow"
          round
          @click="continueNext"
          >{{ $t('submit.metadata.rawData.submitter.buttons.continue') }}
        </el-button>
        <el-button
          type="primary"
          class="btn-primary btn btn-s btn-shadow"
          round
          @click="previewData"
          >{{ $t('submit.metadata.rawData.submitter.buttons.previewSave') }}
        </el-button>
        <el-button class="btn-primary btn btn-round" round @click="reset"
          >{{ $t('submit.metadata.rawData.submitter.buttons.reset') }}
        </el-button>
      </div>
    </div>
    <el-dialog
      v-model="previewDialog"
      :title="$t('submit.metadata.rawData.submitter.preview.title')"
      width="60%"
      class="preview-dialog radius-14"
    >
      <el-divider content-position="left"
        ><h3 class="preview-title">
          {{ $t('submit.metadata.rawData.submitter.preview.submitterTitle') }}
        </h3></el-divider
      >
      <div class="d-flex preview">
        <div>
          <span class="title">{{
            $t('submit.metadata.rawData.submitter.preview.firstName')
          }}</span>
          <span class="content">{{ $text(form.firstName) }}</span>
        </div>
        <div>
          <span class="title">{{
            $t('submit.metadata.rawData.submitter.preview.middleName')
          }}</span>
          <span class="content">{{ $text(form.middleName) }}</span>
        </div>
        <div>
          <span class="title">{{
            $t('submit.metadata.rawData.submitter.preview.lastName')
          }}</span>
          <span class="content">{{ $text(form.lastName) }}</span>
        </div>
        <div>
          <span class="title">{{
            $t('submit.metadata.rawData.submitter.preview.organization')
          }}</span>
          <span class="content">{{ $text(form.orgName) }}</span>
        </div>
        <div>
          <span class="title">{{
            $t('submit.metadata.rawData.submitter.preview.department')
          }}</span>
          <span class="content">{{ $text(form.deptName) }}</span>
        </div>
        <div>
          <span class="title">{{
            $t('submit.metadata.rawData.submitter.preview.piName')
          }}</span>
          <span class="content">{{ $text(form.piName) }}</span>
        </div>
        <div>
          <span class="title">{{
            $t('submit.metadata.rawData.submitter.preview.email')
          }}</span>
          <span class="content">{{ $text(form.email) }}</span>
        </div>
        <div>
          <span class="title">{{
            $t('submit.metadata.rawData.submitter.preview.phone')
          }}</span>
          <span class="content">{{ $text(form.phone) }}</span>
        </div>
        <div>
          <span class="title">{{
            $t('submit.metadata.rawData.submitter.preview.fax')
          }}</span>
          <span class="content">{{ $text(form.fax) }}</span>
        </div>
        <div>
          <span class="title">{{
            $t('submit.metadata.rawData.submitter.preview.street')
          }}</span>
          <span class="content">{{ $text(form.street) }}</span>
        </div>
        <div>
          <span class="title">{{
            $t('submit.metadata.rawData.submitter.preview.city')
          }}</span>
          <span class="content">{{ $text(form.city) }}</span>
        </div>
        <div>
          <span class="title">{{
            $t('submit.metadata.rawData.submitter.preview.stateProvince')
          }}</span>
          <span class="content">{{ $text(form.stateProvince) }}</span>
        </div>
        <div>
          <span class="title">{{
            $t('submit.metadata.rawData.submitter.preview.postalCode')
          }}</span>
          <span class="content">{{ $text(form.postalCode) }}</span>
        </div>
        <div>
          <span class="title">{{
            $t('submit.metadata.rawData.submitter.preview.countryRegion')
          }}</span>
          <span class="content">{{ $text(form.countryRegion) }}</span>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <div class="text-align-center">
            <el-button
              type="primary"
              class="btn-primary btn btn-s btn-shadow"
              round
              @click="saveData(null)"
              >{{
                $t('submit.metadata.rawData.submitter.preview.save')
              }}</el-button
            >
            <el-button
              round
              class="btn-primary btn btn-round"
              @click="previewDialog = false"
              >{{
                $t('submit.metadata.rawData.submitter.preview.backEdit')
              }}</el-button
            >
          </div>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import {
    getCurrentInstance,
    nextTick,
    onMounted,
    onUnmounted,
    reactive,
    ref,
    toRefs,
  } from 'vue';
  import bus from '@/utils/bus';
  import useUserStore from '@/store/modules/user';
  import useSubmissionStore from '@/store/modules/metadata';
  import { useRoute, useRouter } from 'vue-router';
  import { getSubmission, saveSubmitter } from '@/api/metadata/submitter';
  import { deepClone } from '@/utils';
  import { BusEnum } from '@/utils/enums';
  import { submissionIsEditable } from '@/store/util';
  import FillTip from '@/views/submit/components/FillTip.vue';

  const emit = defineEmits(['continueMessage']);

  const { proxy } = getCurrentInstance();

  const route = useRoute();
  const router = useRouter();
  const oldFormStr = ref('');

  const previewDialog = ref(false);
  const { member } = useUserStore();

  const data = reactive({
    form: {
      memberId: '',
      firstName: '',
      middleName: '',
      lastName: '',
      orgName: '',
      deptName: '',
      piName: '',
      email: '',
      phone: '',
      fax: '',
      street: '',
      city: '',
      stateProvince: '',
      postalCode: '',
      countryRegion: '',
    },
    rules: {
      firstName: [
        {
          required: true,
          message: proxy.$t(
            'submit.metadata.rawData.submitter.validation.firstNameRequired',
          ),
          trigger: 'blur',
        },
        {
          min: 1,
          max: 20,
          message: proxy.$t(
            'submit.metadata.rawData.submitter.validation.nameLength',
          ),
          trigger: 'blur',
        },
      ],
      lastName: [
        {
          required: true,
          message: proxy.$t(
            'submit.metadata.rawData.submitter.validation.lastNameRequired',
          ),
          trigger: 'blur',
        },
        {
          min: 1,
          max: 20,
          message: proxy.$t(
            'submit.metadata.rawData.submitter.validation.nameLength',
          ),
          trigger: 'blur',
        },
      ],
      orgName: [
        {
          required: true,
          message: proxy.$t(
            'submit.metadata.rawData.submitter.validation.organizationRequired',
          ),
          trigger: 'blur',
        },
      ],
      email: [
        {
          required: true,
          message: proxy.$t(
            'submit.metadata.rawData.submitter.validation.emailRequired',
          ),
          trigger: 'blur',
        },
        {
          type: 'email',
          message: proxy.$t(
            'submit.metadata.rawData.submitter.validation.emailFormat',
          ),
          trigger: ['blur', 'change'],
        },
      ],
      countryRegion: [
        {
          required: true,
          message: proxy.$t(
            'submit.metadata.rawData.submitter.validation.countryRequired',
          ),
          trigger: 'blur',
        },
      ],
    },
  });

  const { form, rules } = toRefs(data);

  /** 初始化数据 */
  function initData() {
    const subNo = route.params.subNo;
    // 如果存在SUB NO则说明是编辑数据，从数据库回显
    if (subNo) {
      proxy.$modal.loading(
        proxy.$t('submit.metadata.rawData.submitter.messages.loading'),
      );
      getSubmission(subNo)
        .then(response => {
          submissionIsEditable(response.data.status, router).then(editable => {
            if (editable) {
              form.value = response.data.submitter;
              useSubmissionStore().update(response.data);
              stringifyFormData();
            }
          });
        })
        .finally(() => {
          proxy.$modal.closeLoading();
        });
    } else {
      // 不存在SUB NO，则是新增数据，从node-cas中回填用户基本信息
      form.value = deepClone(member);
    }
    stringifyFormData();
  }

  /** 等所有的子组件全部渲染完成后序列化json */
  function stringifyFormData() {
    // 等所有的子组件全部渲染完成后序列化json
    nextTick().then(() => {
      oldFormStr.value = JSON.stringify(form.value);

      initRules();
    });
  }

  /** 自动补全过滤 Organization */
  const queryOrganizationSearch = (queryString, cb) => {
    const results = queryString
      ? node_organization.value.filter(createFilter(queryString))
      : node_organization.value;
    cb(results);
  };
  const createFilter = queryString => {
    return node_organization => {
      return (
        node_organization.value
          .toLowerCase()
          .indexOf(queryString.toLowerCase()) !== -1
      );
    };
  };

  /** 预览数据 */
  function previewData() {
    proxy.$refs['submitterForm'].validate(valid => {
      if (valid) {
        previewDialog.value = true;
      }
    });
  }

  /** 提交数据 */
  function saveData(callback) {
    proxy.$refs['submitterForm'].validate(valid => {
      if (valid) {
        form.value.subNo = useSubmissionStore().submission.subNo;
        if (route.path.indexOf('/rawData') !== -1) {
          form.value.dataType = 'rawData';
        } else {
          form.value.dataType = 'analysisData';
        }
        saveSubmitter(form.value)
          .then(response => {
            useSubmissionStore().update(response.data);

            previewDialog.value = false;

            stringifyFormData();
            // 将浏览器地址修改为编辑地址，这样页面刷新了就还是一个subNo
            proxy.$router.push({
              path: proxy.$route.path + '/edit/' + response.data.subNo,
            });

            // 如果是其他组件调用的保存方法，则会有回调
            if (callback) {
              callback();
            } else {
              proxy.$modal.alertSuccess(
                proxy.$t(
                  'submit.metadata.rawData.submitter.messages.saveSuccess',
                ),
              );
            }
          })
          .catch(() => {
            proxy.$modal.closeLoading();
          });
      } else {
        proxy.$modal.closeLoading();
        proxy.$modal.msgError(
          proxy.$t('submit.metadata.rawData.submitter.messages.saveError'),
        );
      }
    });
  }

  /** 重置表单 */
  function reset() {
    proxy.resetForm('submitterForm');
    initData();
  }

  /** 继续 */
  function continueNext() {
    if (validateSaved()) {
      sendContinueMessage();
    } else {
      proxy.$modal
        .confirm(
          proxy.$t('submit.metadata.rawData.submitter.messages.unsavedConfirm'),
        )
        .then(function () {
          sendContinueMessage();
        })
        .catch(() => {});
    }
  }

  /** 跳转到下一页 */
  function sendContinueMessage() {
    // raw-data类型跳转到Project、 Analysis 跳转到批量提交分析数据
    const val = route.path.includes('analysisData')
      ? 'AnalysisMultiple'
      : 'Project';
    emit('continueMessage', val);
  }

  /** 校验表单数据是否被更改 */
  function validateSaved() {
    const currentFormStr = JSON.stringify(form.value);
    return currentFormStr === oldFormStr.value;
  }

  /** 初始化字典数据 */
  const { node_organization } = proxy.useDict('node_organization');
  const { node_country } = proxy.useDict('node_country');

  const initRules = () => {
    const props = Object.keys(form.value);
    props.forEach(function (item) {
      let target = {
        validator: proxy.$validateChinese,
        trigger: 'blur',
      };
      if (!Object.prototype.hasOwnProperty.call(rules.value, item)) {
        rules.value[item] = [];
      }
      rules.value[item].push(target);
    });
  };

  onMounted(() => {
    // 初始化数据
    initData();
    bus.on(BusEnum.SUBMIT_SUBMITTER, saveData);

    const userAgent = navigator.userAgent;
    if (
      userAgent.includes('Mobile') ||
      userAgent.includes('Android') ||
      userAgent.includes('iPhone')
    ) {
      proxy.$modal.alertWarning(
        proxy.$t('submit.metadata.rawData.submitter.messages.mobileWarning'),
      );
    }
  });

  onUnmounted(() => {
    bus.off(BusEnum.SUBMIT_SUBMITTER);
  });
</script>

<style lang="scss" scoped>
  .submitData {
    .before-circle:before {
      background-color: #999999 !important;
    }

    .el-form {
      .el-form-item {
        width: 30%;

        .el-select {
          width: 100%;
        }

        .el-radio {
          width: 100%;
          margin-right: 0;

          :deep(.el-radio__label) {
            width: 100%;
          }
        }
      }

      :deep(.el-form-item__label) {
        font-weight: 700;
      }
    }
  }
</style>
