<template>
  <div class="w-100">
    <div v-loading="isRequesting" class="card card-container">
      <div class="category-title font-600 text-main-color">
        {{ $t('submit.metadata.rawData.experiment.expMultiple.title') }}
      </div>
      <h3 class="plr-20 d-flex align-items-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="30"
          height="30"
          viewBox="0 0 32 32"
          fill="none"
        >
          <g opacity="1" transform="translate(0 0)  rotate(0)">
            <path
              id="路径 546"
              fill-rule="evenodd"
              style="fill: #3a78e8"
              opacity="1"
              d="M30.5027,16.0022c0,8.01 -6.49,14.51 -14.5,14.51c-8.01002,0 -14.51002,-6.5 -14.51002,-14.51c0,-8.01001 6.5,-14.51001 14.51002,-14.51001c8.01,0 14.5,6.5 14.5,14.51001zM18.7176,8.97919c0,-1.3 -1.06,-2.36 -2.36,-2.36c-1.3,0 -2.36,1.06 -2.36,2.36c0,1.30001 1.06,2.36001 2.36,2.36001c1.3,0 2.36,-1.06 2.36,-2.36001zM20.5514,23.3909l-0.99,-0.37c-0.56,-0.2 -0.93,-0.73 -0.93,-1.32v-8.32h-5.98v1.06l0.99,0.36c0.55,0.21 0.92,0.73 0.92,1.32v5.58c0,0.59 -0.37,1.12 -0.92,1.32l-0.99,0.37v1.03h7.9z"
            ></path>
          </g>
        </svg>
        <span class="font-600 ml-05">{{
          $t('submit.metadata.rawData.experiment.expMultiple.description')
        }}</span>
      </h3>

      <div class="bg-gray mt-1 p-20">
        <p class="exp-type font-16 text-main-color font-600">
          {{
            $t('submit.metadata.rawData.experiment.expMultiple.experimentType')
          }}
        </p>
        <el-radio-group v-model="form.expType" class="exp-radiogroup">
          <el-radio
            v-for="item in expTypeList"
            :key="'expMulti' + item.name"
            :label="item.name"
            >{{ item.name }}
          </el-radio>
        </el-radio-group>
      </div>

      <DownloadTemplate
        :key="'downloadTemp' + currStage"
        v-model="isRequesting"
        v-model:show-sap-attr-detail-dialog="showSapAttrDetailDialog"
        :curr-stage="currStage"
        :curr-data-type="form.expType"
        @change-ht-table-data="changeHtTableData"
      ></DownloadTemplate>

      <HtTable
        :key="'exp-ht-table-' + updateKey"
        ref="expHtTable"
        v-model="isRequesting"
        :hot-table-data="hotTableData"
        :hot-columns="hotColumns"
        :hide-col-index="hideColIndex"
      ></HtTable>

      <ResultLog
        v-if="resultDialogOpen"
        ref="resultLog"
        :log-data="resultDialogData"
        :curr-exp-type="form.expType"
      ></ResultLog>

      <div class="text-align-right mt-2 pr-20">
        <el-button
          type="primary"
          class="btn-primary btn btn-s btn-shadow"
          round
          @click="continueNext"
          >{{
            $t(
              'submit.metadata.rawData.experiment.expMultiple.buttons.continue',
            )
          }}
        </el-button>
        <el-button
          type="primary"
          :disabled="isRequesting"
          class="btn-primary btn btn-s btn-shadow"
          round
          @click="saveData"
          >{{
            $t(
              'submit.metadata.rawData.experiment.expMultiple.buttons.checkSave',
            )
          }}
        </el-button>
        <el-button class="btn-primary btn btn-round" round @click="resetForm"
          >{{
            $t('submit.metadata.rawData.experiment.expMultiple.buttons.reset')
          }}
        </el-button>
        <el-button
          :disabled="!subNo"
          type="danger"
          class="btn"
          plain
          round
          @click="deleteForm"
          >{{
            $t('submit.metadata.rawData.experiment.expMultiple.buttons.delete')
          }}
        </el-button>
      </div>
    </div>
    <DeleteLog
      ref="deleteLog"
      :curr-type="
        $t('submit.metadata.rawData.experiment.expMultiple.deleteLog.type')
      "
    ></DeleteLog>
    <exp-sap-attr-detail-dialog
      v-model:curr-type="form.expType"
      v-model:show-dialog="showSapAttrDetailDialog"
      v-model:attr-list="attrList"
      base-type="Experiment"
    >
    </exp-sap-attr-detail-dialog>
  </div>
</template>

<script setup>
  import {
    getCurrentInstance,
    onActivated,
    onDeactivated,
    reactive,
    ref,
    toRefs,
    watch,
  } from 'vue';
  import DownloadTemplate from '@/views/submit/metadata/rawData/common/DownloadTemplate.vue';
  import DeleteLog from '@/views/submit/metadata/rawData/common/DeleteLog.vue';
  import ResultLog from '@/views/submit/metadata/rawData/common/ResultLog.vue';
  import { getExperimentType } from '@/api/metadata/dict';

  import {
    batchSaveExp,
    deleteExperiment,
    getExpTypeData,
  } from '@/api/metadata/experiment';
  import useSubmissionStore from '@/store/modules/metadata';
  import { storeToRefs } from 'pinia';
  import bus from '@/utils/bus';
  import { BusEnum } from '@/utils/enums';
  import HtTable from '@/components/HtTable/index.vue';
  import { isArrEmpty } from '@/utils';
  import { initHtTableTitle } from '@/utils/ht/util';
  import { getExperimentGeneralInfo } from '@/api/app/experiment';
  import { getSubmission } from '@/api/metadata/submitter';
  import ExpSapAttrDetailDialog from '@/views/submit/metadata/rawData/common/ExpSapAttrDetailDialog.vue';

  const updateKey = ref(1);
  const emit = defineEmits(['continueMessage']);
  let { proxy } = getCurrentInstance();
  // 提交者信息
  const submissionStore = useSubmissionStore();
  // 提交编号
  const { subNo } = storeToRefs(submissionStore);

  // 当前阶段
  const currStage = ref('experiment');
  // 系统拥有的组学类型列表
  const expTypeList = ref([]);
  const data = reactive({
    form: {
      subNo: undefined,
      expNo: undefined,
      expType: null,
    },
  });
  const { form } = toRefs(data);

  const isRequesting = ref(false);

  // 后台校验错误信息弹窗
  const resultDialogOpen = ref(false);
  const resultDialogData = ref([]);

  // 表格数据
  const hotTableData = reactive([]);
  // 表格列配置
  const hotColumns = reactive([]);
  // 表格隐藏列配置
  const hideColIndex = reactive([]);

  let showSapAttrDetailDialog = ref(false);

  // const hotTableLoading = ref(false);

  /** 加载系统所拥有的组学类型 */
  function loadExperiment() {
    return getExperimentType().then(response => {
      expTypeList.value = response.data;
    });
  }

  let attrList = ref([]);

  /** 切换实验类型时，刷新表格 */
  function loadExpTable() {
    changeLoadingFlag(true);
    let type = form.value.expType;
    if (type) {
      getExpTypeData(type, subNo.value)
        .then(response => {
          initHtTable(response.data.attributes);
          let rows = response.data.rows;
          attrList.value = response.data.attributes;
          if (!isArrEmpty(rows)) {
            hotTableData.length = 0;
            hotTableData.push(...rows);
          }
          updateKey.value++;
          if (type === 'Proteomic-Spatial proteomics') {
            proxy.$modal.alert(
              proxy.$t(
                'submit.metadata.rawData.experiment.expMultiple.messages.antibodyInfo',
              ),
            );
          }
        })
        .catch(() => {
          changeLoadingFlag(false);
        });
    } else {
      changeLoadingFlag(false);
    }
  }

  /** 初始化表格数据和配置 */
  function initHtTable(data) {
    initHtTableTitle(data, hotTableData, hotColumns, hideColIndex);
  }

  /** 上传excel成功后初始化ht表格 */
  function changeHtTableData(excelData) {
    changeLoadingFlag(true);
    hotTableData.length = 0;
    excelData.forEach(item => {
      let row = {};
      for (let key2 in item) {
        row[key2] = item[key2]['value'];
      }
      hotTableData.push(row);
    });
    updateKey.value++;
  }

  /** 继续 */
  function continueNext() {
    const { validateChanged } = proxy.$refs['expHtTable'];
    if (validateChanged) {
      if (validateChanged()) {
        sendMessage('SampleMultiple');
      } else {
        proxy.$modal
          .confirm(
            proxy.$t(
              'submit.metadata.rawData.experiment.expMultiple.messages.unsavedConfirm',
            ),
          )
          .then(function () {
            sendMessage('SampleMultiple');
          })
          .catch(() => {});
      }
    }
  }

  //  continue
  const sendMessage = val => {
    emit('continueMessage', val);
  };

  function checkAndSubmit() {
    proxy.$modal.loading(
      proxy.$t(
        'submit.metadata.rawData.experiment.expMultiple.messages.saving',
      ),
    );
    if (!subNo.value) {
      // 如果没有subNo代表用户没有保存过Submitter数据，自动保存
      bus.emit(BusEnum.SUBMIT_SUBMITTER, saveForm);
    } else {
      saveForm();
    }
  }

  function changeLoadingFlag(flag) {
    isRequesting.value = flag;
  }

  /** 提交数据 */
  const saveData = () => {
    changeLoadingFlag(true);
    // 使用setTimeout，保证loading动画立即弹出
    setTimeout(checkAndSubmit, 100);
  };

  /** 删除数据 */
  const deleteForm = () => {
    proxy.$modal
      .confirm(
        proxy.$t(
          'submit.metadata.rawData.experiment.expMultiple.messages.deleteConfirm',
        ),
      )
      .then(() => {
        const params = {
          subNo: subNo.value,
          single: false,
        };
        deleteExperiment(params).then(response => {
          if (response.data) {
            proxy.$refs['deleteLog'].openLog(response.data);
            return;
          }
          proxy.$modal.msgSuccess(
            proxy.$t(
              'submit.metadata.rawData.experiment.expMultiple.messages.deleteSuccess',
            ),
          );
          resetForm();
        });
      })
      .catch(() => {});
  };

  function saveForm() {
    form.value.subNo = subNo.value;
    const expHtTbRef = proxy.$refs['expHtTable'];
    const { validatePromise, refreshOldData, hotInstance } = expHtTbRef;
    if (!validatePromise) {
      proxy.$modal.closeLoading();
      return;
    }
    validatePromise().then(valid => {
      if (!valid) {
        changeLoadingFlag(false);
        proxy.$modal.closeLoading();
        // 校验失败
        proxy.$modal.msgWarning(
          proxy.$t(
            'submit.metadata.rawData.experiment.expMultiple.messages.invalidCells',
          ),
        );
      } else {
        doSaveForm(hotInstance, refreshOldData);
      }
    });
  }

  function doSaveForm(hotInstance, refreshOldData) {
    resultDialogOpen.value = false;
    let expData = hotInstance.getData();
    if (isArrEmpty(expData)) {
      changeLoadingFlag(false);
      proxy.$modal.closeLoading();
      proxy.$modal.alertWarning(
        proxy.$t(
          'submit.metadata.rawData.experiment.expMultiple.messages.dataEmpty',
        ),
      );
      return false;
    }
    let titles = hotInstance.getColHeader();
    let paramData = {
      subNo: form.value.subNo,
      expType: form.value.expType,
      stage: currStage.value,
      datas: expData,
      titles: titles,
    };
    batchSaveExp(paramData)
      .then(response => {
        let data = response.data;
        if (data) {
          // 提交失败，展示错误信息
          resultDialogData.value = data;
          resultDialogOpen.value = true;
        } else {
          refreshOldData();
          proxy.$modal.alertSuccess(
            proxy.$t(
              'submit.metadata.rawData.experiment.expMultiple.messages.saveSuccess',
            ),
          );
        }
        changeLoadingFlag(false);
      })
      .catch(() => {
        changeLoadingFlag(false);
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  /** 表格重置 */
  const resetForm = () => {
    loadExpTable();
  };

  onActivated(() => {
    loadExperiment().then(() => {
      // 如果有subNo，看看有没有实验数据然后获取expType
      if (subNo.value) {
        getSubmission(subNo.value).then(response => {
          let expMultipleNos = response.data.expMultipleNos;
          if (expMultipleNos?.length > 0) {
            getExperimentGeneralInfo(expMultipleNos[0])
              .then(response => {
                form.value.expType = response.data.expType;
              })
              .catch(() => {
                form.value.expType = expTypeList.value[0].name;
              });
          } else if (expTypeList.value.length > 0) {
            form.value.expType = expTypeList.value[0].name;
          }
        });
      } else if (expTypeList.value.length > 0) {
        form.value.expType = expTypeList.value[0].name;
      }
    });
  });

  onDeactivated(() => {
    form.value.expType = null;
  });

  /** 切换实验类型 */
  watch(
    () => form.value.expType,
    () => {
      loadExpTable();
    },
  );
</script>

<style lang="scss" scoped>
  .exp-type {
    margin: 0;
  }

  .select-column {
    .select-col-btn {
      color: #ffffff;

      :deep(.el-tag__content) {
        display: flex;
        align-items: center;
        font-weight: 600;
        color: #ffffff;
      }

      &:hover {
        cursor: pointer;
      }
    }

    .el-tag {
      color: #333333;
    }

    .required-tag {
      border: none;
      background-color: #e7e5a5;
    }

    .recommend-tag {
      border: none;
      background-color: #c8e6cb;
    }

    .invalid-tag {
      border: none;
      background-color: #ffbeba;
    }

    .col-radio {
      padding: 2px 6px;
      border-radius: 12px;
    }

    :deep(.el-radio.el-radio--large .el-radio__label) {
      font-size: 12px !important;
    }

    .color-key {
      font-size: 13px;
    }

    .w-85 {
      width: 85%;
    }

    .popover-btn {
      :deep(span) {
        padding-top: 1px;
      }
    }
  }

  .el-checkbox-group {
    :deep(.el-checkbox) {
      width: 25%;
      margin-right: 65px;
      //&:nth-child(odd) {
      //  margin-right: 70px;
      //}
    }

    :deep(.el-checkbox__label) {
      font-size: 12px;
    }
  }

  :deep(.el-upload-dragger) {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 16px;
    background-color: #eff3f9;

    .el-icon--upload {
      margin-right: 0.5rem;
      font-size: 40px;
      color: #fe7f2b;
      margin-bottom: 0;
    }
  }

  :deep(.el-upload-list) {
    margin: 0;
  }
</style>
