<template>
  <el-divider content-position="left"
    ><h3 class="preview-title">Attributes</h3></el-divider
  >
  <div class="d-flex preview">
    <template v-for="(item, idx) in attrFiled" :key="'expAttrFiled-' + idx">
      <div v-if="attributes[item.attributesField]">
        <span class="title">{{ item.attributesName }}</span>
        <span class="content">{{
          $text(attributes[item.attributesField])
        }}</span>
      </div>
    </template>
  </div>
</template>

<script setup>
  import { onMounted, ref } from 'vue';
  import { getExpSapData } from '@/api/metadata/dict';

  const props = defineProps({
    attrData: {
      type: Object,
      default() {
        return {};
      },
    },
    expType: {
      type: String,
      required: true,
    },
  });

  const attributes = ref(props.attrData);
  const expType = ref(props.expType);
  const attrFiled = ref([]);

  /** 加载当前系统所拥有的组学类型数据字典 */
  function loadExperimentData(type) {
    getExpSapData(type).then(response => {
      attrFiled.value = response.data.attributes;
    });
  }

  onMounted(() => {
    loadExperimentData(expType.value);
  });
</script>
