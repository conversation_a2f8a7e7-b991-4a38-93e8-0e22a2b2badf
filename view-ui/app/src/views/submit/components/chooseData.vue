<template>
  <div class="d-flex submit-step">
    <div
      class="rawdata"
      :class="{ active: activeMenu === 'rawdata' }"
      @click="menuOnClick('rawdata')"
    >
      <div class="d-flex align-items-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          xmlns:xlink="http://www.w3.org/1999/xlink"
          width="32"
          height="32"
          viewBox="0 0 32 32"
          fill="none"
        >
          <g opacity="1" transform="translate(0 0)  rotate(0)">
            <path
              id="路径 519"
              fill-rule="evenodd"
              :style="{ fill: activeMenu === 'rawdata' ? '#fff' : '#3A78E8' }"
              opacity="1"
              d="M0,2.94c0,-1.62 1.37,-2.94 3.07,-2.94h24.72c1.7,0 3.07,1.32 3.07,2.94v20.41c0,1.62 -1.37,2.94 -3.07,2.94h-23.42c-1.17,0 -2.09,0.81 -2.09,1.76c0,0.95 0.9,1.75 2.05,1.77h26.53c0.63,0 1.14,0.49 1.14,1.09c0,0.59 -0.5,1.08 -1.11,1.09h-26.52c-2.39,0 -4.37,-1.75 -4.37,-3.95c0,-0.07 0,-0.14 0.01,-0.22c-0.01,-0.03 -0.01,-0.06 -0.01,-0.09zM3.0493,24.1036h24.74c0.43,0 0.78,-0.33 0.79,-0.73v-20.43c0,-0.41 -0.34,-0.75 -0.76,-0.76h-24.75c-0.43,0 -0.78,0.33 -0.79,0.73v20.44c0,0.41 0.34,0.74 0.77,0.75zM26.647,9.7404c0,0.6 -0.49,1.08 -1.11,1.09h-20.19c-0.63,0 -1.14,-0.48 -1.14,-1.09c0,-0.59 0.5,-1.08 1.11,-1.09h20.2c0.62,0 1.13,0.49 1.13,1.09zM26.647,15.9552c0,0.6 -0.49,1.08 -1.11,1.09h-20.19c-0.63,0 -1.14,-0.49 -1.14,-1.09c0,-0.59 0.5,-1.08 1.11,-1.09h20.2c0.62,0 1.13,0.49 1.13,1.09z"
            />
          </g>
        </svg>
        <span class="font-600 ml-1">
          {{ $t('submit.components.chooseData.uploadData') }}
        </span>
      </div>
      <el-icon>
        <CaretRight />
      </el-icon>
    </div>
    <div
      class="metadata"
      :class="{ active: activeMenu === 'metadata' }"
      @click="menuOnClick('metadata')"
    >
      <div class="d-flex align-items-center">
        <!--              '#07BCB4'-->
        <svg
          xmlns="http://www.w3.org/2000/svg"
          xmlns:xlink="http://www.w3.org/1999/xlink"
          width="32"
          height="32.001953125"
          viewBox="0 0 32 32.001953125"
          fill="none"
        >
          <path
            id="路径 520"
            fill-rule="evenodd"
            :style="{ fill: activeMenu === 'metadata' ? '#fff' : '#FE7F2B' }"
            opacity="1"
            d="M27.04,3.86v10.67c0,0.59 -0.47,1.07 -1.04,1.07c-0.57,0 -1.03,-0.46 -1.04,-1.04v-10.63c0,-0.99 -0.77,-1.79 -1.73,-1.8h-19.39c-0.96,0 -1.74,0.79 -1.76,1.77v23.93c0,0.98 0.77,1.78 1.73,1.8h11.83c0.58,0 1.04,0.47 1.04,1.06c0,0.58 -0.45,1.05 -1.01,1.06h-11.83c-2.1,0 -3.81,-1.72 -3.84,-3.86v-23.96c0,-2.15 1.69,-3.9 3.78,-3.93h19.42c2.1,0 3.81,1.72 3.84,3.86zM6.9595,7.4473h13.04c0.58,0 1.04,0.48 1.04,1.06c0,0.58 -0.45,1.05 -1.01,1.07h-13.05c-0.57,0 -1.04,-0.48 -1.04,-1.07c0,-0.58 0.45,-1.05 1.02,-1.06zM6.9895,14.7305l12.98,0.08c0.58,0.01 1.04,0.49 1.04,1.07c-0.01,0.58 -0.46,1.05 -1.03,1.06h-0.02l-12.99,-0.08c-0.57,-0.01 -1.03,-0.49 -1.03,-1.07c0,-0.58 0.46,-1.05 1.02,-1.06zM24.6393,16.9414c4.07,0 7.36,3.37 7.36,7.53c0,4.16 -3.29,7.53 -7.36,7.53c-4.06,0 -7.36,-3.37 -7.36,-7.53c0,-4.16 3.3,-7.53 7.36,-7.53zM24.6413,29.8684c2.92,0 5.28,-2.42 5.28,-5.4c0,-2.98 -2.36,-5.4 -5.28,-5.4c-2.92,0 -5.28,2.42 -5.28,5.4c0,2.98 2.36,5.4 5.28,5.4zM24.7393,19.9592h0.02c0.09,0.01 0.17,0.03 0.25,0.06c0.03,0.01 0.05,0.02 0.08,0.03v0.01l0.02,0.01c0.1,0.05 0.19,0.12 0.27,0.19c0.01,0.02 0.03,0.04 0.04,0.05l2.31,2.63l0.02,0.02c0.37,0.44 0.33,1.1 -0.09,1.48c-0.43,0.4 -1.09,0.36 -1.47,-0.08l-0.51,-0.57v4.04c0,0.58 -0.47,1.06 -1.04,1.06c-0.57,0 -1.03,-0.46 -1.04,-1.04v-4.06l-0.51,0.57c-0.38,0.44 -1.04,0.48 -1.47,0.08c-0.42,-0.38 -0.46,-1.04 -0.09,-1.48l0.02,-0.02l2.31,-2.63l0.04,-0.04c0.08,-0.08 0.17,-0.15 0.26,-0.2c0.02,-0.01 0.03,-0.01 0.03,-0.02h0.01l0.02,-0.01h0.01l0.03,-0.02c0.01,0 0.03,-0.01 0.04,-0.01c0.07,-0.03 0.14,-0.04 0.22,-0.05h0.01l0.04,-0.01h0.14c0.01,0 0.02,0 0.03,0.01zM13.5995,23.1577c0,0.58 -0.45,1.05 -1.01,1.07h-5.61c-0.57,0 -1.04,-0.48 -1.04,-1.07c0,-0.58 0.45,-1.05 1.02,-1.06h5.6c0.58,0 1.04,0.48 1.04,1.06z"
          />
        </svg>
        <span class="font-600 ml-1">
          {{ $t('submit.components.chooseData.metaData') }}
        </span>
      </div>
      <el-icon>
        <CaretRight />
      </el-icon>
    </div>
    <div
      class="archiving"
      :class="{ active: activeMenu === 'archiving' }"
      @click="menuOnClick('metadata')"
    >
      <div class="d-flex align-items-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          xmlns:xlink="http://www.w3.org/1999/xlink"
          width="36"
          height="36"
          viewBox="0 0 36 36"
          fill="none"
        >
          <g opacity="1" transform="translate(0 0)  rotate(0)">
            <g opacity="1" transform="translate(0 2.720703125)  rotate(0)">
              <path
                id="路径 521"
                fill-rule="evenodd"
                :style="{
                  fill: activeMenu === 'archiving' ? '#fff' : '#07BCB4',
                }"
                opacity="1"
                d="M0,3.17c0,-1.75 1.47,-3.17 3.28,-3.17h11.91c1.51,0 2.73,1.18 2.73,2.64v0.83c0,1 0.84,1.82 1.88,1.82h9.94c1.82,0 3.29,1.42 3.29,3.17v5.92c1.67,1.72 2.69,4.04 2.69,6.58c0,2.93 -1.36,5.56 -3.5,7.33c-0.31,0.34 -0.7,0.62 -1.13,0.81c-1.57,0.97 -3.43,1.53 -5.44,1.53c-1.8,0 -3.49,-0.45 -4.96,-1.25h-17.41c-1.81,0 -3.28,-1.42 -3.28,-3.18zM4.2852,26.9972h13.5c-1.37,-1.65 -2.2,-3.75 -2.2,-6.04c0,-5.34 4.51,-9.67 10.07,-9.67c1.78,0 3.45,0.44 4.9,1.22v-4.49h-10.25c-2.91,0 -5.27,-2.34 -5.32,-5.24v-0.41h-10.7c-1.06,0 -1.92,0.83 -1.92,1.85v20.93c0,1.02 0.86,1.85 1.92,1.85zM25.648,13.6152c-4.23,0 -7.65,3.29 -7.65,7.34c0,4.06 3.42,7.35 7.65,7.35c4.23,0 7.66,-3.29 7.66,-7.35c0,-4.05 -3.43,-7.34 -7.66,-7.34zM27.8098,17.9528l3.12,2.83c0.12,0.1 0.12,0.27 0.02,0.38l-0.02,0.01l-3.12,2.82c-0.08,0.08 -0.2,0.1 -0.3,0.05c-0.1,-0.04 -0.17,-0.14 -0.17,-0.24v-1.89h-5.53c-0.53,0 -0.97,-0.42 -0.97,-0.94v-0.09c0,-0.52 0.44,-0.94 0.97,-0.94h5.53v-1.79c0,-0.11 0.07,-0.21 0.17,-0.25c0.1,-0.04 0.22,-0.02 0.3,0.05z"
              />
            </g>
          </g>
        </svg>
        <span class="font-600 ml-1">
          {{ $t('submit.components.chooseData.archiving') }}
        </span>
      </div>
      <el-icon>
        <CaretRight />
      </el-icon>
    </div>
  </div>
</template>

<script setup>
  import { useRouter } from 'vue-router';
  import { defineProps, toRefs } from 'vue';
  import { CaretRight } from '@element-plus/icons-vue';

  const router = useRouter();
  const props = defineProps({
    activeMenu: {
      type: String,
    },
  });
  const { activeMenu } = toRefs(props);

  // const activeMenu = ref('RawData')
  const menuOnClick = menu => {
    // activeMenu.value = menu
    router.push({
      path: `/submit/${menu}`,
    });
  };
</script>

<style lang="scss" scoped>
  .submit-step {
    margin: 20px 0;

    svg {
      width: 20px;
    }

    & > div {
      flex: 1;
      display: flex;
      color: #666666;
      justify-content: space-between;
      align-items: center;
      border: 2px solid;
      padding: 0px 32px;
      border-radius: 30px;
      margin-right: 12px;
      position: relative;
      transition: all 0.5s ease;

      &:hover {
        cursor: pointer;
      }

      &.active:after {
        position: absolute;
        content: '';
        border: 8px solid;
        left: 8%;
        bottom: -17px;
        border-color: #3a78e8 transparent transparent transparent;
        /* transform: translateX(-50%); */
      }
    }

    & > div:last-child {
      margin-right: 0;
    }

    .rawdata {
      border-color: #3a78e8;

      &:hover {
        background-color: #e9f0fd;
      }

      .el-icon {
        color: #3a78e8;
      }

      &.active {
        background-color: #3a78e8;
        color: #fff;

        &:after {
          border-color: #3a78e8 transparent transparent transparent;
        }

        & .el-icon {
          color: #ffffff;
        }
      }
    }

    .metadata {
      border-color: #fe7f2b;

      .el-icon {
        color: #fe7f2b;
      }

      &:hover {
        background-color: #faf2ed;
      }

      &.active {
        background-color: #fe7f2b;
        color: #fff;

        &:after {
          border-color: #fe7f2b transparent transparent transparent;
        }

        & .el-icon {
          color: #ffffff;
        }
      }
    }

    .archiving {
      border-color: #07bcb4;

      &:hover {
        background-color: #e8faf9;
      }

      el-icon {
        color: #07bcb4;
      }

      &.active {
        background-color: #07bcb4;
        color: #fff;

        &:after {
          border-color: #07bcb4 transparent transparent transparent;
        }

        & .el-icon {
          color: #ffffff;
        }
      }
    }
  }
</style>
