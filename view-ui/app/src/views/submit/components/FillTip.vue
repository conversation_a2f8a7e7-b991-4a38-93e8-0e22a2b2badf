<template>
  <div class="d-flex align-items-center justify-end pr-20">
    <span class="text-danger ml-05 mr-05 mt-05">*</span>
    <span>{{ $t('submit.components.fillTip.required') }}</span>
    <el-divider v-if="recommend" direction="vertical" />
    <el-icon
      v-if="recommend"
      size="12"
      color="#3A78E8"
      style="margin-right: 5px"
      ><InfoFilled
    /></el-icon>
    <span v-if="recommend">{{
      $t('submit.components.fillTip.recommend')
    }}</span>
  </div>
</template>

<script setup>
  import { defineProps, ref } from 'vue';

  const props = defineProps({
    recommend: {
      type: Boolean,
      required: false,
      default: false,
    },
  });

  const recommend = ref(props.recommend);
</script>
