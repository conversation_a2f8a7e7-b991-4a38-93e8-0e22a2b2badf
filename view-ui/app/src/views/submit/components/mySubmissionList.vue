<template>
  <div class="pos-relative d-flex align-items-center mb-1">
    <el-input
      v-model="queryParams.subNo"
      clearable
      :placeholder="$t('submit.components.mySubmissionList.searchPlaceholder')"
      class="w-50"
      @keyup.enter="getDataList"
    />
    <span class="font-600 text-main-color ml-1 mr-1 hidden-xs-only">{{
      $t('submit.components.mySubmissionList.createTime')
    }}</span>
    <el-date-picker
      v-model="dateRange"
      class="hidden-xs-only"
      value-format="YYYY-MM-DD"
      type="daterange"
      :range-separator="$t('submit.components.mySubmissionList.dateTo')"
      :start-placeholder="$t('submit.components.mySubmissionList.startDate')"
      :end-placeholder="$t('submit.components.mySubmissionList.endDate')"
    />
    <el-button class="radius-12 ml-1 mr-1" type="primary" @click="getDataList"
      >{{ $t('submit.components.mySubmissionList.search') }}
    </el-button>
  </div>
  <el-divider class="mb-1 mt-0" />
  <div class="status-select mb-1">
    <span class="font-600 text-main-color mr-2"
      >{{ $t('submit.components.mySubmissionList.status') }}:</span
    >
    <el-radio-group v-model="queryParams.status" @change="getDataList">
      <el-radio label="">{{
        $t('submit.components.mySubmissionList.statusOptions.all')
      }}</el-radio>
      <el-radio label="editing">{{
        $t('submit.components.mySubmissionList.statusOptions.editing')
      }}</el-radio>
      <el-radio v-if="qcStatus === ConfigEnum.Enable" label="waiting"
        >{{
          $t('submit.components.mySubmissionList.statusOptions.waitingReview')
        }}
      </el-radio>
      <el-radio v-if="qcStatus === ConfigEnum.Enable" label="reviewing"
        >{{ $t('submit.components.mySubmissionList.statusOptions.reviewing') }}
      </el-radio>
      <el-radio label="complete">{{
        $t('submit.components.mySubmissionList.statusOptions.complete')
      }}</el-radio>
      <el-radio v-if="qcStatus === ConfigEnum.Enable" label="rejected"
        >{{ $t('submit.components.mySubmissionList.statusOptions.rejected') }}
      </el-radio>
      <el-radio label="deleted">{{
        $t('submit.components.mySubmissionList.statusOptions.deleted')
      }}</el-radio>
    </el-radio-group>
  </div>
  <el-table
    v-loading="loading"
    :data="tableData"
    :header-cell-style="{
      backgroundColor: '#f2f2f2',
      color: '#333333',
      fontWeight: 700,
    }"
    :row-style="{
      position: 'relative',
    }"
    :height="height"
    style="width: 99%"
    border
    :default-sort="queryParams"
    @sort-change="handleSortChange"
  >
    <el-table-column
      prop="id"
      :label="$t('submit.components.mySubmissionList.columns.submissionId')"
      sortable
      width="140"
    >
      <template #default="scope">
        <a class="text-primary" @click="toDetail(scope.row)">
          {{ scope.row.subNo }}
        </a>
      </template>
    </el-table-column>

    <el-table-column
      prop="dataNumber"
      min-width="230"
      :label="$t('submit.components.mySubmissionList.columns.dataEntries')"
    >
      <template #default="scope">
        <div class="d-flex">
          <template v-if="scope.row.dataType === 'rawData'">
            <div class="d-flex align-items-center mr-1">
              <svg-icon icon-class="proj" class-name="svg-data"></svg-icon>
              <span class="number ml-05">
                {{ scope.row.projNum }}
              </span>
            </div>
            <div class="d-flex align-items-center mr-1">
              <svg-icon icon-class="exp" class-name="svg-data"></svg-icon>
              <span class="number ml-05"> {{ scope.row.expNum }}</span>
            </div>
            <div class="d-flex align-items-center mr-1">
              <svg-icon icon-class="samp" class-name="svg-data"></svg-icon>
              <span class="number ml-05">
                {{ scope.row.sapNum }}
              </span>
            </div>
            <div class="d-flex align-items-center mr-1">
              <svg-icon icon-class="run" class-name="svg-data"></svg-icon>
              <span class="number ml-05">
                {{ scope.row.runNum }}
              </span>
            </div>
            <div class="d-flex align-items-center">
              <svg-icon icon-class="data" class-name="svg-data"></svg-icon>
              <span class="number ml-05">
                {{ scope.row.dataNum }}
              </span>
            </div>
          </template>

          <template v-else-if="scope.row.dataType === 'analysisData'">
            <div class="d-flex align-items-center mr-1">
              <svg-icon icon-class="analysis" class-name="svg-data"></svg-icon>
              <span class="number ml-05">
                {{ scope.row.analNum }}
              </span>
            </div>
            <div class="d-flex align-items-center">
              <svg-icon icon-class="data" class-name="svg-data"></svg-icon>
              <span class="number ml-05">
                {{ scope.row.dataNum }}
              </span>
            </div>
          </template>
          <template v-else-if="scope.row.dataType === 'project'">
            <svg-icon icon-class="proj" class-name="svg-data"></svg-icon>
            <span class="number ml-05">
              {{ scope.row.projNum }}
            </span>
          </template>
          <template v-else-if="scope.row.dataType === 'experiment'">
            <svg-icon icon-class="exp" class-name="svg-data"></svg-icon>
            <span class="number ml-05"> {{ scope.row.expNum }}</span>
          </template>
          <template v-else-if="scope.row.dataType === 'sample'">
            <svg-icon icon-class="samp" class-name="svg-data"></svg-icon>
            <span class="number ml-05">
              {{ scope.row.sapNum }}
            </span>
          </template>
          <template v-else-if="scope.row.dataType === 'run'">
            <svg-icon icon-class="run" class-name="svg-data"></svg-icon>
            <span class="number ml-05">
              {{ scope.row.runNum }}
            </span>
          </template>
          <template v-else-if="scope.row.dataType === 'data'">
            <svg-icon icon-class="data" class-name="svg-data"></svg-icon>
            <span class="number ml-05">
              {{ scope.row.dataNum }}
            </span>
          </template>
          <template v-else-if="scope.row.dataType === 'analysis'">
            <div class="d-flex align-items-center mr-1">
              <svg-icon icon-class="analysis" class-name="svg-data"></svg-icon>
              <span class="number ml-05">
                {{ scope.row.analNum }}
              </span>
            </div>
          </template>
          <template v-else-if="scope.row.dataType === 'publish'">
            <svg-icon
              icon-class="publish"
              class-name="svg-data"
              class="publish-icon"
            ></svg-icon>
            <span class="number ml-05">
              {{ 1 }}
            </span>
          </template>
        </div>
      </template>
    </el-table-column>

    <el-table-column
      prop="submitter"
      :label="$t('submit.components.mySubmissionList.columns.submitter')"
      min-width="110"
      :show-overflow-tooltip="true"
    />

    <el-table-column
      prop="status"
      :label="$t('submit.components.mySubmissionList.columns.status')"
      min-width="155"
      sortable
    >
      <template #default="scope">
        <div class="d-flex align-items-center">
          <template v-if="scope.row.status === 'revoke'">
            <el-icon color="#808080" size="19">
              <CircleCloseFilled />
            </el-icon>
            <span class="ml-05 mr-05" style="color: gray">{{
              $t('submit.components.mySubmissionList.statusText.revoke')
            }}</span>
          </template>
          <template v-if="scope.row.status === 'editing'">
            <svg-icon icon-class="edit" class-name="edit-svg"></svg-icon>
            <span class="ml-05 mr-05" style="color: #3a78e8">{{
              $t('submit.components.mySubmissionList.statusText.editing')
            }}</span>
          </template>
          <template v-if="scope.row.status === 'reviewing'">
            <svg-icon icon-class="reviewing" class-name="edit-svg"></svg-icon>
            <span class="ml-05 mr-05" style="color: #fe7f2b">{{
              $t('submit.components.mySubmissionList.statusText.reviewing')
            }}</span>
          </template>
          <template v-if="scope.row.status === 'waiting'">
            <svg-icon icon-class="waiting" class-name="edit-svg"></svg-icon>
            <span class="ml-05 mr-05" style="color: #fe7f2b">{{
              scope.row.processing
                ? $t('submit.components.mySubmissionList.statusText.processing')
                : $t(
                    'submit.components.mySubmissionList.statusText.waitingReview',
                  )
            }}</span>
          </template>
          <template v-if="scope.row.status === 'deleted'">
            <el-icon color="#F56C6C" size="19">
              <CircleCloseFilled />
            </el-icon>
            <span class="ml-05 mr-05" style="color: #f56c6c">{{
              $t('submit.components.mySubmissionList.statusText.deleted')
            }}</span>
          </template>
          <template v-if="scope.row.status === 'complete'">
            <el-icon color="#07BCB4" size="19">
              <CircleCheckFilled />
            </el-icon>
            <span class="ml-05 mr-05" style="color: #07bcb4">{{
              $t('submit.components.mySubmissionList.statusText.complete')
            }}</span>
          </template>
          <template v-if="scope.row.status === 'rejected'">
            <el-icon size="19" color="#F56C6C">
              <WarningFilled />
            </el-icon>
            <span class="ml-05 mr-05" style="color: #ff8989">{{
              $t('submit.components.mySubmissionList.statusText.rejected')
            }}</span>
            <el-popover placement="right" width="450" trigger="hover">
              <template #reference>
                <el-icon color="#DA0619" class="cursor-pointer">
                  <ChatDotRound />
                </el-icon>
              </template>
              <div v-if="scope.row.rejectReason">
                <div class="text-main-color font-600 font-16 text-center">
                  {{
                    $t('submit.components.mySubmissionList.rejectReason.title')
                  }}
                </div>
                <el-divider class="mb-1 mt-1"></el-divider>
                <div class="mb-1">
                  <div
                    v-for="(item, idx) in scope.row.rejectReason"
                    :key="'row-rejectReason' + idx"
                  >
                    <el-divider v-if="idx !== 0" class="mb-1 mt-1"></el-divider>
                    <div class="mb-05">
                      <span
                        class="text-secondary-color fail-label font-600 mr-05"
                        >{{
                          $t(
                            'submit.components.mySubmissionList.rejectReason.reasonType',
                          )
                        }}:</span
                      >
                      <span class="text-secondary-color">{{ item.type }}</span>
                    </div>
                    <div class="d-flex">
                      <span
                        class="text-secondary-color fail-label font-600 label mr-05"
                        >{{
                          $t(
                            'submit.components.mySubmissionList.rejectReason.details',
                          )
                        }}:</span
                      >
                      <span class="text-secondary-color">{{
                        item.reason
                      }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </el-popover>
          </template>
        </div>
      </template>
    </el-table-column>
    <el-table-column
      prop="createTime"
      :label="$t('submit.components.mySubmissionList.columns.createTime')"
      min-width="160"
      sortable
    />
    <el-table-column
      prop="updateTime"
      :label="$t('submit.components.mySubmissionList.columns.lastModified')"
      min-width="160"
      sortable
    />
    <el-table-column
      v-if="queryParams.status !== 'complete'"
      :label="$t('submit.components.mySubmissionList.columns.operate')"
      width="110"
      fixed="right"
    >
      <template #default="scope">
        <div v-if="scope.row.processing === false" class="text-align-left">
          <el-tooltip
            :content="$t('submit.components.mySubmissionList.operations.edit')"
          >
            <svg-icon
              v-if="
                scope.row.status === 'editing' ||
                scope.row.status === 'rejected'
              "
              icon-class="usercenterEdit"
              class-name="operate-svg"
              @click="edit(scope.row)"
            ></svg-icon>
          </el-tooltip>
          <el-tooltip
            :content="
              $t('submit.components.mySubmissionList.operations.submit')
            "
          >
            <svg-icon
              v-if="scope.row.status === 'editing'"
              icon-class="submit"
              class-name="operate-svg"
              @click="submitSubmission(scope.row)"
            ></svg-icon>
          </el-tooltip>
          <el-tooltip
            :content="
              $t('submit.components.mySubmissionList.operations.delete')
            "
          >
            <svg-icon
              v-if="
                scope.row.status === 'editing' ||
                scope.row.status === 'rejected'
              "
              icon-class="delete"
              class-name="operate-svg"
              @click="deletedSubmission(scope.row)"
            ></svg-icon>
          </el-tooltip>
          <el-tooltip
            :content="
              $t('submit.components.mySubmissionList.operations.revoke')
            "
          >
            <svg-icon
              v-if="scope.row.status === 'waiting'"
              icon-class="revoke"
              class-name="operate-svg"
              @click="revokeSubmission(scope.row)"
            ></svg-icon>
          </el-tooltip>
        </div>
      </template>
    </el-table-column>
    <template #empty
      >{{ $t('submit.components.mySubmissionList.noData') }}
      <h4>
        {{ $t('submit.components.mySubmissionList.contactMessage') }}
        <a href="mailto:<EMAIL>" class="font-600 text-primary"
          ><EMAIL></a
        >
      </h4></template
    >
  </el-table>
  <pagination
    v-show="tableDataTotal > 0"
    v-model:page="queryParams.pageNum"
    v-model:limit="queryParams.pageSize"
    class="mb-05 mt-1"
    :total="tableDataTotal"
    @pagination="getDataList"
  />
  <EditPublish ref="editPublishRef"></EditPublish>
  <DeleteLog ref="deleteLog"></DeleteLog>
</template>

<script setup>
  import {
    defineProps,
    getCurrentInstance,
    onMounted,
    reactive,
    toRefs,
  } from 'vue';
  import {
    deleted,
    getSubmissionList,
    rejectToEdit,
    revoke,
    submit,
  } from '@/api/submission';
  import { useRouter } from 'vue-router';
  import EditPublish from '@/components/Publish/editPublish.vue';
  import DeleteLog from '@/views/submit/metadata/rawData/common/DeleteLog.vue';
  import { ConfigEnum } from '@/utils/enums';

  defineProps({
    height: {
      type: Number,
      required: true,
      default: 500,
    },
  });
  const { proxy } = getCurrentInstance();

  const qcStatus = proxy.getConfigVal(ConfigEnum.QC_Status);

  const router = useRouter();

  const data = reactive({
    tableData: [],
    tableDataTotal: 0,
    queryParams: {
      subNo: '',
      status: '',
      orderByColumn: 'updateTime',
      isAsc: 'descending',
      pageNum: 1,
      pageSize: 10,
    },
    dateRange: [],
    loading: true,
  });

  const { tableData, tableDataTotal, queryParams, dateRange, loading } =
    toRefs(data);

  onMounted(() => {
    getDataList();
  });

  /** 查询数据 */
  function getDataList() {
    loading.value = true;

    getSubmissionList(proxy.addDateRange(queryParams.value, dateRange.value))
      .then(response => {
        tableData.value = response.rows;
        tableDataTotal.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  /** 触发排序事件 */
  function handleSortChange(column) {
    if (column.order) {
      queryParams.value.orderByColumn = column.prop;
      queryParams.value.isAsc = column.order;
      getDataList();
    }
  }

  /** 编辑 */
  function edit(row) {
    if (row.dataType === 'publish') {
      proxy.$refs['editPublishRef'].editPublish(row.publishId);
      return;
    }
    // 如果是失败，则先要转换给编辑中状态
    if (row.status === 'rejected') {
      rejectToEdit(row.subNo).finally(() => {
        router.push({
          path: `/submit/metadata/${row.dataType}/edit/${row.subNo}`,
        });
      });
    }
    // 已经是编辑状态，直接跳转到编辑页面
    if (row.status === 'editing') {
      router.push({
        path: `/submit/metadata/${row.dataType}/edit/${row.subNo}`,
      });
    }
  }

  /** 删除 */
  function deletedSubmission(row) {
    proxy.$modal
      .confirm(
        proxy.$t('submit.components.mySubmissionList.messages.deleteConfirm', {
          subNo: row.subNo,
        }),
        '',
      )
      .then(() => {
        proxy.$modal.loading(
          proxy.$t('submit.components.mySubmissionList.messages.deleting'),
        );
        deleted(row.subNo)
          .then(response => {
            if (response.data) {
              proxy.$refs['deleteLog'].openLog(response.data);
              return;
            }
            proxy.$modal.msgSuccess(
              proxy.$t(
                'submit.components.mySubmissionList.messages.deleteSuccess',
              ),
            );
            getDataList();
          })
          .finally(() => {
            proxy.$modal.closeLoading();
          });
      })
      .catch(() => {});
  }

  /** 提交 */
  function submitSubmission(row) {
    /*if (
      (row.dataType === 'rawData' || row.dataType === 'analysisData') &&
      row.dataNum === 0
    ) {
      proxy.$modal.alertWarning('Please archive the data before submitting');
      return;
    }*/
    proxy.$modal
      .confirm(
        proxy.$t('submit.components.mySubmissionList.messages.submitConfirm', {
          subNo: row.subNo,
        }),
      )
      .then(() => {
        proxy.$modal.loading(
          proxy.$t('submit.components.mySubmissionList.messages.submitting'),
        );
        submit(row.subNo)
          .then(response => {
            if (response.data) {
              proxy.$alert(
                "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
                  response.data +
                  '</div>',
                proxy.$t('submit.components.mySubmissionList.messages.error'),
                { dangerouslyUseHTMLString: true },
              );
            } else {
              proxy.$modal.msgSuccess(
                proxy.$t(
                  'submit.components.mySubmissionList.messages.submitSuccess',
                ),
              );
              getDataList();
            }
          })
          .finally(() => {
            proxy.$modal.closeLoading();
          });
      });
  }

  /** 撤回提交 */
  function revokeSubmission(row) {
    proxy.$modal
      .confirm(
        proxy.$t('submit.components.mySubmissionList.messages.revokeConfirm', {
          subNo: row.subNo,
        }),
      )
      .then(() => {
        proxy.$modal.loading(
          proxy.$t('submit.components.mySubmissionList.messages.revoking'),
        );
        revoke(row.subNo)
          .then(() => {
            proxy.$modal.msgSuccess(
              proxy.$t(
                'submit.components.mySubmissionList.messages.revokeSuccess',
              ),
            );
            getDataList();
          })
          .finally(() => {
            proxy.$modal.closeLoading();
          });
      })
      .catch(() => {});
  }

  /** 跳转详情页 */
  function toDetail(row) {
    router.push({
      path: `/submit/submission/detail/${row.subNo}`,
    });
  }
</script>

<style lang="scss">
  .el-table__empty-text {
    width: 100% !important;
  }
</style>

<style lang="scss" scoped>
  :deep(.tool-tip) {
    word-break: break-word;
    min-width: 210px;
  }

  .svg-data {
    width: 20px;
    height: 22px;
  }

  .edit-svg {
    width: 17px;
    height: 17px;
  }

  .publish-icon {
    height: 22px;
    width: 22px;
    margin-left: -2px;
  }

  .fail-label {
    display: inline-block;
    width: 85px;
    text-align: right;
  }
</style>
