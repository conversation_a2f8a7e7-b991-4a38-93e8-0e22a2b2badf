<template>
  <div class="page submission">
    <div class="container-fluid">
      <Breadcrumb :bread-item="$t('submit.submission.list.breadcrumb')" />
      <div class="card mt-1 hidden-xs-only">
        <h2 class="text-main-color">
          {{ $t('submit.submission.list.startNewSubmission') }}
        </h2>
        <div class="d-flex align-items-center mb-1">
          <div class="d-flex flex-column align-items-center">
            <router-link to="/submit/rawdata" class="step rawdata mb-05"
              >{{ $t('submit.submission.list.steps.uploadData') }}
            </router-link>
            <el-icon
              :size="22"
              color="#999999"
              style="position: relative; top: 8px"
            >
              <CaretBottom />
            </el-icon>
            <router-link to="/submit/metadata" class="step metadata mt-05 mb-05"
              >{{ $t('submit.submission.list.steps.metadata') }}
            </router-link>
            <el-icon
              :size="22"
              color="#999999"
              style="position: relative; top: 18px"
            >
              <CaretBottom />
            </el-icon>
            <router-link to="/submit/metadata" class="step archiving mt-2"
              >{{ $t('submit.submission.list.steps.archivingData') }}
            </router-link>
          </div>
          <div class="ml-1">
            <p class="bg-gray radius-14 mb-1">
              {{ $t('submit.submission.list.descriptions.uploadData') }}
            </p>
            <p class="bg-gray radius-14 mb-1">
              {{ $t('submit.submission.list.descriptions.metadata') }}
            </p>
            <p class="bg-gray radius-14">
              {{ $t('submit.submission.list.descriptions.archiving') }}
            </p>
          </div>
        </div>
      </div>
      <div class="card list mt-1">
        <h2 class="text-main-color">
          {{ $t('submit.submission.list.submissionList') }}
        </h2>
        <my-submission-list :height="450"></my-submission-list>
      </div>
    </div>
  </div>
</template>

<script setup>
  import Breadcrumb from '@/components/breadcrumb.vue';
  import MySubmissionList from '@/views/submit/components/mySubmissionList.vue';
</script>

<style lang="scss" scoped>
  .submission {
    padding: 20px 0 25px 0;

    .step {
      width: 140px;
      text-align: center;
      background-color: #3a78e8;
      color: #ffffff;
      font-size: 16px;
      border-radius: 14px;
      padding: 15px;
      box-shadow: 0 3px 6px 0 #c6bebe;

      &.metadata {
        background-color: #07bbb3;
        position: relative;
        top: 12px;
      }

      &.archiving {
        background-color: #fe7f2b;
      }
    }

    p {
      display: flex;
      align-items: center;
      color: #666666;
      line-height: 1.5;
      padding: 8px 10px;
      text-align: justify;
      min-height: 84px;
    }

    .list {
      a {
        &:hover {
          color: #3a78e8;
        }
      }

      .number {
        margin-left: 0.1rem;
      }
    }
  }

  :deep(.tool-tip) {
    word-break: break-word;
    min-width: 210px;
  }

  :deep(.el-divider--horizontal) {
    margin: 5px 0;
  }

  .svg-data {
    width: 20px;
    height: 22px;
  }

  .edit-svg {
    width: 19px;
    height: 19px;
  }
</style>
