/* MaterialDesignIcons.com */
@font-face {
  font-family: "Material Design Icons";
  src: url("../fonts/materialdesignicons-webfont.eot?v=5.5.55");
  src: url("../fonts/materialdesignicons-webfont.eot?#iefix&v=5.5.55") format("embedded-opentype"), url("../fonts/materialdesignicons-webfont.woff2?v=5.5.55") format("woff2"), url("../fonts/materialdesignicons-webfont.woff?v=5.5.55") format("woff"), url("../fonts/materialdesignicons-webfont.ttf?v=5.5.55") format("truetype");
  font-weight: normal;
  font-style: normal;
}

.mdi:before,
.mdi-set {
  display: inline-block;
  font: normal normal normal 24px/1 "Material Design Icons";
  font-size: inherit;
  text-rendering: auto;
  line-height: inherit;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.mdi-chevron-right::before {
  content: "\F0142";
}

.mdi-filter::before {
  content: "\F0232";
}

.mdi-chart-arc::before {
  content: "\F0126";
}

.mdi-download::before {
  content: "\F01DA";
}

.mdi-table-cog::before {
  content: "\F13C2";
}

.mdi-arrow-up::before {
  content: "\F005D";
}

.mdi-help-circle::before {
  content: "\f02d7";
}

.mdi-basket-fill::before {
  content: "\f0077";
}

.mdi-basket::before {
  content: "\f0076";
}

.mdi-table-remove::before {
  content: "\f0a76";
}

.mdi-database-check::before {
  content: "\f0aa9";
}

.mdi-database-lock::before {
  content: "\f0aaa";
}

.mdi-download::before {
  content: "\f01da";
}

.mdi-database-import::before {
  content: "\f095d";
}

.mdi-table-column-remove::before {
  content: "\f04ee";
}