@import url(../fonts/sourceHanSansCn/font.css); @font-face {
  font-family: "iconfont";
  /* Project id 3397386 */
  src: url('../fonts/iconfont.woff2?t=1652409207990') format('woff2'), url('../fonts/iconfont.woff?t=1652409207990') format('woff'), url('../fonts/iconfont.ttf?t=1652409207990') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-main-browse:before {
  content: "\e60b";
}

.icon-item-ico06:before {
  content: "\e60a";
}

.icon-item-ico05:before {
  content: "\e609";
}

.icon-item-ico04:before {
  content: "\e608";
}

.icon-item-ico03:before {
  content: "\e607";
}

.icon-item-ico02:before {
  content: "\e606";
}

.icon-item-ico01:before {
  content: "\e605";
}

.icon-main-ico04:before {
  content: "\e604";
}

.icon-main-ico03:before {
  content: "\e603";
}

.icon-main-ico02:before {
  content: "\e602";
}

.icon-main-ico01:before {
  content: "\e601";
}
