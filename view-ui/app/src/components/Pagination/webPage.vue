<template>
  <!--前端分页插件-->
  <el-pagination
    v-if="total > 0"
    :teleported="false"
    class="justify-center"
    :background="true"
    :current-page="currentPage"
    :page-sizes="pageSizeArr"
    :page-size="pageSize"
    layout="total, sizes, prev, pager, next, jumper"
    :total="total"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
  />
</template>

<script setup>
  import {
    computed,
    ref,
    reactive,
    onMounted,
    toRaw,
    defineModel,
    defineExpose,
  } from 'vue';

  // 双向绑定数据，在子组件中更新后将同步到父组件v-model数据。从 Vue 3.4 开始，推荐的实现方式是使用 defineModel() 宏
  const webTableData = defineModel({
    default: {
      // 分页、排序后显示的每页数据
      tableData: [],
      // 所有原始数据
      originalTableData: [],
      // 渲染次数，用于设置组件key，以刷新组件
      changeNum: 0,
    },
    required: true,
  });

  // 分页相关数据
  const currentPage = ref(1);
  const pageSizeArr = reactive([10, 20, 30, 50]);
  const pageSize = ref(toRaw(pageSizeArr)[0]);
  const total = computed(() => webTableData.value.originalTableData.length);

  // 排序相关数据
  const sortKey = ref(''); // 存储排序的字段
  const sortOrder = ref(''); // 存储排序顺序

  // 自定义排序比较器函数
  function compare(a, b, key) {
    if (a[key] < b[key]) {
      return -1;
    } else if (a[key] > b[key]) {
      return 1;
    } else {
      return 0;
    }
  }

  // 排序后的数据
  const sortedTableData = computed(() => {
    if (!sortKey.value || !sortOrder.value) {
      return webTableData.value.originalTableData.slice();
    } else {
      return webTableData.value.originalTableData.slice().sort((a, b) => {
        if (sortOrder.value === 'ascending') {
          return compare(a, b, sortKey.value);
        } else {
          return compare(b, a, sortKey.value);
        }
      });
    }
  });

  // 计算显示的每页分页数据
  function displayedTableData() {
    const start = (currentPage.value - 1) * pageSize.value;
    const end = start + pageSize.value;
    webTableData.value.tableData = toRaw(sortedTableData.value).slice(
      start,
      end,
    );
  }

  // 处理每页显示数量改变
  const handleSizeChange = val => {
    pageSize.value = val;
    displayedTableData();
  };

  // 处理当前页改变
  const handleCurrentChange = val => {
    currentPage.value = val;
    displayedTableData();
  };

  // 更新排序状态，在父组件的sort-change事件中调用此方法
  const handleSortChange = (prop, order) => {
    sortKey.value = prop;
    sortOrder.value = order;
    displayedTableData();
  };

  // 暴露子组方法
  defineExpose({
    handleSortChange,
  });

  // 初始化数据
  onMounted(() => {
    displayedTableData();
  });
  /*watch(
    () => webTableData.value.originalTableData,
    () => {
      displayedTableData();
    },
    {
      immediate: true,
      deep: true,
    },
  );*/
</script>

<style scoped lang="scss"></style>
