<template>
  <div>
    <el-dialog
      v-model="requestToDialog"
      :title="$t('requestTo.dialog.title')"
      width="1100"
      class="radius-14"
    >
      <el-alert type="error" :closable="false">
        <div class="font-14">
          {{ $t('requestTo.alert.restrictedDataInfo') }}
        </div>
        <div class="font-14">
          {{ $t('requestTo.alert.directSendInfo') }}
        </div>
        <div class="font-14">
          {{ $t('requestTo.alert.tooManyObjectsWarning') }}
        </div>
      </el-alert>
      <div class="bg-gray pl-15 mb-1 mt-1">
        <el-form :inline="true" :model="formInline" class="demo-form-inline">
          <el-form-item
            v-if="type !== 'analysis'"
            :label="$t('requestTo.form.experimentId')"
          >
            <el-select
              v-model="formInline.expNo"
              filterable
              clearable
              remote
              reserve-keyword
              :placeholder="$t('requestTo.form.placeholder')"
              :remote-method="
                (queryString, cb) => {
                  remoteSelect(queryString, 'expNo');
                }
              "
              :loading="selectLoading"
              style="width: 200px"
            >
              <el-option
                v-for="(value, key) in selectOption['expNo']"
                :key="'exp-option-' + key"
                :label="value"
                :value="value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="type !== 'analysis'"
            :label="$t('requestTo.form.sampleId')"
          >
            <el-select
              v-model="formInline.sapNo"
              filterable
              clearable
              remote
              reserve-keyword
              :placeholder="$t('requestTo.form.placeholder')"
              :remote-method="
                (queryString, cb) => {
                  remoteSelect(queryString, 'sapNo');
                }
              "
              :loading="selectLoading"
              style="width: 200px"
            >
              <el-option
                v-for="(value, key) in selectOption['sapNo']"
                :key="'sapNo-option-' + key"
                :label="value"
                :value="value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="type !== 'analysis'"
            :label="$t('requestTo.form.runId')"
          >
            <el-select
              v-model="formInline.runNo"
              filterable
              clearable
              remote
              reserve-keyword
              :placeholder="$t('requestTo.form.placeholder')"
              :remote-method="
                (queryString, cb) => {
                  remoteSelect(queryString, 'runNo');
                }
              "
              :loading="selectLoading"
              style="width: 200px"
            >
              <el-option
                v-for="(value, key) in selectOption['runNo']"
                :key="'runNo-option-' + key"
                :label="value"
                :value="value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('requestTo.form.dataId')">
            <el-select
              v-model="formInline.datNo"
              filterable
              clearable
              remote
              reserve-keyword
              :placeholder="$t('requestTo.form.placeholder')"
              :remote-method="
                (queryString, cb) => {
                  remoteSelect(queryString, 'datNo');
                }
              "
              :loading="selectLoading"
              style="width: 200px"
            >
              <el-option
                v-for="(value, key) in selectOption['datNo']"
                :key="'datNo-option-' + key"
                :label="value"
                :value="value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item class="ml-2">
            <el-button type="primary" round @click="getDataList"
              >{{ $t('requestTo.buttons.search') }}
            </el-button>
            <el-button
              round
              class="btn-primary btn btn-round reset"
              @click="resetFilter()"
              >{{ $t('requestTo.buttons.reset') }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-table
        v-loading="loading"
        :data="dataTable"
        style="width: 100%; margin-bottom: 20px"
        :header-cell-style="{
          backgroundColor: '#f2f2f2',
          color: '#333333',
          fontWeight: 700,
        }"
        border
        max-height="300"
        tooltip-effect="dark"
        :row-key="row => row.datNo"
        :sort-orders="['ascending', 'descending']"
        @sort-change="relTableSortChange"
        @selection-change="selectionDataChange"
      >
        <el-table-column
          type="selection"
          :reserve-selection="true"
          width="45"
        />
        <el-table-column
          prop="datNo"
          :label="$t('requestTo.table.dataId')"
          width="120"
          sortable
        >
        </el-table-column>
        <el-table-column
          prop="name"
          :label="$t('requestTo.table.dataName')"
          sortable
          show-overflow-tooltip
        />
        <el-table-column
          v-if="type !== 'analysis'"
          prop="expName"
          :label="$t('requestTo.table.experiment')"
          sort-by="expNo"
          sortable
          show-overflow-tooltip
        >
          <template #default="scope">
            <router-link
              v-if="scope.row.expNo && scope.row.expName"
              :to="`/experiment/detail/${scope.row.expNo}`"
              class="text-primary"
              target="_blank"
            >
              {{ scope.row.expNo }} ({{ scope.row.expName }})
            </router-link>
          </template>
        </el-table-column>
        <el-table-column
          v-if="type !== 'analysis'"
          prop="sapName"
          :label="$t('requestTo.table.sample')"
          sort-by="sapNo"
          sortable
          show-overflow-tooltip
        >
          <template #default="scope">
            <router-link
              v-if="scope.row.sapName && scope.row.sapNo"
              :to="`/sample/detail/${scope.row.sapNo}`"
              class="text-primary"
              target="_blank"
            >
              {{ scope.row.sapNo }} ({{ scope.row.sapName }})
            </router-link>
          </template>
        </el-table-column>
        <el-table-column
          v-if="type !== 'analysis'"
          prop="runName"
          :label="$t('requestTo.table.run')"
          sort-by="runNo"
          sortable
          show-overflow-tooltip
        >
          <template #default="scope">
            <router-link
              v-if="scope.row.runNo && scope.row.runName"
              :to="`/run/detail/${scope.row.runNo}`"
              class="text-primary"
              target="_blank"
            >
              {{ scope.row.runNo }} ({{ scope.row.runName }})
            </router-link>
          </template>
        </el-table-column>

        <el-table-column
          v-if="type === 'analysis'"
          :label="$t('requestTo.table.analysis')"
          sortable
          sort-by="analNo"
          show-overflow-tooltip
        >
          <template #default="scope">
            <router-link
              :to="`/analysis/detail/${scope.row.analNo}`"
              class="text-primary"
              target="_blank"
            >
              {{ scope.row.analNo }} ({{ scope.row.analName }})
            </router-link>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="queryPageAndSort.totalCount > 0"
        v-model:page="queryPageAndSort.pageNum"
        v-model:limit="queryPageAndSort.pageSize"
        :page-sizes="[5, 10, 100, 500, 1000]"
        class="mb-1 mt-2 justify-center"
        :total="queryPageAndSort.totalCount"
        @pagination="pageDataList"
      />
      <div class="bg-gray p-15">
        <el-row>
          <el-col :span="3">
            <span class="text-secondary-color font-600">{{
              $t('requestTo.form.requestText')
            }}</span>
          </el-col>
          <el-col :span="21">
            <el-input v-model="textarea" :rows="2" type="textarea" />
          </el-col>
        </el-row>
      </div>

      <template #footer>
        <div class="dialog-footer text-center">
          <el-button class="radius-8" type="primary" @click="saveRequest">
            {{ $t('requestTo.buttons.confirm') }}
          </el-button>
          <el-button
            plain
            class="radius-8"
            type="primary"
            @click="requestToDialog = false"
            >{{ $t('requestTo.buttons.cancel') }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { getCurrentInstance, ref } from 'vue';
  import {
    checkRequest,
    getRequestList,
    saveRequestData,
  } from '@/api/app/request';
  import { trimStr } from '@/utils';
  import { searchSelectWord } from '@/api/app/security';

  const { proxy } = getCurrentInstance();

  const requestToDialog = ref(false);

  const type = ref('');
  const typeNo = ref('');

  const queryPageAndSort = ref({
    sortKey: '',
    sortType: '',
    pageNum: 1,
    pageSize: 10,
    totalCount: 0,
  });

  const selectOption = ref({});

  const loading = ref(false);
  const dataTable = ref([]);

  const formInline = ref({
    expNo: '',
    sapNo: '',
    runNo: '',
    datNo: '',
  });

  function resetFilter() {
    formInline.value = {
      expNo: '',
      sapNo: '',
      runNo: '',
      datNo: '',
    };

    queryPageAndSort.value = {
      sortKey: '',
      sortType: '',
      pageNum: 1,
    };
    getDataList();
  }

  /** 数据分页 */
  function pageDataList(pageData) {
    queryPageAndSort.value.pageSize = pageData.limit;
    queryPageAndSort.value.pageNum = pageData.page;
    getDataList();
  }

  function relTableSortChange(column) {
    let { prop, order } = column;
    if (order) {
      queryPageAndSort.value.sortKey = prop;
      queryPageAndSort.value.sortType = order === 'ascending' ? 'asc' : 'desc';
      getDataList();
    }
  }

  // Request请求文本
  const textarea = ref('');

  function init(optionType, optionNo) {
    type.value = optionType;
    typeNo.value = optionNo;

    // 判断是否有打开的权限
    const params = {
      type: type.value,
      typeNo: typeNo.value,
    };

    checkRequest(params).then(response => {
      if (response.data) {
        proxy.$modal.alertWarning(
          proxy.$t('requestTo.messages.applicationSubmitted'),
        );
        return;
      }

      getDataList();
      requestToDialog.value = true;
    });
  }

  function getDataList() {
    let params = {
      type: type.value,
      typeNo: typeNo.value,
    };

    let pagePram = queryPageAndSort.value;

    params = { ...params, ...pagePram, ...formInline };

    loading.value = true;
    getRequestList(params)
      .then(response => {
        dataTable.value = response.data.dataVos;

        queryPageAndSort.value.totalCount = response.data.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const selectLoading = ref(false);

  function remoteSelect(queryStr, field) {
    queryStr = trimStr(queryStr);

    let params = {
      type: type.value,
      typeNo: typeNo.value,
      security: 'Restricted',
      field: field,
      keyword: queryStr,
    };

    selectLoading.value = true;
    searchSelectWord(params)
      .then(response => {
        selectOption.value[field] = response.data;
      })
      .finally(() => {
        selectLoading.value = false;
      });
  }

  const selectedDataRows = ref([]);

  function selectionDataChange(selection) {
    selectedDataRows.value = selection;
  }

  function saveRequest() {
    if (selectedDataRows.value.length === 0) {
      proxy.$modal.alertWarning(
        proxy.$t('requestTo.messages.pleaseSelectData'),
      );
      return;
    }

    let dataNos = selectedDataRows.value.map(it => it.datNo);

    const data = {
      type: type.value,
      typeNo: typeNo.value,
      dataNo: dataNos,
      description: textarea.value,
    };

    proxy.$modal.loading(proxy.$t('requestTo.messages.requesting'));

    saveRequestData(data)
      .then(response => {
        if (response.code === 200) {
          proxy.$modal.alertSuccess(
            proxy.$t('requestTo.messages.applySuccess'),
          );
        }
      })
      .finally(() => {
        proxy.$modal.closeLoading();
        requestToDialog.value = false;
      });
  }

  defineExpose({
    init,
  });
</script>

<style lang="scss" scoped>
  :deep(.el-dialog__body) {
    padding-top: 0 !important;
  }

  :deep(.el-form-item) {
    margin: 8px;
    .el-form-item__label {
      width: 110px;
    }
  }
  .reset {
    padding: 8px 15px !important;
  }
  .sort .el-button:nth-child(2) {
    border-radius: 12px 0 0 12px;
  }
  .collapse {
    height: 52px;
    overflow: hidden;
  }
  .arrow {
    cursor: pointer;
    position: relative;
    top: 6px;
  }
</style>
