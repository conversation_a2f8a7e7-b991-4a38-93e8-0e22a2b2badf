<template>
  <div>
    <el-dialog
      v-model="reviewToDialog"
      :title="$t('reviewTo.dialog.title')"
      width="59%"
      class="radius-14"
    >
      <el-alert type="warning" :closable="false">
        <div class="font-14">
          {{ $t('reviewTo.alert.urlInvalidInfo') }}
        </div>
        <div class="font-14">
          {{ $t('reviewTo.alert.securitySuggestion') }}
        </div>
        <div class="font-14">
          {{ $t('reviewTo.alert.periodSuggestion') }}
        </div>
      </el-alert>

      <div class="mb-1 mt-1">
        <el-form
          ref="reviewForm"
          :rules="rules"
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
        >
          <el-form-item :label="$t('reviewTo.form.name')">
            <el-input v-model="formInline.user" clearable />
          </el-form-item>
          <el-form-item :label="$t('reviewTo.form.email')" prop="email">
            <el-input v-model="formInline.email" clearable />
          </el-form-item>
          <el-form-item
            :label="$t('reviewTo.form.validPeriod')"
            prop="date"
            required
          >
            <el-date-picker
              v-model="formInline.date"
              :teleported="false"
              type="date"
              value-format="YYYY-MM-DD"
              :placeholder="$t('reviewTo.form.pickDate')"
              clearable
            />
          </el-form-item>
        </el-form>
      </div>

      <div class="bg-gray checkbox p-15">
        <template v-if="projList.idList && projList.idList.length !== 0">
          <el-checkbox
            v-model="projList.checkAll"
            @change="handleCheckAllChange(projList)"
            >{{ $t('reviewTo.dataTypes.project') }}
          </el-checkbox>
          <el-checkbox-group
            v-model="projList.checkedID"
            @change="handleCheckedCitiesChange(projList)"
          >
            <el-checkbox v-for="it in projList.idList" :key="it" :label="it">
              <router-link :to="`/project/detail/${it}`" target="_blank">
                <div class="id-list mr-1">
                  <span class="btn-project">P</span>
                  <span>{{ it }}</span>
                </div>
              </router-link>
            </el-checkbox>
          </el-checkbox-group>
          <el-divider class="mb-1 mt-1"></el-divider>
        </template>

        <template v-if="analList.idList && analList.idList.length !== 0">
          <el-checkbox
            v-model="analList.checkAll"
            @change="handleCheckAllChange(analList)"
            >{{ $t('reviewTo.dataTypes.analysis') }}
          </el-checkbox>
          <el-checkbox-group
            v-model="analList.checkedID"
            @change="handleCheckedCitiesChange(analList)"
          >
            <el-checkbox v-for="it in analList.idList" :key="it" :label="it">
              <router-link :to="`/analysis/detail/${it}`" target="_blank">
                <div class="id-list mr-1">
                  <span class="btn-project">A</span>
                  <span>{{ it }}</span>
                </div>
              </router-link>
            </el-checkbox>
          </el-checkbox-group>
        </template>

        <template v-if="exprList.idList && exprList.idList.length !== 0">
          <el-checkbox
            v-model="exprList.checkAll"
            @change="handleCheckAllChange(exprList)"
            >{{ $t('reviewTo.dataTypes.experiment') }}
          </el-checkbox>
          <el-checkbox-group
            v-model="exprList.checkedID"
            @change="handleCheckedCitiesChange(exprList)"
          >
            <el-checkbox v-for="it in exprList.idList" :key="it" :label="it">
              <router-link :to="`/experiment/detail/${it}`" target="_blank">
                <div class="id-list mr-1">
                  <span class="btn-experiment">X</span>
                  <span>{{ it }}</span>
                </div>
              </router-link>
            </el-checkbox>
          </el-checkbox-group>
          <el-divider class="mb-05 mt-05"></el-divider>
        </template>

        <template v-if="sampList.idList && sampList.idList.length !== 0">
          <el-checkbox
            v-model="sampList.checkAll"
            @change="handleCheckAllChange(sampList)"
            >{{ $t('reviewTo.dataTypes.sample') }}
          </el-checkbox>
          <el-checkbox-group
            v-model="sampList.checkedID"
            @change="handleCheckedCitiesChange(sampList)"
          >
            <el-checkbox v-for="it in sampList.idList" :key="it" :label="it">
              <router-link :to="`/sample/detail/${it}`" target="_blank">
                <div class="id-list mr-1">
                  <span class="btn-sample">S</span>
                  <span>{{ it }}</span>
                </div>
              </router-link>
            </el-checkbox>
          </el-checkbox-group>
          <el-divider class="mb-05 mt-05"></el-divider>
        </template>
      </div>

      <template #footer>
        <div class="dialog-footer text-center">
          <el-button class="radius-8" type="primary" @click="saveData">
            {{ $t('reviewTo.buttons.confirm') }}
          </el-button>
          <el-button
            plain
            class="radius-8"
            type="primary"
            @click="reviewToDialog = false"
            >{{ $t('reviewTo.buttons.cancel') }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { getCurrentInstance, reactive, ref } from 'vue';
  import { getReviewList, saveReviewData } from '@/api/app/review';
  import { ElMessageBox } from 'element-plus';

  const { proxy } = getCurrentInstance();

  const type = ref('');
  const typeNo = ref('');
  function init(optionType, optionNo) {
    type.value = optionType;
    typeNo.value = optionNo;

    getDataList();
    reviewToDialog.value = true;
  }

  const reviewToDialog = ref(false);
  const loading = ref(false);

  function getDataList() {
    let params = {
      type: type.value,
      typeNo: typeNo.value,
    };

    loading.value = true;
    getReviewList(params)
      .then(response => {
        const result = response.data;
        projList.idList = result.projNos;
        projList.checkedID = result.projNos;

        analList.idList = result.analNos;
        analList.checkedID = result.analNos;

        exprList.idList = result.expNos;
        exprList.checkedID = result.expNos;

        sampList.idList = result.sapNos;
        sampList.checkedID = result.sapNos;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const rules = reactive({
    email: [
      {
        type: 'email',
        message: proxy.$t('reviewTo.validation.emailFormat'),
        trigger: ['blur', 'change'],
      },
    ],
    date: [
      {
        required: true,
        message: proxy.$t('reviewTo.validation.dateRequired'),
        trigger: 'blur',
      },
    ],
  });

  function saveData() {
    proxy.$refs['reviewForm'].validate(valid => {
      if (valid) {
        const data = {
          reviewToName: formInline.user,
          reviewToEmail: formInline.email,
          validDate: formInline.date,
          projNos: projList.checkedID,
          expNos: exprList.checkedID,
          sapNos: sampList.checkedID,
          analNos: analList.checkedID,
        };

        if (
          projList.checkedID?.length === 0 &&
          exprList.checkedID?.length === 0 &&
          sampList.checkedID?.length === 0 &&
          analList.checkedID?.length === 0
        ) {
          proxy.$modal.alertError(
            proxy.$t('reviewTo.messages.selectAtLeastOne'),
          );
          return;
        }

        // 将字符串转换为日期对象
        const dateParts = formInline.date.split('-');
        const year = parseInt(dateParts[0]);
        const month = parseInt(dateParts[1]) - 1; // 月份从0开始计数，所以需要减1
        const day = parseInt(dateParts[2]);
        const date = new Date(year, month, day);

        // 获取当前日期
        const currentDate = new Date();

        // 将当前日期增加3个月
        const futureDate = new Date();
        futureDate.setMonth(currentDate.getMonth() + 3);

        // 比较日期
        if (date <= futureDate) {
          proxy.$modal
            .confirm(proxy.$t('reviewTo.messages.shortPeriodWarning'))
            .then(() => {
              submitData(data);
            })
            .catch(() => {});
        } else {
          submitData(data);
        }
      }
    });
  }

  function submitData(data) {
    proxy.$modal.loading(proxy.$t('reviewTo.messages.sending'));
    saveReviewData(data)
      .then(response => {
        // console.log(response);
        if (response.code === 200 && response.msg) {
          const url = response.msg;
          const linkText = proxy.$t('reviewTo.messages.linkIs');
          const saveText = proxy.$t('reviewTo.messages.saveCarefully');
          const html = `${linkText} <a href="${url}" target="_blank" class="text-primary">${url} </a></br>${saveText}`;

          ElMessageBox.alert(
            html,
            proxy.$t('reviewTo.messages.reviewSuccess'),
            {
              dangerouslyUseHTMLString: true,
            },
          );
        }
      })
      .finally(() => {
        proxy.$modal.closeLoading();
        reviewToDialog.value = false;
      });
  }

  const formInline = reactive({
    name: '',
    email: '',
    date: '',
  });

  const projList = reactive({
    checkAll: true,
    checkedID: [],
    idList: [],
  });

  const analList = reactive({
    checkAll: true,
    checkedID: [],
    idList: [],
  });

  const exprList = reactive({
    checkAll: true,
    checkedID: [],
    idList: [],
  });

  const sampList = reactive({
    checkAll: true,
    checkedID: [],
    idList: [],
  });

  const handleCheckAllChange = list => {
    list.checkedID = list.checkAll ? list.idList : [];
  };

  const handleCheckedCitiesChange = list => {
    const checkedCount = list.checkedID.length;
    list.checkAll = checkedCount === list.idList.length;
  };

  defineExpose({
    init,
  });
</script>

<style lang="scss" scoped>
  .svg-share {
    width: 24px;
    height: 30px;
    margin-left: 0.5rem;
    cursor: pointer;
    &:focus {
      outline: none;
    }
  }
  .checkbox {
    .id-list > span:first-child {
      line-height: 20px !important;
    }
  }

  :deep(.el-dialog__body) {
    padding-top: 0 !important;
  }
  :deep(.el-select__wrapper) {
    border-radius: 12px;
  }
  :deep(.el-form-item) {
    margin-bottom: 0;
  }
</style>
