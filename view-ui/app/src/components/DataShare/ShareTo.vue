<template>
  <div>
    <el-dialog
      v-model="shareToDialog"
      :title="$t('shareTo.dialog.title')"
      width="50%"
      class="radius-14"
    >
      <div v-loading="loading">
        <div>
          <span class="mr-1" style="font-weight: bolder">{{
            $t('shareTo.form.shareToEmail', { count: allEmailInp.length })
          }}</span>
          <!--<el-select
            v-model="email"
            multiple
            clearable
            filterable
            remote
            :reserve-keyword="true"
            placeholder="Email"
            :remote-method="remoteEmail"
            :loading="emailLoading"
          >
            <el-option
              v-for="(item, index) in emailList"
              :key="`share-email-${item}-${index}`"
              :label="item"
              :value="item"
            />
          </el-select>-->
          <div style="max-height: 600px; overflow: auto">
            <el-form ref="emailsRef">
              <template
                v-for="(it, index) in allEmailInp"
                :key="`email-share-${index}`"
              >
                <el-row>
                  <el-col :span="20">
                    <el-form-item label="" prop="email">
                      <el-input
                        v-model="allEmailInp[index]"
                        clearable
                        :placeholder="$t('shareTo.form.emailPlaceholder')"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="4">
                    <el-button
                      :class="index === 0 ? 'icon-minus ml-1' : 'ml-1'"
                      circle
                      type="warning"
                      plain
                      @click="removeParam(index)"
                    >
                      <el-icon>
                        <Minus />
                      </el-icon>
                    </el-button>

                    <el-button
                      v-if="index === allEmailInp.length - 1"
                      type="primary"
                      class="ml-2"
                      circle
                      plain
                      @click="addParam"
                    >
                      <el-icon>
                        <Plus />
                      </el-icon>
                    </el-button>
                  </el-col>
                </el-row>
              </template>
            </el-form>
          </div>
        </div>
        <div
          v-if="
            !isArrEmpty(projList.idList) ||
            !isArrEmpty(exprList.idList) ||
            !isArrEmpty(analList.idList) ||
            !isArrEmpty(sampList.idList)
          "
          class="bg-gray checkbox p-15 mt-1"
        >
          <template v-if="!isArrEmpty(projList.idList)">
            <el-checkbox
              v-model="projList.checkAll"
              @change="handleCheckAllChange(projList)"
              >{{ $t('shareTo.dataTypes.project') }}
            </el-checkbox>
            <el-checkbox-group
              v-model="projList.checkedID"
              @change="handleCheckedCitiesChange(projList)"
            >
              <el-checkbox v-for="it in projList.idList" :key="it" :label="it">
                <router-link target="_blank" :to="`/project/detail/${it}`">
                  <div class="id-list mr-1">
                    <span class="btn-project">P</span>
                    <span>{{ it }}</span>
                  </div>
                </router-link>
              </el-checkbox>
            </el-checkbox-group>
            <el-divider class="mb-1 mt-1"></el-divider>
          </template>

          <template v-if="!isArrEmpty(exprList.idList)">
            <el-checkbox
              v-model="exprList.checkAll"
              @change="handleCheckAllChange(exprList)"
              >{{ $t('shareTo.dataTypes.experiment') }}
            </el-checkbox>
            <el-checkbox-group
              v-model="exprList.checkedID"
              @change="handleCheckedCitiesChange(exprList)"
            >
              <el-checkbox v-for="it in exprList.idList" :key="it" :label="it">
                <router-link target="_blank" :to="`/experiment/detail/${it}`">
                  <div class="id-list mr-1">
                    <span class="btn-experiment">X</span>
                    <span>{{ it }}</span>
                  </div>
                </router-link>
              </el-checkbox>
            </el-checkbox-group>
            <el-divider class="mb-05 mt-05"></el-divider>
          </template>

          <template v-if="!isArrEmpty(sampList.idList)">
            <el-checkbox
              v-model="sampList.checkAll"
              @change="handleCheckAllChange(sampList)"
              >{{ $t('shareTo.dataTypes.sample') }}
            </el-checkbox>
            <el-checkbox-group
              v-model="sampList.checkedID"
              @change="handleCheckedCitiesChange(sampList)"
            >
              <el-checkbox v-for="it in sampList.idList" :key="it" :label="it">
                <router-link target="_blank" :to="`/sample/detail/${it}`">
                  <div class="id-list mr-1">
                    <span class="btn-sample">S</span>
                    <span>{{ it }}</span>
                  </div>
                </router-link>
              </el-checkbox>
            </el-checkbox-group>
            <el-divider class="mb-05 mt-05"></el-divider>
          </template>

          <template v-if="!isArrEmpty(analList.idList)">
            <el-checkbox
              v-model="analList.checkAll"
              @change="handleCheckAllChange(analList)"
              >{{ $t('shareTo.dataTypes.analysis') }}
            </el-checkbox>
            <el-checkbox-group
              v-model="analList.checkedID"
              @change="handleCheckedCitiesChange(analList)"
            >
              <el-checkbox v-for="it in analList.idList" :key="it" :label="it">
                <router-link target="_blank" :to="`/analysis/detail/${it}`">
                  <div class="id-list mr-1">
                    <span class="btn-project">A</span>
                    <span>{{ it }}</span>
                  </div>
                </router-link>
              </el-checkbox>
            </el-checkbox-group>
            <el-divider class="mb-05 mt-05"></el-divider>
          </template>
        </div>
        <div v-else>
          <el-empty
            :image-size="100"
            :description="$t('shareTo.empty.noData')"
          />
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer text-center">
          <el-button
            :disabled="!hasData"
            class="radius-8"
            type="primary"
            @click="saveShare()"
          >
            {{ $t('shareTo.buttons.confirm') }}
          </el-button>
          <el-button
            plain
            class="radius-8"
            type="primary"
            @click="shareToDialog = false"
            >{{ $t('shareTo.buttons.cancel') }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { computed, getCurrentInstance, reactive, ref } from 'vue';
  import { getShareList, saveShareData } from '@/api/app/share';
  import { isArrEmpty, isEmail, trimStr } from '@/utils';

  const { proxy } = getCurrentInstance();
  const type = ref('');
  const typeNo = ref('');
  const loading = ref(false);

  const email = ref([]);
  const shareToDialog = ref(false);

  const projList = reactive({
    checkAll: false,
    checkedID: [],
    idList: [],
  });
  const exprList = reactive({
    checkAll: false,
    checkedID: [],
    idList: [],
  });
  const sampList = reactive({
    checkAll: false,
    checkedID: [],
    idList: [],
  });
  const analList = reactive({
    checkAll: false,
    checkedID: [],
    idList: [],
  });

  const emailsRef = ref();

  const allEmailInp = reactive(['']);

  // 删除一行查询条件
  function removeParam(index) {
    if (allEmailInp.length <= 1) {
      return false;
    }
    allEmailInp.splice(index, 1);
  }

  // 添加一行查询条件
  function addParam() {
    if (allEmailInp.length > 15) {
      return false;
    }
    allEmailInp.push('');
  }

  function init(optionType, optionNo) {
    initEmptyData();
    if (!optionType || !optionNo) {
      return;
    }
    type.value = optionType;
    typeNo.value = optionNo;
    let params = {
      type: type.value,
      typeNo: typeNo.value,
    };
    shareToDialog.value = true;

    loading.value = true;
    getShareList(params)
      .then(response => {
        let data = response.data;
        initListData(projList, data.shareProjectNos);
        initListData(exprList, data.shareExperimentNos);
        initListData(sampList, data.shareSampleNos);
        initListData(analList, data.shareAnalysisNos);
      })
      .finally(() => {
        loading.value = false;
      });
  }

  function initEmptyData() {
    email.value = [];
    initListData(projList);
    initListData(exprList);
    initListData(sampList);
    initListData(analList);
  }

  function initListData(listReact, nos) {
    listReact.checkAll = false;
    listReact.checkedID = [];
    listReact.idList = [];
    if (!isArrEmpty(nos)) {
      listReact.idList.push(...nos);
      listReact.checkAll = true;
      listReact.checkedID.push(...nos);
    }
  }

  const hasData = computed(() => {
    return (
      projList.idList.length > 0 ||
      exprList.idList.length > 0 ||
      sampList.idList.length > 0 ||
      analList.idList.length > 0
    );
  });

  const handleCheckAllChange = list => {
    list.checkedID = list.checkAll ? list.idList : [];
  };

  const handleCheckedCitiesChange = list => {
    const checkedCount = list.checkedID.length;
    list.checkAll = checkedCount === list.idList.length;
  };

  function saveShare() {
    email.value = [];
    let errorEmail = [];
    if (!isArrEmpty(allEmailInp)) {
      for (let i = 0; i < allEmailInp.length; i++) {
        let item = trimStr(allEmailInp[i]);
        if (item) {
          if (isEmail(item)) {
            email.value.push(item);
          } else {
            errorEmail.push(item);
          }
        }
      }
    }
    if (errorEmail.length > 0) {
      proxy.$modal.alertWarning(
        proxy.$t('shareTo.messages.emailFormatError', {
          emails: errorEmail.join(', '),
        }),
      );
      return false;
    }
    // console.log(email.value);

    if (isArrEmpty(email.value)) {
      proxy.$modal.alertWarning(proxy.$t('shareTo.messages.pleaseSelectEmail'));
      return false;
    }
    let shareProjectNos = projList.checkedID;
    let shareExperimentNos = exprList.checkedID;
    let shareSampleNos = sampList.checkedID;
    let shareAnalysisNos = analList.checkedID;
    let data = {
      shareProjectNos,
      shareExperimentNos,
      shareSampleNos,
      shareAnalysisNos,
    };
    if (!hasCheckedData(data)) {
      proxy.$modal.alertWarning(proxy.$t('shareTo.messages.selectAtLeastOne'));
      return;
    }
    data.shareTo = [...email.value];
    shareToDialog.value = true;
    proxy.$modal.loading(proxy.$t('shareTo.messages.sharing'));
    saveShareData(data)
      .then(() => {
        shareToDialog.value = false;
        proxy.$modal.alertSuccess(proxy.$t('shareTo.messages.shareSuccess'));
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  // 校验是否勾选分享项
  function hasCheckedData(data) {
    // 获取对象的所有自身可枚举属性的键数组
    const keys = Object.keys(data);
    for (let i = 0; i < keys.length; i++) {
      let key = keys[i];
      if (!isArrEmpty(data[key])) {
        return true;
      }
    }
    return false;
  }

  // email远程加载
  /*
const emailList = ref([]);
const emailLoading = ref(false);
function remoteEmail(query) {
  query = trimStr(query);
  if (query) {
    emailLoading.value = true;
    getShareEmailList(query)
      .then(response => {
        emailList.value = response.data;
      })
      .finally(() => {
        emailLoading.value = false;
      });
  } else {
    emailList.value = [];
  }
}*/

  defineExpose({
    init,
  });
</script>

<style lang="scss" scoped>
  .svg-share {
    width: 28px;
    height: 30px;
    margin-left: 0.2rem;
    cursor: pointer;

    &:focus {
      outline: none;
    }
  }

  .checkbox {
    .id-list > span:first-child {
      line-height: 20px !important;
    }
  }

  :deep(.el-dialog__body) {
    padding-top: 0 !important;
  }

  :deep(.el-select__wrapper) {
    border-radius: 12px;
  }
</style>
