<template>
  <svg :class="className" aria-hidden="true">
    <use :xlink:href="'#icon-' + props.iconClass" />
  </svg>
</template>

<script setup>
  import { defineProps } from 'vue';
  const props = defineProps({
    iconClass: {
      type: String,
      required: true,
    },
    className: {
      type: String,
      default: '',
    },
  });
  const { className } = props;
</script>

<style scope lang="scss"></style>
