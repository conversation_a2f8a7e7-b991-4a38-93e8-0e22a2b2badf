<template>
  <el-dialog
    v-model="visible"
    :title="add ? 'Add Publish' : 'Edit Publish'"
    class="edit-publish"
    width="850"
    append-to-body
  >
    <el-form
      ref="submitPublishForm"
      :model="form"
      :rules="rules"
      :inline="true"
      label-width="100"
    >
      <el-form-item label="Title" prop="articleName">
        <el-input v-model="form.articleName" style="width: 710px" />
      </el-form-item>

      <el-form-item label="DOI" prop="doi">
        <el-input v-model="form.doi" style="width: 270px" :disabled="!add" />
        <el-tooltip content="Click to auto-fill.">
          <img
            src="@/assets/images/plosp.png"
            alt=""
            style="margin-left: 2px; width: 20px; cursor: pointer"
            @click="getFromPlosp(form.doi)"
          />
        </el-tooltip>
      </el-form-item>

      <el-form-item label="PMID" class="mr-0" prop="pmid">
        <el-input v-model="form.pmid" style="width: 290px" />
      </el-form-item>

      <el-form-item label="Publication" prop="publication">
        <el-autocomplete
          v-model="form.publication"
          :teleported="false"
          class="w-100"
          :fetch-suggestions="queryJournalSearch"
          clearable
          style="width: 710px"
        />
      </el-form-item>

      <el-form-item label="Reference" prop="reference">
        <el-input
          v-model="form.reference"
          type="textarea"
          rows="2"
          style="width: 710px"
        />
      </el-form-item>
    </el-form>

    <div class="bg-gray p-10-15">
      <el-row :gutter="20" class="align-items-center">
        <el-col :span="6">
          <span class="font-600 text-primary mr-05">{{
            form?.relatedId?.length || 0
          }}</span>
          <span class="font-600 text-main-color">items are related</span>
        </el-col>

        <el-col :span="18" class="text-align-right">
          <el-input v-model="relatedId" style="width: 240px"></el-input>
          <el-button
            type="primary"
            round
            class="ml-1"
            size="small"
            @click="addRelatedId"
            >Add
          </el-button>
        </el-col>
      </el-row>

      <el-divider></el-divider>

      <div
        v-if="form?.relatedId && form?.relatedId?.length !== 0"
        class="d-flex mt-1 flex-wrap gap-12"
      >
        <el-tag
          v-for="(item, idx) in form.relatedId"
          :key="item"
          closable
          effect="light"
          round
          @close="removeRelatedId(idx)"
        >
          <router-link
            v-if="item.startsWith('OEP')"
            :to="`/project/detail/${item}`"
            target="_blank"
          >
            {{ item }}
          </router-link>
          <router-link
            v-if="item.startsWith('OEX')"
            :to="`/experiment/detail/${item}`"
            target="_blank"
          >
            {{ item }}
          </router-link>
          <router-link
            v-if="item.startsWith('OES')"
            :to="`/sample/detail/${item}`"
            target="_blank"
          >
            {{ item }}
          </router-link>
          <router-link
            v-if="item.startsWith('OEZ')"
            :to="`/analysis/detail/${item}`"
            target="_blank"
          >
            {{ item }}
          </router-link>
        </el-tag>
      </div>
    </div>

    <template #footer>
      <div class="text-center">
        <el-button type="primary" @click="submit">Confirm</el-button>
        <el-button @click="visible = false">Cancel</el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog
    v-model="previewDialog"
    title="Preview"
    class="preview-dialog radius-14"
  >
    <div class="d-flex preview">
      <div class="w-100">
        <span class="title">Journal</span>
        <span class="content" v-text="plospInfo.publication"></span>
      </div>
      <div class="w-100">
        <span class="title">DOI</span>
        <span class="content" v-text="plospInfo.doi"></span>
      </div>
      <div class="w-100">
        <span class="title">PMID</span>
        <span class="content" v-text="plospInfo.pmid"></span>
      </div>
      <div class="w-100">
        <span class="title">Title</span>
        <span class="content" v-text="plospInfo.articleName"></span>
      </div>
      <div class="w-100">
        <span class="title">Reference</span>
        <span class="content" v-text="plospInfo.reference"></span>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <div class="text-align-center">
          <el-button
            type="primary"
            class="btn-primary btn btn-s btn-shadow"
            round
            @click="fillIn"
            >Fill In</el-button
          >
          <el-button
            round
            class="btn-primary btn btn-round"
            @click="previewDialog = false"
            >Cancel</el-button
          >
        </div>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
  import { defineExpose, getCurrentInstance, reactive, ref, toRefs } from 'vue';
  import {
    existRelatedId,
    getPubInfoFromPlosp,
    getPublishById,
    savePublish,
  } from '@/api/app/publish';
  import router from '@/router';
  import { isStrBlank } from '@/utils';

  const { proxy } = getCurrentInstance();

  const visible = ref(false);

  const data = reactive({
    form: {
      articleName: '',
      doi: '',
      pmid: '',
      publication: '',
      reference: '',
    },
    relatedId: '',
    add: true,
    rules: {
      articleName: [
        {
          required: true,
          message: 'Please input title',
          trigger: 'blur',
        },
        {
          validator: proxy.$validateChinese,
          trigger: 'blur',
        },
      ],
      publication: [
        {
          required: true,
          message: 'Please input publication',
          trigger: 'blur',
        },
        {
          validator: proxy.$validateChinese,
          trigger: 'blur',
        },
      ],
      doi: [
        {
          required: true,
          message: 'Please input doi',
          trigger: 'blur',
        },
        {
          pattern: /10\.\d{4,}\/\S+/,
          message: 'DOI format is incorrect',
          trigger: 'blur',
        },
        {
          validator: proxy.$validateChinese,
          trigger: 'blur',
        },
      ],
      pmid: [
        {
          pattern: /\b\d{8,9}\b/,
          message: 'PMID format is incorrect',
          trigger: 'blur',
        },
      ],
      reference: [
        {
          validator: proxy.$validateChinese,
          trigger: 'blur',
        },
      ],
    },
  });

  let plospInfo = ref({
    id: undefined, // ID
    publication: undefined, // Journal
    doi: undefined,
    pmid: undefined,
    articleName: undefined, // Title
    reference: undefined,
  });

  let previewDialog = ref(false);

  const { form, rules, relatedId, add } = toRefs(data);

  // 新增文献
  function addPublish() {
    resetData();
    add.value = true;
    visible.value = true;
    proxy.$refs['submitPublishForm'].resetFields();
  }

  // 编辑文献
  function editPublish(publishId) {
    resetData();
    if (!publishId) {
      proxy.$modal.msgError('查询文献详细信息错误，文献DI不能为空');
      return;
    }
    add.value = false;
    form.value.publishId = publishId;

    // 查询文献
    getPublishById(publishId).then(response => {
      form.value = response.data;
      visible.value = true;
      proxy.$refs['submitPublishForm'].resetFields();
    });
  }

  // 新增或修改文献
  function submit() {
    if (!form.value?.relatedId || form.value?.relatedId?.length === 0) {
      proxy.$modal.alertWarning('Please enter the Related items Id');
      return;
    }
    proxy.$refs['submitPublishForm'].validate(valid => {
      if (valid) {
        savePublish(form.value).then(response => {
          proxy.$modal.msgSuccess('Save successful');

          if (proxy.$route.path.includes('/submit/submission/detail')) {
            window.location.reload();
          } else {
            router.push({
              path: `/submit/submission/detail/${response.msg}`,
            });
          }
        });
      }
    });
  }

  // 判断ID是否存在
  function addRelatedId() {
    if (!relatedId.value) {
      proxy.$modal.alertWarning(
        'Please enter the Project ID, Experiment ID, Sample ID, or Analysis ID',
      );
      return;
    }
    if (!form.value?.relatedId) {
      form.value['relatedId'] = [];
    }
    if (form.value.relatedId.includes(relatedId.value.trim())) {
      proxy.$modal.alertWarning('The ID already exists');
      return;
    }
    existRelatedId(relatedId.value).then(() => {
      form.value.relatedId.push(relatedId.value.trim());
      relatedId.value = '';
    });
  }

  function removeRelatedId(idx) {
    form.value.relatedId.splice(idx, 1);
  }

  // 清空表单数据
  function resetData() {
    form.value = {
      articleName: '',
      doi: '',
      pmid: '',
      publication: '',
      reference: '',
    };
    relatedId.value = '';
  }

  const { node_journal } = proxy.useDict('node_journal');

  /** 期刊 自动补全提醒 */
  const queryJournalSearch = (queryString, cb) => {
    const results = queryString
      ? node_journal.value.filter(createFilter(queryString))
      : node_journal.value;
    cb(results);
  };
  const createFilter = queryString => {
    return node_journal => {
      return (
        node_journal.value.toLowerCase().indexOf(queryString.toLowerCase()) ===
        0
      );
    };
  };

  function getFromPlosp(doi) {
    if (isStrBlank(doi) || !/10\.\d{4,}\/\S+/.test(doi)) {
      proxy.$modal.msgError('Please enter correct DOI!');
      return;
    }

    // 从PLOSP获取数据
    proxy.$modal.loading('Loading...');
    getPubInfoFromPlosp(doi)
      .then(response => {
        if (!response.data) {
          proxy.$modal.alertError('Publication information not found!');
          return;
        }
        plospInfo.value = response.data;
        previewDialog.value = true;
      })
      .catch(e => {
        proxy.$modal.alertError(e);
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  function fillIn() {
    proxy.$modal.msgSuccess('Publication filled successfully!');

    form.value.publication = plospInfo.value.publication;
    form.value.pmid = plospInfo.value.pmid;
    form.value.doi = plospInfo.value.doi;
    form.value.articleName = plospInfo.value.articleName;
    form.value.reference = plospInfo.value.reference;

    previewDialog.value = false;
  }

  defineExpose({
    addPublish,
    editPublish,
  });
</script>

<style lang="scss" scoped>
  :deep(.el-input__wrapper) {
    width: 710px;
  }
</style>
