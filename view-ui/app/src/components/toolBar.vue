<template>
  <!--v-model:visible="visible"-->
  <!--trigger="hover"-->
  <el-popover
    v-model:visible="visible"
    trigger="hover"
    placement="bottom"
    :width="width"
  >
    <template #reference>
      <el-button
        type="primary"
        :icon="Menu"
        class="radius-12 hidden-xs-only"
        @click="visible = true"
      >
        {{ $t('common.columnVisibility') }}
      </el-button>
    </template>

    <div>
      <el-input
        v-model="filterInput"
        style="width: 96%; margin-bottom: 5px"
        clearable
        :placeholder="$t('common.search')"
      />
      <el-checkbox-group v-model="radioList">
        <el-checkbox
          v-for="item in columns"
          v-show="item.show"
          :key="item.label"
          :style="{ width: `${checkboxWidth}px` }"
          :label="item.label"
          :value="item.label"
          class="col-cb-item"
          :title="item.label"
        />
      </el-checkbox-group>
    </div>

    <div class="mt-1 text-center">
      <el-button type="success" size="small" class="mr-1" @click="selectAll">
        {{ $t('common.selectAll') }}
      </el-button>
      <el-button
        type="primary"
        size="small"
        class="mr-1"
        @click="selectCol(radioList)"
      >
        {{ $t('common.confirm') }}
      </el-button>
      <el-button type="info" size="small" @click="defaultSet">
        {{ $t('common.default') }}
      </el-button>
    </div>
  </el-popover>
</template>

<script setup>
  import { Menu } from '@element-plus/icons-vue';
  import { defineProps, ref, watch } from 'vue';
  import { trimStr } from '@/utils';

  const props = defineProps({
    /* 显隐列信息 */
    columns: {
      type: Array,
    },
    width: {
      type: Number,
    },
    checkboxWidth: {
      type: Number,
      required: false,
    },
  });
  const visible = ref(false);
  const filterInput = ref('');

  const columns = ref(props.columns);

  const radioList = ref([]);
  const map = new Map();

  columns.value.forEach(item => {
    if (item.visible) {
      radioList.value.push(item.label);
      map.set(item.label, item.label);
    }
  });

  const selectAll = () => {
    columns.value.forEach(item => {
      item.visible = true;
    });
    radioList.value = Array.from(
      new Set(columns.value.map(item => item.label)),
    );
    visible.value = false;
  };
  const selectCol = list => {
    columns.value.forEach(item => {
      const status = list.some(it => it === item.label);
      item.visible = status;
    });
    visible.value = false;
  };
  const defaultSet = () => {
    radioList.value = Array.from(map.values());
    selectCol(radioList.value);
  };

  // 复选框名称过滤监听
  watch(
    filterInput,
    newValue => {
      let val = trimStr(newValue).toLowerCase();
      if (val) {
        columns.value.forEach(ele => {
          ele.show = !!ele.label?.toLowerCase().includes(val);
        });
      } else {
        columns.value.forEach(ele => {
          ele.show = true;
        });
      }
    },
    {
      immediate: true,
      deep: true,
    },
  );
</script>

<style lang="scss" scoped>
  //.el-checkbox {
  //  width: 150px;
  //}
  :deep(.el-checkbox__label) {
    font-size: 14px;
  }

  :deep(.el-checkbox) {
    margin-right: 10px;
  }

  :deep(.col-cb-item) {
    .el-checkbox__label {
      //max-width: 180px !important;
      text-overflow: ellipsis; /* 使用省略符号 */
      white-space: nowrap; /* 确保文本不换行 */
      overflow: hidden; /* 确保文本被裁剪 */
    }
  }

  :deep(.el-checkbox-group) {
    max-height: 230px;
    overflow: auto;
  }
</style>
