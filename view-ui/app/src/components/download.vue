<template>
  <div class="submit-page">
    <div class="container-fluid">
      <div class="card mt-1">
        <div class="text-center">
          <img
            src="@/assets/images/downloading.png"
            alt=""
            style="height: 200px"
          />
          <div class="font-600 text-secondary-color font-20">
            {{ $t('download.downloadAboutToStart') }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { getCurrentInstance, onMounted } from 'vue';

  const { proxy } = getCurrentInstance();
  let path = proxy.$route.path;
  onMounted(() => {
    proxy.download(path).then(() => {
      // 浏览器关闭当前页面
      // window.open('about:blank', '_self').close();
    });
  });
</script>

<style lang="scss" scoped></style>
