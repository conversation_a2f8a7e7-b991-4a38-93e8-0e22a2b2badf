<template>
  <div>
    <el-breadcrumb :separator-icon="ArrowRight">
      <el-breadcrumb-item :to="{ path: '/' }">{{
        $t('common.home')
      }}</el-breadcrumb-item>
      <el-breadcrumb-item v-if="$route.path.includes('/submit')">
        {{ $t('common.submit') }}
      </el-breadcrumb-item>
      <el-breadcrumb-item>{{ props.breadItem }}</el-breadcrumb-item>
    </el-breadcrumb>
  </div>
</template>

<script setup>
  import { ArrowRight } from '@element-plus/icons-vue';
  import { defineProps } from 'vue';

  const props = defineProps({
    breadItem: {
      type: String,
    },
  });
  // const {breadItem} = toRefs(props)
</script>

<style lang="scss" scoped></style>
