{"root": true, "env": {"browser": true, "es2021": true, "node": true}, "extends": ["eslint:recommended", "plugin:vue/vue3-recommended", "plugin:prettier/recommended", "prettier"], "parser": "vue-eslint-parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["vue", "html", "prettier"], "rules": {"prettier/prettier": "error", "vue/require-default-prop": "off", "vue/multi-word-component-names": "off"}}