import request from '@/utils/request';
import { trimStr } from '@/utils';

// 根据字典类型，查询字典详细数据
export function getDictValue(type) {
  return request({
    url: `/upload/dict/value/${type}`,
    method: 'get',
  });
}

// 根据字典类型，查询字典详细数据
export function getDictTypeData(type) {
  return request({
    url: `/upload/dict/labelValue/${type}`,
    method: 'get',
  });
}

// 根据字典类型，查询字典详细数据
export function getExpSapData(type) {
  return request({
    url: `/upload/dict/expSapData/${type}`,
    method: 'get',
  });
}

// 获取实验的类型，包括描述信息
export function getExperimentType() {
  return request({
    url: `/upload/dict/getExperimentType`,
    method: 'get',
  });
}

// 获取样本的类型，包括描述信息
export function getSampleType(currSubNo) {
  return request({
    url: `/upload/dict/getSampleType?currSubNo=${trimStr(currSubNo)}`,
    method: 'get',
  });
}
