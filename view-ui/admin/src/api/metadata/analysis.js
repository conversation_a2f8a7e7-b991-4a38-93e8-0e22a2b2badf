import request from '@/utils/request';

const baseURL = '/system/metadata/analysis';

// 查询用户拥有的Analysis列表
export function listAnalysis(data) {
  return request({
    url: `${baseURL}/listAnalysis`,
    method: 'post',
    data: data,
  });
}

// 根据expNo查询用户的Analysis信息
export function getAnalysisInfo(analNo) {
  return request({
    url: `${baseURL}/getByNo/${analNo}`,
    method: 'get',
  });
}

// 获取target的选项
export function getTargetOptions(query) {
  return request({
    url: `${baseURL}/getTargetOptions`,
    method: 'post',
    data: query,
  });
}

// 获取pipeline的选项
export function getPipelineOptions(query) {
  return request({
    url: `${baseURL}/getPipelineOptions`,
    method: 'post',
    data: query,
  });
}

// 保存编辑
export function editAnalysis(data) {
  return request({
    url: `${baseURL}/edit`,
    method: 'post',
    headers: {
      repeatSubmit: true,
    },
    data: data,
  });
}

// Analysis删除预检查
export function analysisDeleteCheck(analNo) {
  return request({
    url: `${baseURL}/deleteCheck/${analNo}`,
    method: 'get',
  });
}

// 删除Analysis所有以及相关联的
export function deleteAnalysisAll(params) {
  return request({
    url: `${baseURL}/deleteAll`,
    method: 'delete',
    headers: {
      repeatSubmit: true,
    },
    params: params,
  });
}

// 获取Analysis的ExpType
export function getAuditedAnalType() {
  return request({
    url: `${baseURL}/getAuditedAnalType`,
    method: 'get',
  });
}

// 修改Analysis及关联的数据的creator
export function updateAnalysisCreator(params) {
  return request({
    url: `${baseURL}/updateCreator`,
    method: 'post',
    headers: {
      repeatSubmit: true,
    },
    params: params,
  });
}

// 刷新ES索引
export function refreshIndex(analNo) {
  return request({
    url: `${baseURL}/refreshIndex/${analNo}`,
    method: 'get',
  });
}
