import request from '@/utils/request';

// 查询列表
export function fdMultSapResList(query) {
  return request({
    url: '/system/fdMultSample/list',
    method: 'get',
    params: query,
  });
}

// 添加弹窗中，查询列表
export function fdMultSapResDialogList(query) {
  return request({
    url: '/system/fdMultSample/dialogList',
    method: 'get',
    params: query,
  });
}

// 弹窗保存数据
export function saveMultSapRes(data) {
  return request({
    url: '/system/fdMultSample/save',
    method: 'post',
    data: data,
  });
}

// 批量修改状态
export function batchUpdateMultSapStatus(data) {
  return request({
    url: `/system/fdMultSample/batchUpdateStatus`,
    method: 'post',
    data: data,
  });
}

// 批量新增
export function batchImportMultSap(data) {
  return request({
    url: `/system/fdMultSample/batchImport`,
    method: 'post',
    data: data,
  });
}

// 批量删除
export function batchDeleteMultSap(data) {
  return request({
    url: `/system/fdMultSample/batchDelete`,
    method: 'post',
    data: data,
  });
}
