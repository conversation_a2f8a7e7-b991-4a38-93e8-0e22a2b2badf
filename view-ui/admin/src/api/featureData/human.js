import request from '@/utils/request';

// 查询参数列表
export function fdHumanResourceList(query) {
  return request({
    url: '/system/fdHumanResource/list',
    method: 'get',
    params: query,
  });
}

// 查询项目编号
export function fdSearchPrjId(keyword) {
  return request({
    url: `/system/fdHumanResource/searchPrjId/${keyword}`,
    method: 'get',
  });
}

// 保存
export function saveHumanResource(data) {
  return request({
    url: `/system/fdHumanResource/saveHumanResource`,
    method: 'post',
    data: data,
  });
}

// 批量修改状态
export function batchUpdateHrStatus(data) {
  return request({
    url: `/system/fdHumanResource/batchUpdateHrStatus`,
    method: 'post',
    data: data,
  });
}

// 批量删除
export function batchDeleteHr(data) {
  return request({
    url: `/system/fdHumanResource/batchDeleteHr`,
    method: 'post',
    data: data,
  });
}

// 清理缓存
export function refreshFdCache() {
  return request({
    url: `/system/fdWeb/refreshFdCache`,
    method: 'get',
  });
}
