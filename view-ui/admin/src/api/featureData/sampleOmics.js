import request from '@/utils/request';

// 查询列表
export function fdSingleSapResList(query) {
  return request({
    url: '/system/fdSingleSap/list',
    method: 'get',
    params: query,
  });
}

// 添加弹窗中，查询列表
export function fdSingleSapResDialogList(query) {
  return request({
    url: '/system/fdSingleSap/dialogList',
    method: 'get',
    params: query,
  });
}

// 弹窗保存数据
export function saveSingleSapRes(data) {
  return request({
    url: '/system/fdSingleSap/save',
    method: 'post',
    data: data,
  });
}

// 批量修改状态
export function batchUpdateSingleSapStatus(data) {
  return request({
    url: `/system/fdSingleSap/batchUpdateStatus`,
    method: 'post',
    data: data,
  });
}

// 批量新增
export function batchImportSingleSap(data) {
  return request({
    url: `/system/fdSingleSap/batchImport`,
    method: 'post',
    data: data,
  });
}

// 批量删除
export function batchDeleteSingleSap(data) {
  return request({
    url: `/system/fdSingleSap/batchDelete`,
    method: 'post',
    data: data,
  });
}
