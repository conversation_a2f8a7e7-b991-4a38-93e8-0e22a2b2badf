import request from '@/utils/request';

// 查询列表
export function fdMultOmicResList(query) {
  return request({
    url: '/system/fdMultOmic/list',
    method: 'get',
    params: query,
  });
}

// 添加弹窗中，查询列表
export function fdMultOmicResDialogList(query) {
  return request({
    url: '/system/fdMultOmic/dialogList',
    method: 'get',
    params: query,
  });
}

// 弹窗保存数据
export function saveMultOmicRes(data) {
  return request({
    url: '/system/fdMultOmic/save',
    method: 'post',
    data: data,
  });
}

// 批量修改状态
export function batchUpdateMultOmicStatus(data) {
  return request({
    url: `/system/fdMultOmic/batchUpdateStatus`,
    method: 'post',
    data: data,
  });
}

// 批量新增
export function batchImportMultOmic(data) {
  return request({
    url: `/system/fdMultOmic/batchImport`,
    method: 'post',
    data: data,
  });
}

// 批量删除
export function batchDeleteMultOmic(data) {
  return request({
    url: `/system/fdMultOmic/batchDelete`,
    method: 'post',
    data: data,
  });
}
