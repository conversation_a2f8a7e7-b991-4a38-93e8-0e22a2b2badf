import request from '@/utils/request';

// 查询平台
export function findPlatformLike(query) {
  return request({
    url: `/search/findPlatformLike`,
    method: 'get',
    params: query,
  });
}

// 查询Taxonomy
export function findTaxonomyLike(query) {
  return request({
    url: `/search/findTaxonomyLike`,
    method: 'get',
    params: query,
  });
}

// 查询Disease
export function findDiseaseLike(query) {
  return request({
    url: `/search/findDiseaseLike`,
    method: 'get',
    params: query,
  });
}

// 查询Biome
export function findBiomeLike(query) {
  return request({
    url: `/search/findBiomeLike`,
    method: 'get',
    params: query,
  });
}
