import request from '@/utils/request';

const baseURL = '/app/request';

// 判断详情页面Request按钮是否显示
export function showRequestButton(params) {
  return request({
    url: `${baseURL}/show`,
    method: 'get',
    params: params,
  });
}

// 判断是否存在请求中的数据
export function checkRequest(params) {
  return request({
    url: `${baseURL}/checkRequest`,
    method: 'get',
    params: params,
  });
}

// 获取Request的data数据
export function getRequestList(params) {
  return request({
    url: `${baseURL}/getRequestList`,
    method: 'get',
    params: params,
  });
}

// 保存Request的data数据
export function saveRequestData(data) {
  return request({
    url: `${baseURL}/saveRequestData`,
    method: 'post',
    headers: {
      repeatSubmit: true,
    },
    data: data,
  });
}

// 判断是否存在请求中的数据
export function getNewRequestNum() {
  return request({
    url: `${baseURL}/getNewRequestNum`,
    method: 'get',
  });
}

// 查询我的数据或者来自他人的数据列表
export function getApplyRequestList(params) {
  return request({
    url: `${baseURL}/getApplyRequestList`,
    method: 'get',
    params: params,
  });
}

// 查看申请记录的data列表
export function getRequestDataList(params) {
  return request({
    url: `${baseURL}/getRequestDataList`,
    method: 'get',
    params: params,
  });
}

// 修改请求结果状态
export function saveRequestStatus(params) {
  return request({
    url: `${baseURL}/saveRequestStatus`,
    method: 'get',
    headers: {
      repeatSubmit: true,
    },
    params: params,
  });
}

// 修改请求see状态
export function updateSee(id) {
  return request({
    url: `${baseURL}/updateSee/${id}`,
    method: 'post',
  });
}
