import request from '@/utils/request';

const baseURL = '/app/review';

// 获取review的数据列表
export function getReviewList(params) {
  return request({
    url: `${baseURL}/getReviewList`,
    method: 'get',
    params: params,
  });
}

// 保存review的数据
export function saveReviewData(data) {
  return request({
    url: `${baseURL}/saveReviewData`,
    method: 'post',
    data: data,
  });
}

// 判断是否存在请求中的数据
export function getNewReviewNum() {
  return request({
    url: `${baseURL}/getNewReviewNum`,
    method: 'get',
  });
}

// 查询我的数据
export function getMyReviewList(params) {
  return request({
    url: `${baseURL}/getMyReviewList`,
    method: 'get',
    params: params,
  });
}

// 查看记录的data列表
export function getReviewDataList(params) {
  return request({
    url: `${baseURL}/getReviewDataList`,
    method: 'get',
    params: params,
  });
}

// 取消分享
export function cancel(params) {
  return request({
    url: `${baseURL}/cancel`,
    method: 'post',
    params: params,
  });
}

// 延长生效日期
export function extendDate(params) {
  return request({
    url: `${baseURL}/extendDate`,
    method: 'post',
    params: params,
  });
}

// 修改请求see状态
export function updateSee(id) {
  return request({
    url: `${baseURL}/updateSee/${id}`,
    method: 'post',
  });
}

// review详情页
export function getDetailData(params) {
  return request({
    url: `${baseURL}/detail`,
    method: 'post',
    params: params,
  });
}
