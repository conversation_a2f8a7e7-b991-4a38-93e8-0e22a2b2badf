import request from '@/utils/request';

const baseURL = '/app/share';

// 获取Share的data数据
export function getShareList(params) {
  return request({
    url: `${baseURL}/getShareList`,
    method: 'get',
    params: params,
  });
}

// 保存Share的data数据
export function saveShareData(data) {
  return request({
    url: `${baseURL}/saveShareData`,
    method: 'post',
    data: data,
  });
}

// 查询未查看的分享数据的总数
export function getNewShareNum() {
  return request({
    url: `${baseURL}/getNewShareNum`,
    method: 'get',
  });
}

// 查询我的分享和来自他人的分享
export function getMyShareList(params) {
  return request({
    url: `${baseURL}/getMyShareList`,
    method: 'get',
    params: params,
  });
}

// 查看记录的data列表
export function getShareDataList(params) {
  return request({
    url: `${baseURL}/getShareDataList`,
    method: 'get',
    params: params,
  });
}

// 取消分享
export function cancelShare(params) {
  return request({
    url: `${baseURL}/cancel`,
    method: 'post',
    params: params,
  });
}
