import request from '@/utils/request';

// 查询列表
export function listSubmission(query) {
  return request({
    url: '/system/submission/list',
    method: 'get',
    params: query,
  });
}

// 查看提交详情
export function getDetailData(subNo) {
  return request({
    url: `/upload/metadata/submission/detail/${subNo}`,
    timeout: 0,
    method: 'get',
  });
}

// 日志列表
export function listLog(query) {
  return request({
    url: `/qc/log/list`,
    method: 'get',
    params: query,
  });
}
