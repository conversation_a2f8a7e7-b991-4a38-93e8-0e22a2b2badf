import request from '@/utils/request';
import { trimStr } from '@/utils';

// 查询用户列表
export function attrDetail(standType, id) {
  return request({
    url: `/system/stdMg/attrDetail/${trimStr(standType)}/${trimStr(id)}`,
    method: 'get',
  });
}

// 属性数据提交保存
export function saveAttrStandMg(data) {
  return request({
    url: `/system/stdMg/saveAttr`,
    method: 'post',
    headers: {
      repeatSubmit: true,
    },
    data: data,
  });
}

// 删除属性数据
export function delAttrStandMg(standId, id) {
  return request({
    url: `/system/stdMg/delAttr/${standId}/${id}`,
    method: 'delete',
  });
}

// 修改属性状态
export function updateAttrStatus(data) {
  return request({
    url: `/system/stdMg/updateAttrStatus`,
    method: 'post',
    data: data,
  });
}
