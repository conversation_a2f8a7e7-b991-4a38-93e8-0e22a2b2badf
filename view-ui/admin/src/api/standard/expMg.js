import request from '@/utils/request';

// 查询用户列表
export function stdExpList(query) {
  return request({
    url: '/system/stdMg/list',
    method: 'get',
    params: query,
  });
}

// 修改状态
export function updateStandMgStatus(data) {
  return request({
    url: `/system/stdMg/updateStatus`,
    method: 'post',
    data: data,
  });
}

// 数据提交保存
export function saveStandMg(data) {
  return request({
    url: `/system/stdMg/save`,
    method: 'post',
    headers: {
      repeatSubmit: true,
    },
    data: data,
  });
}
