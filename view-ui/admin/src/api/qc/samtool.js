import request from '@/utils/request';

const baseURL = '/system/samToolTask';

// 列表
export function listSamToolTask(data) {
  return request({
    url: `${baseURL}/list`,
    method: 'post',
    data: data,
  });
}

export function getSamToolTaskInfo(no) {
  return request({
    url: `${baseURL}/getByNo/${no}`,
    method: 'get',
  });
}

export function retrySamToolTask(params) {
  return request({
    url: `${baseURL}/retry`,
    method: 'get',
    params: params,
  });
}

export function changeTaskPriority(params) {
  return request({
    url: `${baseURL}/changePriority`,
    method: 'get',
    params: params,
  });
}
