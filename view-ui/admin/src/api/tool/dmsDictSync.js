import request from '@/utils/request';

export function listDmsDictSync() {
  return request({
    url: '/system/dmsDictSync/list',
    method: 'get',
  });
}

export function syncDictToDb(dictName) {
  return request({
    url: '/system/dmsDictSync/syncDictToDb',
    method: 'get',
    params: { dictName },
  });
}

export function syncDictToEs(dictName) {
  return request({
    url: '/system/dmsDictSync/syncDictToEs',
    method: 'get',
    params: { dictName },
  });
}
