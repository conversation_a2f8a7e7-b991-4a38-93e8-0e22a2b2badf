import request from '@/utils/request';

// 查询邮件模版列表
export function templateList(query) {
  return request({
    url: '/system/batchEmail/templateList',
    method: 'get',
    params: query,
  });
}

// 查询邮件发送日志
export function sendLogList(query) {
  return request({
    url: '/system/batchEmail/sendLogList',
    method: 'get',
    params: query,
  });
}

// 数据提交保存
export function saveEmailTemp(data) {
  return request({
    url: `/system/batchEmail/save`,
    method: 'post',
    data: data,
  });
}

// 发送邮件
export function sendBatchEmail(data) {
  return request({
    url: `/system/batchEmail/sendBatchEmail`,
    method: 'post',
    data: data,
  });
}

// 预览模版文件
export function previewTemp(query) {
  return request({
    url: '/system/batchEmail/preview',
    method: 'get',
    params: query,
  });
}
