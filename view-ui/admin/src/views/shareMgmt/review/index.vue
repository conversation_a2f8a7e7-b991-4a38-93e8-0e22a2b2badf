<template>
  <div class="app-container">
    <div class="card">
      <el-form ref="searchForm" :model="queryParams" :inline="true">
        <el-form-item label="审核编号" prop="shareNo">
          <el-input
            v-model="queryParams.shareNo"
            clearable
            style="width: 150px"
            @keyup.enter="getDataList"
          ></el-input>
        </el-form-item>
        <el-form-item label="创建人" prop="sourceEmail">
          <el-input
            v-model="queryParams.sourceEmail"
            clearable
            style="width: 150px"
            @keyup.enter="getDataList"
          ></el-input>
        </el-form-item>
        <el-form-item label="被分享邮箱" prop="targetEmail">
          <el-input
            v-model="queryParams.targetEmail"
            clearable
            style="width: 150px"
            @keyup.enter="getDataList"
          ></el-input>
        </el-form-item>
        <el-form-item label="类型ID" prop="projNo">
          <el-input
            v-model="queryParams.projNo"
            clearable
            style="width: 150px"
            @keyup.enter="getDataList"
          ></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select
            v-model="queryParams.status"
            clearable
            style="width: 150px"
          >
            <el-option value="reviewing" label="审核中"></el-option>
            <el-option value="cancled" label="已取消"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="年份" prop="year">
          <el-select v-model="queryParams.year" clearable style="width: 150px">
            <el-option
              v-for="item in yearOpt"
              :key="`shareYear-${item.value}`"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="getDataList"
            >搜索
          </el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <el-divider class="mt-05"></el-divider>

      <div
        v-if="reviewList && reviewList.length !== 0"
        v-loading="loading"
        class="mt-1"
      >
        <div v-for="(item, index) in reviewList" :key="'reviewList-' + index">
          <div class="d-flex align-items-center justify-space-between">
            <div class="d-flex align-items-center">
              <el-tag type="warning" round class="mr-05">审核</el-tag>
              <el-tag
                v-if="item.see === 'No'"
                type="danger"
                effect="dark"
                round
                size="small"
                class="mr-05"
                >新
              </el-tag>
              <span class="font-600 text-warning mr-05">
                {{ item.reviewId }}
              </span>
              <el-tooltip
                :width="20"
                placement="right"
                trigger="hover"
                content="数据ID列表"
              >
                <svg-icon
                  icon-class="dataIdList"
                  class-name="svg-idList"
                  @click="expandDataList(index)"
                ></svg-icon>
              </el-tooltip>
              <el-tag
                v-if="item.status === 'reviewing'"
                type="warning"
                round
                class="ml-2"
                effect="plain"
                >审核中</el-tag
              >
              <el-tag
                v-if="item.status === 'cancled'"
                type="danger"
                round
                class="ml-2"
                effect="plain"
                >已取消</el-tag
              >
            </div>

            <div class="font-600 text-main-color float-right">
              <el-button
                v-if="item.status === 'reviewing'"
                type="danger"
                round
                size="small"
                @click="openCancel(item.id)"
              >
                <span class="font-600">取消</span>
              </el-button>

              <el-button
                v-if="item.status === 'reviewing'"
                type="warning"
                round
                size="small"
                @click="openExtendDate(item.id)"
              >
                <span class="font-600">延期</span>
              </el-button>

              <!--              <el-button
                round
                size="small"
                type="primary"
                @click="exportDataLink(item.reviewId)"
              >
                <span class="font-600">Export Data Links</span>
              </el-button>-->
            </div>
          </div>
          <!--data id list-->
          <el-collapse-transition>
            <div
              v-if="reviewList[index].expand"
              class="radius-12 bg-gray p-15 mt-05 list"
            >
              <el-row
                v-if="reviewList[index]?.expandData?.projNos"
                :gutter="20"
              >
                <el-col :span="4">
                  <span class="text-secondary-color count-type font-600 mr-05">
                    项目数量
                  </span>
                  <el-tag type="success" class="tag-success" size="small">{{
                    reviewList[index]?.expandData?.projNos?.length
                  }}</el-tag>
                </el-col>
                <el-col :span="20" class="d-flex row-gap-10 flex-wrap">
                  <div
                    v-for="id in reviewList[index]?.expandData?.projNos"
                    :key="'projNos-' + id"
                    class="id-list mr-1"
                  >
                    <span class="btn-project">P</span>
                    <span>
                      <a
                        href="javascript:void(0)"
                        @click="showDetail('project', item.creator, id)"
                      >
                        {{ id }}
                      </a>
                    </span>
                  </div>
                </el-col>
                <el-divider class="mg-divider"></el-divider>
              </el-row>
              <el-row
                v-if="reviewList[index]?.expandData?.analNos"
                :gutter="20"
              >
                <el-col :span="5">
                  <span class="text-secondary-color count-type font-600 mr-05">
                    分析数量
                  </span>
                  <el-tag type="success" class="tag-success" size="small">{{
                    reviewList[index]?.expandData?.analNos.length
                  }}</el-tag>
                </el-col>
                <el-col :span="19" class="d-flex row-gap-10 flex-wrap">
                  <div
                    v-for="id in reviewList[index]?.expandData?.analNos"
                    :key="'analNos-' + id"
                    class="id-list mr-1"
                  >
                    <span class="btn-project">A</span>
                    <span>
                      <a
                        href="javascript:void(0)"
                        @click="showDetail('analysis', item.creator, id)"
                      >
                        {{ id }}
                      </a>
                    </span>
                  </div>
                </el-col>
                <el-divider class="mg-divider"></el-divider>
              </el-row>
              <el-row v-if="reviewList[index]?.expandData?.expNos" :gutter="20">
                <el-col :span="5">
                  <span class="text-secondary-color count-type font-600 mr-05">
                    实验数量
                  </span>
                  <el-tag type="success" class="tag-success" size="small">{{
                    reviewList[index]?.expandData?.expNos.length
                  }}</el-tag>
                </el-col>
                <el-col :span="19" class="d-flex row-gap-10 flex-wrap">
                  <div
                    v-for="id in reviewList[index]?.expandData?.expNos"
                    :key="'expNos' + id"
                    class="id-list mr-1"
                  >
                    <span class="btn-experiment">X</span>
                    <a
                      href="javascript:void(0)"
                      @click="showDetail('experiment', item.creator, id)"
                    >
                      {{ id }}
                    </a>
                  </div>
                </el-col>
                <el-divider class="mg-divider"></el-divider>
              </el-row>
              <el-row v-if="reviewList[index]?.expandData?.sapNos" :gutter="20">
                <el-col :span="5">
                  <span class="text-secondary-color count-type font-600 mr-05">
                    样本数量
                  </span>
                  <el-tag type="success" class="tag-success" size="small">{{
                    reviewList[index]?.expandData?.sapNos.length
                  }}</el-tag>
                </el-col>
                <el-col :span="19" class="d-flex row-gap-10 flex-wrap">
                  <div
                    v-for="id in reviewList[index]?.expandData?.sapNos"
                    :key="'sapNos' + id"
                    class="id-list mr-1"
                  >
                    <span class="btn-sample">S</span>
                    <a
                      href="javascript:void(0)"
                      @click="showDetail('sample', item.creator, id)"
                    >
                      {{ id }}
                    </a>
                  </div>
                </el-col>
                <el-divider class="mg-divider"></el-divider>
              </el-row>
              <el-row v-if="reviewList[index]?.expandData?.runNos" :gutter="20">
                <el-col :span="5">
                  <span class="text-secondary-color count-type font-600 mr-05">
                    批次数量
                  </span>
                  <el-tag type="success" class="tag-success" size="small">{{
                    reviewList[index]?.expandData?.runNos.length
                  }}</el-tag>
                </el-col>
                <el-col :span="19" class="d-flex row-gap-10 flex-wrap">
                  <div
                    v-for="id in reviewList[index]?.expandData?.runNos"
                    :key="'runNos' + id"
                    class="id-list mr-1"
                  >
                    <span class="btn-run">R</span>
                    <a
                      href="javascript:void(0)"
                      @click="showDetail('run', item.creator, id)"
                    >
                      {{ id }}
                    </a>
                  </div>
                </el-col>
                <el-divider class="mg-divider"></el-divider>
              </el-row>
              <el-row
                v-if="reviewList[index]?.expandData?.dataNos"
                :gutter="20"
              >
                <el-col :span="5">
                  <span class="text-secondary-color count-type font-600 mr-05">
                    数据数量
                  </span>
                  <el-tag type="success" class="tag-success" size="small">{{
                    reviewList[index]?.expandData?.dataNos.length
                  }}</el-tag>
                </el-col>
                <el-col :span="19" class="d-flex row-gap-10 flex-wrap">
                  <div
                    v-for="id in reviewList[index]?.expandData?.dataNos"
                    :key="'dataNos' + id"
                    class="id-list mr-1"
                  >
                    <span class="btn-data">D</span>
                    <span>{{ id }}</span>
                  </div>
                </el-col>
                <el-divider class="mg-divider"></el-divider>
              </el-row>
            </div>
          </el-collapse-transition>

          <div class="radius-12 bg-gray mt-05 d-flex">
            <div class="d-flex align-items-center mr-2">
              <el-tag type="primary" round size="small" class="mr-05"
                >链接</el-tag
              >
              <svg-icon icon-class="link2" class-name="svg mr-05"></svg-icon>
              <a :href="item.url" target="_blank">
                {{ item.url }}
              </a>
              <el-tooltip
                effect="dark"
                content="复制"
                placement="right"
                popper-class="copy-tooltip"
              >
                <svg-icon
                  icon-class="copy"
                  class-name="svg-copy"
                  @click="copyText(item.url)"
                ></svg-icon>
              </el-tooltip>
            </div>
          </div>

          <div class="radius-12 bg-gray mt-05 d-flex">
            <div class="d-flex align-items-center mr-2">
              <svg-icon icon-class="request-date" class-name="svg"></svg-icon>
              <span class="text-other-color mr-05 ml-05">审查日期:</span>
              <span class="text-other-color">{{ item.reviewDate }}</span>
            </div>
            <div class="d-flex align-items-center mr-2">
              <svg-icon icon-class="request-date" class-name="svg"></svg-icon>
              <span class="text-other-color mr-05 ml-05">过期日期:</span>
              <span class="text-other-color">{{
                parseTime(item.expiredDate, '{y}-{m}-{d}')
              }}</span>
            </div>
            <div
              v-if="item.reviewFromEmail"
              class="d-flex align-items-center mr-2"
            >
              <el-icon color="#07BCB4">
                <Avatar />
              </el-icon>
              <span class="text-other-color mr-05 ml-05">创建人:</span>
              <span class="text-other-color">{{ item.reviewFromEmail }}</span>
            </div>
            <div
              v-if="item.reviewToEmail"
              class="d-flex align-items-center mr-2"
            >
              <svg-icon icon-class="shareEmail" class-name="svg"></svg-icon>
              <span class="text-other-color mr-05 ml-05">被分享邮箱:</span>
              <span class="text-other-color">{{ item.reviewToEmail }}</span>
            </div>
          </div>
          <el-divider></el-divider>
        </div>
        <pagination
          v-show="total > 0"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          :total="total"
          class="mb-1"
          @pagination="getDataList"
        />
      </div>
      <div v-else v-loading="loading">
        <el-empty></el-empty>
      </div>

      <!--取消分享-->
      <el-dialog
        v-model="cancelReviewDialog"
        title="确定要取消审核吗？"
        width="400"
        center
        class="round-dialog"
      >
        <template #footer>
          <div>
            <el-button size="small" type="primary" @click="cancelReview">
              确认
            </el-button>
            <el-button size="small" @click="cancelReviewDialog = false"
              >取消</el-button
            >
          </div>
        </template>
      </el-dialog>

      <!--延长过期时间-->
      <el-dialog
        v-model="extensionDialog"
        title="延期"
        width="500"
        center
        class="round-dialog extension-dialog"
      >
        <div class="mb-05">
          <el-alert type="warning" :closable="false">
            <div class="font-14">1. 审核URL将在有效期后失效。</div>
            <div class="font-14">2. 建议在有效期后将安全性设置为公开。</div>
            <div class="font-14">3. 建议设置有效期超过3个月。</div>
          </el-alert>
          <div class="mt-1-5 text-center">
            <span class="demonstration mr-1">有效期</span>
            <el-date-picker
              v-model="validPeriod"
              :teleported="false"
              value-format="YYYY-MM-DD"
              type="date"
              placeholder="选择日期"
              size="default"
            />
          </div>
        </div>
        <template #footer>
          <div>
            <el-button size="small" type="primary" @click="extensionDate()">
              确认
            </el-button>
            <el-button size="small" @click="extensionDialog = false"
              >取消</el-button
            >
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup>
  import { getCurrentInstance, onMounted, reactive, ref, toRefs } from 'vue';
  import {
    cancel,
    extendDate,
    getMyReviewList,
    getReviewDataList,
  } from '@/api/share/review';
  import { parseTime } from '@/utils/ruoyi';
  import { createAccessToken } from '@/api/login';

  const { proxy } = getCurrentInstance();

  const cancelReviewDialog = ref(false);
  const extensionDialog = ref(false);

  const yearOpt = ref([]);

  /** 响应式数据 */
  const data = reactive({
    total: 0,
    queryParams: {
      pageNum: 1,
      pageSize: 10,
    },
  });

  /** 解构 */
  const { total, queryParams } = toRefs(data);

  const validPeriod = ref('');

  const sortBtn = reactive([
    {
      label: 'Review Date',
      field: 'review_date',
      highlighted: true,
      sortOrder: 'descending',
    },
    {
      label: 'Review ID',
      field: 'review_id',
      highlighted: false,
      sortOrder: 'descending',
    },
    {
      label: 'Status',
      field: 'status',
      highlighted: false,
      sortOrder: 'descending',
    },
  ]);

  // 左上角排序
  const queryPageAndSort = ref({
    sortKey: sortBtn[0].field,
    sortType: sortBtn[0].sortOrder,
  });

  const loading = ref(false);
  const reviewList = ref([]);

  // 查询外层的请求列表
  function getDataList() {
    const params = {
      sourceEmail: queryParams.value.sourceEmail,
      shareNo: queryParams.value.shareNo,
      projNo: queryParams.value.projNo,
      status: queryParams.value.status,
      year: queryParams.value.year,
      targetEmail: queryParams.value.targetEmail,
      orderByColumn: queryPageAndSort.value.sortKey,
      isAsc: queryPageAndSort.value.sortType,
      pageNum: queryParams.value.pageNum,
      pageSize: queryParams.value.pageSize,
    };
    loading.value = true;
    getMyReviewList(params)
      .then(response => {
        reviewList.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  function resetQuery() {
    proxy.resetForm('searchForm');
    getDataList();
  }

  /** 导出页面中data的下载链接 */
  function exportDataLink(id) {
    let type = 'review';
    proxy.download(
      `/download/node/exportDownloadLink/${type}/${id}`,
      null,
      `${type}_${id}_data_download_link.zip`,
    );
  }

  // 生成最近3年内的查询下拉词
  function getRecentYears() {
    let currentYear = new Date().getFullYear();

    for (let i = 0; i < 3; i++) {
      yearOpt.value.push({ value: currentYear - i, label: currentYear - i });
    }

    const lastYear = yearOpt.value[yearOpt.value.length - 1].value;
    yearOpt.value.push({
      value: '<' + lastYear,
      label: 'before ' + lastYear,
    });
  }

  const expandDataList = index => {
    let item = reviewList.value[index];

    // 已经展开，点击则收起
    if (item?.expand) {
      item.expand = false;
      return;
    }
    getReviewDataList({ reviewId: item.id }).then(response => {
      item.expandData = response.data;
      item.expand = true;
    });
  };

  const currentReviewId = ref('');

  function openCancel(id) {
    currentReviewId.value = id;
    cancelReviewDialog.value = true;
  }

  function openExtendDate(id) {
    currentReviewId.value = id;
    validPeriod.value = '';
    extensionDialog.value = true;
  }

  function cancelReview() {
    cancel({ reviewId: currentReviewId.value }).then(() => {
      proxy.$modal.msgSuccess('取消成功');
      cancelReviewDialog.value = false;
      getDataList();
    });
  }

  function extensionDate() {
    if (validPeriod.value === '') {
      proxy.$modal.alertError('请选择一个日期');
      return;
    }
    extendDate({
      reviewId: currentReviewId.value,
      date: validPeriod.value,
    }).then(() => {
      proxy.$modal.msgSuccess('延期成功');
      extensionDialog.value = false;
      getDataList();
    });
  }

  function copyText(text) {
    // 添加一个input元素放置需要的文本内容
    const input = document.createElement('input');
    input.value = text;
    document.body.appendChild(input);
    // 选中并复制文本到剪切板
    input.select();
    document.execCommand('copy');
    // 移除input元素
    document.body.removeChild(input);
    proxy.$modal.msgSuccess('复制成功');
  }

  function showDetail(type, creator, typeNo) {
    // 预先生成access_token
    createAccessToken({ memberId: creator }).then(response => {
      const token = response.data;
      let href = `${
        import.meta.env.VITE_APP_WEB_URL
      }/${type}/detail/${typeNo}?access-token=${token}`;
      // 打开一个新页面
      window.open(href);
    });
  }

  onMounted(() => {
    getRecentYears();
    getDataList();
  });
</script>

<style lang="scss" scoped>
  .list {
    .count-type {
      display: inline-block;
      width: 129px;
    }
  }

  .cancel-review {
    width: 83px;
    background-color: #f4f4f5 !important;
    color: gray !important;
    border-color: gray !important;
    transition: all 0.3s linear;
  }
  .bg-primary {
    background-color: #f5f8fe;
    .search {
      padding: 6px 10px;
    }
  }
  .tag-success {
    background-color: #cfefed !important;
    color: #07bcb4;
    font-weight: 600;
    border-radius: 8px;
  }
  :deep(.el-tag__content) {
    font-weight: 600;
  }
  :deep(.el-button--small.is-round) {
    padding: 5px 11px !important;
  }
  .svg {
    width: 16px;
    height: 16px;
  }
  .svg-idList {
    width: 13px;
    height: 13px;
    cursor: pointer;
    &:focus {
      outline: none;
    }
  }
  .bg-gray {
    padding: 6px 15px;
  }

  .btn-round-success:focus {
    background-color: #f0f9eb;
    color: #67c23a;
    border: 1px solid #67c23a;
  }
  :deep(.el-dialog__body) {
    padding: 0 !important;
  }
  :deep(.extension-dialog .el-dialog__body) {
    padding: 0 15px !important;
  }
  :deep(.el-select__wrapper) {
    border-radius: 12px;
  }
  .svg-copy {
    width: 18px;
    height: 18px;
    margin-left: 0.5rem;
    cursor: pointer;
    &:focus {
      outline: none;
    }
  }
  .copy-tooltip {
    width: 20px;
  }

  .mg-divider {
    margin: 10px;
  }
</style>
