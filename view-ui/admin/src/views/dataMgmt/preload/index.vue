<template>
  <div class="app-container">
    <div class="card list">
      <el-form
        ref="searchFormRef"
        :model="queryParams"
        :inline="true"
        class="pos-relative"
      >
        <el-form-item label="创建人" prop="creatorEmail">
          <el-input
            v-model="queryParams.creatorEmail"
            clearable
            style="width: 180px"
            @keyup.enter="getDataList"
          ></el-input>
        </el-form-item>
        <el-form-item label="路径" prop="path">
          <el-input
            v-model="queryParams.path"
            clearable
            style="width: 180px"
            @keyup.enter="getDataList"
          ></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select
            v-model="queryParams.status"
            clearable
            style="width: 160px"
          >
            <el-option value="上传中" label="上传中" />
            <el-option value="待校验" label="待校验" />
            <el-option value="排队中" label="排队中" />
            <el-option value="校验中" label="校验中" />
            <el-option value="校验成功" label="校验成功" />
            <el-option value="校验失败" label="校验失败" />
            <el-option value="错误" label="错误" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间" prop="dateRange">
          <el-date-picker
            v-model="dateRange"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="getDataList"
            >搜索
          </el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <el-text v-if="showCreatorFtpHome">
        创建人FTP主目录: {{ creatorFtpHomePath }}
      </el-text>
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%; margin-bottom: 20px"
        :header-cell-style="{
          backgroundColor: '#f2f2f2',
          color: '#333333',
          fontWeight: 700,
        }"
        border
        :row-key="row => row.id"
        :default-sort="defaultSort"
        @sort-change="handleSortChange"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          :reserve-selection="true"
          width="55"
          align="center"
        />
        <el-table-column
          prop="name"
          label="名称"
          show-overflow-tooltip
          sortable
          min-width="120"
        />
        <el-table-column
          prop="path"
          label="路径"
          show-overflow-tooltip
          min-width="180"
        />

        <el-table-column prop="readableFileSize" label="大小" width="120" />
        <el-table-column
          prop="creatorEmail"
          label="创建人"
          show-overflow-tooltip
          width="180"
        />
        <el-table-column
          prop="updateTime"
          label="更新日期"
          sortable
          width="180"
        >
          <template #default="scope">
            {{ parseTime(scope.row.updateTime) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="createTime"
          label="创建时间"
          sortable
          width="180"
        >
          <template #default="scope">
            {{ parseTime(scope.row.createTime) }}
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="150">
          <template #default="scope">
            <div class="d-flex align-items-center">
              <el-icon :color="iconColor(scope.row.status)">
                <CircleCheckFilled
                  v-if="scope.row.status === '校验成功'"
                ></CircleCheckFilled>
                <CircleCloseFilled
                  v-else-if="
                    scope.row.status === '校验失败' ||
                    scope.row.status === '错误'
                  "
                />
                <RemoveFilled v-else-if="scope.row.status === '待校验'" />
                <WarningFilled v-else-if="scope.row.status === '上传完成'" />
                <svg-icon
                  v-else
                  icon-class="checking"
                  class-name="svg svg-link"
                ></svg-icon>
              </el-icon>
              <span
                :style="{ color: iconColor(scope.row.status) }"
                class="ml-03"
                >{{ scope.row.status }}</span
              >
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="failCause"
          label="失败原因"
          show-overflow-tooltip
          min-width="150"
        >
          <template #default="scope">
            <span v-if="scope.row.failCause">{{ scope.row.failCause }}</span>
          </template>
        </el-table-column>
      </el-table>
      <el-button
        v-show="tableData.length > 0"
        :disabled="selectedRows.length === 0"
        style="float: left"
        type="danger"
        icon="Delete"
        @click="handleDelete"
        >删除
      </el-button>
      <el-button
        v-show="tableData.length > 0"
        :disabled="selectedRows.length === 0"
        type="primary"
        icon="Check"
        @click="showDialog"
        >数据完整性检查
      </el-button>

      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @pagination="getDataList"
      />
    </div>
    <el-dialog
      v-model="showCheckDialog"
      title="数据完整性检查"
      width="40%"
      append-to-body
      class="integrity-check"
    >
      <div class="integrity-body mb-1">
        <div class="d-flex align-items-center">
          <el-icon color="#8A6D3B" size="large">
            <WarnTriangleFilled />
          </el-icon>
          <span class="note font-600 font-18">注意:</span>
        </div>
        <div>
          1:
          您需要为您的文件提供MD5文件。确保它只包含单个文件的32位MD5值，并且其名称与该文件的名称匹配。例如：
          <strong> sample1.fastq.gz</strong>
          和 <strong>sample1.fastq.gz.md5</strong>
        </div>
        <div class="mt-05">
          2: 完整性检查完成后，数据会自动移动到
          <strong>"我的数据"</strong>
          的
          <strong>"未归档数据"</strong>
          中。您可以在"未归档数据"列表中通过数据完整性检查查看数据。
        </div>
        <div class="mt-05">
          3:
          请确保文件路径和名称不包含特殊字符，如空格、&符号、%符号、*号或希腊字母。建议使用大写字母(A-Z)、小写字母(a-z)、数字(0-9)、下划线(_)和连字符(-)的组合来构建您的文件名。
        </div>
        <div class="mt-05">4: 完整性检查需要一段时间，请耐心等待。</div>
      </div>
      以下文件将进行数据完整性验证。您要继续吗？
      <div v-for="(it, index) in uncheckSelected" :key="index">
        <el-tag class="mt-05">{{ it.path }}</el-tag>
      </div>
      <el-divider class="mb-0"></el-divider>

      <template #footer>
        <div class="text-center">
          <el-button
            type="primary"
            class="btn-round-primary"
            round
            @click="ftpFileBatchVerify"
            >确认
          </el-button>
          <el-button
            class="btn btn-round"
            round
            @click="showCheckDialog = false"
            >取消
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { getCurrentInstance, onMounted, reactive, ref, toRefs } from 'vue';
  import {
    checkFtpFile,
    deleteFtpFile,
    getMemberFtpHomePath,
    listFtpPreLoad,
    verifyFtpFile,
  } from '@/api/metadata/data';
  import { FtpFileLogStatus } from '../../../utils/enums';
  import { parseTime } from '../../../utils/ruoyi';
  import { isStrBlank } from '@/utils';

  const { proxy } = getCurrentInstance();

  const data = reactive({
    tableData: [],
    total: 0,
    queryParams: {
      creatorEmail: '',
      status: '',
      pageNum: 1,
      pageSize: 20,
      orderByColumn: 'createTime',
      isAsc: 'descending',
    },
    dateRange: [],
    defaultSort: { prop: 'createTime', order: 'descending' },
    loading: true,
  });

  let showCreatorFtpHome = ref(false);
  let creatorFtpHomePath = ref('');

  const { queryParams, tableData, total, dateRange, defaultSort, loading } =
    toRefs(data);

  function getDataList() {
    loading.value = true;
    listFtpPreLoad(proxy.addDateRange(queryParams.value, dateRange.value))
      .then(response => {
        tableData.value = response.rows;
        total.value = response.total;
        getCreatorFtpHome();
      })
      .finally(() => {
        loading.value = false;
      });
  }

  function getCreatorFtpHome() {
    if (isStrBlank(queryParams.value.creatorEmail)) {
      showCreatorFtpHome.value = false;
      return;
    }
    getMemberFtpHomePath({
      creatorEmail: queryParams.value.creatorEmail,
    }).then(response => {
      creatorFtpHomePath.value = response.msg;
      showCreatorFtpHome.value = true;
    });
  }

  /** 排序触发事件 */
  function handleSortChange(column) {
    if (column.order) {
      queryParams.value.orderByColumn = column.prop;
      queryParams.value.isAsc = column.order;
      getDataList();
    }
  }

  function resetQuery() {
    dateRange.value = [];
    proxy.resetForm('searchFormRef');

    getDataList();
  }

  const showCheckDialog = ref(false);

  let selectedRows = ref([]);

  /** 多选框选中数据 */
  function handleSelectionChange(selection) {
    selectedRows.value = selection;
  }

  let uncheckSelected = reactive([]);

  /* 批量校验sftp里面的文件 */
  function showDialog() {
    // 过滤出不是dir且有md5的文件的path
    uncheckSelected = selectedRows.value.filter(
      item => item.md5FileStatus === 'Provided',
    );
    if (uncheckSelected.length === 0) {
      proxy.$modal.alertError('请选择需要检查且提供了md5文件的文件');
      return;
    }
    let ids = uncheckSelected.map(it => it.id);
    checkFtpFile(ids).then(response => {
      if (response.data) {
        proxy.$modal.alertError(`${response.data}`, true);
      } else {
        showCheckDialog.value = true;
      }
    });
  }

  function ftpFileBatchVerify() {
    proxy.$modal.loading('请求处理中！');
    verifyFtpFile(uncheckSelected.map(it => it.id))
      .then(response => {
        if (response.data) {
          proxy.$modal.alertError(`${response.data}`, true);
        } else {
          getDataList();
          showCheckDialog.value = false;
        }
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  function handleDelete() {
    let selectedIds = selectedRows.value.map(it => it.id);
    proxy.$modal.confirm(`确定要删除选中的数据吗？`).then(() => {
      proxy.$modal.loading('删除中...');
      deleteFtpFile(selectedIds)
        .then(response => {
          getDataList();
        })
        .finally(() => {
          proxy.$modal.closeLoading();
        });
    });
  }

  const iconColor = status => {
    if (status === '排队中') {
      return '#07BCB4';
    } else if (status === '校验失败') {
      return '#FF8989';
    } else if (status === '校验成功') {
      return '#999999';
    } else return '#3A78E8';
  };

  onMounted(() => {
    getDataList();
  });
</script>

<style lang="scss" scoped>
  :deep(.integrity-check .el-dialog__body) {
    padding: 0 15px !important;
  }

  :deep(.integrity-check .el-dialog__title) {
    font-weight: 600 !important;
  }

  .integrity-body {
    background-color: #fcf8e3;
    border: 1px solid #efe8c5;
    padding: 10px 15px;
    border-radius: 8px;
    color: #8f7443;
    text-align: justify;
    font-size: 14px;

    .note {
      color: #8a6d3b;
    }
  }
</style>
<style lang="scss">
  .el-popper {
    max-width: 350px !important;
  }
</style>
