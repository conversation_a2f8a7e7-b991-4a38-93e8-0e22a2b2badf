<template>
  <div class="app-container">
    <div class="card list">
      <el-form ref="searchFormRef" :model="queryParams" :inline="true">
        <el-form-item label="数据ID" prop="dataNo">
          <el-input
            v-model="queryParams.dataNoStr"
            style="width: 220px"
            clearable
            type="textarea"
            :rows="1"
          ></el-input>
        </el-form-item>
        <el-form-item label="文件名" prop="name">
          <el-input
            v-model="queryParams.name"
            style="width: 220px"
            clearable
            @keyup.enter="getDataList"
          ></el-input>
        </el-form-item>
        <el-form-item label="实验ID" prop="expNo">
          <el-input
            v-model="queryParams.expNoStr"
            style="width: 220px"
            clearable
            type="textarea"
            :rows="1"
            @keyup.enter="getDataList"
          ></el-input>
        </el-form-item>
        <el-form-item label="样本ID" prop="sapNo">
          <el-input
            v-model="queryParams.sapNoStr"
            style="width: 220px"
            clearable
            type="textarea"
            :rows="1"
            @keyup.enter="getDataList"
          ></el-input>
        </el-form-item>
        <el-form-item label="创建人" prop="creatorEmail">
          <el-input
            v-model="queryParams.creatorEmail"
            style="width: 220px"
            clearable
            @keyup.enter="getDataList"
          ></el-input>
        </el-form-item>
        <el-form-item label="创建时间" prop="dateRange">
          <el-date-picker
            v-model="dateRange"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 220px"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="getDataList"
            >搜索
          </el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          <el-button
            v-hasPermi="['metadata:rawdata:export']"
            type="info"
            icon="download"
            @click="exportData"
            >导出
          </el-button>
        </el-form-item>
      </el-form>

      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%; margin-bottom: 20px"
        :row-key="row => row.id"
        :header-cell-style="{
          backgroundColor: '#f2f2f2',
          color: '#333333',
          fontWeight: 700,
        }"
        border
        :default-sort="defaultSort"
        @sort-change="handleSortChange"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          :reserve-selection="true"
          width="50"
          align="center"
        />
        <el-table-column prop="datNo" label="数据ID" width="120" sortable />
        <el-table-column
          prop="name"
          label="文件名"
          min-width="140"
          show-overflow-tooltip
          sortable
        />
        <el-table-column
          prop="dataType"
          label="数据类型"
          width="120"
          sortable
        />
        <el-table-column prop="filesize" label="文件大小" width="100" sortable>
          <template #default="scope">
            {{ scope.row.readableFileSize }}
          </template>
        </el-table-column>
        <el-table-column
          sortable
          prop="security"
          label="数据安全级别"
          width="150"
        >
        </el-table-column>

        <el-table-column prop="projNo" label="项目" width="120">
          <template #default="scope">
            <a
              class="text-primary"
              href="javascript:void(0)"
              @click="
                showDetail(scope.row.projNo, scope.row.creator, 'project')
              "
            >
              {{ scope.row.projNo }}
            </a>
          </template>
        </el-table-column>
        <el-table-column prop="expNo" label="实验" width="120">
          <template #default="scope">
            <a
              class="text-primary"
              href="javascript:void(0)"
              @click="
                showDetail(scope.row.expNo, scope.row.creator, 'experiment')
              "
            >
              {{ scope.row.expNo }}
            </a>
          </template>
        </el-table-column>
        <el-table-column prop="sapNo" label="样本" width="120">
          <template #default="scope">
            <a
              class="text-primary"
              href="javascript:void(0)"
              @click="showDetail(scope.row.sapNo, scope.row.creator, 'sample')"
            >
              {{ scope.row.sapNo }}
            </a>
          </template>
        </el-table-column>
        <el-table-column prop="creatorEmail" label="创建人" width="180" />
        <el-table-column
          prop="createDate"
          label="创建时间"
          width="160"
          sortable
        >
          <template #default="scope">
            {{ parseTime(scope.row.createDate) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="90">
          <template #default="scope">
            <el-tooltip content="删除">
              <svg-icon
                icon-class="delete"
                class-name="meta-svg"
                @click="handleDelete(scope.row)"
              ></svg-icon>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <el-button
        v-show="total > 0"
        style="float: left"
        type="danger"
        icon="Delete"
        :disabled="dataNos.length === 0"
        @click="handleDelete"
        >删除
      </el-button>
      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        class="mb-1"
        @pagination="getDataList"
      />
    </div>
  </div>
</template>

<script setup>
  import {
    getCurrentInstance,
    onMounted,
    reactive,
    ref,
    toRefs,
    watch,
  } from 'vue';
  import { deleteByDataNos, listRawData } from '@/api/metadata/data';
  import { createAccessToken } from '@/api/login';

  onMounted(() => {
    getDataList();
  });

  const { proxy } = getCurrentInstance();

  const data = reactive({
    tableData: [],
    total: 0,
    queryParams: {
      dataNoStr: '',
      dataNos: [],
      name: '',
      expNoStr: '',
      expNos: [],
      sapNoStr: '',
      sapNos: [],
      creatorEmail: '',
      pageNum: 1,
      pageSize: 20,
      orderByColumn: 'createDate',
      isAsc: 'descending',
    },
    dateRange: [],
    loading: true,
    defaultSort: { prop: 'createDate', order: 'descending' },
  });

  /** 解构 */
  const { tableData, total, queryParams, dateRange, loading, defaultSort } =
    toRefs(data);

  // 监听 noStr 的变化，并同步更新 no
  watch(
    () => data.queryParams.dataNoStr,
    newVal => {
      data.queryParams.dataNos = newVal ? newVal.split('\n') : [];
    },
  );
  watch(
    () => data.queryParams.expNoStr,
    newVal => {
      data.queryParams.expNos = newVal ? newVal.split('\n') : [];
    },
  );
  watch(
    () => data.queryParams.sapNoStr,
    newVal => {
      data.queryParams.sapNos = newVal ? newVal.split('\n') : [];
    },
  );

  function resetQuery() {
    dateRange.value = [];
    queryParams.value.dataNoStr = '';
    queryParams.value.expNoStr = '';
    queryParams.value.sapNoStr = '';
    proxy.resetForm('searchFormRef');

    getDataList();
  }

  /** 排序触发事件 */
  function handleSortChange(column) {
    if (column.order) {
      queryParams.value.orderByColumn = column.prop;
      queryParams.value.isAsc = column.order;
      getDataList();
    }
  }

  function showDetail(no, creator, type) {
    proxy.$modal.loading('正在打开，请稍候');
    // 预先生成access_token
    createAccessToken({ memberId: creator })
      .then(response => {
        const token = response.data;
        let href = `${
          import.meta.env.VITE_APP_WEB_URL
        }/${type}/detail/${no}?access-token=${token}`;
        proxy.$modal.closeLoading();
        // 打开一个新页面
        window.open(href);
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  function getDataList() {
    loading.value = true;
    listRawData(proxy.addDateRange(queryParams.value, dateRange.value))
      .then(response => {
        // 将结果赋值给tableData
        tableData.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  /** data删除 */
  let dataNos = ref([]);

  function handleSelectionChange(selection) {
    dataNos.value = selection.map(item => item.datNo);
  }

  function handleDelete(row) {
    let nos = row.datNo || dataNos.value;
    proxy.$modal
      .confirm('确定要删除数据ID为 "' + nos + '" 的数据项吗？')
      .then(function () {
        return deleteByDataNos(nos);
      })
      .then(() => {
        getDataList();
        proxy.$modal.msgSuccess('删除成功');
      })
      .catch(() => {});
  }

  function exportData() {
    let query = JSON.stringify(
      proxy.addDateRange(queryParams.value, dateRange.value),
    );
    proxy.download(
      'system/metadata/data/exportRawData',
      {
        query,
      },
      `RawData_${new Date().getTime()}.json`,
    );
  }
</script>

<style lang="scss" scoped>
  .meta-svg {
    width: 20px;
    height: 20px;
    margin-right: 0.5rem;
    cursor: pointer;
  }
</style>
<style lang="scss">
  .el-popper {
    max-width: 350px !important;
  }
</style>
