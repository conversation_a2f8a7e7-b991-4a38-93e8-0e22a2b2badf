<template>
  <div :id="id" :style="{ width: width, height: height }"></div>
</template>

<script setup name="pieChart">
  import * as echarts from 'echarts';
  import { onMounted, nextTick, defineProps, computed, ref } from 'vue';
  const props = defineProps({
    id: {
      type: String,
      required: true,
    },
    height: {
      type: String,
      default: '200px',
    },
    width: {
      type: String,
      default: '100%',
    },
    color: {
      type: Array,
    },
    data: {
      type: Array,
    },
    title: {
      type: String,
    },
  });
  const echartInit = () => {
    //堆积柱形图
    const pieChart = echarts.init(document.getElementById(props.id));
    const option = {
      color: props.color,
      title: {
        text: props.title,
        textStyle: {
          rich: {
            a: {
              fontSize: 22,
              color: '#FFF',
            },
          },
        },
        x: 'center',
        y: 'center',
      },
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)',
      },
      series: [
        {
          type: 'pie',
          radius: ['75%', '90%'],
          center: ['50%', '50%'],
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
          data: props.data,
        },
      ],
    };
    pieChart.setOption(option);
    window.onresize = function () {
      pieChart.resize();
    };
  };
  onMounted(() => {
    nextTick(() => {
      echartInit();
    });
  });
</script>

<style scoped lang="scss"></style>
