<template>
  <div class="bg-gray radius-8 d-flex align-items-center log-card p-10">
    <svg-icon :icon-class="icon"> </svg-icon>
    <div class="ml-05">
      <div class="text-main-color font-600 font-18 name">{{ name }}</div>
      <div>
        <el-tooltip effect="light" content="success" placement="top">
          <span class="text-primary cursor-pointer">{{ count }}</span>
        </el-tooltip>

        <span class="text-secondary-color" style="margin: 0 0.3rem">/</span>

        <el-tooltip effect="light" content="total" placement="top">
          <span class="text-secondary-color text-primary cursor-pointer"
            >500</span
          >
        </el-tooltip>
      </div>

      <div
        v-if="name === 'Run' || name === 'Analysis' || name === 'Publish'"
        class="font-16"
      >
        <span class="text-warning">{{ size }}</span>
        <span class="text-secondary-color" style="margin: 0 0.3rem">/</span>
        <span class="text-secondary-color text-warning">500</span>
        <span class="text-warning font-12 ml-05">TB</span>
      </div>
    </div>
  </div>
</template>

<script setup name="pieChart">
  import { defineProps } from 'vue';
  defineProps({
    icon: {
      type: String,
    },
    name: {
      type: String,
    },
    size: {
      type: String,
    },
    count: {
      type: String,
    },
  });
</script>

<style scoped lang="scss">
  .svg-icon {
    width: 55px;
    height: 55px;
  }
  .log-card {
    height: 98px;
  }
</style>
