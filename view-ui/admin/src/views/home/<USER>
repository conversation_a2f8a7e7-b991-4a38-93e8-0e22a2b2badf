<template>
  <div class="app-container">
    <el-row :gutter="20" class="mb-05">
      <el-col :span="14" :xs="24">
        <span class="title">Data Submission Volumn</span>
      </el-col>
      <el-col :span="10" :xs="24">
        <div class="d-flex align-items-center">
          <el-radio-group
            v-model="subDateSelect"
            text-color="#1981F4"
            fill="#ffffff"
          >
            <el-radio-button label="Today" value="Today" />
            <el-radio-button
              label="This Month"
              value="This Month"
              class="ml-1"
            />
          </el-radio-group>
          <el-date-picker
            v-model="subDateVal"
            type="daterange"
            range-separator="To"
            start-placeholder="Start date"
            end-placeholder="End date"
            style="width: 300px"
            class="ml-1"
          />
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="3" :xs="12">
        <SubmitVol title="Submission" count="234"></SubmitVol>
      </el-col>
      <el-col :span="3" :xs="12">
        <SubmitVol title="Project" count="304"></SubmitVol>
      </el-col>
      <el-col :span="3" :xs="12">
        <SubmitVol title="Experiment" count="135"></SubmitVol>
      </el-col>
      <el-col :span="3" :xs="12">
        <SubmitVol title="Sample" count="456"></SubmitVol>
      </el-col>
      <el-col :span="3" :xs="12">
        <SubmitVol title="Run" count="564"></SubmitVol>
      </el-col>
      <el-col :span="3" :xs="12">
        <SubmitVol title="Data" count="246"></SubmitVol>
      </el-col>
      <el-col :span="3" :xs="12">
        <SubmitVol title="Analysis" count="543"></SubmitVol>
      </el-col>
      <el-col :span="3" :xs="12">
        <SubmitVol title="Publish" count="754"></SubmitVol>
      </el-col>
    </el-row>
    <el-row :gutter="20" class="mb-05 mt-1-5">
      <el-col :span="14" :xs="12">
        <span class="title">Audit Overview</span>
      </el-col>
      <el-col :span="10" :xs="12">
        <div class="d-flex align-items-center">
          <el-radio-group
            v-model="viewDateSelect"
            text-color="#1981F4"
            fill="#ffffff"
          >
            <el-radio-button label="Today" value="Today" />
            <el-radio-button
              label="This Month"
              value="This Month"
              class="ml-1"
            />
          </el-radio-group>
          <el-date-picker
            v-model="viewDateVal"
            type="daterange"
            range-separator="To"
            start-placeholder="Start date"
            end-placeholder="End date"
            style="width: 300px"
            class="ml-1"
          />
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="8" :xs="24">
        <AuditOverview
          title="Submission Review Times"
          count="343"
          total="400"
          unit-one="reviews"
          unit-two="total"
        ></AuditOverview>
      </el-col>
      <el-col :span="8" :xs="24">
        <AuditOverview
          title="Implementation Audit Frequency"
          count="675"
          total="700"
          unit-one="items"
          unit-two="total"
        ></AuditOverview>
      </el-col>
      <el-col :span="8" :xs="24">
        <AuditOverview
          title="Number of Audit Pass"
          count="796"
          total="820"
          unit-one="success"
          unit-two="submission"
        ></AuditOverview>
      </el-col>
    </el-row>
    <!--Audit Overview-->
    <el-row :gutter="20" class="mb-05 mt-1-5">
      <el-col :span="17" :xs="24">
        <div class="card h-100">
          <div class="d-flex justify-space-between align-items-center">
            <h3>Audit Detail</h3>
            <div>
              <span class="font-600 text-secondary-color mr-05">Auditor:</span>
              <el-select
                v-model="auditor"
                filterable
                multiple
                placeholder="Select"
                style="width: 240px"
                class="mr-1"
              >
                <el-option label="Audit 1" value="Audit 1" />
                <el-option label="Audit 2" value="Audit 2" />
                <el-option label="Audit 3" value="Audit 3" />
              </el-select>
              <span class="font-600 text-secondary-color mr-05">Year:</span>
              <el-select
                v-model="year"
                filterable
                multiple
                placeholder="Select"
                style="width: 240px"
              >
                <el-option label="2024" value="2024" />
                <el-option label="2023" value="2023" />
                <el-option label="2022" value="2022" />
                <el-option label="before 2022" value="before 2022" />
              </el-select>
              <el-button type="success" class="ml-1" plain>Export</el-button>
            </div>
          </div>
          <el-divider class="mb-1 mt-1"></el-divider>
          <div class="bg-gray radius-8">
            <div id="auditDetail" style="width: 100%; height: 420px"></div>
          </div>
        </div>
      </el-col>
      <el-col :span="7" :xs="24">
        <div class="card">
          <h3 class="audit-data">Audit Data</h3>
          <el-divider class="mt-1"></el-divider>
          <div id="auditData" style="width: 100%; height: 180px"></div>
          <div class="d-flex justify-space-around mt-1">
            <div>
              <div class="before-circle success text-success font-17 font-600">
                SUCCESS
              </div>
              <div class="ml-1">
                <span class="font-600 font-18">85%</span>
                <span class="text-other-color ml-1">1234</span>
              </div>
            </div>
            <div>
              <div class="before-circle fail text-warning font-17 font-600">
                FAIL
              </div>
              <div class="ml-1">
                <span class="font-600 font-18">15%</span>
                <span class="text-other-color ml-1">234</span>
              </div>
            </div>
          </div>
          <el-divider class="mb-1 mt-1"></el-divider>
          <div class="d-flex">
            <PieChart
              id="failReason"
              width="50%"
              height="175px"
              :color="failColor"
              :data="failData"
              :title="'Failure\nReason'"
            ></PieChart>
            <div class="w-50">
              <div>
                <div class="d-flex justify-space-between">
                  <span class="before-circle font-600 top1">Top1</span>
                  <span>45%</span>
                </div>
                <div class="text-other-color ml-1">Reason for failure 1</div>
              </div>
              <el-divider class="mt-03 mb-0"></el-divider>
              <div>
                <div class="d-flex justify-space-between">
                  <span class="before-circle font-600 top2">Top2</span>
                  <span>40%</span>
                </div>
                <div class="text-other-color ml-1">Reason for failure 2</div>
              </div>
              <el-divider class="mt-03 mb-0"></el-divider>
              <div>
                <div class="d-flex justify-space-between">
                  <span class="before-circle font-600 top3">Top3</span>
                  <span>25%</span>
                </div>
                <div class="text-other-color ml-1">Reason for failure 3</div>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <!-- log  -->
    <el-row :gutter="20" class="mt-1-5">
      <el-col :span="14" :xs="12">
        <span class="title">Quality Control Log</span>
      </el-col>
      <el-col :span="10" :xs="12">
        <div class="d-flex align-items-center">
          <el-radio-group
            v-model="logDateSelect"
            text-color="#1981F4"
            fill="#ffffff"
          >
            <el-radio-button label="Today" value="Today" />
            <el-radio-button
              label="This Month"
              value="This Month"
              class="ml-1"
            />
          </el-radio-group>
          <el-date-picker
            v-model="logDateVal"
            type="daterange"
            range-separator="To"
            start-placeholder="Start date"
            end-placeholder="End date"
            style="width: 300px"
            class="ml-1"
          />
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="20" class="mt-1">
      <el-col :span="10" :xs="24">
        <div class="card h-100 pt-20">
          <el-row :gutter="20" class="row-gap-20">
            <el-col :span="8" :xs="24">
              <LogData
                icon="proj"
                name="Project"
                size="1012"
                count="456"
              ></LogData>
            </el-col>
            <el-col :span="8" :xs="24">
              <LogData
                icon="exp"
                name="Experiment"
                size="1012"
                count="456"
              ></LogData>
            </el-col>
            <el-col :span="8" :xs="24">
              <LogData
                icon="samp"
                name="Sample"
                size="1012"
                count="456"
              ></LogData>
            </el-col>
            <el-col :span="8" :xs="24">
              <LogData
                icon="anal"
                name="Analysis"
                size="1012"
                count="456"
              ></LogData>
            </el-col>
            <el-col :span="8" :xs="24">
              <LogData icon="run" name="Run" size="1012" count="456"></LogData>
            </el-col>
            <el-col :span="8" :xs="24">
              <LogData
                icon="publish"
                name="Publish"
                size="1012"
                count="456"
              ></LogData>
            </el-col>
          </el-row>
        </div>
      </el-col>
      <el-col :span="7" :xs="24">
        <div class="card">
          <h3>Operator Statistics</h3>
          <el-divider class="mt-1"> </el-divider>
          <div class="d-flex">
            <PieChart
              id="operatorPie"
              width="50%"
              height="170px"
              :color="operatorColor"
              :data="operatorData"
              title="Operator"
            ></PieChart>
            <div class="w-50">
              <div>
                <div class="d-flex justify-space-between">
                  <span class="before-circle font-600 one">50%</span>
                  <span>45</span>
                </div>
                <div class="text-other-color ml-1">Li Ming</div>
              </div>
              <el-divider class="mt-03 mb-0"></el-divider>
              <div>
                <div class="d-flex justify-space-between">
                  <span class="before-circle font-600 two">40%</span>
                  <span>35</span>
                </div>
                <div class="text-other-color ml-1">Wang Xiao</div>
              </div>
              <el-divider class="mt-03 mb-0"></el-divider>
              <div>
                <div class="d-flex justify-space-between">
                  <span class="before-circle font-600 three">10%</span>
                  <span>5</span>
                </div>
                <div class="text-other-color ml-1">Zhang Qing He</div>
              </div>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="7" :xs="24">
        <div class="card">
          <h3>Operation Content Statistics</h3>
          <el-divider class="mt-1"> </el-divider>
          <div class="d-flex">
            <PieChart
              id="pie4"
              width="50%"
              height="170px"
              :color="operatorColor"
              :data="operationData"
              :title="'Operation\nContent'"
            ></PieChart>
            <div class="w-50">
              <div>
                <div class="d-flex justify-space-between">
                  <span class="before-circle font-600 one">53%</span>
                  <span>50</span>
                </div>
                <div class="text-other-color ml-1">
                  Content1....................
                </div>
              </div>
              <el-divider class="mt-03 mb-0"></el-divider>
              <div>
                <div class="d-flex justify-space-between">
                  <span class="before-circle font-600 two">40%</span>
                  <span>35</span>
                </div>
                <div class="text-other-color ml-1">
                  Content2....................
                </div>
              </div>
              <el-divider class="mt-03 mb-0"></el-divider>
              <div>
                <div class="d-flex justify-space-between">
                  <span class="before-circle font-600 three">7%</span>
                  <span>5</span>
                </div>
                <div class="text-other-color ml-1">
                  Content3....................
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="Index">
  import * as echarts from 'echarts';
  import { onMounted, nextTick, ref } from 'vue';
  import PieChart from '@/views/home/<USER>/pieChart.vue';
  import SubmitVol from '@/views/home/<USER>/submitVol.vue';
  import AuditOverview from '@/views/home/<USER>/auditOverview.vue';
  import LogData from '@/views/home/<USER>/logData.vue';

  const failColor = ref(['#E98BAD', '#EDA2BD', '#F2B9CE', '#F6D0DE']);
  const failData = ref([
    { value: 50, name: 'Reason for failue 1' },
    { value: 40, name: 'Reason for failue 2' },
    { value: 30, name: 'Reason for failue 3' },
    { value: 15, name: 'Other' },
  ]);

  const operatorColor = ref(['#768FC7', '#88AFD9', '#8DCCDE']);
  const operatorData = ref([
    { value: 50, name: 'Li Ming' },
    { value: 34, name: 'Wang Xiao' },
    { value: 25, name: 'Zhang Qing He' },
  ]);
  const operationData = ref([
    { value: 50, name: 'Content1' },
    { value: 34, name: 'Content2' },
    { value: 25, name: 'Content3' },
  ]);

  const subDateSelect = ref('');
  const subDateVal = ref('');

  const viewDateSelect = ref('');
  const viewDateVal = ref('');

  const logDateSelect = ref('');
  const logDateVal = ref('');

  const auditor = ref([]);
  const year = ref('');

  const echartInit = () => {
    //堆积柱形图
    const auditDetail = echarts.init(document.getElementById('auditDetail'));
    const option = {
      color: ['#FEA52B', '#2ADAC9', '#3A78E8'],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter: function (params) {
          return (
            '<span class="mr-05">' +
            params[0].axisValue +
            ':' +
            '</span>' +
            '<span class="mb-2">' +
            '(success / reviews: items)' +
            '</span>' +
            '<hr class="mt-03 mb-03">' +
            '<span class="mr-1">' +
            params[0].seriesName +
            ':' +
            '</span>' +
            '<span>' +
            params[0].data +
            ' / 400' +
            ' :' +
            '<span class="ml-03">' +
            '450' +
            '</span>' +
            '</span>' +
            '<br>' +
            '<span class="mr-1">' +
            params[1].seriesName +
            ':' +
            '</span>' +
            '<span>' +
            params[1].data +
            ' / 400' +
            ' :' +
            '<span class="ml-03">' +
            '450' +
            '</span>' +
            '<br>' +
            '<span class="mr-1">' +
            params[2].seriesName +
            ':' +
            '</span>' +
            '<span>' +
            params[2].data +
            ' / 400' +
            ' :' +
            '<span class="ml-03">' +
            '450' +
            '</span>'
          );
        },
        confine: true,
      },
      grid: {
        top: '8%',
        left: '3%',
        right: '4%',
        bottom: '5%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          data: [
            '2023.01',
            '2023.02',
            '2023.03',
            '2023.04',
            '2023.05',
            '2023.06',
            '2023.07',
            '2023.08',
            '2023.09',
            '2023.10',
          ],
        },
      ],
      yAxis: [
        {
          type: 'value',
          axisLine: {
            show: true,
          },
          splitLine: {
            show: false,
          },
        },
      ],
      series: [
        {
          name: 'Audit 1',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          data: [20, 32, 21, 34, 65, 30, 21, 35, 45, 56],
        },
        {
          name: 'Audit 2',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          data: [22, 82, 31, 34, 20, 33, 31, 34, 56, 54],
        },
        {
          name: 'Audit 3',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          data: [15, 32, 21, 54, 90, 33, 41, 34, 56, 56],
          barMaxWidth: 22,
        },
      ],
    };
    auditDetail.setOption(option);

    //双圆环
    const auditData = echarts.init(document.getElementById('auditData'));
    const color1 = {
      Submission: '#EBACAA',
      Project: '#E98BAD',
      Experiment: '#A296B1',
      Sample: '#768FC7',
      Analysis: '#9BCF99',
      Publish: '#F4DE93',
    };
    const option1 = {
      color: ['#FE7F2B', '#07BCB4'],
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)',
      },
      legend: {
        icon: 'circle',
        top: 'middle',
        orient: 'vertical',
        itemHeight: 6,
        itemWidth: 6,
        align: 'left',
        right: '10%',
        data: [
          'Submission',
          'Project',
          'Experiment',
          'Sample',
          'Analysis',
          'Publish',
        ],
        textStyle: {
          fontSize: 14,
        },
      },
      series: [
        {
          type: 'pie',
          selectedMode: 'single',
          radius: [0, '60%'],
          center: ['35%', '50%'],
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
          data: [
            { value: 1, name: 'fail' },
            { value: 3, name: 'success' },
          ],
        },
        {
          type: 'pie',
          center: ['35%', '50%'],
          radius: ['80%', '100%'],
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
          data: [
            { value: 5, name: 'Publish' },
            { value: 5, name: 'Analysis' },
            { value: 5, name: 'Sample' },
            { value: 5, name: 'Experiment' },
            { value: 5, name: 'Project' },
            { value: 5, name: 'Submission' },
          ],
          itemStyle: {
            color: function (params) {
              return color1[params.name];
            },
          },
        },
      ],
    };
    auditData.setOption(option1);
    window.onresize = function () {
      auditData.resize();
      auditDetail.resize();
    };
  };
  onMounted(() => {
    nextTick(() => {
      echartInit();
    });
  });
</script>

<style scoped lang="scss">
  .title {
    color: #333333;
    font-weight: 600;
    padding-left: 15px;
    font-size: 16px;
    border-left: 3px solid #3a78e8;
  }
  .audit-data {
    height: 32px;
    line-height: 32px;
  }
  .success.before-circle:before {
    width: 7px;
    height: 7px;
    background-color: #07bcb4;
  }
  .fail.before-circle:before {
    width: 7px;
    height: 7px;
    background-color: #fe7f2b;
  }
  .one.before-circle:before {
    background-color: #768fc7;
  }
  .two.before-circle:before {
    background-color: #88afd9;
  }
  .three.before-circle:before {
    background-color: #91d2e4;
  }
  .top1.before-circle:before {
    background-color: #e98bad;
  }
  .top2.before-circle:before {
    background-color: #eda2bd;
  }
  .top3.before-circle:before {
    background-color: #f2b9ce;
  }
  :deep(.el-range-editor.el-input__wrapper) {
    border-radius: 8px !important;
  }
  :deep(.el-radio-button__inner) {
    border: 1px solid #dcdfe6 !important;
    border-radius: 8px !important;
  }
  .el-radio-button.is-active {
    :deep(.el-radio-button__inner) {
      border: 1px solid #3a78e8 !important;
    }
  }
</style>
