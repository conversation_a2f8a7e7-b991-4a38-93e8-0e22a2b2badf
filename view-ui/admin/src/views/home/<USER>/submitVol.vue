<template>
  <div class="p-10-15 radius-14 h-100 bg-white">
    <div class="before-circle" :class="circleColor(title)">
      {{ title }}
      <span v-if="title === 'Data'" class="text-warning ml-05"
        >1012 <span class="font-12">TB</span></span
      >
    </div>
    <span class="text-main-color font-600 font-28 ml-1">{{ count }}</span>
    <span class="text-other-color ml-05">items</span>
  </div>
</template>

<script setup name="pieChart">
  import { defineProps, ref } from 'vue';
  defineProps({
    title: {
      type: String,
    },
    count: {
      type: String,
    },
  });
  const circleColor = title => {
    return `${title}-circle`;
  };
</script>

<style scoped lang="scss">
  .before-circle:before {
    width: 7px;
    height: 7px;
  }
  .Submission-circle:before {
    background-color: #ebacaa;
  }
  .Project-circle:before {
    background-color: #e98bad;
  }
  .Experiment-circle:before {
    background-color: #a296b1;
  }
  .Sample-circle:before {
    background-color: #768fc7;
  }
  .Run-circle:before {
    background-color: #88afd9;
  }
  .Data-circle:before {
    background-color: #8dccde;
  }
  .Anaysis-circle:before {
    background-color: #9bcf99;
  }
  .Publish-circle:before {
    background-color: #f4de93;
  }
</style>
