<template>
  <div class="app-container">
    <div class="card list">
      <el-row :gutter="20">
        <el-col :span="24" :xs="24">
          <el-form ref="searchFormRef" :model="queryParams" :inline="true">
            <el-form-item label="数据ID" prop="dataNo">
              <el-input
                v-model="queryParams.dataNoStr"
                style="width: 200px"
                type="textarea"
                :rows="1"
                clearable
                placeholder="数据ID"
              />
            </el-form-item>
            <el-form-item label="提交ID" prop="subNo">
              <el-input
                v-model="queryParams.subNoStr"
                style="width: 200px"
                type="textarea"
                :rows="1"
                clearable
                placeholder="提交ID"
              />
            </el-form-item>
            <el-form-item label="实验ID" prop="expNo">
              <el-input
                v-model="queryParams.expNoStr"
                style="width: 200px"
                type="textarea"
                :rows="1"
                clearable
                placeholder="实验ID"
              />
            </el-form-item>
            <el-form-item label="样本ID" prop="sapNo">
              <el-input
                v-model="queryParams.sapNoStr"
                style="width: 200px"
                type="textarea"
                :rows="1"
                clearable
                placeholder="样本ID"
              />
            </el-form-item>
            <el-form-item label="分析ID" prop="analNo">
              <el-input
                v-model="queryParams.analNoStr"
                style="width: 200px"
                type="textarea"
                :rows="1"
                clearable
                placeholder="分析ID"
              />
            </el-form-item>
            <el-form-item label="优先级" prop="priority">
              <el-select
                v-model="queryParams.priority"
                clearable
                style="width: 100px"
              >
                <el-option
                  v-for="(item, index) in Object.keys(priorityMap)"
                  :key="index"
                  :label="priorityMap[item]"
                  :value="Number(item)"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="失败原因" prop="failCause">
              <el-input
                v-model="queryParams.failCause"
                style="width: 200px"
                clearable
                placeholder="失败原因"
                @keyup.enter="getDataList"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="getDataList"
                >搜索
              </el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button type="info" icon="download" @click="exportData"
                >导出
              </el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <div class="d-flex align-items-center mb-05">
        <span class="font-600 text-secondary-color mr-1">状态: </span>
        <el-radio-group v-model="queryParams.status" @change="getDataList">
          <el-radio value="" label="全部">全部</el-radio>
          <el-radio value="ready" label="就绪">就绪</el-radio>
          <el-radio value="queuing" label="排队中">排队中</el-radio>
          <el-radio value="running" label="运行中">运行中</el-radio>
          <el-radio value="success" label="成功">成功</el-radio>
          <el-radio value="failed" label="失败">失败</el-radio>
        </el-radio-group>
      </div>

      <el-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        style="width: 100%; margin-bottom: 20px"
        :row-key="row => row.id"
        :header-cell-style="{
          backgroundColor: '#f2f2f2',
          color: '#333333',
          fontWeight: 700,
        }"
        border
        :default-sort="defaultSort"
        @sort-change="handleSortChange"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          :reserve-selection="true"
          :selectable="selectHandle"
          align="center"
          width="50"
        />
        <el-table-column prop="dataNo" label="数据ID" width="120" />
        <el-table-column
          prop="dataFileName"
          label="数据文件名"
          width="150"
          show-overflow-tooltip
        />
        <el-table-column prop="priority" label="任务优先级" width="150">
          <template #default="scope">
            {{ priorityMap[scope.row.priority] }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="120" sortable>
          <template #default="scope">
            <div class="d-flex align-items-center">
              <el-icon
                v-if="scope.row.status === 'success'"
                color="#07BCB4"
                size="17"
              >
                <CircleCheckFilled />
              </el-icon>
              <el-icon
                v-else-if="scope.row.status === 'failed'"
                size="17"
                color="#FF8181"
              >
                <CircleCloseFilled />
              </el-icon>
              <el-icon
                v-else-if="scope.row.status === 'ready'"
                color="#409EFF"
                size="17"
              >
                <InfoFilled />
              </el-icon>
              <el-icon
                v-else-if="scope.row.status === 'running'"
                color="#E6A23C"
                size="17"
              >
                <Loading />
              </el-icon>
              <span class="ml-05">{{ getStatusText(scope.row.status) }} </span>
            </div>
          </template>
        </el-table-column>

        <el-table-column
          prop="dataFilePath"
          label="数据文件路径"
          show-overflow-tooltip
          min-width="180"
        />
        <el-table-column
          prop="failCause"
          label="失败原因"
          min-width="180"
          show-overflow-tooltip
        />
        <el-table-column
          prop="dataCreateDate"
          label="数据创建时间"
          width="180"
          sortable
        >
          <template #default="scope">
            {{ parseTime(scope.row.dataCreateDate) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="createDate"
          label="任务创建时间"
          width="180"
          sortable
        >
          <template #default="scope">
            {{ parseTime(scope.row.createDate) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="updateDate"
          label="状态更新时间"
          width="180"
          sortable
        >
          <template #default="scope">
            {{ parseTime(scope.row.updateDate) }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          min-width="110"
          fixed="right"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-tooltip content="查看">
              <svg-icon
                v-if="
                  scope.row.status === 'failed' ||
                  scope.row.status === 'success'
                "
                icon-class="view"
                class-name="meta-svg"
                @click="showDetail(scope.row.dataNo)"
              ></svg-icon>
            </el-tooltip>
            <el-tooltip content="查看错误日志">
              <svg-icon
                v-if="scope.row.status === 'failed'"
                icon-class="attr"
                class-name="meta-svg"
                @click="toErrorLogPage(scope.row.dataNo)"
              ></svg-icon>
            </el-tooltip>
            <el-tooltip content="修改优先级">
              <svg-icon
                v-if="
                  scope.row.status === 'ready' || scope.row.status === 'failed'
                "
                icon-class="job"
                class-name="meta-svg"
                @click="showPriority(scope.row.dataNo)"
              ></svg-icon>
            </el-tooltip>
            <el-tooltip content="重试">
              <svg-icon
                v-if="scope.row.status === 'failed'"
                icon-class="retry"
                class-name="meta-svg"
                @click="retryTask(scope.row.dataNo)"
              ></svg-icon>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <el-button
        v-show="tableData.length > 0"
        :disabled="selectedRows.length === 0"
        style="float: left"
        type="danger"
        @click="showPriority()"
        >批量修改优先级
      </el-button>
      <el-button
        v-show="tableData.length > 0"
        :disabled="selectedRows.length === 0"
        style="float: left"
        type="warning"
        @click="batchRetry()"
        >批量重试
      </el-button>
      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        class="mb-1"
        @pagination="getDataList"
      />
    </div>
    <!-- 详情 -->
    <el-dialog v-model="showDialog" title="详情" width="850px">
      <el-form
        :model="taskInfo"
        label-width="200px"
        label-position="left"
        style="max-height: 70vh; overflow-y: auto"
      >
        <el-form-item label="数据ID：">{{ taskInfo.dataNo }}</el-form-item>
        <el-form-item label="文件名："
          >{{ taskInfo.dataFileName }}
        </el-form-item>
        <el-form-item label="状态：">{{
          getStatusText(taskInfo.status)
        }}</el-form-item>
        <el-form-item label="任务创建时间："
          >{{ parseTime(taskInfo.createDate) }}
        </el-form-item>
        <el-form-item label="数据创建时间："
          >{{ parseTime(taskInfo.dataCreateDate) }}
        </el-form-item>
        <el-form-item label="状态更新时间："
          >{{ parseTime(taskInfo.updateDate) }}
        </el-form-item>
        <el-form-item label="耗时：">{{ taskInfo.useTime }}</el-form-item>
        <el-form-item v-if="taskInfo.status === 'failed'" label="失败原因："
          >{{ taskInfo.failCause }}
        </el-form-item>
        <el-form-item v-if="taskInfo.status === 'failed'" label="错误日志路径："
          >{{ taskInfo.errorLogPath }}
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="text-center">
          <el-button @click="showDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 优先级 -->
    <el-dialog
      v-model="showPriorityDialog"
      title="修改优先级"
      width="300px"
      class="dialog radius-14"
      @close="showPriorityDialog = false"
    >
      <el-form ref="priorityForm" label-width="80px">
        <el-form-item label="优先级">
          <el-select v-model="priority" clearable style="width: 200px">
            <el-option
              v-for="(item, index) in Object.keys(priorityMap)"
              :key="index"
              :label="priorityMap[item]"
              :value="Number(item)"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button
            :disabled="!priority"
            type="primary"
            class="btn-primary btn btn-s btn-shadow"
            round
            @click="handleChangePriority"
            >确认</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import {
    getCurrentInstance,
    onMounted,
    reactive,
    ref,
    toRefs,
    watch,
  } from 'vue';
  import {
    changeTaskPriority,
    getSamToolTaskInfo,
    listSamToolTask,
    retrySamToolTask,
  } from '@/api/qc/samtool';

  onMounted(() => {
    getDataList();
  });

  const { proxy } = getCurrentInstance();

  const data = reactive({
    tableData: [],
    total: 0,
    queryParams: {
      dataNoStr: '',
      dataNos: [],
      subNoStr: '',
      subNos: [],
      analNoStr: '',
      analNos: [],
      expNoStr: '',
      expNos: [],
      sapNoStr: '',
      sapNos: [],
      status: '',
      failCause: '',
      priority: undefined,
      pageNum: 1,
      pageSize: 20,
      orderByColumn: 'dataCreateDate',
      isAsc: 'descending',
    },
    dateRange: [],
    loading: true,
    defaultSort: { prop: 'dataCreateDate', order: 'descending' },
  });

  /** 解构 */
  const { tableData, total, queryParams, dateRange, loading, defaultSort } =
    toRefs(data);

  // 监听 noStr 的变化，并同步更新 no
  watch(
    () => data.queryParams.dataNoStr,
    newVal => {
      data.queryParams.dataNos = newVal ? newVal.split('\n') : [];
    },
  );
  watch(
    () => data.queryParams.subNoStr,
    newVal => {
      data.queryParams.subNos = newVal ? newVal.split('\n') : [];
    },
  );
  watch(
    () => data.queryParams.expNoStr,
    newVal => {
      data.queryParams.expNos = newVal ? newVal.split('\n') : [];
    },
  );
  watch(
    () => data.queryParams.sapNoStr,
    newVal => {
      data.queryParams.sapNos = newVal ? newVal.split('\n') : [];
    },
  );
  watch(
    () => data.queryParams.analNoStr,
    newVal => {
      data.queryParams.analNos = newVal ? newVal.split('\n') : [];
    },
  );

  function resetQuery() {
    dateRange.value = [];
    proxy.resetForm('searchFormRef');
    getDataList();
  }

  /** 排序触发事件 */
  function handleSortChange(column) {
    if (column.order) {
      queryParams.value.orderByColumn = column.prop;
      queryParams.value.isAsc = column.order;
      getDataList();
    }
  }

  function getDataList() {
    loading.value = true;
    listSamToolTask(proxy.addDateRange(queryParams.value, dateRange.value))
      .then(response => {
        // 将结果赋值给tableData
        tableData.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  let taskInfo = ref({});
  let showDialog = ref(false);

  function showDetail(no) {
    proxy.$modal.loading('正在打开，请稍候');
    getSamToolTaskInfo(no)
      .then(response => {
        taskInfo.value = response.data;
        showDialog.value = true;
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  const getStatusText = status => {
    const statusMap = {
      success: '成功',
      failed: '失败',
      ready: '就绪',
      running: '运行中',
      queuing: '排队中',
    };
    return statusMap[status] || status;
  };

  let showPriorityDialog = ref(false);
  let dataNos = ref([]);
  let priority = ref('');

  function showPriority(no) {
    showPriorityDialog.value = true;
    if (no) {
      dataNos.value = [no];
    } else {
      dataNos.value = selectedRows.value.map(it => it.dataNo);
    }
  }

  function handleChangePriority() {
    changeTaskPriority({
      dataNos: dataNos.value.join(','),
      priority: priority.value,
    }).then(() => {
      proxy.$modal.alertSuccess('优先级已修改');
      // 清空选中
      proxy.$refs['table'].clearSelection();
      getDataList();
      showPriorityDialog.value = false;
    });
  }

  function batchRetry() {
    dataNos.value = selectedRows.value.map(it => it.dataNo);
    proxy.$modal.confirm(`确定要批量重试任务吗？`).then(() => {
      retrySamToolTask({
        dataNos: dataNos.value.join(','),
      })
        .then(response => {
          proxy.$modal.alertSuccess('任务已重新提交');
          // 清空选中
          proxy.$refs['table'].clearSelection();
          getDataList();
        })
        .finally(() => {});
    });
  }

  function retryTask(no) {
    proxy.$modal.confirm(`确定要重试任务 ${no} 吗？`).then(() => {
      retrySamToolTask({
        dataNos: no,
      })
        .then(response => {
          proxy.$modal.alertSuccess('任务已重新提交');
          getDataList();
        })
        .finally(() => {});
    });
  }

  function selectHandle(row, index) {
    return row.status === 'ready' || row.status === 'failed';
  }

  let selectedRows = ref([]);

  /** 多选框选中数据 */
  function handleSelectionChange(selection) {
    selectedRows.value = selection;
  }

  let priorityMap = {
    1: '低',
    4: '中低',
    5: '中',
    10: '高',
  };

  function exportData() {
    let query = JSON.stringify(
      proxy.addDateRange(queryParams.value, dateRange.value),
    );
    proxy.download(
      'system/samToolTask/exportData',
      {
        query,
      },
      `SamToolTask_${new Date().getTime()}.json`,
    );
  }

  function toErrorLogPage(dataNo) {
    window.open(
      `${import.meta.env.VITE_APP_BASE_API}/app/fastqc/samtool/errorLog/${dataNo}`,
    );
  }
</script>

<style lang="scss" scoped>
  .el-dialog {
    .el-form-item {
      margin-bottom: 0;
    }
  }

  :deep(.el-dialog .el-dialog__body) {
    padding-top: 0;
    padding-bottom: 0;
  }

  .meta-svg {
    width: 20px;
    height: 20px;
    margin-right: 0.5rem;
    cursor: pointer;
  }
</style>
<style lang="scss">
  .el-popper {
    max-width: 350px !important;
  }
</style>
