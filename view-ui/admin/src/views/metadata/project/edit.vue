<template>
  <div class="project app-container w-100">
    <div class="card general-info card-container mt-1">
      <div class="category-title font-600 text-main-color">基本信息</div>
      <div class="plr-20 bg-gray mt-1">
        <el-form
          ref="formRef"
          label-position="top"
          label-width="100px"
          :model="form"
          :rules="rules"
          :inline="true"
          style="padding-top: 8px"
        >
          <el-form-item label="Project ID" class="proj-width">
            <div class="d-flex align-items-center w-100">
              <el-input v-model="form.projectNo" disabled />
              <span v-if="form.usedIds" class="text-warning used-id">
                已使用ID: {{ form.usedIds.join('; ') }}
              </span>
            </div>
          </el-form-item>
          <el-form-item label="Project Name" prop="name" style="width: 30%">
            <el-input v-model="form.name" />
          </el-form-item>
          <el-form-item class="w-100" label="Project Description">
            <el-input v-model="form.description" type="textarea" rows="5" />
          </el-form-item>
          <RelatedLinks
            :key="'project-RelatedLinks' + componentKey"
            v-model:relatedLinks="form.relatedLinks"
          ></RelatedLinks>
        </el-form>
      </div>
      <Publications
        :key="'project-Publications' + componentKey"
        v-model:publish-data="form.publish"
      ></Publications>
      <Submitter
        ref="submitterRef"
        :key="'project-Publications' + componentKey"
        v-model:submitter-data="form.submitter"
      ></Submitter>
      <div class="text-align-right mt-2 pr-20">
        <el-button
          type="primary"
          class="btn-primary btn btn-s btn-shadow"
          round
          @click="saveData"
          >保存
        </el-button>
        <el-button class="btn-primary btn btn-round" round @click="handleClose"
          >返回
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
  import Publications from '@/views/metadata/common/Publications.vue';
  import Submitter from '@/views/metadata/common/Submitter.vue';
  import RelatedLinks from '@/views/metadata/common/RelatedLinks.vue';

  import { getCurrentInstance, onMounted, reactive, ref, toRefs } from 'vue';
  import { useRoute } from 'vue-router';
  import { editProject, getProjInfo } from '@/api/metadata/project';

  const route = useRoute();
  let id = route.params.id;
  const { proxy } = getCurrentInstance();

  const componentKey = ref(1);
  const data = reactive({
    form: {
      projectNo: undefined,
      name: '',
      description: undefined,
      relatedLinks: undefined,
      publish: [
        {
          id: undefined,
          publication: undefined,
          doi: undefined,
          pmid: undefined,
          reference: undefined,
          articleName: undefined,
        },
      ],
      submitter: {},
    },
    rules: {
      name: [
        {
          required: true,
          trigger: 'blur',
        },
      ],
    },
  });

  const { form, rules } = toRefs(data);

  onMounted(() => {
    // console.log(id);
    getProjInfo(id).then(response => {
      form.value = response.data;
      componentKey.value++;
    });
  });

  /** 返回按钮操作 */
  function handleClose() {
    const obj = { path: '/metadata/project' };
    proxy.$tab.closeOpenPage(obj);
  }

  let resultArr = reactive([]); // 存放子组件的数组
  let errListMsg = ref(''); // 用来存储错误提示

  // 创建Promise 实例，为多个组件校验使用
  const checkForm = formChild => {
    let result = new Promise((resolve, reject) => {
      formChild.validate((valid, fields) => {
        if (valid) {
          resolve();
        } else {
          Object.keys(fields).forEach((v, index) => {
            if (index === 0) {
              // 定位到错误的位置
              // const PropName = fields[v][0].field;
              // formChild.scrollToField(PropName);
              errListMsg.value = fields[v][0].message;
            }
          });
          reject();
        }
      });
    });
    resultArr.push(result);
  };

  function saveData() {
    // console.log(form.value);
    checkForm(proxy.$refs['formRef']);
    checkForm(proxy.$refs['submitterRef'].$refs['formRef']);

    Promise.all(resultArr)
      .then(() => {
        proxy.$modal.loading('正在保存...');
        // 校验通过
        editProject(form.value)
          .then(response => {
            form.value = response.data;
            componentKey.value++;
            proxy.$modal.alertSuccess('保存成功');
          })
          .finally(() => {
            proxy.$modal.closeLoading();
          });
      })
      .catch(() => {
        // 校验不通过提示
        proxy.$modal.msgError(errListMsg.value);
      });
    resultArr = []; // 每次请求完要清空数组
    errListMsg.value = ''; // 提示也需要清空
  }
</script>

<style lang="scss" scoped>
  .project {
    :deep(.el-radio__label) {
      font-weight: 600;
      color: #333333;
      font-size: 16px;
    }

    .tips {
      font-size: 14px;
      margin-left: 1.3rem;
    }

    .general-info {
      .w-45 {
        width: 45%;
      }

      .used-id {
        width: 220px;
        margin-left: 0.5rem;
      }
    }

    .links {
      .el-button {
        padding: 2px 8px;
        border-radius: 50%;
      }

      :deep(.el-form-item__label) {
        font-weight: 700;
      }

      :deep(.el-form-item__content) {
        flex-direction: column;
        align-items: flex-start;

        & + .el-form-item__label {
          font-weight: 700;
        }
      }
    }
  }

  .el-form.tag {
    .el-form-item {
      width: 30%;

      .el-select {
        width: 100%;
      }
    }
  }

  .proj-width {
    width: calc(30% + 146px);
  }

  .links-width {
    width: calc(30% + 72px);
  }
</style>
