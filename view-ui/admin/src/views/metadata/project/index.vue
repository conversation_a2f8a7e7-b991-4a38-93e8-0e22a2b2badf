<template>
  <div class="app-container">
    <div class="card list">
      <el-form ref="queryRef" :model="queryParams" :inline="true">
        <el-form-item label="ID" prop="nos">
          <el-input
            v-model="queryParams.noStr"
            placeholder="搜索ID"
            style="width: 240px"
            type="textarea"
            :rows="1"
            clearable
            @keyup.enter="getDataList"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input
            v-model="queryParams.name"
            clearable
            placeholder="搜索名称"
            style="width: 300px"
            @keyup.enter="getDataList"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="创建人" prop="creatorEmail">
          <el-input
            v-model="queryParams.creatorEmail"
            clearable
            placeholder="搜索创建人"
            style="width: 250px"
            @keyup.enter="getDataList"
          />
        </el-form-item>
        <el-form-item label="提交人邮箱" prop="submitterEmail">
          <el-input
            v-model="queryParams.submitterEmail"
            clearable
            style="width: 220px"
            @keyup.enter="getDataList"
          />
        </el-form-item>
        <el-form-item label="提交人机构" prop="submitterOrgName">
          <el-input
            v-model="queryParams.submitterOrgName"
            clearable
            style="width: 220px"
            @keyup.enter="getDataList"
          />
        </el-form-item>
        <el-form-item prop="sourceProject" label="标签">
          <el-select
            v-model="queryParams.tags"
            clearable
            style="width: 220px"
            :teleported="false"
            multiple
          >
            <el-option
              v-for="dict in tag"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="提交时间" prop="dateRange">
          <el-date-picker
            v-model="dateRange"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 220px"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="getDataList"
            >搜索
          </el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          <el-button type="warning" icon="download" @click="exportEmail"
            >导出邮箱
          </el-button>
          <el-button
            v-hasPermi="['metadata:project:export']"
            type="info"
            icon="download"
            @click="exportData"
            >导出
          </el-button>
        </el-form-item>
      </el-form>

      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%; margin-bottom: 20px"
        :header-cell-style="{
          backgroundColor: '#f2f2f2',
          color: '#333333',
          fontWeight: 700,
        }"
        border
        :default-sort="defaultSort"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="projectNo" label="ID" sortable width="115">
          <template #default="scope">
            <a
              class="text-primary"
              href="javascript:void(0)"
              @click="showDetail(scope.row)"
            >
              {{ scope.row.projectNo }}
            </a>
          </template>
        </el-table-column>
        <el-table-column
          prop="name"
          label="名称"
          min-width="120"
          sortable
          show-overflow-tooltip
        />
        <el-table-column
          prop="expTypes"
          label="实验类型"
          min-width="160"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ scope.row.expTypes.join('; ') }}
          </template>
        </el-table-column>
        <el-table-column
          prop="sapTypes"
          label="样本类型"
          min-width="160"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ scope.row.sapTypes.join('; ') }}
          </template>
        </el-table-column>
        <el-table-column
          prop="organisms"
          label="物种"
          min-width="160"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ scope.row.organisms.join('; ') }}
          </template>
        </el-table-column>
        <el-table-column
          prop="description"
          label="描述"
          min-width="160"
          show-overflow-tooltip
        />

        <el-table-column
          prop="submitter"
          label="提交人"
          width="150"
          show-overflow-tooltip
        />
        <el-table-column
          prop="creatorEmail"
          label="创建人"
          width="165"
          show-overflow-tooltip
        />

        <el-table-column
          prop="visibleStatus"
          label="状态"
          min-width="130"
          sortable
        >
          <template #default="scope">
            <div class="d-flex align-items-center">
              <el-icon
                v-if="scope.row.visibleStatus === 'Accessible'"
                color="#67C23A"
              >
                <View />
              </el-icon>
              <el-icon v-else color="#F56C6C">
                <Hide />
              </el-icon>
              <div
                class="ml-05"
                :style="{
                  color:
                    scope.row.visibleStatus === 'Accessible'
                      ? '#67C23A'
                      : '#F56C6C',
                }"
              >
                {{ scope.row.visibleStatus }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="安全级别" width="80">
          <template #default="scope">
            <div class="lock" :style="computedStyle(scope.row.dataCount)">
              <el-tooltip placement="right">
                <template #content>
                  <div>
                    <span>公开 : </span>
                    <span>{{ scope.row.dataCount.Public }}</span>
                  </div>
                  <div>
                    <span>私有: </span>
                    <span>{{ scope.row.dataCount.Private }}</span>
                  </div>
                  <div>
                    <span>受限: </span>
                    <span>{{ scope.row.dataCount.Restricted }}</span>
                  </div>
                </template>
                <el-icon class="cursor-pointer">
                  <Lock v-if="scope.row.visibleStatus === 'Accessible'" />
                  <Unlock v-else />
                </el-icon>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="createDate"
          label="提交时间"
          width="160"
          sortable
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ parseTime(scope.row.createDate) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="390" fixed="right" width="200">
          <template #default="scope">
            <el-tooltip content="更改创建人">
              <svg-icon
                icon-class="creator"
                class-name="meta-svg"
                @click="preChangeCheck(scope.row.projectNo)"
              ></svg-icon>
            </el-tooltip>
            <el-tooltip content="编辑">
              <svg-icon
                icon-class="edits"
                class-name="meta-svg"
                @click="toEdit(scope.row.projectNo)"
              ></svg-icon>
            </el-tooltip>
            <el-tooltip content="更改安全级别">
              <svg-icon
                icon-class="security"
                class-name="meta-svg"
                @click="openSecurityDialog(scope.row.projectNo)"
              ></svg-icon>
            </el-tooltip>
            <el-tooltip content="删除">
              <svg-icon
                icon-class="delete"
                class-name="meta-svg"
                @click="preDeleteCheck(scope.row.projectNo)"
              ></svg-icon>
            </el-tooltip>

            <el-tooltip content="导出数据">
              <svg-icon
                icon-class="export-data-links"
                class-name="export-svg"
                @click="exportDataByNo(scope.row.projectNo)"
              ></svg-icon>
            </el-tooltip>
            <el-tooltip content="刷新索引">
              <svg-icon
                icon-class="index"
                class-name="meta-svg"
                @click="refreshEsIndex(scope.row.projectNo)"
              ></svg-icon>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        class="mb-1"
        @pagination="getDataList"
      />
    </div>

    <Security ref="securityRef"></Security>

    <delete-log ref="deleteLog" curr-type="Project"></delete-log>
    <!-- change creator -->
    <change-creator-confirm
      v-model:new-creator="newCreator"
      v-model:show-dialog="showChangeDialog"
      :delete-check-result="deleteCheckResult"
      @change-creator-method="confirmChange"
    >
    </change-creator-confirm>
    <delete-confirm
      v-model:show-dialog="showDeleteDialog"
      :delete-check-result="deleteCheckResult"
      @delete-method="confirmDelete"
    ></delete-confirm>
  </div>
</template>

<script setup>
  import Security from '@/components/Security/index.vue';
  import DeleteLog from '@/views/metadata/common/DeleteLog.vue';
  import DeleteConfirm from '@/views/metadata/common/DeleteConfirm.vue';

  import {
    getCurrentInstance,
    onMounted,
    reactive,
    ref,
    toRefs,
    watch,
  } from 'vue';
  import { useRouter } from 'vue-router';
  import {
    deleteProjectAll,
    listProject,
    projDeleteCheck,
    refreshIndex,
    updateProjCreator,
  } from '@/api/metadata/project';
  import ChangeCreatorConfirm from '@/views/metadata/common/ChangeCreatorConfirm.vue';
  import { createAccessToken } from '@/api/login';

  const router = useRouter();

  const { proxy } = getCurrentInstance();
  const { tag } = proxy.useDict('tag');

  onMounted(() => {
    getDataList();
  });

  /** 响应式数据 */
  const data = reactive({
    tableData: [],
    total: 0,
    queryParams: {
      name: '',
      noStr: '',
      nos: [],
      submitterEmail: '',
      submitterOrgName: '',
      tags: [],
      pageNum: 1,
      pageSize: 20,
      creatorEmail: '',
      orderByColumn: 'createDate',
      isAsc: 'descending',
    },
    dateRange: [],
    loading: true,
    defaultSort: { prop: 'createDate', order: 'descending' },
  });

  /** 解构 */
  const { tableData, total, queryParams, dateRange, loading, defaultSort } =
    toRefs(data);

  // 监听 noStr 的变化，并同步更新 no
  watch(
    () => data.queryParams.noStr,
    newVal => {
      data.queryParams.nos = newVal ? newVal.split('\n') : [];
    },
  );

  /** 查询列表数据*/
  function getDataList() {
    loading.value = true;
    listProject(proxy.addDateRange(queryParams.value, dateRange.value))
      .then(response => {
        // 将结果赋值给tableData
        tableData.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  function resetQuery() {
    dateRange.value = [];
    queryParams.value.noStr = '';
    proxy.resetForm('queryRef');

    getDataList();
  }

  // 导出
  function exportEmail() {
    proxy.download(
      'system/metadata/project/exportProjectEmail',
      {},
      `project_email_${new Date().getTime()}.xlsx`,
    );
  }

  /** 排序触发事件 */
  function handleSortChange(column) {
    if (column.order) {
      queryParams.value.orderByColumn = column.prop;
      queryParams.value.isAsc = column.order;
      getDataList();
    }
  }

  const openSecurityDialog = projNo => {
    proxy.$refs['securityRef'].init('project', projNo);
  };

  const toEdit = no => {
    router.push({
      path: `/metadata/edit/project/${no}`,
    });
  };

  let showDeleteDialog = ref(false);
  let projNo = ref('');
  let newCreator = ref('');
  let deleteCheckResult = ref({});

  function preDeleteCheck(projectNo) {
    proxy.$modal.loading('正在打开删除对话框，请稍候');
    projNo.value = projectNo;
    projDeleteCheck(projectNo)
      .then(response => {
        if (response.data.errors && response.data.errors.length !== 0) {
          proxy.$refs['deleteLog'].openLog(response.data.errors);
          return;
        }
        deleteCheckResult.value = response.data;
        showDeleteDialog.value = true;
        // 验证密码
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  function exportDataByNo(projectNo) {
    let param = {
      type: 'project',
      typeNo: projectNo,
    };
    proxy.download(
      '/system/metadata/project/downloadData',
      param,
      `${projectNo}_Data_${new Date().getTime()}.xlsx`,
    );
  }

  let showChangeDialog = ref(false);

  function preChangeCheck(projectNo) {
    proxy.$modal.loading('正在打开更改创建人对话框，请稍候');
    projNo.value = projectNo;
    projDeleteCheck(projectNo)
      .then(response => {
        if (response.data.errors && response.data.errors.length !== 0) {
          proxy.$refs['deleteLog'].openLog(response.data.errors);
          return;
        }
        deleteCheckResult.value = response.data;
        showChangeDialog.value = true;
        // 验证密码
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  function confirmDelete() {
    proxy.$modal.loading('正在删除，请稍候');
    deleteProjectAll({
      projectNo: projNo.value,
    })
      .then(() => {
        showDeleteDialog.value = false;
        proxy.$modal.alertSuccess('删除成功');
        getDataList();
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  function confirmChange() {
    proxy.$modal.loading('正在更改，请稍候');
    updateProjCreator({
      projectNo: projNo.value,
      newCreator: newCreator.value,
    })
      .then(() => {
        showChangeDialog.value = false;
        newCreator.value = '';
        proxy.$modal.alertSuccess('更改创建人成功');
        getDataList();
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  function showDetail(row) {
    // 预先生成access_token
    createAccessToken({ memberId: row.creator }).then(response => {
      const token = response.data;
      let href = `${import.meta.env.VITE_APP_WEB_URL}/project/detail/${
        row.projectNo
      }?access-token=${token}`;
      // 打开一个新页面
      window.open(href);
    });
  }

  function refreshEsIndex(projNo) {
    proxy.$modal
      .confirm('确认刷新该Project下的Elasticsearch索引?')
      .then(function () {
        refreshIndex(projNo);
      })
      .then(() => {
        proxy.$modal.msgSuccess('执行成功，请等待几分钟后查看数据');
      })
      .catch(() => {});
  }

  const computedStyle = security => {
    return {
      '--my-bg-public': security.Public ? '#07bcb4' : '#DDDDDD',
      '--my-bg-private': security.Private ? '#3a78e8' : '#CCCCCC',
      '--my-bg-restricted': security.Restricted ? '#fe7f2b' : '#bebebe',
    };
  };

  function exportData() {
    let query = JSON.stringify(
      proxy.addDateRange(queryParams.value, dateRange.value),
    );
    proxy.download(
      'system/metadata/project/exportData',
      {
        query,
      },
      `Project_${new Date().getTime()}.json`,
    );
  }
</script>

<style lang="scss" scoped>
  .meta-svg {
    width: 20px;
    height: 20px;
    margin-right: 0.5rem;
    cursor: pointer;
  }

  .export-svg {
    top: 2px;
    width: 24px;
    height: 24px;
    margin-right: 0.5rem;
    cursor: pointer;
  }

  :deep(.el-dialog__body) {
    padding: 10px !important;

    .el-form {
      .el-input {
        //width: 300px;
      }
    }

    .el-textarea__inner {
      border-radius: 12px;
    }
  }

  .lock {
    --bg-public: var(--my-bg-public, #07bcb4);
    --bg-private: var(--my-bg-private, #3a78e8);
    --bg-restricted: var(--my-bg-restricted, #fe7f2b);

    display: inline-flex;
    flex-grow: 1;
    flex-wrap: wrap;
    justify-content: flex-end;
    margin-left: 5px;
    padding: 0.7em 0.7em;
    border-radius: 50%;
    background: radial-gradient(white calc(52% - 1px), transparent 30%),
      conic-gradient(
        from 18deg,
        var(--bg-public) 33.3%,
        var(--bg-private) 0% 66.6%,
        var(--bg-restricted) 0%
      );
  }
</style>
<style lang="scss">
  .el-popper {
    max-width: 350px !important;
  }
</style>
