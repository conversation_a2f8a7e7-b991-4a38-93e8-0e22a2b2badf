<template>
  <div class="project app-container w-100">
    <div class="card card-container pt-0">
      <FillTip></FillTip>
      <div class="category-title font-600 text-main-color">基本信息</div>
      <div class="plr-20 bg-gray general-info mt-1">
        <el-form
          ref="analForm"
          label-position="top"
          :model="form"
          :inline="true"
          :scroll-to-error="true"
          style="padding-top: 8px"
          :rules="rules"
        >
          <el-form-item label="Analysis ID">
            <el-input
              v-model="form.analysisNo"
              disabled
              placeholder="将自动生成。"
            />
          </el-form-item>
          <el-form-item prop="name" label="Analysis Name">
            <el-input v-model="form.name" />
          </el-form-item>
          <el-form-item class="w-100" label="Analysis Description">
            <el-input v-model="form.description" type="textarea" rows="5" />
          </el-form-item>
        </el-form>
      </div>
      <div class="category-title font-600 text-main-color">
        <span class="text-danger">*</span>分析类型
      </div>
      <div class="bg-gray analysis-type p-20">
        <el-radio-group v-model="form.analysisType">
          <el-radio
            v-for="dict in node_analysis_type"
            :key="dict.value"
            :label="dict.value"
            >{{ dict.label }}
          </el-radio>
          <div class="d-flex">
            <el-radio label="Other" style="width: 100px; margin-right: 0.3rem"
              >其他
            </el-radio>
            <el-input
              v-if="form.analysisType === 'Other'"
              v-model="form.customAnalysisType"
              type="text"
            ></el-input>
          </div>
        </el-radio-group>
      </div>
      <div class="category-title font-600 text-main-color">
        <span class="text-danger">*</span>目标
      </div>
      <div class="plr-20 target d-flex mt-1 flex-wrap">
        <target
          :key="'targetComponent-' + componentKey"
          v-model:target-data="form.target"
          :creator="form.creator"
        ></target>
        <el-divider class="mb-05 mr-1 mt-1"></el-divider>
        <other-target
          :key="'otherTargetComponent-' + componentKey"
          v-model:custom-target-data="form.customTarget"
        ></other-target>
      </div>

      <div class="category-title font-600 text-main-color">流程</div>
      <pipeline
        :key="'pipelineComponent-' + componentKey"
        v-model:pipeline-data="form.pipeline"
        :creator="form.creator"
      ></pipeline>
      <Publication
        :key="'exp-Publication' + componentKey"
        v-model:publishData="form.publish"
      ></Publication>
      <Submitter
        ref="submitterRef"
        :key="'analysis-submitter-' + componentKey"
        v-model:submitter-data="form.submitter"
      ></Submitter>
      <div class="text-align-right mt-2 pr-20">
        <el-button
          type="primary"
          class="btn-primary btn btn-s btn-shadow"
          round
          @click="saveData"
          >保存
        </el-button>
        <el-button class="btn-primary btn btn-round" round @click="handleClose"
          >返回
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { getCurrentInstance, onMounted, reactive, ref, toRefs } from 'vue';
  import Target from '@/views/metadata/common/Target.vue';
  import Pipeline from '@/views/metadata/common/Pipeline.vue';
  import OtherTarget from '@/views/metadata/common/OtherTarget.vue';
  import { deepClone, isArrEmpty, isStrBlank } from '@/utils';
  import { editAnalysis, getAnalysisInfo } from '@/api/metadata/analysis';
  import { useRoute } from 'vue-router';
  import FillTip from '@/views/metadata/common/FillTip.vue';
  import Submitter from '@/views/metadata/common/Submitter.vue';
  import Publication from '@/views/metadata/common/Publications.vue';

  let route = useRoute();
  let id = route.params.id;
  let { proxy } = getCurrentInstance();
  /** 获取字典数据 */
  const { node_analysis_type } = proxy.useDict('node_analysis_type');

  onMounted(() => {
    initData();
  });

  function initData() {
    getAnalysisInfo(id).then(response => {
      let data = response.data;
      if (isArrEmpty(data.target)) {
        data.target = [
          {
            type: 'project',
            nos: [],
          },
        ];
      }
      if (isArrEmpty(data.customTarget)) {
        data.customTarget = [
          {
            name: undefined,
            link: undefined,
          },
        ];
      }
      if (isArrEmpty(data.pipeline)) {
        data.pipeline = [
          {
            program: undefined,
            link: undefined,
            version: undefined,
            note: undefined,
            output: [],
          },
        ];
      }
      componentKey.value++;
      form.value = data;
    });
  }

  let data = reactive({
    form: {
      analysisNo: undefined,
      name: undefined,
      description: undefined,
      analysisType: undefined,
      customAnalysisType: undefined,
      target: [
        {
          type: 'project',
          nos: [],
        },
      ],
      customTarget: [
        {
          name: undefined,
          link: undefined,
        },
      ],
      pipeline: [
        {
          program: undefined,
          link: undefined,
          version: undefined,
          note: undefined,
          output: [],
        },
      ],
      submitter: {},
      publish: [
        {
          id: undefined,
          publication: undefined,
          doi: undefined,
          pmid: undefined,
          reference: undefined,
          articleName: undefined,
        },
      ],
      creator: '',
    },
    rules: {
      name: [
        {
          required: true,
          trigger: 'blur',
        },
      ],
    },
  });
  let { form, rules } = toRefs(data);
  let componentKey = ref(0);

  function resetForm() {
    proxy.resetForm('analForm');
    // 重置数据
    form.value = {
      analysisNo: undefined,
      name: undefined,
      description: undefined,
      analysisType: undefined,
      customAnalysisType: undefined,
      target: [
        {
          type: 'project',
          nos: [],
          textNos: '',
        },
      ],
      customTarget: [
        {
          name: undefined,
          link: undefined,
        },
      ],
      pipeline: [
        {
          program: undefined,
          link: undefined,
          version: undefined,
          notes: undefined,
          output: [],
        },
      ],
      submitter: undefined,
      publish: {
        publication: undefined,
        doi: undefined,
        pmid: undefined,
        reference: undefined,
        articleName: undefined,
      },
    };
    // 刷新组件
    componentKey.value++;
    // initData();
  }

  let submitData = reactive({});

  /** 返回按钮操作 */
  function handleClose() {
    const obj = { path: '/metadata/analysis' };
    proxy.$tab.closeOpenPage(obj);
  }

  /** 提交数据 */
  const saveData = () => {
    proxy.$refs['analForm'].validate(valid => {
      if (isStrBlank(form.value.name)) {
        proxy.$modal.msgError('请输入分析名称');
        return;
      }
      if (valid) {
        if (isStrBlank(form.value.analysisType)) {
          proxy.$modal.msgError('请选择分析类型');
          return;
        }
        if (
          form.value.analysisType === 'Other' &&
          isStrBlank(form.value.customAnalysisType)
        ) {
          proxy.$modal.msgError('请填写其他分析类型');
          return;
        }
        let targetValidFail = false;
        for (let item of form.value.target) {
          if (isArrEmpty(item.nos) && isStrBlank(item.textNos)) {
            targetValidFail = true;
            break;
          }
        }
        let otherTargetValidFail = false;
        for (let item of form.value.customTarget) {
          if (isStrBlank(item.name) && isStrBlank(item.link)) {
            otherTargetValidFail = true;
            break;
          }
        }
        if (targetValidFail && otherTargetValidFail) {
          proxy.$modal.msgError('请填写目标或其他目标，请不要留空卡片');
          return;
        }
        let otherTargetRequiredFail = false;
        if (!otherTargetValidFail) {
          for (let item of form.value.customTarget) {
            if (isStrBlank(item.name) || isStrBlank(item.link)) {
              otherTargetRequiredFail = true;
              break;
            }
          }
        }
        if (otherTargetRequiredFail) {
          proxy.$modal.msgError('请填写其他目标名称和链接');
          return;
        }
        let pipelineVaildFail = false;
        let pipelineNameVaildFail = false;
        for (let item of form.value.pipeline) {
          if (isStrBlank(item.program)) {
            pipelineNameVaildFail = true;
          }
          if (
            isStrBlank(item.program) &&
            isStrBlank(item.link) &&
            isStrBlank(item.version) &&
            isStrBlank(item.note) &&
            isArrEmpty(item.output)
          ) {
            pipelineVaildFail = true;
            break;
          }
        }
        if (pipelineVaildFail && form.value.pipeline.length > 1) {
          proxy.$modal.msgError('请填写流程，不要留空卡片');
          return;
        }
        if (pipelineNameVaildFail && form.value.pipeline.length > 1) {
          proxy.$modal.msgError('请填写流程名称，名称是必需的');
          return;
        }

        submitData = deepClone(form.value);
        if (targetValidFail) {
          submitData.target = [];
        } else {
          submitData.target.forEach(item => {
            if (!isStrBlank(item.textNos)) {
              let strings = item.textNos.split('\n');
              // 把每一项两边trims,不为 '' 保留
              strings.forEach(it => {
                if (it.trim() !== '') {
                  item.nos.push(it.trim());
                }
              });
            }
          });
        }
        if (otherTargetValidFail) {
          submitData.customTarget = [];
        }
        if (pipelineVaildFail) {
          submitData.pipeline = [];
        }

        proxy.$modal.loading('正在保存...');
        // 弹出预览框
        editAnalysis(submitData)
          .then(response => {
            form.value = response.data;
            componentKey.value++;
            proxy.$modal.alertSuccess('保存成功');
          })
          .finally(() => {
            // 关闭loading
            proxy.$modal.closeLoading();
          });
      }
    });
  };
</script>

<style lang="scss" scoped>
  .project {
    .general-info {
      .el-form-item {
        width: 30%;
      }
    }

    .analysis-type {
      :deep(.el-radio) {
        width: 200px;
      }
    }

    .pipeline,
    .target {
      gap: 10px;

      .pipeline-item,
      .target-item {
        flex: 1;
        margin-right: 1rem;

        :deep(.el-radio__label) {
          font-size: 14px;
          font-weight: 700;
          color: #606266;
        }
      }

      .target-form {
        padding: 0 10px;

        .el-radio {
          width: 110px;
        }

        :deep(.el-form-item__label) {
          width: 27px !important;
          justify-content: flex-start;
        }
      }

      .pipeline-form {
        padding-top: 12px;

        :deep(.el-form-item__label) {
          min-width: 70px;
          justify-content: flex-start;
        }
      }

      .source-name {
        width: 31%;
      }
    }

    :deep(.el-form-item__label) {
      font-weight: 700;
    }

    :deep(.el-radio__label) {
      font-size: 16px;
    }

    :deep(.el-textarea__inner) {
      border-radius: 12px;
    }
  }

  .item {
    margin-top: 0.5rem;
    width: 100%;
    display: flex;

    .label {
      color: #666666;
      font-weight: 600;
      display: inline-block;
      min-width: 150px;
    }

    .el-tag:hover {
      cursor: pointer;
      background-color: #d9e2f6;
    }

    .bg-gray {
      width: 100%;
      padding: 4px 15px;
      border-radius: 6px;
    }

    &.pipeline {
      margin-top: 1rem;
    }

    :deep(.el-step__icon) {
      background-color: #d6e5ff;
      color: #5686dc;
      border: 2px solid #759fea;
    }

    :deep(.el-step__line) {
      background-color: #e8e8e8;
    }
  }

  .target-number {
    background-color: #d6e5ff;
    color: #3a78e8;
    border-radius: 50%;
    padding: 0 2px;
    border: 1px solid #3a78e8;
  }
</style>
