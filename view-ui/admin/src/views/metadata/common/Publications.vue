<template>
  <div class="mt-1">
    <div class="category-title font-600 text-main-color">出版物</div>

    <div
      v-for="(item, idx) in publishArray"
      :key="'Publications-' + idx"
      class="plr-20 bg-gray pos-relative mt-1"
    >
      <!--添加-->
      <el-button
        v-if="idx === 0"
        type="primary"
        circle
        plain
        class="add-btn"
        @click="addPublish"
      >
        <el-icon>
          <Plus />
        </el-icon>
      </el-button>
      <!--删除-->
      <el-button
        v-if="idx !== 0"
        type="warning"
        circle
        plain
        class="add-btn"
        @click="removePublish(idx)"
      >
        <el-icon>
          <Minus />
        </el-icon>
      </el-button>
      <el-form
        label-position="top"
        label-width="100px"
        :model="publishArray[idx]"
        :inline="true"
        style="padding-top: 8px"
      >
        <el-form-item v-show="false" hidden prop="id">
          <el-input v-model="item.id" />
        </el-form-item>
        <el-form-item label="Journal" prop="publication">
          <el-autocomplete
            v-model="item.publication"
            :teleported="false"
            class="w-100"
            :fetch-suggestions="queryJournalSearch"
            clearable
          />
        </el-form-item>
        <el-form-item label="DOI" prop="doi">
          <el-input v-model="item.doi" />
        </el-form-item>
        <el-form-item class="pmid" label="PMID" prop="pmid">
          <el-input v-model="item.pmid" />
        </el-form-item>
        <el-form-item label="Title" class="w-100" prop="articleName">
          <el-input v-model="item.articleName" />
        </el-form-item>
        <el-form-item class="w-100" label="Reference" prop="reference">
          <el-input v-model="item.reference" />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
  import { defineProps, getCurrentInstance, ref, watch } from 'vue';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    publishData: {
      type: Object,
      default() {
        return [
          {
            id: undefined, // ID
            publication: undefined, // Journal
            doi: undefined,
            pmid: undefined,
            articleName: undefined, // Title
            reference: undefined,
          },
        ];
      },
    },
  });

  const publishArray = ref(
    props.publishData && props.publishData.length > 0
      ? props.publishData
      : [
          {
            id: undefined,
            publication: undefined,
            doi: undefined,
            pmid: undefined,
            articleName: undefined,
            reference: undefined,
          },
        ],
  );

  const addPublish = () => {
    publishArray.value.push({
      id: undefined,
      publication: undefined,
      doi: undefined,
      pmid: undefined,
      articleName: undefined,
      reference: undefined,
    });
  };

  const removePublish = index => {
    publishArray.value.splice(index, 1);
  };

  /** 期刊 自动补全提醒 */
  const queryJournalSearch = (queryString, cb) => {
    const results = queryString
      ? node_journal.value.filter(createFilter(queryString))
      : node_journal.value;
    cb(results);
  };
  const createFilter = queryString => {
    return node_journal => {
      return (
        node_journal.value.toLowerCase().indexOf(queryString.toLowerCase()) ===
        0
      );
    };
  };

  watch(
    () => props.publishData,
    newValue => {
      // 使用深拷贝更新第一个输入框的值
      publishArray.value[0] = JSON.parse(
        JSON.stringify(
          newValue && newValue.length > 0
            ? newValue[0]
            : {
                id: undefined,
                publication: undefined,
                doi: undefined,
                pmid: undefined,
                articleName: undefined,
                reference: undefined,
              },
        ),
      );
    },
  );

  watch(
    publishArray,
    newVal => {
      proxy.$emit('update:publishData', newVal);
    },
    {
      immediate: true,
      deep: true,
    },
  );

  const { node_journal } = proxy.useDict('node_journal');
</script>

<style lang="scss" scoped>
  .el-form {
    .el-form-item {
      width: calc((100% - 99px) / 3);
    }

    :deep(.el-form-item__label) {
      font-weight: 700;
    }
  }

  .add-btn {
    position: absolute;
    right: 0;
    margin-top: 8px;
    margin-right: 8px;
  }
</style>
