<template>
  <div class="d-flex submitData w-100">
    <div class="card w-100 card-container">
      <div class="category-title font-600 text-main-color">提交人</div>
      <div>
        <el-form
          ref="formRef"
          label-position="top"
          label-width="100px"
          :model="form"
          :rules="rules"
          style="padding-top: 8px"
        >
          <div class="d-flex bg-gray p-20">
            <el-form-item label="First name" class="mr-1" prop="firstName">
              <el-input v-model="form.firstName" maxlength="20" />
            </el-form-item>
            <el-form-item label="Middle name" class="mr-1" prop="middleName">
              <el-input v-model="form.middleName" maxlength="20" />
            </el-form-item>
            <el-form-item label="Last name" prop="lastName">
              <el-input v-model="form.lastName" maxlength="20" />
            </el-form-item>
          </div>
          <div class="d-flex bg-gray mt-1 p-20">
            <el-form-item class="mr-1" label="Organization" prop="orgName">
              <el-autocomplete
                v-model="form.orgName"
                :teleported="false"
                class="w-100"
                :fit-input-width="true"
                :fetch-suggestions="queryOrganizationSearch"
                clearable
              >
                <template #default="{ item }">
                  <span :title="item.label" v-html="item.label"></span>
                </template>
              </el-autocomplete>
            </el-form-item>
            <el-form-item label="Department" class="mr-1" prop="deptName">
              <el-input v-model="form.deptName" />
            </el-form-item>
            <el-form-item label="PI Name" prop="piName">
              <el-input v-model="form.piName" />
            </el-form-item>
          </div>
          <div class="d-flex bg-gray mt-1 p-20">
            <el-form-item label="Email" class="mr-1" prop="email">
              <el-input v-model="form.email" />
            </el-form-item>
            <el-form-item label="Phone" class="mr-1" prop="phone">
              <el-input v-model="form.phone" maxlength="20" />
            </el-form-item>
            <el-form-item label="Fax" prop="fax">
              <el-input v-model="form.fax" />
            </el-form-item>
          </div>
          <div class="bg-gray mt-1 p-20">
            <div class="d-flex mt-1">
              <el-form-item label="Street" class="mr-1" prop="street">
                <el-input v-model="form.street" />
              </el-form-item>
              <el-form-item label="City" class="mr-1" prop="city">
                <el-input v-model="form.city" />
              </el-form-item>
              <el-form-item label="State/Province" prop="stateProvince">
                <el-input v-model="form.stateProvince" />
              </el-form-item>
            </div>
            <div class="d-flex mt-1">
              <el-form-item label="Postal code" class="mr-1" prop="postalCode">
                <el-input v-model="form.postalCode" />
              </el-form-item>
              <el-form-item label="Country/Region" prop="countryRegion">
                <el-select
                  v-model="form.countryRegion"
                  :teleported="false"
                  filterable
                  placeholder="Please select a country"
                >
                  <el-option
                    v-for="(val, idx) in node_country"
                    :key="'country-' + idx"
                    :label="val.label"
                    :value="val.value"
                  />
                </el-select>
              </el-form-item>
            </div>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup>
  import {
    defineProps,
    getCurrentInstance,
    reactive,
    ref,
    toRefs,
    watch,
  } from 'vue';

  const { proxy } = getCurrentInstance();

  /** 初始化字典数据 */
  const { node_organization } = proxy.useDict('node_organization');
  const { node_country } = proxy.useDict('node_country');
  /** 自动补全过滤 Organization */
  const queryOrganizationSearch = (queryString, cb) => {
    const results = queryString
      ? node_organization.value.filter(createFilter(queryString))
      : node_organization.value;
    cb(results);
  };
  const createFilter = queryString => {
    return node_organization => {
      return (
        node_organization.value
          .toLowerCase()
          .indexOf(queryString.toLowerCase()) !== -1
      );
    };
  };
  const props = defineProps({
    submitterData: {
      type: Object,
      default() {
        return {
          memberId: '',
          firstName: '',
          middleName: '',
          lastName: '',
          orgName: '',
          deptName: '',
          piName: '',
          email: '',
          phone: '',
          fax: '',
          street: '',
          city: '',
          stateProvince: '',
          postalCode: '',
          countryRegion: '',
        };
      },
    },
  });
  const data = reactive({
    rules: {
      firstName: [
        { required: true, message: 'Please input First name', trigger: 'blur' },
        {
          min: 1,
          max: 20,
          message: 'Length should be 1 to 20',
          trigger: 'blur',
        },
      ],
      lastName: [
        { required: true, message: 'Please input Last name', trigger: 'blur' },
        {
          min: 1,
          max: 20,
          message: 'Length should be 1 to 20',
          trigger: 'blur',
        },
      ],
      orgName: [
        {
          required: true,
          message: 'Please input Organization',
          trigger: 'blur',
        },
      ],
      email: [
        {
          required: true,
          message: 'Please input email address',
          trigger: 'blur',
        },
        {
          type: 'email',
          message: 'Please input correct email address',
          trigger: ['blur', 'change'],
        },
      ],
      countryRegion: [
        {
          required: true,
          message: 'Please select a Country/Region',
          trigger: 'blur',
        },
      ],
    },
  });

  const { rules } = toRefs(data);

  const form = reactive(props.submitterData);

  watch(
    form,
    newVal => {
      proxy.$emit('update:submitterData', newVal);
    },
    {
      immediate: true,
      deep: true,
    },
  );

  let formRef = ref();
  defineExpose({
    formRef,
  });
</script>

<style lang="scss" scoped>
  .submitData {
    .before-circle:before {
      background-color: #999999 !important;
    }

    .el-form {
      .el-form-item {
        width: 30%;

        .el-select {
          width: 100%;
        }
      }
    }
  }
</style>
