<template>
  <el-form
    ref="expAttrForm"
    label-position="top"
    :model="attributes"
    :inline="true"
    :rules="rules"
    class="exist-form"
  >
    <recommend-tip
      v-if="recommendNum > 0"
      :recommend-num="recommendNum"
      :recommend-tip="recommendTipText"
    ></recommend-tip>
    <transition v-loading="!show" name="el-fade-in-linear">
      <div v-if="show" class="d-flex w-100 flex-wrap">
        <el-form-item
          v-for="item in attrList"
          :key="item.attributesField"
          :prop="item.attributesField"
        >
          <!--字段标题-->
          <template #label>
            <!--推荐填写-->
            <recommend-icon
              v-if="item.required === 'recommend'"
            ></recommend-icon>

            <!--非推荐填写，有字段描述信息-->
            <el-popover v-if="item.description" width="500" :teleported="false">
              <template #reference>
                <span style="font-weight: bold">{{ item.attributesName }}</span>
              </template>
              <span v-html="item.description"></span>
            </el-popover>

            <!--无字段描述信息-->
            <span v-else class="font-bold">{{ item.attributesName }}</span>
          </template>

          <el-input
            v-if="item.dataType === 'Input'"
            v-model="attributes[item.attributesField]"
          />

          <el-input-number
            v-if="item.dataType === 'Number_int'"
            v-model="attributes[item.attributesField]"
            :precision="0"
            class="w-100"
            :controls="false"
          />

          <el-input-number
            v-if="item.dataType === 'Number_double'"
            v-model="attributes[item.attributesField]"
            class="w-100"
            :controls="false"
          />

          <el-input
            v-if="item.dataType === 'Textarea'"
            v-model="attributes[item.attributesField]"
            :rows="2"
            type="textarea"
          />

          <el-select
            v-if="item.dataType === 'Select'"
            v-model="attributes[item.attributesField]"
            filterable
            :allow-create="item.allowCreate"
            clearable
            :teleported="false"
            placeholder="Select"
            @change="changeSelect($event, item.attributesField)"
          >
            <el-option
              v-for="val in item.valueRange"
              :key="val"
              :label="val"
              :value="val"
            />
          </el-select>

          <el-date-picker
            v-if="item.dataType === 'Date'"
            v-model="attributes[item.attributesField]"
            class="w-100"
            format="YYYY-MM-DD"
            :teleported="false"
            value-format="YYYY-MM-DD"
            type="date"
          />

          <!--自定义属性-->
          <el-select
            v-if="item.dataType === 'Select2'"
            v-model="attributes[item.attributesField]"
            filterable
            :allow-create="item.allowCreate"
            clearable
            :teleported="false"
            placeholder="Select"
            @change="changeSelect2($event, item.attributesField)"
          >
            <el-option-group
              v-for="group in item.valueRange"
              :key="group.label"
              :label="group.label"
            >
              <el-option
                v-for="option in group.options"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-option-group>
          </el-select>

          <el-select
            v-if="
              item.attributesField === 'platform' && expType === 'Microarray'
            "
            v-model="attributes[item.attributesField]"
            filterable
            remote
            :allow-create="item.allowCreate"
            reserve-keyword
            :teleported="false"
            placeholder="Please Input Search"
            :remote-method="querySearchAsync"
            :loading="platformLoading"
          >
            <el-option
              v-for="platform in platformOptions"
              :key="'platformOption-' + platform.value"
              :label="platform.label"
              :value="platform.value"
            />
          </el-select>
        </el-form-item>
      </div>
    </transition>
  </el-form>
</template>

<script setup>
  import { defineProps, getCurrentInstance, onMounted, ref, watch } from 'vue';
  import { getExpSapData } from '@/api/metadata/dict';
  import { findPlatformLike } from '@/api/search';
  import RecommendIcon from '@/views/metadata/common/RecommendIcon.vue';
  import RecommendTip from '@/views/metadata/common/RecommendTip.vue';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    attributes: {
      type: Object,
      default() {
        return {};
      },
    },
    expType: {
      type: String,
      required: true,
    },
    recommendFilledCount: {
      type: Number,
      required: true,
      default: 0,
    },
  });

  const attributes = ref(props.attributes);
  const expType = ref(props.expType);
  const attrList = ref([]);

  const expAttrForm = ref();

  const rules = ref([]);
  const show = ref(false);

  const recommendNum = ref();
  const recommendTipText = ref('');

  /** 加载当前系统所拥有的组学类型数据字典 */
  function loadExperimentData(type) {
    show.value = false;
    getExpSapData(type).then(response => {
      recommendNum.value = response.data.recommendNum;

      const convertedData = convertValueRange(response.data.attributes);
      attrList.value = convertedData;
      rules.value = generateRules(convertedData);

      updateFiledNum(attributes.value);
      show.value = true;

      updateRecommendFilledCount();

      changeSelect(attributes.value['library_layout'], 'library_layout');
      changeSelect2(attributes.value['platform'], 'platform');
    });
  }

  function updateRecommendFilledCount() {
    proxy.$emit('update:recommendFilledCount', recommendFilledNum.value);
  }

  /** 转换二级下拉框的数据 */
  function convertValueRange(data) {
    return data.map(function (item) {
      if (item.dataType === 'Select2') {
        const valueRange = item.valueRange.map(function (platform) {
          return {
            label: platform.parent_name,
            options: platform.value_array.map(function (platformName) {
              return {
                value: platformName,
                label: platformName,
              };
            }),
          };
        });

        return {
          ...item,
          valueRange: valueRange,
        };
      } else {
        return item;
      }
    });
  }

  /** 生成表单的rules对象*/
  function generateRules(data) {
    const rules = {};

    data.forEach(function (item) {
      const fieldName = item.attributesField;
      const attributesName = item.attributesName;

      if (item.required === 'required') {
        if (
          item.dataType === 'Input' ||
          item.dataType === 'Textarea' ||
          item.dataType === 'Number_double' ||
          item.dataType === 'Number_int'
        ) {
          rules[fieldName] = [
            {
              required: true,
              message: 'Please input ' + attributesName,
              trigger: 'blur',
            },
          ];
        } else if (
          item.dataType === 'Select' ||
          item.dataType === 'Select2' ||
          item.dataType === 'Date' ||
          item.dataType === 'Custom'
        ) {
          rules[fieldName] = [
            {
              required: true,
              message: 'Please choose ' + attributesName,
              trigger: 'change',
            },
          ];
        }
      }

      // 如果有正则校验
      if (item.valueRegex) {
        if (!rules[fieldName] || rules[fieldName].length === 0) {
          rules[fieldName] = [];
        }
        rules[fieldName].push({
          pattern: item.valueRegex,
          message: `${item.valueFormat === null ? '' : 'Data format error, please refer to:' + item.valueFormat}`,
          trigger: 'blur',
        });
      }
    });

    return rules;
  }

  /** Platform改变时，联动判断Read length for mate1(bp) */
  function changeSelect2(value, type) {
    if (type === 'platform') {
      rules.value['read_length_for_mate1(bp)'][0].required = !(
        value.startsWith('PacBio Sequel') || value.startsWith('Ion Torrent')
      );
    }
  }

  /** 如果library_layout填了Paired，则read_length_for_mate2(bp)必填*/
  function changeSelect(value, type) {
    if (type === 'library_layout') {
      if (value === 'Paired') {
        rules.value['read_length_for_mate2(bp)'].push({
          required: true,
          message: 'Please input Read length for mate2(bp)',
          trigger: 'blur',
        });
      } else {
        rules.value['read_length_for_mate2(bp)'].splice(1, 1);
      }
    }
  }

  /** 重置表单 */
  const resetForm = () => {
    proxy.resetForm('expAttrForm');
    attributes.value = {};
  };

  const platformOptions = ref([]);
  const platformLoading = ref(false);

  /** 查询ES中的Platform */
  const querySearchAsync = query => {
    platformLoading.value = true;
    findPlatformLike({ keyword: query }).then(response => {
      platformOptions.value = response.data;
      platformLoading.value = false;
    });
  };

  onMounted(() => {
    loadExperimentData(expType.value);

    if (expType.value === 'Microarray') {
      querySearchAsync(attributes.value?.platform);
    }
  });

  /** 切换实验类型 */
  watch(
    () => props.expType,
    newValue => {
      expType.value = newValue;
      resetForm();
      loadExperimentData(newValue);
    },
  );

  const recommendFilledNum = ref(0);

  function updateFiledNum(newAttr) {
    // 监听表单中 推荐填写的个数
    recommendFilledNum.value = 0;
    attrList.value.forEach(item => {
      if (item.required === 'recommend') {
        let attrVal = newAttr[item.attributesField];
        if (attrVal && attrVal.toString().trim() !== '') {
          recommendFilledNum.value++;
        }
      }
    });
    updateRecommendFilledCount();
  }

  // 监听用户输入的值，动态修改父组件的值
  watch(
    attributes,
    newAttr => {
      updateFiledNum(newAttr);
      proxy.$emit('update:attributes', newAttr);
    },
    {
      immediate: true,
      deep: true,
    },
  );

  defineExpose({
    expAttrForm,
  });
</script>
<style scoped>
  .exist-form {
    padding-top: 8px;
    width: 100%;
    flex-wrap: wrap;

    .el-form-item {
      width: calc((100% - 100px) / 3) !important;
      margin-right: 30px;

      .el-select {
        width: 100%;
      }
    }
  }

  :deep(.el-input__inner[type='number']) {
    text-align: left;
  }
</style>
