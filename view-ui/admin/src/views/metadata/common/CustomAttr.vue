<template>
  <div>
    <div class="d-flex align-items-center ml-1 mt-1">
      <span class="font-600">以下可以填写自定义属性：</span>
    </div>
    <div class="bg-gray radius-12 p-15 mt-1">
      <el-form
        ref="sapCustomAttrForm"
        label-position="top"
        :model="inputs"
        :inline="true"
        style="padding-top: 8px"
        class="exist-form"
      >
        <div
          v-for="(val, idx) in inputs"
          :key="'attr-' + idx"
          class="d-flex align-items-center w-100"
        >
          <el-form-item
            label="Attributes (Suggest starting with “attributes_”)"
            class="w-30"
          >
            <el-input v-model="inputs[idx].attr" />
          </el-form-item>
          <el-form-item label="属性描述" class="w-30">
            <el-input v-model="inputs[idx].attrDesc" />
          </el-form-item>
          <el-form-item label="值" class="w-30">
            <el-input v-model="inputs[idx].value" />
          </el-form-item>

          <el-button
            v-if="idx === 0"
            type="primary"
            class="mt-1"
            circle
            plain
            @click="addAttr"
          >
            <el-icon>
              <Plus />
            </el-icon>
          </el-button>

          <el-button
            v-if="idx !== 0"
            type="warning"
            class="mt-1"
            circle
            plain
            @click="removeAttr(idx)"
          >
            <el-icon>
              <Minus />
            </el-icon>
          </el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup>
  import { defineProps, getCurrentInstance, ref, watch } from 'vue';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    customAttributes: {
      type: Array,
      default: () => [{ attr: '', attrDesc: '', value: '' }],
    },
  });

  const inputs = ref(
    props.customAttributes && props.customAttributes.length > 0
      ? props.customAttributes
      : [{ attr: '', attrDesc: '', value: '' }],
  );

  const addAttr = () => {
    inputs.value.push({ attr: '', attrDesc: '', value: '' });
  };

  const removeAttr = index => {
    inputs.value.splice(index, 1);
  };

  // 监听props中的Value变化，更新inputs数组的第一个元素
  watch(
    () => props.customAttributes,
    newValue => {
      // 使用深拷贝更新第一个输入框的值
      inputs.value[0] = JSON.parse(
        JSON.stringify(
          newValue && newValue.length > 0
            ? newValue[0]
            : { attr: '', attrDesc: '', value: '' },
        ),
      );
    },
  );

  // 监听用户输入的值，动态修改父组件的值
  watch(
    inputs,
    newVal => {
      proxy.$emit('update:customAttributes', newVal);
    },
    {
      immediate: true,
      deep: true,
    },
  );
</script>
<style lang="scss" scoped>
  .exist-form {
    width: 100%;
    flex-wrap: wrap;

    .el-form-item {
      width: calc((100% - 100px) / 3) !important;
      margin-right: 30px;

      .el-select {
        width: 100%;
      }
    }
  }
</style>
