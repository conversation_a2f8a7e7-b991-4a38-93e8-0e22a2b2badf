<template>
  <div
    v-for="(item, index) in targetData"
    :key="'target-source' + index"
    class="d-flex bg-gray target-item flex-column"
  >
    <div class="d-flex bg-primary justify-space-between">
      <p class="text-main-color font-600 pl-10">Target-{{ index + 1 }}</p>
      <el-button
        v-if="index === 0"
        class="ml-2"
        circle
        type="primary"
        size="small"
        plain
        @click="addTarget"
      >
        <el-icon>
          <Plus />
        </el-icon>
      </el-button>
      <el-button
        v-else
        class="ml-2"
        circle
        type="warning"
        size="small"
        plain
        @click="removeTarget(index)"
      >
        <el-icon>
          <Minus />
        </el-icon>
      </el-button>
    </div>
    <div class="target-form">
      <el-radio-group
        v-model="targetData[index].type"
        class="ml-4"
        @change="clearNos(index)"
      >
        <el-radio label="project" size="large">Project ID</el-radio>
        <el-radio label="experiment" size="large">Experiment ID</el-radio>
        <el-radio label="sample" size="large">Sample ID</el-radio>
        <el-radio label="run" size="large">Run ID</el-radio>
        <el-radio label="data" size="large">Data ID</el-radio>
        <el-radio label="analysis" size="large">Analysis ID</el-radio>
      </el-radio-group>
      <el-form :model="targetData" label-width="120px">
        <el-form-item label="ID">
          <el-select
            v-model="targetData[index].nos"
            class="w-100"
            multiple
            :placeholder="`Please select ${getValue(targetData[index].type)}`"
            remote
            reverse-keyword
            filterable
            :remote-method="
              keyword => remoteSearch(targetData[index].type, keyword)
            "
            :loading="loading"
            :teleported="false"
            @visible-change="
              visible => handleVisibleChange(targetData[index].type, visible)
            "
          >
            <el-option
              v-for="(it, index) in selOptions"
              :key="'option-' + it.value"
              :label="it.label"
              :value="it.value"
              :disabled="it.value === 'No data'"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="  " prop="desc">
          <el-input
            v-model="targetData[index].textNos"
            type="textarea"
            rows="5"
            :placeholder="`Enter identifiers, separated by spaces or new lines, into the form field, for example: \n${
              targetInfo[targetData[index].type + 'Placeholder']
            }`"
          />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script setup>
  import { defineProps, getCurrentInstance, reactive, ref, watch } from 'vue';
  import { getTargetOptions } from '@/api/metadata/analysis';

  let { proxy } = getCurrentInstance();
  let props = defineProps({
    targetData: {
      type: Array,
      default: () => [],
    },
    isDisabled: {
      type: Boolean,
    },
    creator: {
      type: String,
      required: true,
    },
  });
  let loading = ref(false);

  let map = {
    project: 'Project ID',
    experiment: 'Experiment ID',
    sample: 'Sample ID',
    run: 'Run ID',
    data: 'Data ID',
    analysis: 'Analysis ID',
  };
  let getValue = v => {
    return map[v];
  };

  let targetInfo = reactive({
    projectPlaceholder: 'OEP000001\nOEP000002\nOEP000003',
    experimentPlaceholder: 'OEX000001\nOEX000002\nOEX000003',
    samplePlaceholder: 'OES000001\nOES000002\nOES000003',
    runPlaceholder: 'OER000001\nOER000002\nOER000003',
    dataPlaceholder: 'OED000001\nOED000002\nOED000003',
    analysisPlaceholder: 'OEZ000001\nOEZ000002\nOEZ000003',
  });

  let targetData = ref(
    props.targetData && props.targetData.length > 0 ? props.targetData : [],
  );

  // 监听用户输入的值，动态修改父组件的值
  watch(
    targetData,
    newVal => {
      proxy.$emit('update:targetData', newVal);
    },
    {
      immediate: true,
      deep: true,
    },
  );

  function clearNos(index) {
    targetData.value[index].nos = [];
    targetData.value[index].textNo = '';
  }

  function addTarget() {
    targetData.value.push({
      type: 'project',
      nos: [],
      textNo: '',
    });
  }

  function removeTarget(index) {
    targetData.value.splice(index, 1);
  }

  let selOptions = ref([]);
  let query = reactive({
    pageNum: 1,
    pageSize: 100,
    name: '',
    type: '',
  });

  /* 远程搜索 */
  function remoteSearch(type, keyword) {
    loading.value = true;
    query.name = keyword;
    query.type = type;
    query.creator = props.creator;

    getTargetOptions(query)
      .then(response => {
        selOptions.value = response.rows;
        if (selOptions.value.length === 0) {
          selOptions.value.push({
            label: 'No data',
            value: 'No data',
          });
        }
      })
      .finally(() => {
        loading.value = false;
      });
  }

  /* visibleChange 暂时废弃 */
  function handleVisibleChange(type, visible) {
    // if (!visible) {
    //   selOptions.value = [];
    // }
    if (visible) {
      // 第一次显示下拉框时触发远程搜索
      remoteSearch(type, '');
    }
  }
</script>
<style scoped lang="scss">
  .target-item {
    margin-right: 1rem;
    width: 30%;

    :deep(.el-radio__label) {
      font-size: 14px;
      font-weight: 700;
      color: #606266;
    }
  }

  .target-form {
    padding: 0 10px;

    .el-radio {
      width: 110px;
    }

    :deep(.el-form-item__label) {
      width: 27px !important;
      justify-content: flex-start;
    }
  }
</style>
