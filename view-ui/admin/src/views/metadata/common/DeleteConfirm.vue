<template>
  <el-dialog
    :model-value="showDialog"
    title="删除"
    width="900"
    class="dialog radius-14"
    @close="
      () => {
        showDialog = false;
      }
    "
  >
    <el-row class="mb-2">
      <el-alert title="注意：以下项目将被删除" type="error" :closable="false" />
    </el-row>

    <el-row class="mb-2">
      <div class="d-flex row-gap-10 flex-wrap">
        <div
          v-for="id in deleteCheckResult.projNos"
          :key="'project-' + id"
          class="id-list mr-1"
        >
          <span class="btn-project">P</span>
          <router-link :to="'/project/detail/' + id">
            {{ id }}
          </router-link>
        </div>
        <div
          v-for="id in deleteCheckResult.expNos"
          :key="'experiments-' + id"
          class="id-list mr-1"
        >
          <span class="btn-experiment">E</span>
          <router-link :to="'/experiment/detail/' + id">
            {{ id }}
          </router-link>
        </div>
        <div
          v-for="id in deleteCheckResult.sapNos"
          :key="'sample-' + id"
          class="id-list mr-1"
        >
          <span class="btn-sample">S</span>
          <router-link :to="'/sample/detail/' + id">
            {{ id }}
          </router-link>
        </div>
        <div
          v-for="id in deleteCheckResult.runNos"
          :key="'run-' + id"
          class="id-list mr-1"
        >
          <span class="btn-run">R</span>
          <router-link :to="'/run/detail/' + id">
            {{ id }}
          </router-link>
        </div>
        <div
          v-for="id in deleteCheckResult.analNos"
          :key="'analysis-' + id"
          class="id-list mr-1"
        >
          <span class="btn-project">A</span>
          <router-link :to="'/analysis/detail/' + id">
            {{ id }}
          </router-link>
        </div>
        <div
          v-for="id in deleteCheckResult.dataNos"
          :key="'data-' + id"
          class="id-list mr-1"
        >
          <span class="btn-data">D</span>
          {{ id }}
        </div>
      </div>
    </el-row>
    <template #footer>
      <span class="dialog-footer">
        <el-button
          type="primary"
          class="btn-primary btn btn-s btn-shadow"
          round
          @click="confirmDelete"
          >确认</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<script setup>
  import { computed, getCurrentInstance, toRefs } from 'vue';

  const { proxy } = getCurrentInstance();
  let props = defineProps({
    showDialog: {
      required: true,
      type: Boolean,
      default: false,
    },
    deleteCheckResult: {
      required: true,
      type: Object,
    },
  });

  const showDialog = computed({
    get() {
      return props.showDialog;
    },
    set(val) {
      proxy.$emit('update:showDialog', val);
    },
  });
  const { deleteCheckResult } = toRefs(props);

  function confirmDelete() {
    proxy.$emit('delete-method');
  }
</script>
<style scoped lang="scss"></style>
