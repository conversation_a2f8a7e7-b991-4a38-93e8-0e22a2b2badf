<template>
  <div class="w-100 bg-gray p-15 mb-1 mr-1 mt-0">
    <p class="text-main-color font-600 mb-05">Other Target Source</p>
    <div v-for="(item, index) in customTargetData" class="d-flex mb-1">
      <div class="d-flex source-name">
        <p class="text-secondary-color font-600">Name</p>
        <el-input v-model="item.name" class="ml-1" />
      </div>
      <div class="d-flex source-name ml-1">
        <p class="text-secondary-color font-600">Link</p>
        <el-input v-model="item.link" class="ml-1" />
      </div>
      <el-button
        v-if="index === 0"
        type="primary"
        class="ml-1"
        circle
        plain
        @click="addTarget"
      >
        <el-icon>
          <Plus />
        </el-icon>
      </el-button>
      <el-button
        v-else
        type="warning"
        class="ml-1"
        circle
        plain
        @click="removeTarget(index)"
      >
        <el-icon>
          <Minus />
        </el-icon>
      </el-button>
    </div>
  </div>
</template>
<script setup>
  import { defineProps, getCurrentInstance, ref, watch } from 'vue';

  const { proxy } = getCurrentInstance();

  let props = defineProps({
    customTargetData: {
      type: Array,
      default: () => [],
    },
    isDisabled: {
      type: Boolean,
    },
  });

  let customTargetData = ref(
    props.customTargetData && props.customTargetData.length > 0
      ? props.customTargetData
      : [],
  );

  // 监听用户输入的值，动态修改父组件的值
  watch(
    customTargetData,
    newVal => {
      proxy.$emit('update:customTargetData', newVal);
    },
    {
      immediate: true,
      deep: true,
    },
  );

  function addTarget() {
    customTargetData.value.push({
      name: '',
      link: '',
    });
  }

  function removeTarget(index) {
    customTargetData.value.splice(index, 1);
  }
</script>
<style scoped lang="scss"></style>
