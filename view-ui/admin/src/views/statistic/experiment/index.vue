<template>
  <div v-loading="loadingFlag" class="app-container">
    <div class="card list">
      <el-row :gutter="20">
        <el-col :span="21" :xs="24">
          <h3 class="mb-0 mt-0">实验类型统计</h3>
        </el-col>
        <el-col :span="3" :xs="24" class="text-align-right">
          <el-button
            plain
            type="primary"
            round
            :icon="Download"
            @click="exportExpExcel"
            >下载
          </el-button>
        </el-col>
      </el-row>
      <el-divider></el-divider>

      <div
        id="typeStatistic"
        style="width: 100%; height: 79vh"
        class="bg-gray radius-12 mb-1 pt-10"
      ></div>
    </div>
  </div>
</template>

<script setup name="Index">
  import { Download } from '@element-plus/icons-vue';
  // import BarChart from '@/views/statistic/common/barChart.vue';
  import { getCurrentInstance, onMounted, reactive, ref } from 'vue';

  import * as echarts from 'echarts';
  import { getExpStatData } from '@/api/statistics/experiment';

  const { proxy } = getCurrentInstance();
  const loadingFlag = ref(false);

  const statisticData = reactive([
    {
      type: 'Genomic',
      accessibleNum: 200,
      unAccessibleNum: 130,
      accessibleFileSize: 0,
      totalFileSize: 0,
      unAccessibleFileSize: 0,
    },
  ]);

  const echartInit = () => {
    const totalData = statisticData.map(
      it => it.accessibleNum + it.unAccessibleNum,
    );

    const stackBarChart = echarts.init(
      document.getElementById('typeStatistic'),
    );
    const stackBarOpt = {
      color: ['#1981F4', '#2ADAC9', '#FEA52B', '#1981F4'],
      tooltip: {
        trigger: 'item',
        confine: true,
        formatter: function (params) {
          if (params.componentSubType === 'line') {
            let item = statisticData[params.dataIndex];
            return `<div style="width: 230px">
                       <div><span class="mr-05">${params.name}:</span> ${params.value}</div>
                       <hr>
                       <div><span class="tooltip-label">Total:</span> ${item.total} ( ${item.totalFileSize} )</div>
                       <div><span class="tooltip-label">Accessible:</span> ${item.accessibleNum} ( ${item.accessibleFileSize} )</div>
                       <div><span class="tooltip-label">UnAccessible:</span> ${item.unAccessibleNum} ( ${item.unAccessibleFileSize} )</div>
                    </div>
                 `;
          } else {
            return `<div>${params.seriesName}</div>
                 <div>${params.value}</div>
                 `;
          }
        },
      },
      legend: {
        top: '4%',
      },
      grid: {
        top: '14%',
        left: '3%',
        right: '4%',
        bottom: '5%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          data: statisticData.map(it => it.type),
          axisLabel: {
            rotate: 40,
            show: true,
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          axisLine: {
            show: true,
          },
          splitLine: {
            show: false,
          },
        },
      ],
      series: [
        {
          name: 'Accessible',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          barMaxWidth: 25,
          data: statisticData.map(it => it.accessibleNum),
        },
        {
          name: 'UnAccessible',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          data: statisticData.map(it => it.unAccessibleNum),
        },
        {
          name: '总数',
          data: totalData,
          type: 'line',
        },
      ],
    };
    stackBarChart.setOption(stackBarOpt);

    window.onresize = function () {
      stackBarChart.resize();
    };
  };

  // 数据导出
  const exportExpExcel = () => {
    let name = 'Experiment';
    proxy.download(`system/statistics/exp/export`, {}, `${name}.xlsx`);
  };

  function loadChartData() {
    statisticData.length = 0;
    loadingFlag.value = true;
    getExpStatData()
      .then(res => {
        if (res.code === 200) {
          let data = res.data;
          if (data) {
            statisticData.push(...data);
          }
          echartInit();
        }
      })
      .finally(() => {
        loadingFlag.value = false;
      });
  }

  onMounted(() => {
    loadChartData();
  });
</script>

<style scoped lang="scss">
  :deep(.el-form-item__label) {
    color: #858181;
  }

  :deep(.tooltip-label) {
    display: inline-block;
    width: 95px;
  }
</style>
