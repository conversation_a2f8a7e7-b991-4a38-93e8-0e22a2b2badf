<template>
  <div class="app-container">
    <div class="card list">
      <h3 class="mb-0 mt-0">Submission Method</h3>
      <el-divider></el-divider>
      <div class="table">
        <el-table
          :data="tableData"
          style="width: 100%; margin-bottom: 20px"
          :header-cell-style="{
            backgroundColor: '#f2f2f2',
            color: '#333333',
            fontWeight: 700,
          }"
          border
        >
          <el-table-column width="220">
            <template #default="scope">
              {{ scope.row.name }}
            </template>
          </el-table-column>
          <el-table-column
            v-for="item in column"
            :key="item.label"
            :prop="item.prop"
            :label="item.label"
            align="center"
          />
        </el-table>
      </div>
    </div>

    <Security ref="securityRef"></Security>
  </div>
</template>

<script setup>
  import Security from '@/components/Security/index.vue';

  import { reactive, ref, toRefs } from 'vue';

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 20,
    },
  });

  const { queryParams } = toRefs(data);

  const tableData = [
    {
      name: 'HTTP',
      date1: '3452 (100 TB)',
      date2: '5332 (100 TB)',
      date3: '5736 (100 TB)',
      date4: '4356 (100 TB)',
    },
    {
      name: 'SFTP',
      date1: '8654 (100 TB)',
      date2: '5743 (100 TB)',
      date3: '3452 (100 TB)',
      date4: '4356 (100 TB)',
    },
    {
      name: 'Express',
      date1: '1024 (100 TB)',
      date2: '3452 (100 TB)',
      date3: '3452 (100 TB)',
      date4: '4356 (100 TB)',
    },
    {
      name: 'Project',
      date1: '6436',
      date2: '4567',
      date3: '3684',
      date4: '4356',
    },
    {
      name: 'Sing Experiment',
      date1: '6478',
      date2: '4689',
      date3: '7543',
      date4: '4356',
    },
    {
      name: 'Sing Sample',
      date1: '6435',
      date2: '5356',
      date3: '5468',
      date4: '4356',
    },
    {
      name: 'Sing Analysis',
      date1: '5663',
      date2: '4566',
      date3: '2473',
      date4: '4356',
    },
    {
      name: 'Sing Archiving',
      date1: '456',
      date2: '643',
      date3: '7433',
      date4: '4356',
    },
    {
      name: 'Multiple Experiment',
      date1: '643',
      date2: '649',
      date3: '340',
      date4: '4356',
    },
    {
      name: 'Multiple Sample',
      date1: '4554',
      date2: '6543',
      date3: '46763',
      date4: '4356',
    },
    {
      name: 'Multiple Analysis',
      date1: '500',
      date2: '300',
      date3: '340',
      date4: '4356',
    },
    {
      name: 'Multiple Archiving',
      date1: '500',
      date2: '300',
      date3: '340',
      date4: '4356',
    },
  ];
  const column = [
    { prop: 'date3', label: '2024' },
    {
      prop: 'date1',
      label: '2023',
    },
    { prop: 'date2', label: '2022' },
    { prop: 'date4', label: '2021' },
  ];

  const total = ref(100);
</script>

<style lang="scss" scoped>
  :deep(.el-table) {
    tbody {
      tr > td:first-child > .cell {
        font-weight: 600;
      }
    }
  }
  //:deep(.el-table) {
  //  thead {
  //    tr:last-of-type {
  //      th:first-of-type:before {
  //        content: '';
  //        position: absolute;
  //        width: 1px;
  //        background-color: #ebeef5;
  //        display: block;
  //        height: 122px; //根据情况调整
  //        bottom: 0;
  //        right: 0; //根据情况调整
  //        transform: rotate(-68deg); //根据情况调整
  //        transform-origin: bottom;
  //      }
  //    }
  //  }
  //}
</style>
<style lang="scss">
  .el-popper {
    max-width: 350px !important;
  }
</style>
