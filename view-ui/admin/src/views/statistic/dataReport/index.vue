<template>
  <div class="app-container">
    <div class="card list mb-1">
      <el-divider>数据量</el-divider>
      <div class="mb-2 d-flex justify-center">
        <el-button
          type="primary"
          round
          :icon="Download"
          @click="exportDataExcel('metadata', '数据量-元数据')"
          >元数据
        </el-button>
        <el-button
          type="primary"
          round
          :icon="Download"
          @click="exportDataExcel('rawData', '数据量 - 原始数据')"
          >原始数据
        </el-button>
        <el-button
          type="primary"
          round
          :icon="Download"
          @click="exportDataExcel('analysisData', '数据量-分析数据')"
          >分析数据
        </el-button>
        <el-button
          type="primary"
          round
          :icon="Download"
          @click="exportDataExcel('dataFlow', '数据量-数据流')"
          >数据流
        </el-button>
      </div>
      <br />

      <el-divider>热门数据</el-divider>
      <div class="mb-2 d-flex justify-center">
        <el-button
          type="primary"
          round
          :icon="Download"
          @click="exportPopularData('Visits', '热门访问数据')"
          >热门访问数据
        </el-button>
        <el-button
          type="primary"
          round
          :icon="Download"
          @click="exportPopularData('Download', '热门下载数据')"
          >热门下载数据
        </el-button>
        <el-button
          type="primary"
          round
          :icon="Download"
          @click="exportPopularData('Requested', '热门被请求数据')"
          >热门被请求数据
        </el-button>
      </div>
      <br />

      <el-divider>文件数据相关</el-divider>
      <div class="mb-2 d-flex justify-center">
        <el-button
          type="primary"
          round
          :icon="Download"
          @click="exportDataExcel('submission', '数据-提交')"
          >数据汇交统计
        </el-button>
        <el-button
          type="primary"
          round
          :icon="Download"
          @click="exportDataExcel('subMethod', '提交方式')"
          >数据提交方式
        </el-button>
        <el-button
          type="primary"
          round
          :icon="Download"
          @click="exportDataExcel('download', '下载')"
          >数据下载统计
        </el-button>
        <el-button
          type="primary"
          round
          :icon="Download"
          @click="exportDataExcel('exportTempData', '临时数据')"
          >临时数据（未归档、公开）统计
        </el-button>
        <el-button
          type="primary"
          round
          :icon="Download"
          @click="exportDataExcel('exportTempFtpData', '临时数据（FTP目录）')"
          >临时数据（FTP home）统计
        </el-button>
        <el-button
          type="primary"
          round
          :icon="Download"
          @click="exportDataExcel('tags', '标签统计')"
          >标签数据统计
        </el-button>
        <el-button
          type="primary"
          round
          :icon="Download"
          @click="exportExcelFromChart('/share/export', '数据共享统计')"
          >数据共享统计
        </el-button>
      </div>
      <br />
      <el-divider>组学、样本</el-divider>
      <div class="mb-2 d-flex justify-center">
        <el-button
          type="primary"
          round
          :icon="Download"
          @click="exportExcelFromChart('/exp/export', '组学统计')"
          >组学统计
        </el-button>

        <el-button
          type="primary"
          round
          :icon="Download"
          @click="exportExcelFromChart('/sap/export', '样本基础统计')"
          >样本基础统计
        </el-button>

        <el-button
          type="primary"
          round
          :icon="Download"
          @click="
            exportExcelFromChart('/sap/exportSapExp', '样本-组学组合统计')
          "
          >样本-组学组合统计
        </el-button>

        <el-button
          type="primary"
          round
          :icon="Download"
          @click="exportExcelFromChart('/dataType/export', '数据类型统计')"
          >数据类型统计
        </el-button>
        <el-button
          type="primary"
          round
          :icon="Download"
          @click="exportExcelFromChart('/multiOmics/export', '多组学项目统计')"
          >多组学项目统计
        </el-button>
        <el-button
          type="primary"
          round
          :icon="Download"
          @click="
            exportExcelFromChart('/multiOmics/exportSap', '多组学样本统计')
          "
          >多组学样本统计
        </el-button>
      </div>
      <br />
      <el-divider>用户相关</el-divider>
      <div class="mb-2 d-flex justify-center">
        <el-button
          type="primary"
          round
          :icon="Download"
          @click="exportDataExcel('nodeMember', 'NODE用户统计')"
          >用户数统计
        </el-button>
        <el-button
          type="primary"
          round
          :icon="Download"
          @click="exportExcelFromChart('/userOrg/export', '用户组织统计')"
          >用户组织统计
        </el-button>
      </div>
      <br />
      <el-divider>文献</el-divider>
      <div class="mb-2 d-flex justify-center">
        <el-button
          type="primary"
          round
          :icon="Download"
          @click="exportDataExcel('publication', '出版物')"
          >出版物
        </el-button>
      </div>
      <el-divider>数据质量</el-divider>
      <div class="mb-2 d-flex justify-center">
        <el-button
          type="primary"
          round
          :icon="Download"
          @click="exportDataExcel('expSapAttr/experiment', '实验属性')"
          >实验属性填写统计
        </el-button>
        <el-button
          type="primary"
          round
          :icon="Download"
          @click="exportDataExcel('expSapAttr/sample', '示例属性')"
          >样本属性填写统计
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup name="Index">
  import { getCurrentInstance } from 'vue';
  import { Download } from '@element-plus/icons-vue';

  const { proxy } = getCurrentInstance();

  const exportPopularData = (statType, name) => {
    proxy.download(
      `system/statistics/report/export/populardata/${statType}`,
      {},
      `${name}.xlsx`,
    );
  };

  // 数据提交方式
  const exportDataExcel = (url, name) => {
    proxy.download(
      `system/statistics/report/export/${url}`,
      {},
      `${name}.xlsx`,
    );
  };

  // 导出存在图表的统计数据
  const exportExcelFromChart = (url, name) => {
    proxy.download(`system/statistics${url}`, {}, `${name}.xlsx`);
  };
</script>

<style scoped lang="scss"></style>
