<template>
  <div v-loading="loadingFlag" class="app-container">
    <div class="card list">
      <el-row :gutter="20">
        <el-col :span="21" :xs="24">
          <h3 class="mb-0 mt-0">样本类型统计</h3>
        </el-col>
        <el-col :span="3" :xs="24" class="text-align-right">
          <el-button
            plain
            type="primary"
            round
            :icon="Download"
            @click="exportSampleExcel"
            >下载
          </el-button>
        </el-col>
      </el-row>
      <el-divider></el-divider>
      <!--<div class="bg-gray p-10-15 radius-12 mb-1">
        <el-form-item label="Sample Type:" style="margin-bottom: 0">
          <el-checkbox-group v-model="sampTypes">
            <el-checkbox
              v-for="item in sampTypeOpts"
              :key="item"
              :value="item"
              :label="item"
              >{{ item }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </div>-->
      <div
        id="typeStatistic"
        style="width: 100%; height: 500px"
        class="bg-gray radius-12 mb-1 pt-10"
      ></div>
    </div>
    <div class="card list mt-1">
      <el-row :gutter="20">
        <el-col :span="21" :xs="24">
          <h3 class="mb-0 mt-0">实验-样本统计</h3>
        </el-col>
        <el-col :span="3" :xs="24" class="text-align-right">
          <el-button
            plain
            type="primary"
            round
            :icon="Download"
            @click="exportSampleExpExcel"
            >Download
          </el-button>
        </el-col>
      </el-row>
      <el-divider></el-divider>

      <el-row :gutter="10">
        <el-col :span="24" class="bg-gray radius-12">
          <!--<div class="text-align-right p-15 pb-0">
            <el-select v-model="select" style="width: 320px">
              <el-option value="All" label="All" />
              <el-option value="Accessible" label="Accessible" />
            </el-select>
          </div>-->
          <div id="omicsPropStat" style="width: 100%; height: 600px"></div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup name="Index">
  import { Download } from '@element-plus/icons-vue';
  import { getCurrentInstance, onMounted, reactive, ref } from 'vue';

  import * as echarts from 'echarts';
  import {
    getSampleStatData,
    getSapExpStatData,
  } from '@/api/statistics/sample';

  const { proxy } = getCurrentInstance();
  const loadingFlag = ref(false);
  const sapExpLoadingFlag = ref(false);

  const colorArr = [
    '#1981F4',
    '#2ADAC9',
    '#FEA52B',
    '#b6a2de',
    '#5ab1ef',
    '#ffb980',
    '#d87a80',
    '#8d98b3',
    '#e5cf0d',
    '#97b552',
    '#95706d',
    '#dc69aa',
    '#07a2a4',
    '#9a7fd1',
    '#588dd5',
    '#f5994e',
    '#c05050',
    '#59678c',
    '#c9ab00',
    '#7eb00a',
    '#6f5553',
    '#c14089',
  ];

  const statisticData = reactive([]);

  const sampleExpChartData = reactive([]);
  const sampleAllExpType = reactive([]);

  const echartInit = () => {
    const totalData = statisticData.map(
      it => it.accessibleNum + it.unAccessibleNum,
    );

    const stackBarChart = echarts.init(
      document.getElementById('typeStatistic'),
    );
    const stackBarOpt = {
      color: ['#1981F4', '#2ADAC9', '#FEA52B', '#1981F4'],
      tooltip: {
        trigger: 'item',
        confine: true,
        formatter: function (params) {
          if (params.componentSubType === 'line') {
            let item = statisticData[params.dataIndex];
            return `<div style="width: 230px">
                       <div><span class="mr-05">${params.name}:</span> ${params.value}</div>
                       <hr>
                       <div><span class="tooltip-label">Total:</span> ${item.total} ( ${item.totalFileSize} )</div>
                       <div><span class="tooltip-label">Accessible:</span> ${item.accessibleNum} ( ${item.accessibleFileSize} )</div>
                       <div><span class="tooltip-label">UnAccessible:</span> ${item.unAccessibleNum} ( ${item.unAccessibleFileSize} )</div>
                    </div>
                 `;
          } else {
            return `<div>${params.seriesName}</div>
                 <div>${params.value}</div>
                 `;
          }
        },
      },
      legend: {
        top: '4%',
      },
      grid: {
        top: '14%',
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          data: statisticData.map(it => it.type),
          axisLabel: {
            rotate: 40,
            show: true,
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          axisLine: {
            show: true,
          },
          splitLine: {
            show: false,
          },
        },
      ],
      dataZoom: [
        {
          type: 'slider',
          show: true,
          xAxisIndex: [0],
          start: 1,
          end: 80,
        },
      ],
      series: [
        {
          name: 'Accessible',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          barMaxWidth: 25,
          data: statisticData.map(it => it.accessibleNum),
        },
        {
          name: 'UnAccessible',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          data: statisticData.map(it => it.unAccessibleNum),
        },
        {
          name: '总数',
          data: totalData,
          type: 'line',
        },
      ],
    };
    stackBarChart.setOption(stackBarOpt);

    window.onresize = function () {
      stackBarChart.resize();
    };
  };

  const sapExpEchartInit = () => {
    const totalData = sampleExpChartData.map(d =>
      d.arr.reduce((acc, item) => acc + item.expTotal, 0),
    );

    const seriesArr = [];
    sampleAllExpType.forEach(expType => {
      let expArr = [];
      sampleExpChartData.forEach(it => {
        let arr = it.arr;
        let expVal = 0;
        for (let i = 0; i < arr.length; i++) {
          let arrElement = arr[i];
          if (expType === arrElement.expType) {
            expVal = arrElement.expTotal;
            break;
          }
        }
        expArr.push(expVal);
      });

      seriesArr.push({
        name: expType,
        type: 'bar',
        stack: 'Ad',
        emphasis: {
          focus: 'series',
        },
        barMaxWidth: 25,
        data: expArr,
      });
    });

    const omicsProportion = echarts.init(
      document.getElementById('omicsPropStat'),
    );
    const omicsPropOpt = {
      color: colorArr,
      tooltip: {
        trigger: 'axis',
        confine: true,
        axisPointer: {
          type: 'shadow',
        },
      },
      legend: {
        top: '4%',
      },
      grid: {
        top: '18%',
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          data: sampleExpChartData.map(it => it.type),
          axisLabel: {
            rotate: 40,
            show: true,
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          axisLine: {
            show: true,
          },
          splitLine: {
            show: false,
          },
        },
      ],
      dataZoom: [
        {
          type: 'slider',
          show: true,
          xAxisIndex: [0],
          start: 1,
          end: 80,
        },
      ],
      series: [
        ...seriesArr,
        {
          name: 'Total Data',
          data: totalData,
          type: 'line',
        },
      ],
    };
    omicsProportion.setOption(omicsPropOpt);

    window.onresize = function () {
      omicsProportion.resize();
    };
  };

  const exportSampleExcel = () => {
    let name = 'Sample';
    proxy.download(`system/statistics/sap/export`, {}, `${name}.xlsx`);
  };

  const exportSampleExpExcel = () => {
    let name = 'Sample-Experiment';
    proxy.download(`system/statistics/sap/exportSapExp`, {}, `${name}.xlsx`);
  };

  function loadChartData() {
    statisticData.length = 0;
    loadingFlag.value = true;
    getSampleStatData()
      .then(res => {
        loadingFlag.value = false;
        if (res.code === 200) {
          let data = res.data;
          if (data) {
            statisticData.push(...data);
          }
          echartInit();
        }
      })
      .catch(() => {
        loadingFlag.value = false;
      });
  }

  function loadSapExpChart() {
    sampleExpChartData.length = 0;
    sampleAllExpType.length = 0;

    sapExpLoadingFlag.value = true;
    getSapExpStatData()
      .then(res => {
        if (res.code === 200) {
          let data = res.data;
          if (data) {
            const mapData = new Map();
            data.forEach(item => {
              let expType = item.expType;
              if (sampleAllExpType.indexOf(expType) < 0) {
                sampleAllExpType.push(expType);
              }

              let sapType = item.sapType;
              let arr = mapData.get(sapType);
              if (!arr) {
                arr = [];
              }
              arr.push(item);
              mapData.set(sapType, arr);
            });

            mapData.forEach((val, key) => {
              sampleExpChartData.push({
                type: key,
                arr: val,
              });
            });
          }
          sapExpEchartInit();
        }
      })
      .finally(() => {
        sapExpLoadingFlag.value = false;
      });
  }

  onMounted(() => {
    loadChartData();
    loadSapExpChart();
  });
</script>

<style scoped lang="scss">
  :deep(.el-form-item__label) {
    color: #858181;
  }

  :deep(.tooltip-label) {
    display: inline-block;
    width: 95px;
  }

  :deep(.tool-label) {
    display: inline-block;
    width: 110px;
  }

  :deep(.el-select__wrapper) {
    border-radius: 12px;
  }
</style>
