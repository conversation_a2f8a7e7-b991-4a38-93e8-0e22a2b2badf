<template>
  <div v-loading="loadingFlag" class="app-container">
    <div class="card list">
      <el-row :gutter="20">
        <el-col :span="21" :xs="24">
          <h3 class="mb-0 mt-0">数据类型统计</h3>
        </el-col>
        <el-col :span="3" :xs="24" class="text-align-right">
          <el-button
            plain
            type="primary"
            round
            :icon="Download"
            @click="exportDataType"
            >Download
          </el-button>
        </el-col>
      </el-row>
      <el-divider></el-divider>
      <div
        id="statistic"
        style="width: 100%; height: 79vh"
        class="bg-gray radius-12 mb-1 pt-10"
      ></div>
    </div>
  </div>
</template>

<script setup name="Index">
  import { Download } from '@element-plus/icons-vue';
  import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
  import * as echarts from 'echarts';
  import { getDataTypeData } from '@/api/statistics/dataType';

  const { proxy } = getCurrentInstance();
  const loadingFlag = ref(false);

  const statisticData = reactive([]);

  const echartInit = () => {
    // console.log(statisticData);
    const statistiChart = echarts.init(document.getElementById('statistic'));
    let xArr = [];
    let publicArr = [];
    let restrictedArr = [];
    let privatedArr = [];
    let totalArr = [];

    let publicSizeArr = [];
    let restrictedSizeArr = [];
    let privatedSizeArr = [];
    let totalSizeArr = [];

    statisticData.forEach(it => {
      xArr.push(it.type);

      publicArr.push(it.totalPublic);
      restrictedArr.push(it.totalRestricted);
      privatedArr.push(it.totalPrivate);
      totalArr.push(it.total);

      publicSizeArr.push(it.totalPublicFileSize);
      restrictedSizeArr.push(it.totalRestrictedFileSize);
      privatedSizeArr.push(it.totalPrivateFileSize);
      totalSizeArr.push(it.totalFileSize);
    });

    const options = {
      color: ['#1981F4', '#2ADAC9', '#FEA52B'],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        confine: true,
        formatter: function (params) {
          let dataIndex = params[0].dataIndex;
          return `
                 <div><span class="mr-05">${params[0].name}</div>
                 <hr>
                 <div><span class="tooltip-label">Public:</span> ${publicArr[dataIndex]} files / ${publicSizeArr[dataIndex]} </div>
                 <div><span class="tooltip-label">Restricted:</span> ${restrictedArr[dataIndex]} files / ${restrictedSizeArr[dataIndex]} </div>
                 <div><span class="tooltip-label">Private:</span> ${privatedArr[dataIndex]} files / ${privatedSizeArr[dataIndex]} </div>
                 <div><span class="tooltip-label">Total Data:</span> ${totalArr[dataIndex]} files / ${totalSizeArr[dataIndex]} </div>
                 `;
        },
      },
      legend: {
        top: '4%',
      },
      grid: {
        top: '14%',
        left: '3%',
        right: '4%',
        bottom: '7%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          data: xArr,
          axisLabel: {
            rotate: 30,
            show: true,
            margin: 13,
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          axisLine: {
            show: true,
          },
          splitLine: {
            show: false,
          },
        },
      ],
      dataZoom: [
        {
          type: 'slider',
          show: true,
          xAxisIndex: [0],
          start: 1,
          end: 100,
        },
      ],
      series: [
        {
          name: 'Public',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          data: publicArr,
        },
        {
          name: 'Restricted',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          barMaxWidth: 25,
          data: restrictedArr,
        },
        {
          name: 'Private',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          barMaxWidth: 25,
          data: privatedArr,
        },
        {
          name: 'Total data',
          data: totalArr,
          type: 'line',
        },
      ],
    };
    statistiChart.setOption(options);

    window.onresize = function () {
      statistiChart.resize();
    };
  };

  // 数据导出
  const exportDataType = () => {
    let name = 'Data Type';
    proxy.download(`system/statistics/dataType/export`, {}, `${name}.xlsx`);
  };

  function loadChartData() {
    statisticData.length = 0;
    loadingFlag.value = true;
    getDataTypeData()
      .then(res => {
        if (res.code === 200) {
          let data = res.data;
          if (data) {
            statisticData.push(...data);
          }
          echartInit();
        }
      })
      .finally(() => {
        loadingFlag.value = false;
      });
  }

  onMounted(() => {
    loadChartData();
  });
</script>

<style scoped lang="scss">
  :deep(.el-form-item__label) {
    color: #858181;
  }

  :deep(.tooltip-label) {
    display: inline-block;
    width: 80px;
  }
</style>
