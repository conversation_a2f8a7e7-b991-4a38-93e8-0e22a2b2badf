<template>
  <div v-loading="loadingFlag" class="app-container">
    <div class="card list">
      <el-row :gutter="20">
        <el-col :span="21" :xs="24">
          <h3 class="mb-0 mt-0">多组学比例统计（基于项目中的实验类型）</h3>
        </el-col>
        <el-col :span="3" :xs="24" class="text-align-right">
          <el-button
            plain
            type="primary"
            round
            :icon="Download"
            @click="exportPrj"
            >下载
          </el-button>
        </el-col>
      </el-row>
      <el-divider></el-divider>
      <el-row :gutter="0" class="radius-12 mb-1 pt-10">
        <el-col :span="24">
          <el-descriptions title="" border :column="6">
            <el-descriptions-item label="多组学">
              {{ totalRef.multi }}
            </el-descriptions-item>
            <el-descriptions-item label="单组学">
              {{ totalRef.single }}
            </el-descriptions-item>
          </el-descriptions>
        </el-col>
      </el-row>
    </div>
    <div class="card list mt-1">
      <el-row :gutter="20">
        <el-col :span="21" :xs="24">
          <h3 class="mb-0 mt-0">多组学比例统计（基于与样本关联的实验类型）</h3>
        </el-col>
        <el-col :span="3" :xs="24" class="text-align-right">
          <el-button
            plain
            type="primary"
            round
            :icon="Download"
            @click="exportSap"
            >下载
          </el-button>
        </el-col>
      </el-row>
      <el-divider></el-divider>
      <div
        id="omicsPropStat"
        style="width: 100%; height: 60vh"
        class="bg-gray radius-12 mb-1 pt-10"
      ></div>
    </div>
  </div>
</template>

<script setup>
  import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
  import { Download } from '@element-plus/icons-vue';

  import * as echarts from 'echarts';
  import {
    getMultiOmicsSapStat,
    getMultiOmicsStat,
  } from '@/api/statistics/multipleExp';

  const { proxy } = getCurrentInstance();
  const loadingFlag = ref(false);

  function exportPrj() {
    let name = '项目多组学统计';
    proxy.download(`system/statistics/multiOmics/export`, {}, `${name}.xlsx`);
  }

  function exportSap() {
    let name = '样本多组学统计';
    proxy.download(
      `system/statistics/multiOmics/exportSap`,
      {},
      `${name}.xlsx`,
    );
  }

  const totalRef = ref({});

  const omicsPropData = reactive([]);

  const echartInit = () => {
    const totalPropData = omicsPropData.map(
      it => it.multiomics + it.singleOmics,
    );

    const omicsProportion = echarts.init(
      document.getElementById('omicsPropStat'),
    );
    const omicsPropOpt = {
      color: ['#1981F4', '#2ADAC9', '#FEA52B', '#1981F4'],
      tooltip: {
        trigger: 'item',
        confine: true,
        formatter: function (params) {
          if (params.componentSubType === 'line') {
            return `<div>
                        <span class="mr-05">${params.name}:</span>
                        ${params.value}
                    </div>
                    <hr>
                    <div>
                       <span class="tooltip-label">多组学:</span>
                       <span>${omicsPropData[params.dataIndex].multiomics} ${percentVal(omicsPropData[params.dataIndex].multiomics, totalPropData[params.dataIndex])}</span>
                    </div>

                    <div>
                       <span class="tooltip-label">单组学:</span>
                       <span>${omicsPropData[params.dataIndex].singleOmics} ${percentVal(omicsPropData[params.dataIndex].singleOmics, totalPropData[params.dataIndex])}</span>
                    </div>
                `;
          } else {
            return `<div>${params.seriesName}</div>
                    <div>${params.value}</div>`;
          }
        },
      },
      legend: {
        top: '4%',
      },
      grid: {
        top: '12%',
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          data: omicsPropData.map(it => it.type),
          axisLabel: {
            rotate: 40,
            show: true,
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          axisLine: {
            show: true,
          },
          splitLine: {
            show: false,
          },
        },
      ],
      dataZoom: [
        {
          type: 'slider',
          show: true,
          xAxisIndex: [0],
          start: 1,
          end: 100,
        },
      ],
      series: [
        {
          name: '多组学',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          barMaxWidth: 25,
          data: omicsPropData.map(it => it.multiomics),
        },
        {
          name: '单组学',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          data: omicsPropData.map(it => it.singleOmics),
        },
        {
          name: '总数据',
          data: totalPropData,
          type: 'line',
        },
      ],
    };
    omicsProportion.setOption(omicsPropOpt);

    window.onresize = function () {
      omicsProportion.resize();
    };
  };

  function loadChartData() {
    getMultiOmicsStat()
      .then(res => {
        if (res.code === 200) {
          totalRef.value = res.data;
        }
      })
      .catch(() => {});

    loadingFlag.value = true;
    omicsPropData.length = 0;
    getMultiOmicsSapStat()
      .then(res => {
        if (res.code === 200 && res.data) {
          res.data.forEach(item => {
            omicsPropData.push({
              type: item.type,
              multiomics: item.multi,
              singleOmics: item.single,
            });
          });
        }
        echartInit();
      })
      .finally(() => {
        loadingFlag.value = false;
      });
  }

  function percentVal(v1, v2) {
    return v2 === 0 ? '' : ` (${Number(((v1 / v2) * 100).toFixed(2))}%)`;
  }

  onMounted(() => {
    loadChartData();
  });
</script>

<style scoped lang="scss">
  :deep(.el-form-item__label) {
    color: #858181;
  }

  :deep(.tooltip-label) {
    display: inline-block;
    width: 110px;
  }
</style>
