<template>
  <div v-loading="loadingFlag" class="app-container">
    <div class="card list mb-1">
      <h3 class="mb-0 mt-0">频次和数据量</h3>
      <el-divider></el-divider>
      <el-row :gutter="15">
        <el-col :span="8" :xs="24">
          <div class="request radius-14">
            <div class="before-circle font-600 accessible font-20">申请</div>
            <div class="bg-gray mb-05 item-number">
              <span>申请次数</span>
              <div>
                <span>{{ totalDataRef.request }}</span>
                <span>次</span>
              </div>
            </div>
            <div class="bg-gray item-number">
              <span>申请数据量</span>
              <div>
                <span>{{ totalDataRef.requestFileNum }}</span>
                <span>个文件</span>
                <span class="ml-05 mr-05">/</span>
                <span>{{ parseSizeNum(totalDataRef.requestFileSize) }}</span>
                <span>{{ parseSizeUnit(totalDataRef.requestFileSize) }}</span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="8" :xs="24">
          <div class="share radius-14">
            <div class="before-circle font-600 accessible font-20">分享</div>
            <div class="bg-gray mb-05 item-number">
              <span>分享次数</span>
              <div>
                <span>{{ totalDataRef.share }}</span>
                <span>次</span>
              </div>
            </div>
            <div class="bg-gray item-number">
              <span>分享数据量</span>
              <div>
                <span>{{ totalDataRef.shareFileNum }}</span>
                <span>个文件</span>
                <span class="ml-05 mr-05">/</span>
                <span>{{ parseSizeNum(totalDataRef.shareFileSize) }}</span>
                <span>{{ parseSizeUnit(totalDataRef.shareFileSize) }}</span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="8" :xs="24">
          <div class="review radius-14">
            <div class="before-circle font-600 accessible font-20">审阅</div>
            <div class="bg-gray mb-05 item-number">
              <span>审阅次数</span>
              <div>
                <span>{{ totalDataRef.review }}</span>
                <span>次</span>
              </div>
            </div>
            <div class="bg-gray item-number">
              <span>审阅数据量</span>
              <div>
                <span>{{ totalDataRef.reviewFileNum }}</span>
                <span>个文件</span>
                <span class="ml-05 mr-05">/</span>
                <span>{{ parseSizeNum(totalDataRef.reviewFileSize) }}</span>
                <span>{{ parseSizeUnit(totalDataRef.reviewFileSize) }}</span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="card list">
      <el-row :gutter="20">
        <el-col :span="21" :xs="24">
          <h3 class="mb-0 mt-0">数据分享统计</h3>
        </el-col>
        <el-col :span="3" :xs="24" class="text-align-right">
          <el-button
            plain
            type="primary"
            round
            :icon="Download"
            @click="exportShareExcel"
          >
            下载
          </el-button>
        </el-col>
      </el-row>
      <el-divider></el-divider>
      <div
        id="statistic"
        class="bg-gray radius-12 mb-1 pt-10"
        style="width: 100%; height: 56vh"
      ></div>
    </div>
  </div>
</template>

<script setup name="Index">
  import { Download } from '@element-plus/icons-vue';
  import { getCurrentInstance, onMounted, reactive, ref } from 'vue';

  import * as echarts from 'echarts';
  import { getShareStatData } from '@/api/statistics/dataShare';

  const { proxy } = getCurrentInstance();
  const loadingFlag = ref(false);

  const totalDataRef = ref({
    request: 0,
    requestFileNum: 0,
    requestFileSize: '0',
    review: 0,
    reviewFileNum: 0,
    reviewFileSize: '0',
    share: 0,
    shareFileNum: 0,
    shareFileSize: '0',
  });

  function parseSizeNum(size = '') {
    return size.split(' ')[0];
  }

  function parseSizeUnit(size = '') {
    return size.split(' ')[1];
  }

  const statisticData = reactive([
    {
      date: '2014',
      request: 60,
      share: 40,
      review: 30,
    },
    {
      date: '2015',
      request: 60,
      share: 45,
      review: 67,
    },
    {
      date: '2016',
      request: 60,
      share: 87,
      review: 24,
    },
    {
      date: '2017',
      request: 60,
      share: 53,
      review: 63,
    },
    {
      date: '2018',
      request: 34,
      share: 40,
      review: 30,
    },
    {
      date: '2019',
      request: 46,
      share: 64,
      review: 35,
    },
    {
      date: '2020',
      request: 75,
      share: 54,
      review: 56,
    },
    {
      date: '2021',
      request: 60,
      share: 40,
      review: 30,
    },
    {
      date: '2022',
      request: 60,
      share: 40,
      review: 30,
    },
    {
      date: '2023',
      request: 60,
      share: 40,
      review: 30,
    },
  ]);

  const echartInit = () => {
    const totalData = statisticData.map(
      it => it.request + it.share + it.review,
    );

    const statistiChart = echarts.init(document.getElementById('statistic'));
    const options = {
      color: ['#1981F4', '#2ADAC9', '#FEA52B', '#1981F4'],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        confine: true,
      },
      legend: {
        top: '4%',
      },
      grid: {
        top: '14%',
        left: '3%',
        right: '4%',
        bottom: '5%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          data: statisticData.map(it => it.year),
        },
      ],
      yAxis: [
        {
          type: 'value',
          axisLine: {
            show: true,
          },
          splitLine: {
            show: false,
          },
        },
      ],
      series: [
        {
          name: '申请',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          barMaxWidth: 25,
          data: statisticData.map(it => it.request),
        },
        {
          name: '分享',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          data: statisticData.map(it => it.share),
        },
        {
          name: '审阅',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          data: statisticData.map(it => it.review),
        },
        {
          name: '总数据',
          data: totalData,
          type: 'line',
        },
      ],
    };
    statistiChart.setOption(options);

    window.onresize = function () {
      statistiChart.resize();
    };
  };

  // 数据导出
  const exportShareExcel = () => {
    let name = '数据分享统计';
    proxy.download(`system/statistics/share/export`, {}, `${name}.xlsx`);
  };

  onMounted(() => {
    loadingFlag.value = true;
    statisticData.length = 0;
    getShareStatData()
      .then(res => {
        let data = res.data;
        if (data) {
          let chartData = data.chartData;
          if (chartData) {
            statisticData.push(...chartData);
          }
          totalDataRef.value = data;
        }
        echartInit();
      })
      .finally(() => {
        loadingFlag.value = false;
      });
  });
</script>

<style scoped lang="scss">
  :deep(.el-form-item__label) {
    color: #858181;
  }

  .request,
  .share,
  .review {
    border: 1px solid #3a78e8;
    padding: 6px 6px 20px 6px;

    .before-circle:before {
      height: 7px;
      width: 7px;
      background-color: #3a78e8;
    }
  }

  .share {
    border: 1px solid #2adac9 !important;

    .before-circle:before {
      background-color: #2adac9 !important;
    }
  }

  .review {
    border: 1px solid #fea52b !important;

    .before-circle:before {
      background-color: #fea52b !important;
    }
  }

  .item-number {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 5px 15px;

    span:first-child {
      font-size: 15px;
      color: #666666;
      font-weight: 600;
    }

    div > span:nth-child(1),
    div > span:nth-child(4) {
      font-size: 19px;
      font-weight: 600;
      color: #333333;
      margin-right: 8px;
    }

    div > span:nth-child(2),
    div > span:last-child {
      color: #999999;
    }
  }
</style>
