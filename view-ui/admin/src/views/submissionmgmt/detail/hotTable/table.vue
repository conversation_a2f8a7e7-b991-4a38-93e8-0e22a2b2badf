<template>
  <div>
    <div class="d-flex align-items-center justify-space-between mt-05">
      <div :style="{ opacity: subType ? 1 : 0 }">
        <span class="font-600 text-secondary-color">类型：</span>
        <span>{{ subType }}</span>
      </div>
      <div class="d-flex">
        <el-popover
          v-model:visible="visible"
          :width="400"
          placement="bottom"
          trigger="hover"
        >
          <template #reference>
            <el-button
              type="primary"
              :icon="Menu"
              class="radius-12"
              size="small"
              @click="openPopover"
            >
              列可见性
            </el-button>
          </template>
          <!--全选框-->
          <div class="d-flex mt-1">
            <el-checkbox
              v-model="checkAll"
              @change="handleCheckAllChange"
            ></el-checkbox>
            <el-input
              v-model.trim="filterText"
              class="w-85 ml-05"
              placeholder="搜索列"
              clearable
              @clear="clearable"
            />
          </div>

          <!--列名-->
          <el-checkbox-group
            v-model="checkList"
            class="d-flex flex-wrap"
            style="overflow-y: auto; max-height: 300px"
          >
            <el-checkbox
              v-for="item in hotColumns"
              v-show="item.show"
              :key="item.title"
              :label="item.title"
              @change="changeRadioStatus"
              >{{ item.title }}
            </el-checkbox>
          </el-checkbox-group>
          <div class="d-flex mt-1 justify-center">
            <el-button type="success" size="small" @click="selectAll">
              全选
            </el-button>
            <el-button
              type="primary"
              class="popover-btn"
              :size="'small'"
              @click="Confirm"
              >确认
            </el-button>
            <el-button type="info" size="small" @click="defaultSet">
              默认
            </el-button>
          </div>
        </el-popover>

        <el-button
          type="info"
          :icon="Download"
          class="radius-12 ml-1"
          size="small"
          @click="exportTableData"
        >
          导出
        </el-button>
      </div>
    </div>
    <div :id="id" class="pos-relative mt-05 text-center"></div>
  </div>
</template>

<script setup>
  import { defineProps, onMounted, reactive, ref, watch } from 'vue';
  import { Download, Menu } from '@element-plus/icons-vue';
  import Handsontable from 'handsontable';
  import { createAccessToken } from '@/api/login';

  let hot = null;
  let plugin = null;
  const props = defineProps({
    hotTable: {
      type: Object,
    },
    hotColumns: {
      type: Object,
    },
    id: {
      type: String,
    },
    subType: {
      type: String,
      required: false,
      default: null,
    },
    type: {
      type: String,
      default: null,
    },
    creator: {
      type: String,
      default: null,
    },
  });
  const hotTable = ref(props.hotTable);
  const hotColumns = ref(props.hotColumns);
  const creator = ref(props.creator);

  const type = reactive(props.type);
  const subType = reactive(props.subType);
  const id = reactive(props.id);

  onMounted(() => {
    if (!creator.value) {
      return;
    }
    // 预先生成access_token
    createAccessToken({ memberId: creator.value }).then(response => {
      const token = response.data;

      if (hotColumns?.value) {
        // 给有详情页面的几种类型增加跳转链接
        hotColumns.value = hotColumns.value.map(str => {
          if (
            str === 'project_id' ||
            str === 'experiment_id' ||
            str === 'sample_id' ||
            str === 'analysis_id'
          ) {
            return {
              title: str,
              type: 'text',
              data: str,
              show: true,
              renderer: (instance, td, row, col, prop, value) => {
                let toType = undefined;
                if (value.includes('OEP')) {
                  toType = 'project';
                }
                if (value.includes('OEX')) {
                  toType = 'experiment';
                }
                if (value.includes('OES')) {
                  toType = 'sample';
                }
                if (value.includes('OEZ')) {
                  toType = 'analysis';
                }
                if (toType) {
                  while (td.firstChild) {
                    td.removeChild(td.firstChild);
                  }

                  const link = document.createElement('a');
                  link.href = `${
                    import.meta.env.VITE_APP_WEB_URL
                  }/${toType}/detail/${value}?access-token=${token}`;

                  link.textContent = value;
                  link.target = '_blank';
                  td.appendChild(link);
                } else {
                  td.textContent = value;
                }
              },
            };
          } else {
            return {
              title: str,
              type: 'text',
              data: str,
              show: true,
            };
          }
        });
      }

      const regex = /^[0-9a-z]{32}$/; // 匹配32位的UUID字符串的正则表达式

      hotTable.value = hotTable.value.map(item => {
        const newItem = Object.assign({}, item); // 创建一个新对象来存储修改后的值
        for (const key in newItem) {
          // eslint-disable-next-line no-prototype-builtins
          if (newItem.hasOwnProperty(key) && key.endsWith('_id')) {
            const value = newItem[key];
            if (regex.test(value)) {
              newItem[key] = ''; // 将符合条件的字段替换为空串
            }
          }
        }
        return newItem;
      });

      let tbHeight = 50 + hotTable.value.length * 23;
      if (tbHeight > 330) {
        tbHeight = 400;
      }

      const container = document.getElementById(id);
      hot = new Handsontable(container, {
        data: hotTable.value,
        colHeaders: true,
        columns: hotColumns.value,
        afterGetColHeader: function (col, th) {
          th.classList.add('rowHeader');
        },
        comments: true,
        currentRowClassName: 'currentRow', // 突出显示行
        currentColClassName: 'currentCol', // 突出显示列
        height: tbHeight, // 自动高度
        autoColumnSize: true,
        stretchH: 'last',
        width: '100%',
        rowHeaders: false, // 显示行号
        copyable: false, // 允许复制
        copyPaste: false, //复制粘贴
        filters: true, // 使用过滤功能
        readOnly: true,
        manualColumnResize: true,
        dropdownMenu: [
          'filter_by_condition',
          'filter_by_value',
          'filter_action_bar',
        ], // 下拉菜单具体功能
        columnSorting: true, // 开启排序
        contextMenu: false,
        licenseKey: 'non-commercial-and-evaluation', //去除底部非商用声明
        hiddenColumns: {
          columns: [], // 要隐藏的列索引数组
        },
      });

      plugin = hot.getPlugin('hiddenColumns');

      initColVisible();
    });
  });

  const visible = ref(false);
  const checkList = ref([]);
  const checkAll = ref(false);
  const filterText = ref('');
  const defaultCheckLish = [];
  const map = new Map();

  function initColVisible() {
    const colIndexes = hotColumns.value.map((item, index) => index);
    hotColumns.value.forEach(item => {
      if (item.colShow) {
        defaultCheckLish.push(item.title);
        checkList.value.push(item.title);
        const showIndex = checkList.value.map(item =>
          hotColumns.value.findIndex(obj => obj.title === item),
        );
        plugin.hideColumns(colIndexes);
        plugin.showColumns(showIndex);
      }
    });
    checkAll.value = checkList.value.length === hotColumns.value.length;
  }

  const getShowCol = () => {
    hotColumns.value.forEach(item => {
      if (item.show) {
        map.set(item.title, item.title);
      }
    });
  };

  // 全选
  const handleCheckAllChange = val => {
    getShowCol();
    map.forEach((item, key) => {
      if (val) {
        checkList.value.push(key);
      } else {
        const index = checkList.value.indexOf(key);
        if (index > -1) {
          checkList.value.splice(index, 1);
        }
      }
    });
    checkList.value = [...new Set(checkList.value)];
    map.clear();
  };

  // 勾选
  const changeRadioStatus = () => {
    getShowCol();
    checkAll.value = Array.from(map.values()).every(value =>
      checkList.value.includes(value),
    );
    map.clear();
  };

  const clearable = () => {
    checkAll.value = hotColumns.value.length === checkList.value.length;
  };

  //select all
  const selectAll = () => {
    checkAll.value = true;
    handleCheckAllChange(true);
  };

  //列显隐
  const Confirm = () => {
    const columnIndexes = hotColumns.value.map((item, index) => index);
    const showIndex = checkList.value.map(item =>
      hotColumns.value.findIndex(obj => obj.title === item),
    );
    plugin.hideColumns(columnIndexes);
    plugin.showColumns(showIndex);
    hot.render();
    visible.value = false;
  };

  const openPopover = () => {
    visible.value = !visible.value;
    filterText.value = '';
    clearable();
  };

  //初始化默认显隐
  const defaultSet = () => {
    checkList.value = [];
    defaultCheckLish.forEach(it => {
      checkList.value.push(it);
    });
    changeRadioStatus();
  };

  watch(filterText, newValue => {
    if (newValue) {
      hotColumns.value.forEach(ele => {
        ele.show =
          ele.title.toLowerCase().indexOf(newValue.toLowerCase()) !== -1;
      });
    } else {
      hotColumns.value.forEach(ele => {
        ele.show = true;
      });
    }
  });

  function exportTableData() {
    if (hot) {
      let fileName = '';
      if (type) {
        fileName = type;
      }
      if (subType) {
        fileName = type + ' ' + subType;
      }
      fileName += '-[YYYY]-[MM]-[DD]';
      const exportPlugin = hot.getPlugin('exportFile');
      exportPlugin.downloadFile('csv', {
        bom: false,
        columnDelimiter: '\t',
        columnHeaders: true,
        exportHiddenColumns: true,
        exportHiddenRows: true,
        fileExtension: 'tsv',
        filename: fileName,
        mimeType: 'text/tab-separated-values',
        rowDelimiter: '\r\n',
        rowHeaders: false,
      });
    }
  }
</script>

<style lang="scss" scoped>
  .el-checkbox-group {
    :deep(.el-checkbox) {
      width: 100%;
      margin-right: 65px;
    }

    :deep(.el-checkbox__label) {
      font-size: 12px;
    }
  }

  .el-tag {
    :deep(.el-tag__content) {
      display: flex;
      align-items: center;
      font-weight: 600;
      color: #ffffff;
      font-size: 14px;
    }
  }
</style>
