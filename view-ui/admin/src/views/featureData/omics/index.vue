<template>
  <div class="app-container">
    <el-row :gutter="15">
      <el-col :span="24">
        <div class="card list">
          <el-form
            v-show="showSearch"
            ref="searchFormRef"
            :model="queryParams"
            :inline="true"
          >
            <el-form-item label="项目ID" prop="projID">
              <el-input
                v-model="queryParams.projID"
                clearable
                style="width: 300px"
                @keyup.enter="doSearch"
              ></el-input>
            </el-form-item>
            <el-form-item label="项目名称" prop="projName">
              <el-input
                v-model="queryParams.projName"
                clearable
                style="width: 300px"
                @keyup.enter="doSearch"
              ></el-input>
            </el-form-item>
            <el-form-item label="实验类型" prop="expType">
              <el-input
                v-model="queryParams.expType"
                clearable
                style="width: 300px"
                @keyup.enter="doSearch"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="doSearch"
                >搜索
              </el-button>
              <el-button icon="Refresh" @click="resetForm(0)">重置</el-button>
            </el-form-item>
          </el-form>
          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button type="primary" icon="Plus" @click="handleAdd"
                >新增
              </el-button>
            </el-col>

            <el-col :span="1.5">
              <el-button type="info" icon="Upload" @click="handleImport"
                >导入
              </el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button type="warning" icon="Download" @click="handleExport"
                >导出
              </el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                :disabled="queryParams.loading"
                type="primary"
                icon="Open"
                @click="doEnable(false)"
                >启用
              </el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                :disabled="queryParams.loading"
                type="danger"
                icon="TurnOff"
                @click="doEnable(true)"
                >禁用
              </el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="danger"
                icon="Delete"
                :disabled="multiple"
                @click="handleBatchDelete"
                >删除
              </el-button>
            </el-col>
          </el-row>

          <el-table
            v-loading="queryParams.loading"
            :data="omicsData"
            style="width: 100%; margin-bottom: 20px"
            :header-cell-style="{
              backgroundColor: '#f2f2f2',
              color: '#333333',
              fontWeight: 700,
            }"
            border
            height="76vh"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" show-overflow-tooltip />
            <el-table-column
              prop="projID"
              label="项目ID"
              width="120"
              show-overflow-tooltip
            >
              <template #default="scope">
                <a
                  target="_blank"
                  class="text-primary"
                  :href="`${webAppUrl}${scope.row.projID}`"
                  >{{ scope.row.projID }}</a
                >
              </template>
            </el-table-column>

            <el-table-column
              prop="expType"
              label="实验类型"
              min-width="220"
              show-overflow-tooltip
            >
              <template #default="scope">
                <template
                  v-for="(item, index) in scope.row.expTypes"
                  :key="`ex_type_${scope.$index}_${index}`"
                >
                  {{
                    index < scope.row.expTypes.length - 1 ? item + '、' : item
                  }}
                </template>
              </template>
            </el-table-column>
            <el-table-column
              prop="projName"
              label="项目名称"
              min-width="220"
              show-overflow-tooltip
            />
            <el-table-column
              prop="des"
              label="描述"
              min-width="200"
              show-overflow-tooltip
            />
            <el-table-column prop="submitter" label="提交人" width="180" />
            <el-table-column prop="status" label="状态" width="90">
              <template #default="scope">
                <el-switch
                  v-model="scope.row.status"
                  active-value="enable"
                  inactive-value="disable"
                  @change="val => doChangeStatus(scope.row.id, val)"
                ></el-switch>
              </template>
            </el-table-column>

            <el-table-column
              prop="modifiedDate"
              label="修改时间"
              width="160"
              show-overflow-tooltip
            >
              <template #default="scope">
                {{ formatDate(scope.row.modifiedDate) }}
              </template>
            </el-table-column>
          </el-table>

          <!--<pagination
            v-show="queryParams.total > 0"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            :total="queryParams.total"
            @pagination="getList"
          />-->
        </div>
      </el-col>
    </el-row>

    <!--   添加对话框-->
    <el-dialog
      v-model="open"
      title="选择多组学"
      width="85%"
      class="exp-dialog"
      @opened="getDialogList"
    >
      <el-row v-loading="dialogQueryParams.loading" :gutter="10">
        <el-col :span="4">
          <div class="card h-100">
            <el-tree
              ref="treeRef"
              style="max-width: 600px"
              :data="treeData"
              :props="treeProps"
              :default-checked-keys="defaultCheckedKeys"
              node-key="id"
              show-checkbox
              default-expand-all
              highlight-current
              @check="(data, checkInfo) => handleTreeCheck(data, checkInfo)"
            />
          </div>
        </el-col>
        <el-col :span="20">
          <div class="card list">
            <el-form
              v-show="showSearch"
              :model="dialogQueryParams"
              :inline="true"
            >
              <el-form-item label="项目ID">
                <el-input
                  v-model="dialogQueryParams.projID"
                  clearable
                  style="width: 200px"
                ></el-input>
              </el-form-item>
              <el-form-item label="项目名称">
                <el-input
                  v-model="dialogQueryParams.projName"
                  clearable
                  style="width: 200px"
                ></el-input>
              </el-form-item>
              <!--
              <el-form-item label="Experiment Type">
                <el-input
                  v-model="dialogQueryParams.expType"
                  clearable
                  style="width: 200px"
                ></el-input>
              </el-form-item>
              -->
              <el-form-item>
                <el-button type="primary" icon="Search" @click="doDialogSearch"
                  >搜索
                </el-button>
                <el-button icon="Refresh" @click="resetForm(1)"
                  >重置
                </el-button>
              </el-form-item>
            </el-form>

            <el-table
              :key="`addMulOmiTb_${openTimes}`"
              ref="dialogTbRef"
              :data="omicsDialogData"
              style="width: 100%; margin-bottom: 20px"
              :header-cell-style="{
                backgroundColor: '#f2f2f2',
                color: '#333333',
                fontWeight: 700,
              }"
              border
              height="560"
              @select-all="dialogHandleSelect"
              @select="dialogHandleSelect"
            >
              <el-table-column type="selection" />
              <el-table-column prop="projID" label="项目ID" width="120">
                <template #default="scope">
                  <a
                    target="_blank"
                    class="text-primary"
                    :href="`${webAppUrl}${scope.row.projID}`"
                    >{{ scope.row.projID }}</a
                  >
                </template>
              </el-table-column>

              <el-table-column
                prop="expType"
                label="实验类型"
                min-width="220"
                show-overflow-tooltip
              >
                <template #default="scope">
                  <template
                    v-for="(item, index) in scope.row.expTypes"
                    :key="`ex_type_${scope.$index}_${index}`"
                  >
                    {{
                      index < scope.row.expTypes.length - 1 ? item + '、' : item
                    }}
                  </template>
                </template>
              </el-table-column>
              <el-table-column
                prop="projName"
                label="项目名称"
                min-width="220"
                show-overflow-tooltip
              />
              <el-table-column
                prop="des"
                label="描述"
                min-width="200"
                show-overflow-tooltip
              />
              <el-table-column prop="submitter" label="提交人" width="180" />

              <el-table-column
                prop="modifiedDate"
                label="修改时间"
                width="160"
                show-overflow-tooltip
              >
                <template #default="scope">
                  {{ formatDate(scope.row.modifiedDate) }}
                </template>
              </el-table-column>
            </el-table>

            <pagination
              v-show="dialogQueryParams.total > 0"
              v-model:page="dialogQueryParams.pageNum"
              v-model:limit="dialogQueryParams.pageSize"
              :total="dialogQueryParams.total"
              @pagination="getDialogList"
            />

            <el-row class="bg-gray p-10-15 mb-1 mt-2">
              <el-col :span="5">
                <span class="text-primary mr-05 font-600">{{
                  selectTag.size
                }}</span>
                <span>项已选择</span>
                <span
                  class="text-primary font-600 ml-1 cursor-pointer"
                  @click="handleClear"
                  >清除</span
                >
              </el-col>
              <el-col :span="19">
                <div class="d-flex flex-wrap gap-12">
                  <el-tag
                    v-for="(item, index) in selectTag"
                    :key="`tag_${item[0]}_${index}`"
                    round
                    closable
                    @close="eve => deleteSingleTag(eve, item[0])"
                  >
                    {{ item[0] }}
                  </el-tag>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-col>
      </el-row>
      <template #footer>
        <div class="text-center">
          <el-button type="primary" @click="addData">确认</el-button>
          <el-button @click="open = false">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 数据导入对话框 -->
    <el-dialog
      v-model="upload.open"
      :title="upload.title"
      width="600px"
      append-to-body
    >
      <el-row>
        <el-col :span="24">
          <div style="margin-bottom: 8px">
            <el-alert
              :closable="false"
              title="请在每行输入一个项目ID。"
              type="warning"
              show-icon
            />
          </div>
        </el-col>
        <el-col :span="24">
          <el-form :model="upload" label-width="auto">
            <el-form-item label="项目ID">
              <el-input
                v-model="upload.batchImportNosStr"
                :rows="10"
                type="textarea"
              />
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确认</el-button>
          <el-button @click="upload.open = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import {
    getCurrentInstance,
    nextTick,
    reactive,
    ref,
    toRaw,
    toRefs,
  } from 'vue';
  import {
    batchDeleteMultOmic,
    batchImportMultOmic,
    batchUpdateMultOmicStatus,
    fdMultOmicResDialogList,
    fdMultOmicResList,
    saveMultOmicRes,
  } from '@/api/featureData/omics';
  import { formatDate, isArrEmpty } from '@/utils';

  const { proxy } = getCurrentInstance();
  const data = reactive({
    treeData: [],
    defaultCheckedKeys: [],
    queryParams: {
      pageNum: 1,
      pageSize: 1000,
      loading: false,
      total: 0,
      projID: '',
      projName: '',
      expType: '',
    },
  });

  const { queryParams, treeData, defaultCheckedKeys } = toRefs(data);
  const dialogQueryParams = ref({ ...toRaw(data.queryParams) });

  /*** 用户导入参数 */
  const upload = reactive({
    // 是否显示弹出层（用户导入）
    open: false,
    // 弹出层标题（用户导入）
    title: '',
    isUploading: false,
    batchImportNosStr: '',
  });

  const treeProps = {
    children: 'data',
    label: 'name',
  };
  // 左侧选中数据
  const leftStatQueries = reactive([]);

  const showSearch = ref(true);
  const multiple = ref(true);
  const multipleSelection = ref([]);

  const webAppUrl = ref(import.meta.env.VITE_APP_WEB_URL + '/project/detail/');
  const omicsData = reactive([]);
  const omicsDialogData = reactive([]);

  const open = ref(false);
  const openTimes = ref(1);

  const selectTag = ref(new Map());

  /** 选择条数  */
  function handleSelectionChange(selection) {
    const len = selection.length;
    multiple.value = len === 0;
    let ids = [];
    for (let i = 0; i < len; i++) {
      ids.push(selection[i].id);
    }
    multipleSelection.value = ids;
  }

  /** 新增按钮操作 */
  function handleAdd() {
    dialogQueryParams.value.pageSize = 10;
    // 重置表格上方查询条件
    resetForm(1);
    // 重置左侧树勾选相关数据
    leftStatQueries.length = 0;
    defaultCheckedKeys.value = [];
    resetTreeCheck();
    // 重置下方选中tag相关数据
    handleClear();
    // 打开dialog弹窗
    open.value = true;
    openTimes.value = openTimes.value + 1;
  }

  /** 弹窗搜索按钮 */
  function doDialogSearch() {
    dialogQueryParams.value.pageNum = 1;
    getDialogList();
  }

  /** 重置搜索表单 */
  function resetForm(isDialog) {
    if (isDialog) {
      dialogQueryParams.value.projID = '';
      dialogQueryParams.value.projName = '';
      dialogQueryParams.value.expType = '';
    } else {
      queryParams.value.projID = '';
      queryParams.value.projName = '';
      queryParams.value.expType = '';
    }
  }

  /** 弹窗列表数据查询 */
  function getDialogList() {
    omicsDialogData.length = 0;
    dialogQueryParams.value.loading = true;
    if (leftStatQueries.length > 0) {
      dialogQueryParams.value.leftStatQueries = leftStatQueries;
    }
    fdMultOmicResDialogList(dialogQueryParams.value)
      .then(res => {
        dialogQueryParams.value.loading = false;
        const resultData = res.data;
        if (resultData) {
          treeData.value = resultData.esTreeItems;
          let { pageInfo } = resultData;
          if (pageInfo) {
            dialogQueryParams.value.total = pageInfo.total;
            omicsDialogData.push(...pageInfo.list);

            // 选中已存在的tag对应的行
            nextTick(() => {
              omicsDialogData.forEach(item => {
                if (selectTag.value.has(item.id)) {
                  proxy.$refs['dialogTbRef']?.toggleRowSelection(item, true);
                }
              });
            });
          } else {
            dialogQueryParams.value.total = 0;
          }
          defaultCheckedKeys.value = resultData.defaultCheckedKeys;
          resetTreeCheck();
        }
      })
      .catch(() => {
        dialogQueryParams.value.loading = false;
      });
  }

  function resetTreeCheck() {
    proxy.$refs['treeRef']?.setCheckedKeys(defaultCheckedKeys.value);
  }

  /** 选择条数  */
  function dialogHandleSelect(selection) {
    // 判断当前页面是否存在取消勾选的数据
    const currPageSelect = selection.map(it => it.id);
    omicsDialogData.forEach(item => {
      let prjId = item.id;
      if (!currPageSelect.includes(prjId)) {
        selectTag.value.delete(prjId);
      }
    });
    // 将选中数据，添加到下方列表
    selection.forEach(item => {
      selectTag.value.set(item.id, item);
    });
  }

  /** 清除按钮操作 */
  function handleClear() {
    selectTag.value = new Map();
    proxy.$refs['dialogTbRef']?.clearSelection();
  }

  /** 删除单个下方tag */
  function deleteSingleTag(eve, key) {
    selectTag.value.delete(key);
    const length = omicsDialogData.length;
    for (let i = 0; i < length; i++) {
      let row = omicsDialogData[i];
      if (row.id === key) {
        proxy.$refs['dialogTbRef']?.toggleRowSelection(row, false);
        break;
      }
    }
  }

  /** 弹窗左侧树复选框点击事件 */
  function handleTreeCheck(data, checkInfo) {
    const { checkedNodes } = checkInfo;
    const len = checkedNodes.length;
    leftStatQueries.length = 0;
    for (let i = 0; i < len; i++) {
      let item = checkedNodes[i];
      if (isArrEmpty(item.data)) {
        leftStatQueries.push(item.fieldName);
      }
    }
    resetForm(1);
    getDialogList();
  }

  /** 添加新数据 */
  function addData() {
    const selectValMap = selectTag.value;
    if (selectValMap.size === 0) {
      proxy.$modal.alertWarning('请至少选择一条数据');
      return false;
    }
    let ids = [];
    for (let key of selectValMap.keys()) {
      ids.push(key);
    }
    dialogQueryParams.value.loading = true;
    saveMultOmicRes(ids)
      .then(res => {
        dialogQueryParams.value.loading = false;
        if (res.data) {
          proxy.$modal.msgSuccess('新增成功');
          open.value = false;
          doSearch();
        }
      })
      .catch(() => {
        dialogQueryParams.value.loading = false;
      });
  }

  /** 添加新数据 */
  function getList() {
    omicsData.length = 0;
    queryParams.value.loading = true;
    fdMultOmicResList(queryParams.value)
      .then(res => {
        queryParams.value.loading = false;
        const resultData = res.data;
        if (resultData) {
          let { totalElements, content } = resultData;
          queryParams.value.total = totalElements;
          omicsData.push(...content);
        } else {
          queryParams.value.total = 0;
        }
      })
      .catch(() => {
        queryParams.value.loading = false;
      });
  }

  // 检索按钮
  function doSearch() {
    queryParams.value.pageNum = 1;
    getList();
  }

  // 导出
  function handleExport() {
    proxy.download(
      `/system/fdMultOmic/download`,
      {},
      `${initTitle()}_${new Date().getTime()}.xlsx`,
    );
  }

  function initTitle() {
    return '多组学资源';
  }

  // 启用(禁用)
  function doEnable(disable) {
    if (queryParams.value.total > 0) {
      const param = {
        ids: multipleSelection.value,
        disable: disable,
      };
      let title;
      if (param.ids.length === 0) {
        title = disable ? '禁用所有数据' : '启用所有数据';
      } else {
        title = disable ? '禁用选中数据' : '启用选中数据';
      }
      proxy.$modal
        .confirm(title)
        .then(function () {
          queryParams.value.loading = true;
          batchUpdateMultOmicStatus(param)
            .then(res => {
              queryParams.value.loading = false;
              if (res.data) {
                getList();
              }
            })
            .catch(() => {
              queryParams.value.loading = false;
            });
        })
        .catch(() => {
          // console.log(2);
        });
    }
  }

  // 单行数据状态变更
  function doChangeStatus(id, status) {
    if (queryParams.value.total > 0) {
      const param = {
        ids: [id],
        disable: status === 'disable',
      };
      batchUpdateMultOmicStatus(param).then(res => {
        if (res.data) {
          // getList();
        }
      });
    }
  }

  // 批量导入
  function submitFileForm() {
    queryParams.value.loading = true;
    batchImportMultOmic(upload)
      .then(res => {
        queryParams.value.loading = false;
        if (res.data) {
          proxy.$modal.msgSuccess('批量导入成功');
          upload.open = false;
          getList();
        }
      })
      .catch(() => {
        queryParams.value.loading = false;
      });
  }

  // 打开批量导入对话框
  function handleImport() {
    upload.title = '批量导入';
    upload.batchImportNosStr = '';
    upload.open = true;
  }

  // 批量删除
  function handleBatchDelete() {
    proxy.$modal
      .confirm('确定要删除选中的数据吗？')
      .then(function () {
        handleDeleteByIds(multipleSelection.value);
      })
      .catch(() => {
        // console.log(2);
      });
  }

  function handleDeleteByIds(ids) {
    if (ids && ids.length > 0) {
      const param = { ids: ids };
      queryParams.value.loading = true;
      batchDeleteMultOmic(param)
        .then(res => {
          queryParams.value.loading = false;
          if (res.data) {
            getList();
          }
        })
        .catch(() => {
          queryParams.value.loading = false;
        });
    }
  }

  // 打开页面调用函数
  doSearch();
</script>

<style lang="scss" scoped>
  :deep(.el-dialog__body) {
    padding: 10px !important;
  }
</style>
<style lang="scss">
  .el-popper {
    max-width: 350px !important;
  }
</style>
