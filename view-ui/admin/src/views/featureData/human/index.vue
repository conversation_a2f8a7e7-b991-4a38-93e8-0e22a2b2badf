<template>
  <div class="app-container">
    <div class="card list">
      <el-row>
        <el-col :span="21">
          <el-form
            v-show="showSearch"
            ref="searchFormRef"
            :model="queryParams"
            :inline="true"
          >
            <el-form-item label="分类1" prop="category1">
              <el-select
                v-model="queryParams.category1"
                style="width: 220px"
                placeholder="请选择"
                filterable
                clearable
              >
                <template
                  v-for="(item, index) in category1Data"
                  :key="`list-cat1${item.value}-${index}`"
                >
                  <el-option
                    v-if="existCategory1Ref.includes(item.value)"
                    :label="item.label"
                    :value="item.value"
                  />
                </template>
              </el-select>
            </el-form-item>
            <el-form-item label="分类2" prop="category2">
              <el-select
                v-model="queryParams.category2"
                style="width: 220px"
                placeholder="请选择"
                filterable
                clearable
              >
                <el-option
                  v-for="(item, index) in existCategory2Ref"
                  :key="`cat2${item}-${index}`"
                  :label="item"
                  :value="item"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="分类3" prop="category3">
              <el-select
                v-model="queryParams.category3"
                style="width: 220px"
                placeholder="请选择"
                filterable
                clearable
              >
                <el-option
                  v-for="(item, index) in existCategory3Ref"
                  :key="`cat3${item}-${index}`"
                  :label="item"
                  :value="item"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="项目ID" prop="projectNo">
              <el-select
                v-model="queryParams.projectNo"
                style="width: 220px"
                placeholder="请选择"
                filterable
                clearable
              >
                <el-option
                  v-for="(item, index) in existProjectNoRef"
                  :key="`prj${item}-${index}`"
                  :label="item"
                  :value="item"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="doSearch"
                >搜索
              </el-button>
              <el-button icon="Refresh" @click="doReset">重置</el-button>
            </el-form-item>
          </el-form>
        </el-col>

        <el-col :span="3">
          <el-button
            :disabled="loadingFlag"
            type="danger"
            icon="Brush"
            @click="doRefreshCache"
          >
            刷新缓存
          </el-button>
        </el-col>
      </el-row>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" icon="Plus" @click="handleAdd"
            >新增
          </el-button>
        </el-col>

        <el-col :span="1.5">
          <el-button type="info" icon="Upload" @click="handleImport"
            >导入
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" icon="Download" @click="handleExport"
            >导出
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            :disabled="loadingFlag"
            type="primary"
            icon="Open"
            @click="doEnable(false)"
            >启用
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            :disabled="loadingFlag"
            type="danger"
            icon="TurnOff"
            @click="doEnable(true)"
            >禁用
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            icon="Delete"
            :disabled="notSelect"
            @click="handleBatchDelete"
            >删除
          </el-button>
        </el-col>

        <right-toolbar
          v-model:showSearch="showSearch"
          @query-table="getList"
        ></right-toolbar>
      </el-row>

      <el-table
        v-loading="loadingFlag"
        :data="humanData"
        style="width: 100%; margin-bottom: 20px"
        :header-cell-style="{
          backgroundColor: '#f2f2f2',
          color: '#333333',
          fontWeight: 700,
        }"
        border
        height="76vh"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="50" align="center" />

        <el-table-column
          prop="category1Des"
          label="分类1"
          sortable
          min-width="150"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="category2"
          label="分类2"
          min-width="200"
          show-overflow-tooltip
        />
        <el-table-column prop="category3" label="分类3" min-width="200" />
        <el-table-column prop="comment" label="备注" min-width="200" />
        <el-table-column
          prop="projectNo"
          label="项目ID"
          min-width="100"
          sortable
        >
          <template #default="scope">
            <a
              target="_blank"
              class="text-primary"
              :href="`${data.webAppUrl}${scope.row.projectNo}`"
            >
              {{ scope.row.projectNo }}
            </a>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="90">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              active-value="enable"
              inactive-value="disable"
              @change="val => doChangeStatus(scope.row.id, val)"
            ></el-switch>
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          width="90"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-tooltip content="编辑">
              <svg-icon
                icon-class="edits"
                class-name="meta-svg"
                @click="handleUpdate(scope.row)"
              ></svg-icon>
            </el-tooltip>
            <el-tooltip content="删除">
              <svg-icon
                icon-class="delete"
                class-name="meta-svg"
                @click="handleDelete(scope.row.id)"
              ></svg-icon>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <!--<pagination
        v-show="total > 0"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.size"
        :total="total"
      />-->
    </div>

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog v-model="open" :title="title" width="500px" class="exp-dialog">
      <el-form
        ref="addHuResForm"
        :model="form"
        label-width="120px"
        :inline="true"
        :rules="rules"
      >
        <el-form-item label="分类1" required prop="category1">
          <el-select v-model="form.category1" :clearable="false" filterable>
            <el-option
              v-for="(item, index) in category1Data"
              :key="`dia-cat1${item.value}-${index}`"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="分类2">
          <el-input v-model="form.category2"></el-input>
        </el-form-item>

        <el-form-item label="分类3">
          <el-input v-model="form.category3"></el-input>
        </el-form-item>

        <el-form-item label="项目ID" required prop="projectNo">
          <el-select
            v-model="form.projectNo"
            filterable
            clearable
            remote
            :remote-method="searchPrjId"
          >
            <el-option
              v-for="(item, index) in projectIdData"
              :key="`dia-prj${item}-${index}`"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="备注">
          <el-input v-model="form.comment"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="text-center">
          <el-button type="primary" @click="addHumanData">确认</el-button>
          <el-button @click="open = false">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 数据导入对话框 -->
    <el-dialog
      v-model="upload.open"
      :title="upload.title"
      width="400px"
      append-to-body
    >
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx"
        :headers="upload.headers"
        :action="`${upload.url}?microbeFlag=${microbeFlag}&deleteOld=${upload.deleteOld}`"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :on-error="handleFileError"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload">
          <upload-filled />
        </el-icon>
        <div class="el-upload__text">将文件拖拽到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <div class="el-upload__tip">
              <el-checkbox v-model="upload.deleteOld">
                <strong class="text-danger">删除所有</strong>
                现有数据
              </el-checkbox>
            </div>
            <span>仅允许导入xlsx格式文件。</span>
            <el-link
              type="primary"
              :underline="false"
              style="font-size: 12px; vertical-align: baseline"
              @click="importTemplate"
              >下载模板
            </el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">上传</el-button>
          <el-button @click="upload.open = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { getToken } from '@/utils/auth';
  import { defineProps, getCurrentInstance, reactive, ref, toRefs } from 'vue';
  import {
    batchDeleteHr,
    batchUpdateHrStatus,
    fdHumanResourceList,
    fdSearchPrjId,
    refreshFdCache,
    saveHumanResource,
  } from '@/api/featureData/human';
  import { isArrEmpty, trimStr } from '@/utils';

  const props = defineProps({
    microbeFlag: {
      type: Boolean,
      required: false,
      default() {
        return false;
      },
    },
  });

  const { microbeFlag } = props;

  const { proxy } = getCurrentInstance();

  const data = reactive({
    form: { microbeFlag },
    webAppUrl: import.meta.env.VITE_APP_WEB_URL + '/project/detail/',
  });

  const queryParams = reactive({
    page: 0,
    size: 1000,
    category1: '',
    category2: '',
    category3: '',
    projectNo: '',
    microbeFlag,
  });

  /*** 用户导入参数 */
  const upload = reactive({
    // 是否显示弹出层（用户导入）
    open: false,
    // 弹出层标题（用户导入）
    title: '',
    deleteOld: false,
    // 是否禁用上传
    isUploading: false,
    // 设置上传的请求头部
    headers: { Authorization: 'Bearer ' + getToken() },
    // 上传的地址
    url: `${import.meta.env.VITE_APP_BASE_API}/system/fdHumanResource/importData`,
  });

  const { form } = toRefs(data);
  const currId = ref('');
  const rules = ref({
    category1: [
      {
        required: true,
        trigger: 'blur',
      },
    ],
    projectNo: [
      {
        required: true,
        trigger: 'blur',
      },
    ],
  });

  const showSearch = ref(true);

  const loadingFlag = ref(false);
  const category1Data = ref([]);
  const projectIdData = ref([]);

  const notSelect = ref(true);
  const multipleSelection = ref([]);
  const total = ref(0);
  const humanData = reactive([]);
  const existCategory1Ref = ref([]);
  const existCategory2Ref = ref([]);
  const existCategory3Ref = ref([]);
  const existProjectNoRef = ref([]);

  const title = ref('');
  const open = ref(false);

  /** 选择条数  */
  function handleSelectionChange(selection) {
    const len = selection.length;
    notSelect.value = len === 0;
    let ids = [];
    for (let i = 0; i < len; i++) {
      ids.push(selection[i].id);
    }
    multipleSelection.value = ids;
  }

  function initTitle() {
    return microbeFlag ? '微生物资源' : '人类资源';
  }

  /** 新增按钮操作 */
  function handleAdd() {
    currId.value = '';
    form.value = { microbeFlag };
    open.value = true;
    title.value = `新增${initTitle()}`;
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    currId.value = row.id;
    form.value = { ...row, microbeFlag };
    open.value = true;
    title.value = `编辑${initTitle()}`;
  }

  // 批量删除
  function handleBatchDelete() {
    proxy.$modal
      .confirm('确定要删除选中的数据吗？')
      .then(function () {
        handleDeleteByIds(multipleSelection.value);
      })
      .catch(() => {
        // console.log(2);
      });
  }

  function handleDelete(id) {
    proxy.$modal
      .confirm('确定要删除当前数据吗？')
      .then(function () {
        handleDeleteByIds([id]);
      })
      .catch(() => {
        // console.log(2);
      });
  }

  function handleDeleteByIds(ids) {
    if (ids && ids.length > 0) {
      const param = { ids: ids, microbeFlag };
      loadingFlag.value = true;
      batchDeleteHr(param)
        .then(res => {
          if (res.data) {
            getList();
          }
        })
        .finally(() => {
          loadingFlag.value = false;
        });
    }
  }

  function handleImport() {
    upload.title = '批量导入';
    upload.open = true;
    upload.deleteOld = false;
  }

  /**文件上传中处理 */
  const handleFileUploadProgress = (event, file, fileList) => {
    loadingFlag.value = true;
    upload.isUploading = true;
  };

  /** 文件上传成功处理 */
  const handleFileSuccess = (response, file, fileList) => {
    loadingFlag.value = false;
    upload.open = false;
    upload.isUploading = false;
    proxy.$refs['uploadRef'].handleRemove(file);
    proxy.$alert(
      "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
        response.msg +
        '</div>',
      '导入结果',
      { dangerouslyUseHTMLString: true },
    );
    doSearch();
  };

  const handleFileError = (response, file, fileList) => {
    loadingFlag.value = false;
    proxy.$refs['uploadRef'].clearFiles();
  };

  /** 下载模板操作 */
  function importTemplate() {
    proxy.download(
      `system/fdHumanResource/importTemplate`,
      { microbeFlag },
      `${initTitle()} template_${new Date().getTime()}.xlsx`,
    );
  }

  /** 提交上传文件 */
  function submitFileForm() {
    proxy.$refs['uploadRef'].submit();
  }

  // 导出
  function handleExport() {
    proxy.download(
      `/system/fdHumanResource/downloadHr`,
      { microbeFlag },
      `${initTitle()}_${new Date().getTime()}.xlsx`,
    );
  }

  function doSearch() {
    queryParams.page = 0;
    getList();
  }

  // 查询列表数据
  function getList() {
    loadingFlag.value = true;
    humanData.length = 0;
    fdHumanResourceList(queryParams)
      .then(res => {
        let data = res.data;
        let {
          pageInfo,
          selectVoList,
          existCategory1,
          existCategory2,
          existCategory3,
          existProjectNo,
        } = data;
        existCategory1Ref.value = existCategory1;
        existCategory2Ref.value = existCategory2;
        existCategory3Ref.value = existCategory3;
        existProjectNoRef.value = existProjectNo;
        if (pageInfo && !isArrEmpty(pageInfo.content)) {
          humanData.push(...pageInfo.content);
          total.value = pageInfo.totalElements;
        } else {
          total.value = 0;
        }
        category1Data.value = selectVoList;
      })
      .finally(() => {
        loadingFlag.value = false;
      });
  }

  function doReset() {
    proxy.resetForm('searchFormRef');
  }

  // 清理redis缓存
  function doRefreshCache() {
    proxy.$modal
      .confirm('确定要刷新所有缓存吗？')
      .then(function () {
        loadingFlag.value = true;
        refreshFdCache()
          .then(res => {
            if (res.data) {
              proxy.$modal.msgSuccess('刷新成功');
            } else {
              proxy.$modal.msgError(res.msg);
            }
          })
          .finally(() => {
            loadingFlag.value = false;
          });
      })
      .catch(() => {});
  }

  // 查询项目编号
  function searchPrjId(query) {
    query = trimStr(query);
    if (query) {
      fdSearchPrjId(query).then(response => {
        projectIdData.value = response.data;
      });
    }
  }

  // 新增数据
  function addHumanData() {
    proxy.$refs['addHuResForm'].validate(valid => {
      if (valid) {
        const data = { ...form.value };
        const id = trimStr(currId.value);
        if (id) {
          data.id = id;
        } else {
          data.id = null;
        }
        data.microbeFlag = microbeFlag;
        saveHumanResource(data).then(res => {
          if (res.data) {
            proxy.$modal.msgSuccess('保存成功');
            open.value = false;
            doSearch();
          }
        });
      }
    });
  }

  // 启用(禁用)
  function doEnable(disable) {
    if (total.value > 0) {
      const param = {
        ids: multipleSelection.value,
        disable: disable,
        microbeFlag,
      };
      let title;
      if (param.ids.length === 0) {
        title = disable ? '禁用所有数据' : '启用所有数据';
      } else {
        title = disable ? '禁用选中数据' : '启用选中数据';
      }
      proxy.$modal
        .confirm(title)
        .then(function () {
          loadingFlag.value = true;
          batchUpdateHrStatus(param)
            .then(res => {
              if (res.data) {
                getList();
              }
            })
            .finally(() => {
              loadingFlag.value = false;
            });
        })
        .catch(() => {});
    }
  }

  // 单行数据状态变更
  function doChangeStatus(id, status) {
    if (total.value > 0) {
      const param = {
        ids: [id],
        disable: status === 'disable',
        microbeFlag,
      };
      batchUpdateHrStatus(param).then(res => {
        if (res.data) {
          // getList();
        }
      });
    }
  }

  doSearch();
</script>

<style lang="scss" scoped>
  :deep(.el-dialog__body) {
    padding: 10px !important;

    .el-select,
    .el-input {
      width: 330px;
    }
  }

  .meta-svg {
    width: 20px;
    height: 20px;
    margin-right: 0.5rem;
    cursor: pointer;
  }
</style>
<style lang="scss">
  .el-popper {
    max-width: 350px !important;
  }
</style>
