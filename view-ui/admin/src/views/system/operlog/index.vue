<template>
  <div class="app-container">
    <div class="card">
      <el-form
        v-show="showSearch"
        ref="queryRef"
        :model="queryParams"
        :inline="true"
      >
        <el-form-item label="Operation IP" prop="operIp">
          <el-input
            v-model="queryParams.operIp"
            placeholder="Please input operation address"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="System Module" prop="title">
          <el-input
            v-model="queryParams.title"
            placeholder="Please input system module"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="Operator" prop="operName">
          <el-input
            v-model="queryParams.operName"
            placeholder="Please input operator"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="Operation Type" prop="businessType">
          <el-select
            v-model="queryParams.businessType"
            placeholder="Select"
            clearable
            style="width: 240px"
          >
            <el-option
              v-for="dict in sys_oper_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Status" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="status"
            clearable
            style="width: 240px"
          >
            <el-option
              v-for="dict in sys_common_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Operating Time">
          <el-date-picker
            v-model="dateRange"
            style="width: 300px"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            range-separator="-"
            start-placeholder="Start Date"
            end-placeholder="End Date"
            :default-time="[
              new Date(2000, 1, 1, 0, 0, 0),
              new Date(2000, 1, 1, 23, 59, 59),
            ]"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >Search</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">Reset</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['system:operlog:remove']"
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            >Delete</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['system:operlog:remove']"
            type="danger"
            plain
            icon="Delete"
            @click="handleClean"
            >Clear</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['system:operlog:export']"
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            >Export</el-button
          >
        </el-col>
        <right-toolbar
          v-model:showSearch="showSearch"
          @query-table="getList"
        ></right-toolbar>
      </el-row>

      <el-table
        ref="operlogRef"
        v-loading="loading"
        :data="operlogList"
        :default-sort="defaultSort"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column label="Log ID" align="center" prop="operId" />
        <el-table-column
          label="System Module"
          align="center"
          prop="title"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="Operation Type"
          align="center"
          prop="businessType"
        >
          <template #default="scope">
            <dict-tag
              :options="sys_oper_type"
              :value="scope.row.businessType"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="Request Method"
          align="center"
          prop="requestMethod"
        />
        <el-table-column
          label="Operator"
          align="center"
          prop="operName"
          width="110"
          :show-overflow-tooltip="true"
          sortable="custom"
          :sort-orders="['descending', 'ascending']"
        />
        <el-table-column
          label="Operation IP"
          align="center"
          prop="operIp"
          width="130"
          :show-overflow-tooltip="true"
        />
        <el-table-column label="Operating Status" align="center" prop="status">
          <template #default="scope">
            <dict-tag :options="sys_common_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column
          label="Operating Time"
          align="center"
          prop="operTime"
          width="180"
          sortable="custom"
          :sort-orders="['descending', 'ascending']"
        >
          <template #default="scope">
            <span>{{ parseTime(scope.row.operTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="Time Consuming"
          align="center"
          prop="costTime"
          width="150"
          :show-overflow-tooltip="true"
          sortable="custom"
          :sort-orders="['descending', 'ascending']"
        >
          <template #default="scope">
            <span>{{ scope.row.costTime }}毫秒</span>
          </template>
        </el-table-column>
        <el-table-column
          label="Operate"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-button
              v-hasPermi="['system:operlog:query']"
              link
              type="primary"
              icon="View"
              @click="handleView(scope.row, scope.index)"
              >Detail</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @pagination="getList"
      />
    </div>

    <!-- 操作日志详细 -->
    <el-dialog
      v-model="open"
      title="Operation Log Details"
      width="850px"
      append-to-body
    >
      <el-form :model="form" label-width="160px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="Operation Module："
              >{{ form.title }} / {{ typeFormat(form) }}</el-form-item
            >
            <el-form-item label="Login Information："
              >{{ form.operName }} / {{ form.operIp }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="Operation URL：">{{
              form.operUrl
            }}</el-form-item>
            <el-form-item label="Request Method：">{{
              form.requestMethod
            }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="Operation Method：">{{
              form.method
            }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="Request Parameters：">{{
              form.operParam
            }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="Return Parameters：">{{
              form.jsonResult
            }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="Status：">
              <div v-if="form.status === 0">正常</div>
              <div v-else-if="form.status === 1">失败</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="Time Consuming："
              >{{ form.costTime }}毫秒</el-form-item
            >
          </el-col>
          <el-col :span="8">
            <el-form-item label="Operating Time：">{{
              parseTime(form.operTime)
            }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item v-if="form.status === 1" label="Error Message：">{{
              form.errorMsg
            }}</el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="open = false">Close</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Operlog">
  import { list, delOperlog, cleanOperlog } from '@/api/system/operlog';

  const { proxy } = getCurrentInstance();
  const { sys_oper_type, sys_common_status } = proxy.useDict(
    'sys_oper_type',
    'sys_common_status',
  );

  const operlogList = ref([]);
  const open = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref('');
  const dateRange = ref([]);
  const defaultSort = ref({ prop: 'operTime', order: 'descending' });

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      operIp: undefined,
      title: undefined,
      operName: undefined,
      businessType: undefined,
      status: undefined,
    },
  });

  const { queryParams, form } = toRefs(data);

  /** 查询登录日志 */
  function getList() {
    loading.value = true;
    list(proxy.addDateRange(queryParams.value, dateRange.value)).then(
      response => {
        operlogList.value = response.rows;
        total.value = response.total;
        loading.value = false;
      },
    );
  }
  /** 操作日志类型字典翻译 */
  function typeFormat(row, column) {
    return proxy.selectDictLabel(sys_oper_type.value, row.businessType);
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }
  /** 重置按钮操作 */
  function resetQuery() {
    dateRange.value = [];
    proxy.resetForm('queryRef');
    queryParams.value.pageNum = 1;
    proxy.$refs['operlogRef'].sort(
      defaultSort.value.prop,
      defaultSort.value.order,
    );
  }
  /** 多选框选中数据 */
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.operId);
    multiple.value = !selection.length;
  }
  /** 排序触发事件 */
  function handleSortChange(column, prop, order) {
    queryParams.value.orderByColumn = column.prop;
    queryParams.value.isAsc = column.order;
    getList();
  }
  /** 详细按钮操作 */
  function handleView(row) {
    open.value = true;
    form.value = row;
  }
  /** 删除按钮操作 */
  function handleDelete(row) {
    const operIds = row.operId || ids.value;
    proxy.$modal
      .confirm('是否确认删除日志编号为"' + operIds + '"的数据项?')
      .then(function () {
        return delOperlog(operIds);
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess('删除成功');
      })
      .catch(() => {});
  }
  /** 清空按钮操作 */
  function handleClean() {
    proxy.$modal
      .confirm('是否确认清空所有操作日志数据项?')
      .then(function () {
        return cleanOperlog();
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess('清空成功');
      })
      .catch(() => {});
  }
  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      'system/operlog/export',
      {
        ...queryParams.value,
      },
      `config_${new Date().getTime()}.xlsx`,
    );
  }

  getList();
</script>
