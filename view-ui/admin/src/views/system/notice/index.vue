<template>
  <div class="app-container">
    <div class="card">
      <el-form
        v-show="showSearch"
        ref="queryRef"
        :model="queryParams"
        :inline="true"
      >
        <el-form-item label="通知标题" prop="noticeTitle">
          <el-input
            v-model="queryParams.noticeTitle"
            placeholder="请输入通知标题"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="创建人" prop="createBy">
          <el-input
            v-model="queryParams.createBy"
            placeholder="请输入操作员"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="通知状态" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="选择状态"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="dict in sys_notice_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['system:notice:add']"
            type="primary"
            icon="Plus"
            @click="handleAdd"
            >添加</el-button
          >
        </el-col>

        <el-col :span="1.5">
          <el-button
            v-hasPermi="['system:notice:remove']"
            type="danger"
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            >删除</el-button
          >
        </el-col>
        <right-toolbar
          v-model:showSearch="showSearch"
          @query-table="getList"
        ></right-toolbar>
      </el-row>

      <el-table
        v-loading="loading"
        :data="noticeList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column
          label="通知Id"
          align="center"
          prop="noticeId"
          width="100"
        />
        <el-table-column
          label="通知标题"
          align="center"
          prop="noticeTitle"
          :show-overflow-tooltip="true"
        />
        <el-table-column label="状态" align="center" prop="status" width="100">
          <template #default="scope">
            <dict-tag :options="sys_notice_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column
          label="创建人"
          align="center"
          prop="createBy"
          width="100"
        />
        <el-table-column label="开始时间" align="center" prop="startTime">
          <template #default="scope">
            <span>{{ parseTime(scope.row.startTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="结束时间" align="center" prop="endTime">
          <template #default="scope">
            <span>{{ parseTime(scope.row.endTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="90"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-tooltip content="编辑">
              <svg-icon
                v-hasPermi="['system:notice:edit']"
                icon-class="edits"
                class-name="meta-svg"
                @click="handleUpdate(scope.row)"
              ></svg-icon>
            </el-tooltip>

            <el-tooltip content="删除">
              <svg-icon
                v-hasPermi="['system:notice:remove']"
                icon-class="delete"
                class-name="meta-svg"
                @click="handleDelete(scope.row)"
              ></svg-icon>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @pagination="getList"
      />
    </div>

    <!-- 添加或修改公告对话框 -->
    <el-dialog v-model="open" :title="title" width="780px" append-to-body>
      <el-form ref="noticeRef" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="通知标题" prop="noticeTitle">
              <el-input
                v-model="form.noticeTitle"
                placeholder="请输入通知标题"
              />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="开始时间" required prop="startTime">
              <el-date-picker
                v-model="form.startTime"
                style="width: 250px"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetime"
                range-separator="-"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="结束时间">
              <el-date-picker
                v-model="form.endTime"
                style="width: 250px"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetime"
                range-separator="-"
              ></el-date-picker>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="状态">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in sys_notice_status"
                  :key="dict.value"
                  :label="dict.value"
                  >{{ dict.label }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="通知内容">
              <editor v-model="form.noticeContent" :min-height="192" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确认</el-button>
          <el-button @click="cancel">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Notice">
  import {
    listNotice,
    getNotice,
    delNotice,
    addNotice,
    updateNotice,
  } from '@/api/system/notice';
  import { getCurrentInstance, reactive, ref, toRefs } from 'vue';

  const { proxy } = getCurrentInstance();
  const { sys_notice_status } = proxy.useDict('sys_notice_status');

  const noticeList = ref([]);
  const open = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref('');

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      noticeTitle: undefined,
      createBy: undefined,
      status: undefined,
    },
    rules: {
      noticeTitle: [
        {
          required: true,
          message: '通知标题不能为空',
          trigger: 'blur',
        },
      ],
      startTime: [
        {
          required: true,
          message: '生效日期不能为空',
          trigger: 'blur',
        },
      ],
    },
  });

  const { queryParams, form, rules } = toRefs(data);

  /** 查询公告列表 */
  function getList() {
    loading.value = true;
    listNotice(queryParams.value).then(response => {
      noticeList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    });
  }
  /** 取消按钮 */
  function cancel() {
    open.value = false;
    reset();
  }
  /** 表单重置 */
  function reset() {
    form.value = {
      noticeId: undefined,
      noticeTitle: undefined,
      noticeType: '1',
      noticeContent: undefined,
      status: '1',
    };
    proxy.resetForm('noticeRef');
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }
  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm('queryRef');
    handleQuery();
  }
  /** 多选框选中数据 */
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.noticeId);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
  }
  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    open.value = true;
    title.value = '添加通知';
  }
  /**修改按钮操作 */
  function handleUpdate(row) {
    reset();
    const noticeId = row.noticeId || ids.value;
    getNotice(noticeId).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = '编辑通知';
    });
  }
  /** 提交按钮 */
  function submitForm() {
    proxy.$refs['noticeRef'].validate(valid => {
      if (valid) {
        if (form.value.noticeId != undefined) {
          updateNotice(form.value).then(() => {
            proxy.$modal.msgSuccess('编辑成功');
            open.value = false;
            getList();
          });
        } else {
          addNotice(form.value).then(() => {
            proxy.$modal.msgSuccess('添加成功');
            open.value = false;
            getList();
          });
        }
      }
    });
  }
  /** 删除按钮操作 */
  function handleDelete(row) {
    const noticeIds = row.noticeId || ids.value;
    proxy.$modal
      .confirm('您想确认删除数据项吗？')
      .then(function () {
        return delNotice(noticeIds);
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess('已删除');
      })
      .catch(() => {});
  }

  getList();
</script>
<style lang="scss" scoped>
  .meta-svg {
    width: 20px;
    height: 20px;
    margin-right: 0.5rem;
    cursor: pointer;
  }
</style>
