<template>
  <div class="app-container">
    <div class="card list mb-1">
      <h3 class="mb-0 mt-0">修改数据完整性</h3>
      <el-divider></el-divider>
      <el-form ref="formRef" :rules="rules" :model="form" label-width="160">
        <el-form-item prop="dataNo" label="数据编号">
          <el-input
            v-model="form.dataNo"
            type="textarea"
            rows="6"
            class="radius-12"
            :placeholder="`在表单字段中输入标识符，用空格或新行分隔，例如： \nOED00000001\nOED00000002\nOED00000003`"
            style="width: 400px"
          />
        </el-form-item>
        <el-form-item prop="complete" label="完整性校验">
          <el-radio-group v-model="form.complete">
            <el-radio :value="true" label="true">通过</el-radio>
            <el-radio :value="false" label="false">不通过</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSubmit">提交</el-button>
          <el-button type="warning" class="ml-3" @click="handleExport"
            >导出完整性校验不通过的数据</el-button
          >
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script setup>
  import { getCurrentInstance, reactive, toRefs } from 'vue';
  import { isStrBlank } from '@/utils';
  import { batchModifyIntegrity } from '@/api/metadata/data';

  const { proxy } = getCurrentInstance();

  const data = reactive({
    form: {
      dataNo: '',
      complete: true,
      dataNos: [],
    },
    rules: {
      dataNo: [
        {
          required: true,
          trigger: 'change',
          message: '数据ID是必需的',
        },
      ],
    },
  });
  const { form, rules } = toRefs(data);

  function handleExport() {
    proxy.download(
      '/system/dataIntegralityCheck/export',
      {},
      `dataIntegralityCheck${new Date().getTime()}.xlsx`,
    );
  }

  function handleSubmit() {
    proxy.$refs['formRef'].validate(valid => {
      if (valid) {
        // typeNo换行切割，并trim
        form.value.dataNos = form.value.dataNo
          .split('\n')
          .map(x => x.trim())
          .filter(x => !isStrBlank(x));
        proxy.$modal
          .confirm('不存在的数据ID将被跳过。确定要批量修改吗？')
          .then(() => {
            proxy.$modal.loading('正在更新...');
            batchModifyIntegrity(form.value)
              .then(() => {
                // 清空表单
                // Modify data integrity to true successfully
                proxy.$modal.alertSuccess(
                  `更新完整性为 ${form.value.complete} 成功！`,
                );
              })
              .finally(() => {
                proxy.$modal.closeLoading();
              });
          });
      }
    });
  }
</script>
<style scoped lang="scss"></style>
