<template>
  <div class="app-container">
    <div class="card">
      <el-tabs v-model="activeName" type="card" class="demo-tabs">
        <el-tab-pane label="FTP主目录数据" name="Ftp">
          <ftp-home-file-table></ftp-home-file-table>
        </el-tab-pane>
        <el-tab-pane label="未归档数据" name="Unarchived Data">
          <unarchived-data-table />
        </el-tab-pane>
        <el-tab-pane label="未公开数据" name="Unpublish Data">
          <private-data-table />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
  import { ref } from 'vue';
  import UnarchivedDataTable from '@/views/tool/dataCheck/common/UnarchivedDataTable.vue';
  import PrivateDataTable from '@/views/tool/dataCheck/common/PrivateDataTable.vue';
  import FtpHomeFileTable from '@/views/tool/dataCheck/common/FtpHomeFileTable.vue';

  const activeName = ref('Ftp');
</script>

<style lang="scss" scoped></style>

<style lang="scss">
  .el-popper {
    max-width: 350px !important;
  }
</style>
