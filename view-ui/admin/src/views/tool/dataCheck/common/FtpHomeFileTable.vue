<template>
  <div>
    <el-form ref="searchFormRef" :model="queryParams" :inline="true">
      <el-form-item label="创建人" prop="creatorEmail">
        <el-input
          v-model="queryParams.creatorEmail"
          clearable
          style="width: 240px"
          @keyup.enter="getDataList"
        ></el-input>
      </el-form-item>
      <el-form-item label="创建日期" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 240px"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="getDataList"
          >搜索
        </el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
      <el-form-item class="pos-absolute mr-0" style="right: 0">
        <el-button type="danger" @click="handleScan"
          >扫描FTP主目录文件
        </el-button>
        <el-button icon="Download" type="warning" @click="handleExport"
          >导出
        </el-button>
      </el-form-item>
    </el-form>
    <el-table
      v-loading="loading"
      :data="tableData"
      style="width: 100%; margin-bottom: 20px"
      :row-key="row => row.id"
      :header-cell-style="{
        backgroundColor: '#f2f2f2',
        color: '#333333',
        fontWeight: 700,
      }"
      border
      :default-sort="defaultSort"
      @sort-change="handleSortChange"
    >
      <el-table-column prop="name" label="文件名" min-width="140" />
      <el-table-column
        prop="path"
        label="文件路径"
        show-overflow-tooltip
        min-width="180"
      />
      <el-table-column prop="size" label="文件大小" min-width="110" sortable>
        <template #default="scope">
          {{ scope.row.readableFileSize }}
        </template>
      </el-table-column>
      <el-table-column prop="creatorEmail" label="创建人" min-width="140" />
      <el-table-column
        prop="createTime"
        label="创建日期"
        min-width="160"
        sortable
      >
        <template #default="scope">
          {{ parseTime(scope.row.createTime) }}
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      class="mb-1"
      @pagination="getDataList"
    />
  </div>
</template>

<script setup>
  import { getCurrentInstance, onMounted, reactive, toRefs } from 'vue';
  import { listFtpHomeFile, scanFtpHomeFile } from '@/api/metadata/data';

  onMounted(() => {
    getDataList();
  });

  const { proxy } = getCurrentInstance();
  const data = reactive({
    tableData: [],
    total: 0,
    queryParams: {
      creatorEmail: '',
      pageNum: 1,
      pageSize: 20,
      orderByColumn: 'createTime',
      isAsc: 'descending',
    },
    dateRange: [],
    loading: true,
    defaultSort: { prop: 'createTime', order: 'descending' },
  });

  /** 解构 */
  const { tableData, total, queryParams, dateRange, loading, defaultSort } =
    toRefs(data);

  function getDataList() {
    loading.value = true;
    listFtpHomeFile(proxy.addDateRange(queryParams.value, dateRange.value))
      .then(response => {
        // 将结果赋值给tableData
        tableData.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  function resetQuery() {
    dateRange.value = [];
    proxy.resetForm('searchFormRef');

    getDataList();
  }

  /** 排序触发事件 */
  function handleSortChange(column) {
    if (column.order) {
      queryParams.value.orderByColumn = column.prop;
      queryParams.value.isAsc = column.order;
      getDataList();
    }
  }

  function handleScan() {
    proxy.$modal
      .confirm('确定要扫描FTP主目录文件吗？这可能需要很长时间')
      .then(() => {
        scanFtpHomeFile().then(() => {
          proxy.$modal.msgSuccess('开始扫描FTP主目录文件，请稍候...');
        });
      });
  }

  function handleExport() {
    if (tableData.value.length === 0) {
      proxy.$modal.msgError('没有数据可导出');
      return;
    }
    proxy.$modal.confirm('将导出前5000条数据。是否继续？').then(() => {
      proxy.download(
        '/system/metadata/data/ftpHomeFile/export',
        { ...queryParams.value, pageNum: 1, pageSize: 5000 },
        `FtpHomeFile_${new Date().getTime()}.xlsx`,
      );
    });
  }
</script>

<style scoped lang="scss"></style>
