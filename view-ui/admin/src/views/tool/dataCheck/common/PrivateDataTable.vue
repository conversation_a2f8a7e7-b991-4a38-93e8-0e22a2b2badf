<template>
  <div>
    <el-form ref="searchFormRef" :model="queryParams" :inline="true">
      <el-form-item label="数据ID" prop="dataNo">
        <el-input
          v-model="queryParams.dataNo"
          clearable
          style="width: 240px"
          @keyup.enter="getDataList"
        ></el-input>
      </el-form-item>
      <el-form-item label="创建人" prop="creatorEmail">
        <el-input
          v-model="queryParams.creatorEmail"
          clearable
          style="width: 240px"
          @keyup.enter="getDataList"
        ></el-input>
      </el-form-item>
      <el-form-item label="创建日期" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 240px"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="getDataList"
          >搜索
        </el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
      <el-form-item class="pos-absolute mr-0" style="right: 0">
        <el-button icon="Download" type="warning" @click="handleExport"
          >导出
        </el-button>
      </el-form-item>
    </el-form>
    <el-table
      v-loading="loading"
      :data="tableData"
      style="width: 100%; margin-bottom: 20px"
      :row-key="row => row.id"
      :header-cell-style="{
        backgroundColor: '#f2f2f2',
        color: '#333333',
        fontWeight: 700,
      }"
      :default-sort="defaultSort"
      border
      @sort-change="handleSortChange"
    >
      <el-table-column prop="datNo" label="数据ID" width="120" sortable />
      <el-table-column
        prop="name"
        label="文件名"
        min-width="140"
        show-overflow-tooltip
        sortable
      />
      <el-table-column prop="dataType" label="数据类型" width="120" />
      <el-table-column prop="size" label="文件大小" width="100" sortable>
        <template #default="scope">
          {{ scope.row.readableFileSize }}
        </template>
      </el-table-column>
      <el-table-column prop="security" label="数据安全性" width="150">
      </el-table-column>

      <el-table-column prop="projNo" label="项目" width="120">
        <template #default="scope">
          <a
            class="text-primary"
            href="javascript:void(0)"
            @click="showDetail(scope.row.projNo, scope.row.creator, 'project')"
          >
            {{ scope.row.projNo }}
          </a>
        </template>
      </el-table-column>
      <el-table-column prop="expNo" label="实验" width="120">
        <template #default="scope">
          <a
            class="text-primary"
            href="javascript:void(0)"
            @click="
              showDetail(scope.row.expNo, scope.row.creator, 'experiment')
            "
          >
            {{ scope.row.expNo }}
          </a>
        </template>
      </el-table-column>
      <el-table-column prop="sapNo" label="样本" width="120">
        <template #default="scope">
          <a
            class="text-primary"
            href="javascript:void(0)"
            @click="showDetail(scope.row.sapNo, scope.row.creator, 'sample')"
          >
            {{ scope.row.sapNo }}
          </a>
        </template>
      </el-table-column>
      <el-table-column prop="analNo" label="分析" width="120">
        <template #default="scope">
          <a
            class="text-primary"
            href="javascript:void(0)"
            @click="showDetail(scope.row.analNo, scope.row.creator, 'analysis')"
          >
            {{ scope.row.analNo }}
          </a>
        </template>
      </el-table-column>
      <el-table-column prop="creatorEmail" label="创建人" width="180" />
      <el-table-column prop="createDate" label="上传日期" width="160" sortable>
        <template #default="scope">
          {{ parseTime(scope.row.createDate) }}
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      class="mb-1"
      @pagination="getDataList"
    />
  </div>
</template>

<script setup>
  import { getCurrentInstance, onMounted, reactive, toRefs } from 'vue';
  import { createAccessToken } from '@/api/login';
  import { listPrivateData } from '@/api/metadata/data';
  import { parseTime } from '@/utils/ruoyi';

  onMounted(() => {
    getDataList();
  });

  const { proxy } = getCurrentInstance();
  const data = reactive({
    tableData: [],
    total: 0,
    queryParams: {
      dataNo: '',
      creatorEmail: '',
      pageNum: 1,
      pageSize: 20,
      orderByColumn: 'createDate',
      isAsc: 'descending',
    },
    dateRange: [],
    loading: true,
    defaultSort: { prop: 'createDate', order: 'descending' },
  });

  /** 解构 */
  const { tableData, total, queryParams, dateRange, loading, defaultSort } =
    toRefs(data);

  function getDataList() {
    loading.value = true;
    listPrivateData(proxy.addDateRange(queryParams.value, dateRange.value))
      .then(response => {
        // 将结果赋值给tableData
        tableData.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  function resetQuery() {
    dateRange.value = [];
    proxy.resetForm('searchFormRef');

    getDataList();
  }

  /** 排序触发事件 */
  function handleSortChange(column) {
    if (column.order) {
      queryParams.value.orderByColumn = column.prop;
      queryParams.value.isAsc = column.order;
      getDataList();
    }
  }

  function showDetail(no, creator, type) {
    proxy.$modal.loading('正在打开，请稍候');
    // 预先生成access_token
    createAccessToken({ memberId: creator })
      .then(response => {
        const token = response.data;
        let href = `${
          import.meta.env.VITE_APP_WEB_URL
        }/${type}/detail/${no}?access-token=${token}`;
        proxy.$modal.closeLoading();
        // 打开一个新页面
        window.open(href);
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  function handleExport() {
    if (tableData.value.length === 0) {
      proxy.$modal.msgError('没有数据可导出');
      return;
    }
    proxy.$modal.confirm('将导出前5000条数据。是否继续？').then(() => {
      proxy.download(
        '/system/metadata/data/exportPrivateData',
        { ...queryParams.value },
        `UnaccessibleData_${new Date().getTime()}.xlsx`,
      );
    });
  }
</script>

<style scoped lang="scss"></style>
