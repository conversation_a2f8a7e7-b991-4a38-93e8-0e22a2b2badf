<template>
  <div class="app-container">
    <div class="card list mb-1">
      <h3 class="mb-0 mt-0">批量修改标签</h3>
      <el-divider></el-divider>
      <el-form ref="formRef" :rules="rules" :model="form" label-width="160">
        <el-form-item prop="sourceProject" label="标签">
          <el-select
            v-model="form.sourceProject"
            clearable
            style="width: 400px"
            :teleported="false"
            multiple
          >
            <el-option
              v-for="dict in tag"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="type" label="类型">
          <el-select
            v-model="form.type"
            style="width: 400px"
            :teleported="false"
          >
            <el-option value="project" label="项目" />
            <el-option value="experiment" label="实验" />
            <el-option value="sample" label="样本" />
            <el-option value="run" label="批次" />
            <el-option value="analysis" label="分析" />
          </el-select>
        </el-form-item>
        <el-form-item prop="typeNo" label="类型ID">
          <el-input
            v-model="form.typeNo"
            type="textarea"
            rows="5"
            class="radius-12"
            :placeholder="
              isStrBlank(form.type)
                ? ''
                : `在表单字段中输入标识符，用空格或新行分隔，例如: \n${
                    targetInfo[form.type + 'Placeholder']
                  }`
            "
            style="width: 400px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleAddTag(true)"
            >添加标签
          </el-button>
          <el-button type="danger" @click="handleAddTag(false)"
            >移除标签
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="card list mb-1">
      <h3 class="mb-0 mt-0">标签信息</h3>
      <el-divider></el-divider>
      <el-form ref="queryRef" :model="queryParams" :inline="true">
        <el-form-item label="类型" prop="type">
          <el-select
            v-model="queryParams.type"
            clearable
            style="width: 200px"
            :teleported="false"
          >
            <el-option value="project" label="项目" />
            <el-option value="experiment" label="实验" />
            <el-option value="sample" label="样本" />
            <el-option value="run" label="批次" />
            <el-option value="analysis" label="分析" />
          </el-select>
        </el-form-item>
        <el-form-item label="类型ID" prop="typeNo">
          <el-input
            v-model="queryParams.typeNo"
            clearable
            style="width: 200px"
            @keyup.enter="getDataList"
          ></el-input>
        </el-form-item>
        <el-form-item label="标签包含" prop="tag">
          <el-select
            v-model="queryParams.sourceProjectIn"
            clearable
            multiple
            filterable
            style="width: 300px"
            :teleported="false"
          >
            <el-option
              v-for="dict in tag"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="标签不包含" prop="tag">
          <el-select
            v-model="queryParams.sourceProjectNotIn"
            clearable
            multiple
            filterable
            style="width: 300px"
            :teleported="false"
          >
            <el-option
              v-for="dict in tag"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="getDataList"
            >搜索
          </el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          <el-button type="warning" icon="Download" @click="exportId"
            >导出ID
          </el-button>
        </el-form-item>
      </el-form>
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%; margin-bottom: 20px"
        :header-cell-style="{
          backgroundColor: '#f2f2f2',
          color: '#333333',
          fontWeight: 700,
        }"
        border
        :default-sort="defaultSort"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="typeNo" label="ID" sortable width="115">
        </el-table-column>
        <el-table-column
          prop="name"
          label="名称"
          min-width="120"
          sortable
          show-overflow-tooltip
        />
        <el-table-column prop="sourceProject" label="标签">
          <template #default="scope">
            <el-tag
              v-for="(item, index) in scope.row.sourceProject"
              :key="item + index"
            >
              {{ item }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="description"
          label="描述"
          min-width="130"
          sortable
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="submitter"
          label="提交人"
          width="150"
          show-overflow-tooltip
        />
        <el-table-column
          prop="creatorEmail"
          label="创建人"
          width="165"
          show-overflow-tooltip
        />
        <el-table-column
          prop="createDate"
          label="提交日期"
          width="160"
          sortable
          show-overflow-tooltip
        >
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        class="mb-1"
        @pagination="getDataList"
      />
    </div>
  </div>
</template>

<script setup>
  import { getCurrentInstance, onMounted, reactive, toRefs } from 'vue';
  import {
    batchModifySource,
    getSourceProjectMetadataList,
  } from '@/api/metadata/project';
  import { isStrBlank } from '@/utils';

  onMounted(() => {
    getDataList();
  });
  const { proxy } = getCurrentInstance();

  const { tag } = proxy.useDict('tag');

  const data = reactive({
    form: {
      sourceProject: [],
      type: '',
      typeNo: '',
      add: undefined,
    },
    queryParams: {
      type: 'project',
      typeNo: '',
      sourceProjectIn: [],
      sourceProjectNotIn: [],
      pageNum: 1,
      pageSize: 20,
      orderByColumn: 'createDate',
      isAsc: 'descending',
    },
    tableData: [],
    total: 0,
    loading: false,
    defaultSort: { prop: 'createDate', order: 'descending' },
    rules: {
      sourceProject: [
        {
          required: true,
          trigger: 'change',
          message: '标签是必需的',
        },
      ],
      type: [
        {
          required: true,
          trigger: 'change',
          message: '类型是必需的',
        },
      ],
      typeNo: [
        {
          required: true,
          trigger: 'change',
          message: '类型ID是必需的',
        },
      ],
    },
  });

  const { form, queryParams, rules, tableData, total, loading, defaultSort } =
    toRefs(data);

  let targetInfo = reactive({
    projectPlaceholder: 'OEP000001\nOEP000002\nOEP000003',
    experimentPlaceholder: 'OEX000001\nOEX000002\nOEX000003',
    samplePlaceholder: 'OES000001\nOES000002\nOES000003',
    runPlaceholder: 'OER000001\nOER000002\nOER000003',
    dataPlaceholder: 'OED000001\nOED000002\nOED000003',
    analysisPlaceholder: 'OEZ000001\nOEZ000002\nOEZ000003',
  });

  function handleAddTag(isAdd) {
    proxy.$refs['formRef'].validate(valid => {
      if (valid) {
        // typeNo换行切割，并trim
        form.value.nos = form.value.typeNo
          .split('\n')
          .map(x => x.trim())
          .filter(x => !isStrBlank(x));
        if (form.value.nos.length === 0) {
          proxy.$modal.msgError('类型ID不能为空！');
          return;
        }
        form.value.add = isAdd;
        proxy.$modal
          .confirm('不存在的类型ID将被跳过。确定要批量修改吗？')
          .then(() => {
            proxy.$modal.loading('正在更新...');
            batchModifySource(form.value)
              .then(response => {
                // 清空表单
                proxy.$modal.alertSuccess(
                  `${isAdd ? '添加标签' : '移除标签'} 成功！`,
                );
                getDataList();
              })
              .finally(() => {
                proxy.$modal.closeLoading();
              });
          });
      }
    });
  }

  function getDataList() {
    loading.value = true;
    getSourceProjectMetadataList(queryParams.value)
      .then(response => {
        // 将结果赋值给tableData
        tableData.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  /** 重置按钮操作 */
  function resetQuery() {
    queryParams.value.sourceProject = '';
    proxy.resetForm('queryRef');
    getDataList();
  }

  /** 排序触发事件 */
  function handleSortChange(column) {
    if (column.order) {
      queryParams.value.orderByColumn = column.prop;
      queryParams.value.isAsc = column.order;
      getDataList();
    }
  }

  function exportId() {
    proxy.download(
      'system/metadata/project/exportSourceProjectMetadataId',
      {
        ...queryParams.value,
      },
      `${queryParams.value.type}_ID_${new Date().getTime()}.txt`,
    );
  }
</script>

<style lang="scss" scoped>
  :deep(.el-textarea__inner) {
    border-radius: 12px;
  }
</style>
<style lang="scss">
  .el-popper {
    max-width: 350px !important;
  }
</style>
