<template>
  <div class="app-container">
    <div class="card list mb-1">
      <h3 class="mb-0 mt-0">请填写电子邮件和路径</h3>
      <el-divider></el-divider>
      <el-form ref="formRef" :model="form" :rules="rules">
        <el-form-item label="待同步人邮件" label-width="180" prop="email">
          <el-input
            v-model="form.email"
            style="width: 400px"
            clearable
            placeholder="填写前台用户的email地址"
          />
        </el-form-item>
        <el-form-item label="同步路径" label-width="180" prop="path">
          <el-input
            v-model="form.path"
            clearable
            placeholder="填写需要同步的ftp目录地址,直接从用户的ftp根目录开始即可"
            style="width: 400px"
          />
        </el-form-item>
        <el-form-item label="MD5提供方式" prop="readMd5" label-width="180">
          <el-radio-group v-model="form.readMd5">
            <el-radio :label="1">MD5由Node提供</el-radio>
            <el-radio :label="0">MD5由用户提供</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :disabled="isStrBlank(form.email) || isStrBlank(form.path)"
            @click="handleGetRecordNum"
            >获取Ftp文件日志记录编号
          </el-button>
          <el-button
            type="primary"
            :disabled="isStrBlank(form.email) || isStrBlank(form.path)"
            @click="handleCheckFtpFileLog"
            >检查Ftp文件日志记录
          </el-button>
          <el-button
            type="warning"
            :disabled="isStrBlank(form.email) || isStrBlank(form.path)"
            @click="handleStartSync"
            >开始同步
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div v-if="showResultTable" class="card list">
      <h3 class="mb-0 mt-0">数据分配结果</h3>
      <el-divider></el-divider>
      <el-form
        v-show="showSearch"
        ref="searchForm"
        :model="queryParams"
        :inline="true"
      >
        <el-form-item label="路径">
          <el-input v-model="queryParams.path" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="getDataList"
            >搜索
          </el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table
        :data="tableData"
        style="width: 100%; margin-bottom: 20px"
        :header-cell-style="{
          backgroundColor: '#f2f2f2',
          color: '#333333',
          fontWeight: 700,
        }"
        border
      >
        <el-table-column prop="name" label="名称" min-width="120" />
        <el-table-column
          prop="path"
          label="路径"
          show-overflow-tooltip
          min-width="180"
        />
        <el-table-column prop="readableFileSize" label="大小" width="90" />
        <el-table-column
          prop="md5"
          label="MD5"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column prop="md5File" label="MD5文件" width="140" sortable>
          <template #default="scope">
            <div class="d-flex align-items-center">
              <el-icon
                v-if="scope.row.md5FileStatus === 'Provided'"
                :color="iconColor(scope.row.md5FileStatus)"
              >
                <CircleCheckFilled></CircleCheckFilled>
              </el-icon>
              <el-icon
                v-if="scope.row.md5FileStatus === 'Not Provided'"
                :color="iconColor(scope.row.md5FileStatus)"
              >
                <RemoveFilled />
              </el-icon>
              <el-icon
                v-if="scope.row.md5FileStatus === 'Invalid Format'"
                :color="iconColor(scope.row.md5FileStatus)"
              >
                <CircleCloseFilled />
              </el-icon>
              <span
                :style="{
                  color: iconColor(scope.row.md5FileStatus),
                }"
                >{{ getMd5StatusText(scope.row.md5FileStatus) }}</span
              >
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="createTime" label="提交日期" width="180">
          <template #default="scope">
            {{ parseTime(scope.row.createTime) }}
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        class="mb-1"
        :auto-scroll="false"
        @pagination="getDataList"
      />
    </div>
  </div>
</template>

<script setup>
  import { getCurrentInstance, reactive, ref, toRefs } from 'vue';
  import {
    checkFtpFileLog,
    getFtpFileRecordCount,
    startSync,
  } from '@/api/metadata/data';
  import { isStrBlank } from '@/utils';

  const { proxy } = getCurrentInstance();

  const data = reactive({
    total: 0,
    form: {
      email: '',
      path: '',
      readMd5: 0,
    },
    queryParams: {
      path: '',
      pageNum: 1,
      pageSize: 20,
    },
  });
  let rules = reactive({
    path: [
      {
        required: true,
        trigger: 'blur',
      },
    ],
    email: [
      {
        required: true,
        trigger: 'blur',
      },
    ],
    readMd5: [
      {
        required: true,
        trigger: 'blur',
      },
    ],
  });

  const { queryParams, form, total } = toRefs(data);

  const showResultTable = ref(false);
  const showSearch = ref(true);
  let dataList = ref([]);
  let tableData = ref([]);

  function resetQuery() {
    queryParams.value.path = '';
    getDataList();
  }

  function handleGetRecordNum() {
    proxy.$refs['formRef'].validate(valid => {
      if (valid) {
        proxy.$modal.loading('正在获取FtpFileLog记录编号，请稍候。..');
        getFtpFileRecordCount(form.value)
          .then(response => {
            proxy.$modal.alertSuccess(`FtpFileLog记录数量: ${response.data}`);
          })
          .finally(() => {
            proxy.$modal.closeLoading();
          });
      }
    });
  }

  function handleCheckFtpFileLog() {
    proxy.$refs['formRef'].validate(valid => {
      if (valid) {
        proxy.$modal.loading('正在检查ftp_file_log记录，请稍候。..');
        checkFtpFileLog(form.value)
          .then(response => {
            proxy.$modal.alertSuccess(`一致！数量 = ${response.data}`);
          })
          .finally(() => {
            proxy.$modal.closeLoading();
          });
      }
    });
  }

  function handleStartSync() {
    proxy.$refs['formRef'].validate(valid => {
      if (valid) {
        proxy.$modal.loading('同步中，请稍候');
        startSync(form.value)
          .then(response => {
            dataList.value = response.data;
            showResultTable.value = true;
            getDataList();
          })
          .finally(() => {
            proxy.$modal.closeLoading();
          });
      }
    });
  }

  function getDataList() {
    let list = dataList.value;
    if (!isStrBlank(queryParams.value.path)) {
      list = list.filter(
        item => item.path.indexOf(queryParams.value.path) !== -1,
      );
    }
    // 对list进行分页
    total.value = list.length;
    if (total.value > 0) {
      let start = (queryParams.value.pageNum - 1) * queryParams.value.pageSize;
      let end =
        start + queryParams.value.pageNum > total.value
          ? total.value
          : start + queryParams.value.pageSize;
      tableData.value = list.slice(start, end);
    } else {
      tableData.value = [];
    }
  }

  const iconColor = status => {
    if (status === 'Provided') {
      return '#3A78E8';
    } else if (status === 'Invalid Format') {
      return '#FF8989';
    } else if (status === 'Not Provided') {
      return '#999999';
    } else return '#999999';
  };

  const getMd5StatusText = status => {
    const statusMap = {
      'Provided': '已提供',
      'Not Provided': '未提供',
      'Invalid Format': '格式无效',
    };
    return statusMap[status] || status;
  };
</script>

<style lang="scss" scoped></style>
<style lang="scss">
  .el-popper {
    max-width: 350px !important;
  }
</style>
