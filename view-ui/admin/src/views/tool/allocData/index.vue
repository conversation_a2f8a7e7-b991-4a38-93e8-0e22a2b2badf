<template>
  <div class="app-container">
    <div class="card list mb-1">
      <h3 class="mb-0 mt-0">为已经上传的文件分配 数据ID 和 数据所有人</h3>
      <el-divider></el-divider>
      <div class="bg-gray p-10-15 mb-1">
        <div class="mb-03">使用该功能前，请确认如下几点：</div>
        <div>(1) 文件已经通过完整性检查和MD5校验</div>
        <div>(2) 原始文件和相应的MD5文件都存在, 一个文件对应一个MD5文件</div>
        <div>(3) 暂时不支持指定路径下的文件夹的检查</div>
        <div>
          (4) 路径下的所有文件都会分配新的数据,
          默认为private（私有），unarchived（未归档）状态
        </div>
      </div>
      <el-form ref="formRef" :model="form" :inline="true" :rules="rules">
        <el-form-item label="文件路径" prop="path">
          <el-input
            v-model="form.path"
            style="width: 350px"
            clearable
            placeholder="从/data开始填写"
          />
        </el-form-item>
        <el-form-item label="前台邮箱账号" prop="email">
          <el-input
            v-model="form.email"
            placeholder="填写NODE前台的邮箱账号"
            style="width: 350px"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :disabled="isStrBlank(form.path)"
            @click="handleCheckPath"
            >检查路径
          </el-button>
          <el-button
            type="primary"
            :disabled="isStrBlank(form.email)"
            @click="handleCheckEmail"
            >检查邮箱
          </el-button>
          <el-button
            type="warning"
            :disabled="isStrBlank(form.path) || isStrBlank(form.email)"
            @click="handleStartAlloc"
            >开始分配数据编号和创建人
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div v-if="showResultTable" class="card list">
      <h3 class="mb-0 mt-0">数据分配结果</h3>
      <el-divider></el-divider>
      <el-form
        v-show="showSearch"
        ref="searchForm"
        :model="queryParams"
        :inline="true"
      >
        <el-form-item label="数据ID">
          <el-input v-model="queryParams.dataNo" clearable></el-input>
        </el-form-item>
        <el-form-item label="路径">
          <el-input v-model="queryParams.filePath" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="getDataList"
            >搜索
          </el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table
        :data="tableData"
        style="width: 100%; margin-bottom: 20px"
        :header-cell-style="{
          backgroundColor: '#f2f2f2',
          color: '#333333',
          fontWeight: 700,
        }"
        border
      >
        <el-table-column prop="datNo" label="数据ID" min-width="120" />
        <el-table-column
          prop="filePath"
          label="路径"
          show-overflow-tooltip
          min-width="180"
        />
        <el-table-column prop="readableFileSize" label="大小" width="90" />
        <el-table-column prop="security" label="安全性" width="90" />
        <el-table-column prop="creatorEmail" label="创建人" width="180" />
        <el-table-column
          prop="md5"
          label="MD5"
          min-width="140"
          show-overflow-tooltip
        />

        <el-table-column prop="createDate" label="提交日期" width="180">
          <template #default="scope">
            {{ parseTime(scope.row.createDate) }}
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        class="mb-1"
        :auto-scroll="false"
        @pagination="getDataList"
      />
    </div>
  </div>
</template>

<script setup>
  import { getCurrentInstance, reactive, ref, toRefs } from 'vue';
  import { isNumberStr, isStrBlank } from '@/utils';
  import { checkEmail, checkPath, startAlloc } from '@/api/metadata/data';
  import { parseTime } from '@/utils/ruoyi';

  const { proxy } = getCurrentInstance();

  const data = reactive({
    total: 0,
    form: {
      path: '',
      email: '',
    },
    queryParams: {
      dataNo: '',
      filePath: '',
      pageNum: 1,
      pageSize: 20,
    },
  });

  const { queryParams, form, total } = toRefs(data);

  let dataList = ref([]);
  let tableData = ref([]);

  let rules = reactive({
    path: [
      {
        required: true,
        trigger: 'blur',
      },
    ],
    email: [
      {
        required: true,
        trigger: 'blur',
      },
    ],
  });

  function handleCheckPath() {
    if (isStrBlank(form.value.path)) {
      proxy.$modal.msgError('请填写文件路径');
      return;
    }

    proxy.$modal.loading('正在检查路径，请稍候...');
    checkPath(form.value.path)
      .then(response => {
        if (isNumberStr(response.data)) {
          proxy.$modal.alertSuccess(
            `路径检查通过！${response.data} 个文件需要分配数据ID。`,
          );
        } else {
          proxy.$modal.alertError(response.data);
        }
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  function resetQuery() {
    queryParams.value.dataNo = '';
    queryParams.value.filePath = '';

    getDataList();
  }

  function handleCheckEmail() {
    if (isStrBlank(form.value.email)) {
      proxy.$modal.msgError('请填写邮箱');
      return;
    }
    proxy.$modal.loading('正在检查邮箱，请稍候...');
    checkEmail(form.value.email)
      .then(response => {
        proxy.$modal.alertSuccess(`邮箱检查通过！`);
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  function handleStartAlloc() {
    proxy.$refs['formRef'].validate(valid => {
      if (valid) {
        proxy.$modal.loading('正在分配中...，请稍候...');
        startAlloc(form.value)
          .then(response => {
            dataList.value = response.data;
            showResultTable.value = true;
            getDataList();
          })
          .finally(() => {
            proxy.$modal.closeLoading();
          });
      }
    });
  }

  function getDataList() {
    let list = dataList.value;
    if (!isStrBlank(queryParams.value.dataNo)) {
      list = list.filter(
        item => item.datNo.indexOf(queryParams.value.dataNo) !== -1,
      );
    }
    if (!isStrBlank(queryParams.value.filePath)) {
      list = list.filter(
        item => item.filePath.indexOf(queryParams.value.filePath) !== -1,
      );
    }
    // 对list进行分页
    total.value = list.length;
    if (total.value > 0) {
      let start = (queryParams.value.pageNum - 1) * queryParams.value.pageSize;
      let end =
        start + queryParams.value.pageNum > total.value
          ? total.value
          : start + queryParams.value.pageSize;
      tableData.value = list.slice(start, end);
    } else {
      tableData.value = [];
    }
  }

  const showResultTable = ref(false);
  const showSearch = ref(true);
</script>

<style lang="scss" scoped></style>
<style lang="scss">
  .el-popper {
    max-width: 350px !important;
  }
</style>
