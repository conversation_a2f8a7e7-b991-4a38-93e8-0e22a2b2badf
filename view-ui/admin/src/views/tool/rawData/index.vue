<template>
  <div class="app-container">
    <div class="card list">
      <el-row>
        <el-col :span="24" justify="space-between">
          <el-form ref="formRef" :model="queryParams" :inline="true">
            <el-form-item label="项目ID" prop="projNos">
              <el-input
                v-model="queryParams.projNo"
                type="textarea"
                rows="1"
                style="width: 180px"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label="实验ID" prop="expNos">
              <el-input
                v-model="queryParams.expNo"
                type="textarea"
                rows="1"
                style="width: 180px"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label="样本ID" prop="sapNos">
              <el-input
                v-model="queryParams.sapNo"
                type="textarea"
                rows="1"
                style="width: 180px"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label="实验类型" prop="expType">
              <el-input
                v-model="queryParams.expType"
                style="width: 180px"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label="样本类型" prop="sapType">
              <el-input
                v-model="queryParams.sapType"
                style="width: 180px"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label="生物体" prop="organism">
              <el-input
                v-model="queryParams.organism"
                style="width: 180px"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label="安全性" prop="security">
              <el-select
                v-model="queryParams.security"
                style="width: 180px"
                multiple
                clearable
              >
                <el-option value="Private">私有</el-option>
                <el-option value="Restricted">受限</el-option>
                <el-option value="Public">公开</el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="数据类型" prop="dataType">
              <el-input
                v-model="queryParams.dataType"
                style="width: 180px"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="getDataList"
                >搜索
              </el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button
                icon="Download"
                type="warning"
                plain
                @click="handleExport"
                >导出
              </el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>

      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%; margin-bottom: 20px"
        :header-cell-style="{
          backgroundColor: '#f2f2f2',
          color: '#333333',
          fontWeight: 700,
        }"
        border
        :default-sort="defaultSort"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="projNo" label="项目ID" width="120" sortable>
          <template #default="scope">
            <a
              class="text-primary"
              href="javascript:void(0)"
              @click="
                showDetail('project', scope.row.projNo, scope.row.creator)
              "
            >
              {{ scope.row.projNo }}
            </a>
          </template>
        </el-table-column>
        <el-table-column
          prop="projName"
          label="项目名称"
          width="150"
          show-overflow-tooltip
        />
        <el-table-column prop="expNo" label="实验ID" width="150" sortable>
          <template #default="scope">
            <a
              class="text-primary"
              href="javascript:void(0)"
              @click="
                showDetail('experiment', scope.row.expNo, scope.row.creator)
              "
            >
              {{ scope.row.expNo }}
            </a>
          </template>
        </el-table-column>
        <el-table-column
          prop="expName"
          label="实验名称"
          width="150"
          show-overflow-tooltip
        />
        <el-table-column
          prop="expType"
          label="实验类型"
          width="150"
          show-overflow-tooltip
        />
        <el-table-column
          prop="sapNo"
          label="样本ID"
          width="120"
          sortable
          show-overflow-tooltip
        >
          <template #default="scope">
            <a
              class="text-primary"
              href="javascript:void(0)"
              @click="showDetail('sample', scope.row.sapNo, scope.row.creator)"
            >
              {{ scope.row.sapNo }}
            </a>
          </template>
        </el-table-column>
        <el-table-column
          prop="sapName"
          label="样本名称"
          width="150"
          sortable
          show-overflow-tooltip
        />
        <el-table-column
          prop="sapType"
          label="样本类型"
          width="120"
          show-overflow-tooltip
        />
        <el-table-column
          prop="organism"
          label="生物体"
          width="120"
          show-overflow-tooltip
        />
        <el-table-column
          prop="runNo"
          label="批次ID"
          width="120"
          sortable
          show-overflow-tooltip
        >
          <template #default="scope">
            <a
              class="text-primary"
              href="javascript:void(0)"
              @click="showDetail('run', scope.row.runNo, scope.row.creator)"
            >
              {{ scope.row.runNo }}
            </a>
          </template>
        </el-table-column>
        <el-table-column
          prop="runName"
          label="批次名称"
          width="120"
          show-overflow-tooltip
        />
        <el-table-column
          prop="datNo"
          label="数据ID"
          width="120"
          sortable
          show-overflow-tooltip
        />
        <el-table-column
          prop="name"
          label="数据名称"
          width="120"
          show-overflow-tooltip
        />
        <el-table-column
          prop="dataType"
          label="数据类型"
          width="100"
          show-overflow-tooltip
        />
        <el-table-column
          prop="security"
          label="安全性"
          width="100"
          show-overflow-tooltip
        />
        <el-table-column
          prop="fileSize"
          label="文件大小"
          width="120"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ scope.row.readableFileSize }}
          </template>
        </el-table-column>
        <el-table-column
          prop="creator"
          label="创建人"
          width="300"
          show-overflow-tooltip
        >
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        class="mb-1"
        @pagination="getDataList"
      />
    </div>
  </div>
</template>

<script setup>
  import { getCurrentInstance, onMounted, reactive, toRefs } from 'vue';
  import { findRelatedDataPage } from '@/api/tool/rawdata';
  import { isStrBlank } from '@/utils';
  import { createAccessToken } from '@/api/login';

  onMounted(() => {
    getDataList();
  });

  const { proxy } = getCurrentInstance();

  const data = reactive({
    tableData: [],
    total: 0,
    queryParams: {
      projNo: '',
      expNo: '',
      sapNo: '',
      projNos: [],
      expNos: [],
      sapNos: [],
      expType: '',
      sapType: '',
      organism: '',
      security: [],
      dataType: '',
      pageNum: 1,
      pageSize: 20,
      orderByColumn: 'createDate',
      isAsc: 'descending',
    },
    dateRange: [],
    loading: true,
    defaultSort: { prop: 'createDate', order: 'descending' },
  });

  /** 解构 */
  const { tableData, total, queryParams, dateRange, loading, defaultSort } =
    toRefs(data);

  function resetQuery() {
    dateRange.value = [];
    queryParams.value.projNo = '';
    queryParams.value.expNo = '';
    queryParams.value.sapNo = '';
    proxy.$refs['formRef'].resetFields();

    getDataList();
  }

  /** 排序触发事件 */
  function handleSortChange(column) {
    if (column.order) {
      queryParams.value.orderByColumn = column.prop;
      queryParams.value.isAsc = column.order;
      getDataList();
    }
  }

  function obtainQueryParams() {
    if (!isStrBlank(queryParams.value.projNo)) {
      queryParams.value.projNos = queryParams.value.projNo
        .split('\n')
        .map(x => x.trim())
        .filter(x => !isStrBlank(x));
    } else {
      queryParams.value.projNos = [];
    }
    if (!isStrBlank(queryParams.value.expNo)) {
      queryParams.value.expNos = queryParams.value.expNo
        .split('\n')
        .map(x => x.trim())
        .filter(x => !isStrBlank(x));
    } else {
      queryParams.value.expNos = [];
    }
    if (!isStrBlank(queryParams.value.sapNo)) {
      queryParams.value.sapNos = queryParams.value.sapNo
        .split('\n')
        .map(x => x.trim())
        .filter(x => !isStrBlank(x));
    } else {
      queryParams.value.sapNos = [];
    }
  }

  function getDataList() {
    loading.value = true;
    obtainQueryParams();
    findRelatedDataPage(queryParams.value)
      .then(response => {
        let pageInfo = response.data;
        // 将结果赋值给tableData
        tableData.value = pageInfo.list;
        total.value = pageInfo.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  function handleExport() {
    obtainQueryParams();
    proxy.$modal
      .confirm('最多可导出前50,000条数据。是否继续导出？')
      .then(() => {
        proxy.download(
          '/system/nodeRelatedEs/download',
          {
            query: JSON.stringify(queryParams.value),
          },
          `Related_Data_${new Date().getTime()}.xlsx`,
        );
      });
  }

  function showDetail(type, typeNo, creator) {
    // 预先生成access_token
    createAccessToken({ memberId: creator }).then(response => {
      const token = response.data;
      let href = `${import.meta.env.VITE_APP_WEB_URL}/${type}/detail/${
        typeNo
      }?access-token=${token}`;
      // 打开一个新页面
      window.open(href);
    });
  }
</script>

<style lang="scss" scoped>
  :deep(.el-dialog__body) {
    padding: 10px !important;

    .el-select,
    .el-input {
      width: 330px;
    }
  }

  :deep(.el-textarea__inner) {
    border-radius: 12px;
  }
</style>
<style lang="scss">
  .el-popper {
    max-width: 350px !important;
  }
</style>
