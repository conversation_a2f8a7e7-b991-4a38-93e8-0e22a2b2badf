<template>
  <div class="app-container">
    <div class="card list">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%; margin-bottom: 20px"
        :header-cell-style="{
          backgroundColor: '#f2f2f2',
          color: '#333333',
          fontWeight: 700,
        }"
        border
      >
        <el-table-column prop="dictName" label="字典名称" />
        <el-table-column prop="description" label="描述" />
        <el-table-column prop="lastRecordCount" label="上次记录数" />
        <el-table-column prop="recordCount" label="当前记录数" />
        <el-table-column prop="updateTime" label="更新日期" />
        <el-table-column prop="status" label="状态" />
        fixed="right"
        <el-table-column prop="operate" label="操作">
          <template #default="scope">
            <el-tooltip content="同步到MongoDB">
              <svg-icon
                icon-class="mongodb"
                class-name="meta-svg"
                @click="syncToDb(scope.row.dictName)"
              ></svg-icon>
            </el-tooltip>
            <el-tooltip content="同步到索引">
              <svg-icon
                icon-class="index"
                class-name="meta-svg"
                @click="syncToEs(scope.row.dictName)"
              ></svg-icon>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup>
  import { getCurrentInstance, onMounted, reactive, toRefs } from 'vue';
  import {
    listDmsDictSync,
    syncDictToDb,
    syncDictToEs,
  } from '@/api/tool/dmsDictSync';

  const { proxy } = getCurrentInstance();

  const data = reactive({
    tableData: [],
    loading: true,
  });
  /** 解构 */
  const { tableData, loading } = toRefs(data);

  onMounted(() => {
    getDataList();
  });

  function getDataList() {
    loading.value = true;
    listDmsDictSync()
      .then(response => {
        tableData.value = response.data;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  function syncToDb(dictName) {
    syncDictToDb(dictName)
      .then(() => {
        proxy.$modal.msgSuccess('正在同步到数据库，请稍候...');
      })
      .finally(() => {
        getDataList();
      });
  }

  function syncToEs(dictName) {
    syncDictToEs(dictName)
      .then(() => {
        proxy.$modal.msgSuccess('正在同步到索引，请稍候...');
      })
      .finally(() => {
        getDataList();
      });
  }
</script>

<style scoped lang="scss">
  .meta-svg {
    width: 20px;
    height: 20px;
    margin-right: 0.5rem;
    cursor: pointer;
  }
</style>
