<template>
  <div class="app-container">
    <div v-loading="tempLoadingFlag" class="card list">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" icon="Plus" @click="handleAdd">
            添加模板
          </el-button>
        </el-col>
        <right-toolbar :search="false" @query-table="getList"></right-toolbar>
      </el-row>
      <!--模块列表-->
      <el-table
        :data="tempTableData.tableData"
        style="width: 100%; margin-bottom: 20px"
        :header-cell-style="{
          backgroundColor: '#f2f2f2',
          color: '#333333',
          fontWeight: 700,
        }"
        border
        height="380px"
      >
        <el-table-column prop="emailNo" label="ID" width="130" />
        <el-table-column
          prop="subject"
          label="主题"
          show-overflow-tooltip
          min-width="180"
        />
        <el-table-column prop="remark" label="备注" show-overflow-tooltip />
        <el-table-column
          prop="createTime"
          label="创建时间"
          show-overflow-tooltip
          width="160"
        >
        </el-table-column>
        <el-table-column label="群发状态" show-overflow-tooltip width="150">
          <template #default="scope">
            <div v-if="scope.row.sendingAll" style="color: #e8b66c">
              <span>
                <el-icon size="16">
                  <Loading />
                </el-icon>
              </span>
              群发中
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="90">
          <template #default="scope">
            <el-tooltip content="编辑">
              <svg-icon
                icon-class="edits"
                class-name="meta-svg"
                @click="editTemplate(scope.row)"
              ></svg-icon>
            </el-tooltip>
            <el-tooltip content="发送邮件">
              <svg-icon
                icon-class="send"
                class-name="send-svg"
                @click="sendEmail(scope.row)"
              ></svg-icon>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <WebPage
        ref="web_page_template"
        :key="`webPg_temp_${tempTableData.changeNum}`"
        v-model="tempTableData"
      />
    </div>

    <!--邮件日志-->
    <div class="card list mt-1">
      <!--<el-divider></el-divider>-->
      <div v-loading="queryParams.loading">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <h3 class="mb-0 mt-0">邮件发送日志</h3>
          </el-col>
          <right-toolbar
            :search="false"
            @query-table="getSendLogList"
          ></right-toolbar>
        </el-row>
        <el-table
          :max-height="400"
          :data="logTable"
          style="width: 100%; margin-bottom: 20px"
          :header-cell-style="{
            backgroundColor: '#f2f2f2',
            color: '#333333',
            fontWeight: 700,
          }"
          border
          :default-sort="{ prop: 'createTime', order: 'descending' }"
          @sort-change="tableSortChange"
        >
          <el-table-column
            prop="emailNo"
            label="模板ID"
            width="120"
            sortable
            :sort-orders="['ascending', 'descending']"
          />
          <el-table-column
            prop="subject"
            label="主题"
            min-width="140"
            show-overflow-tooltip
            sortable
            :sort-orders="['ascending', 'descending']"
          />
          <el-table-column prop="remark" label="备注" show-overflow-tooltip />
          <el-table-column
            prop="createTime"
            label="发送时间"
            width="160"
            :sort-orders="['ascending', 'descending']"
            sortable
          >
          </el-table-column>
          <el-table-column
            prop="recipient"
            label="收件人"
            min-width="180"
            show-overflow-tooltip
            sortable
            :sort-orders="['ascending', 'descending']"
          />
          <el-table-column
            prop="status"
            label="状态"
            min-width="80"
            sortable
            :sort-orders="['ascending', 'descending']"
          >
            <template #default="scope">
              <div class="d-flex align-items-center">
                <div
                  v-if="scope.row.status === 'success'"
                  style="color: #07bcb4"
                >
                  <el-icon size="19">
                    <CircleCheckFilled />
                  </el-icon>
                  <span class="ml-05 mr-05">{{
                    getStatusText(scope.row.status)
                  }}</span>
                </div>
                <div
                  v-else-if="scope.row.status === 'sending'"
                  style="color: #e8b66c"
                >
                  <el-icon size="19">
                    <Loading />
                  </el-icon>
                  <span class="ml-05 mr-05">{{
                    getStatusText(scope.row.status)
                  }}</span>
                </div>
                <div v-else style="color: #ff8181">
                  <el-icon size="19">
                    <CircleCloseFilled />
                  </el-icon>
                  <span class="ml-05 mr-05">{{
                    getStatusText(scope.row.status)
                  }}</span>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="90">
            <template #default="scope">
              <el-tooltip content="详情">
                <svg-icon
                  icon-class="view"
                  class-name="meta-svg"
                  @click="logDetail(scope.row)"
                ></svg-icon>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="queryParams.total > 0"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          :total="queryParams.total"
          class="mb-1"
          @pagination="
            () => {
              getSendLogList();
            }
          "
        />
      </div>
    </div>

    <!-- 添加/编辑邮件模块,发送邮件 -->
    <el-dialog v-model="open" :title="title" width="950px" append-to-body>
      <el-form
        ref="emailTempRef"
        v-loading="tempLoadingFlag || uploadTemp.isUploading"
        :model="form"
        :rules="rules"
        label-width="130px"
      >
        <el-form-item label="主题" prop="subject">
          <el-input
            v-model="form.subject"
            :disabled="disabled"
            :placeholder="
              disabled
                ? '国家组学数据百科全书-未归档临时数据通知'
                : '请输入邮件主题'
            "
          />
        </el-form-item>

        <el-form-item label="模板文件" prop="templateName">
          <el-upload
            ref="uploadTempRef"
            :key="'uploadTempKey'"
            :limit="1"
            :accept="uploadTemp.fileType.join(',')"
            :headers="uploadTemp.headers"
            :action="`${uploadTemp.url}?id=${currRowId}`"
            :disabled="uploadTemp.isUploading || isSendEmail"
            :drag="!isSendEmail"
            :before-upload="checkBeforeTempUpload"
            :on-progress="handleFileUploadProgress"
            :on-success="handleFileSuccess"
            :on-error="handleFileError"
            :auto-upload="true"
            :show-file-list="false"
            style="width: 800px"
            class="upload-demo"
          >
            <el-icon v-if="!isSendEmail" class="el-icon--upload">
              <upload-filled />
            </el-icon>
            <div class="el-upload__text">
              <div v-if="!isSendEmail">拖拽文件到此处或 <em>点击上传</em></div>
              <div v-else>预览模板文件:</div>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                已上传文件: {{ form.templateName }} &nbsp;&nbsp;
                <el-button
                  round
                  size="small"
                  type="primary"
                  :icon="View"
                  @click="openPreDialog(form.randomFileName)"
                  >预览
                </el-button>
              </div>
            </template>
          </el-upload>
        </el-form-item>

        <el-form-item
          v-if="title === '添加模板' || title === '编辑模板'"
          label="备注"
          prop="remark"
        >
          <el-input
            v-model="form.remark"
            :rows="2"
            type="textarea"
            placeholder="请输入备注"
          />
        </el-form-item>

        <el-form-item v-else label="收件人" prop="configValue">
          <el-tabs
            v-model="form.sendType"
            type="border-card"
            style="width: 100%"
            @tab-change="sendEmailTypeChange"
          >
            <el-tab-pane label="单独发送" name="Single send">
              <el-input
                v-show="!form.allMember"
                v-model="form.recipient"
                type="textarea"
                rows="5"
                :disabled="title === '详情'"
                placeholder="请输入收件人(多个收件人用换行分隔)"
              />
            </el-tab-pane>
            <el-tab-pane label="批量发送" name="Batch send">
              <el-checkbox
                v-model="form.allMember"
                :disabled="title === '详情'"
                label="所有成员"
                @change="changeAllMember"
              />
              <el-input
                v-show="!form.allMember"
                v-model="form.recipient"
                type="textarea"
                rows="5"
                :disabled="title === '详情'"
                placeholder="请输入收件人(多个收件人用换行分隔)"
              />
            </el-tab-pane>
          </el-tabs>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="text-center">
          <el-button
            :disabled="tempLoadingFlag"
            type="primary"
            @click="saveTempForm"
            >{{ isSendEmail ? '发送邮件' : '保存' }}
          </el-button>
          <el-button @click="open = false">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 邮件模版预览弹窗-->
    <el-dialog
      v-model="preOpen"
      title="模板预览"
      width="1024px"
      append-to-body
      :z-index="3000"
      draggable
    >
      <div class="file-pre" v-html="htmlContent"></div>
    </el-dialog>

    <!-- 发送邮件的日志详情 -->
    <el-dialog
      v-model="detailDialog"
      title="邮件发送日志详情"
      width="970px"
      class="detail-dialog"
    >
      <el-form :model="detailForm" label-width="130px">
        <el-form-item label="模板名称">
          <div>{{ detailForm.subject }}</div>
        </el-form-item>
        <el-form-item label="发送时间">
          <div>{{ formatDate(detailForm.createTime) }}</div>
        </el-form-item>
        <el-form-item label="最后更新时间">
          <div>{{ formatDate(detailForm.updateTime) }}</div>
        </el-form-item>
        <el-form-item label="状态">
          <div>{{ getStatusText(detailForm.status) }}</div>
        </el-form-item>
        <el-form-item v-if="detailForm.status === 'error'" label="失败原因">
          <div>{{ detailForm.errorMsg }}</div>
        </el-form-item>

        <el-form-item label="模板内容">
          <div
            style="
              margin: 0 auto;
              max-height: 510px;
              overflow-y: auto;
              padding: 8px;
              border: 1px solid rgba(224, 216, 216, 0.54);
              border-radius: 5px;
            "
            v-html="detailForm.content"
          ></div>
        </el-form-item>

        <el-form-item label="收件人" prop="configValue">
          <el-row :gutter="10">
            <el-col :span="23">
              <div :class="expand ? 'collapse' : ''" style="width: 560px">
                {{ detailForm.recipient }}
              </div>
            </el-col>
            <el-col :span="1">
              <el-icon
                color="#3A78E8"
                class="cursor-pointer"
                @click="expand = !expand"
              >
                <ArrowDownBold v-if="expand" />

                <ArrowUpBold v-else />
              </el-icon>
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script setup>
  import { getToken } from '@/utils/auth';
  import {
    computed,
    getCurrentInstance,
    nextTick,
    reactive,
    ref,
    toRaw,
    toRefs,
  } from 'vue';
  import {
    previewTemp,
    saveEmailTemp,
    sendBatchEmail,
    sendLogList,
    templateList,
  } from '@/api/tool/sendingEmail';
  import { formatDate, isArrEmpty, isEmail, trimStr } from '@/utils';
  import WebPage from '@/components/Pagination/webPage.vue';
  import { View } from '@element-plus/icons-vue';
  import { ElMessageBox } from 'element-plus';

  const { proxy } = getCurrentInstance();

  const isSendEmail = computed(() => title.value === '发送邮件');

  const tempLoadingFlag = ref(false);
  // 项目列表数据，使用前端分页插件
  const tempTableData = ref({
    tableData: [],
    originalTableData: [],
    changeNum: 0,
  });

  const data = reactive({
    form: {
      id: '',
      subject: '',
      templateName: '',
      randomFileName: '',
      remark: '',
      sendType: 'Single send',
      allMember: false,
      recipient: '',
    },
    rules: {
      subject: [
        {
          pattern: /^(?!\s+$).{1,255}$/,
          required: true,
          message: '格式不正确',
          trigger: 'blur',
        },
      ],
      templateName: [
        {
          required: true,
          message: '模板文件是必需的',
          trigger: 'change',
        },
      ],
    },
    detailForm: {
      subject: '',
      status: '',
      recipient: '',
      errorMsg: '',
      content: '',
      createTime: '',
      updateTime: '',
    },
  });

  const { form, detailForm, rules } = toRefs(data);
  // 表单初始数据
  const formInit = reactive(proxy.$_.cloneDeep(toRaw(data.form)));
  const detailFormInit = reactive(proxy.$_.cloneDeep(toRaw(data.detailForm)));
  const currRowId = ref('');
  const currLogRowId = ref('');
  /*** 模版导入参数 */
  const uploadTemp = reactive({
    fileType: ['.ftl'],
    // 弹出层标题（用户导入）
    title: '',
    // 是否禁用上传
    isUploading: false,
    // 设置上传的请求头部
    headers: { Authorization: 'Bearer ' + getToken() },
    // 上传的地址
    url:
      import.meta.env.VITE_APP_BASE_API +
      `/system/batchEmail/uploadEmailTemplate`,
  });

  /**文件上传中处理 */
  function checkBeforeTempUpload(file) {
    return new Promise((resolve, reject) => {
      const fileName = file.name.split('.');
      const fileExt = `.${fileName[fileName.length - 1]}`.toLowerCase();
      const isTypeOk = uploadTemp.fileType.indexOf(fileExt) >= 0;
      if (!isTypeOk) {
        proxy.$modal.msgError(
          `请上传 ${uploadTemp.fileType.join('/')} 格式的文件！`,
        );
        reject();
      } else {
        resolve();
      }
    });
  }

  /**文件上传中处理 */
  function handleFileUploadProgress() {
    uploadTemp.isUploading = true;
  }

  /** 文件上传成功处理 */
  function handleFileSuccess(response, file, fileList, isExample) {
    uploadTemp.isUploading = false;
    proxy.$refs['uploadTempRef'].handleRemove(file);
    if (response.code === 200 && response.data) {
      let data = response.data;
      form.value.templateName = trimStr(data.templateName);
      form.value.randomFileName = trimStr(data.randomFileName);
      // form.value.filePreview = trimStr(data.filePreview);
      proxy.$modal.msgSuccess('上传成功');
    } else {
      form.value.templateName = '';
      form.value.randomFileName = '';
      // form.value.filePreview = '';
      proxy.$modal.alertError(response.msg);
    }
  }

  function handleFileError() {
    uploadTemp.isUploading = false;
  }

  function openPreDialog(fileName) {
    preOpen.value = true;
    htmlContent.value = '';
    previewTemp({ name: fileName }).then(res => {
      if (res.code === 200) {
        htmlContent.value = res.msg;
      }
    });
  }

  // 查询条件
  const queryParams = reactive({
    loading: false,
    total: 0,
    pageNum: 1,
    pageSize: 10,
    sortKey: 'createTime',
    sortType: 'desc',
  });

  const logTable = reactive([]);

  const open = ref(false);
  const preOpen = ref(false);
  const htmlContent = ref('');
  const disabled = ref(false);
  const title = ref('');
  const expand = ref(true);

  const detailDialog = ref(false);

  /** 重置操作表单 */
  async function reset() {
    await nextTick(() => {
      proxy.resetForm('emailTempRef');
      proxy.$refs['uploadTempRef']?.clearFiles();
      form.value = proxy.$_.cloneDeep(toRaw(formInit));
    });
  }

  const getStatusText = status => {
    const statusMap = {
      success: '成功',
      sending: '发送中',
      error: '错误',
      failed: '失败',
    };
    return statusMap[status] || status;
  };

  /** 添加email弹窗 */
  async function handleAdd() {
    await reset();
    currRowId.value = '';
    disabled.value = false;
    title.value = '添加模板';
    open.value = true;
  }

  /** 编辑email弹窗 */
  async function editTemplate(row) {
    await fillRowData(row);
    disabled.value = false;
    open.value = true;
    title.value = '编辑模板';
  }

  /** 打开发送邮件弹窗 */
  async function sendEmail(row) {
    await fillRowData(row);
    disabled.value = true;
    open.value = true;
    title.value = '发送邮件';
  }

  /** 填充行数据到弹窗表单中 */
  async function fillRowData(rowObj) {
    let row = proxy.$_.cloneDeep(toRaw(rowObj));
    await reset();
    // 设置编辑弹窗回显数据
    for (let key in form.value) {
      if (key in row) {
        form.value[key] = row[key];
      }
    }
    currRowId.value = row.id;
  }

  function sendEmailTypeChange(val) {
    form.value.recipient = '';
    form.value.allMember = false;
  }

  function changeAllMember(val) {
    if (val) {
      form.value.recipient = '';
    }
  }

  /** 保存email模版 */
  function saveTempForm() {
    proxy.$refs['emailTempRef'].validate(valid => {
      if (valid) {
        const param = { ...form.value };
        const sendEmailFlag = isSendEmail.value;
        if (sendEmailFlag) {
          // 发送邮件
          const allMember = param.allMember;
          if (!allMember) {
            let recipient = trimStr(param.recipient);
            let emailArr = recipient.split('\n');
            let emailErrorArr = [];
            let emailAll = [];
            emailArr.forEach(item => {
              item = trimStr(item);
              if (item) {
                if (!isEmail(item)) {
                  emailErrorArr.push(item);
                } else {
                  emailAll.push(item);
                }
              }
            });
            if (emailErrorArr.length > 0) {
              proxy.$modal.alertError(
                '无效邮箱: ' + JSON.stringify(emailErrorArr),
              );
              return false;
            } else if (emailAll.length === 0) {
              proxy.$modal.alertError('收件人不能为空');
              return false;
            } else {
              param.recipient = [...new Set(emailAll)].join(',');
            }
          }

          tempLoadingFlag.value = true;
          const confirmMsg = allMember
            ? `确定要发送邮件给 <strong class="text-danger">所有节点用户</strong> 吗？这将消耗大量时间`
            : `确定要发送邮件吗？`;
          ElMessageBox.confirm(confirmMsg, '提示', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
            dangerouslyUseHTMLString: true,
          })
            .then(() => {
              sendBatchEmail(param)
                .then(res => {
                  tempLoadingFlag.value = false;
                  if (res.code === 200) {
                    proxy.$modal.msgSuccess(`邮件批量发送成功`);
                    open.value = false;
                    getList();
                  }
                })
                .catch(() => {
                  tempLoadingFlag.value = false;
                });
            })
            .catch(() => {
              tempLoadingFlag.value = false;
            });
        } else {
          tempLoadingFlag.value = true;
          // 保存邮件模版
          saveEmailTemp(param)
            .then(res => {
              tempLoadingFlag.value = false;
              if (res.code === 200) {
                proxy.$modal.msgSuccess(`${param.id ? '更新' : '添加'} 成功`);
                open.value = false;
                getList();
              }
            })
            .catch(() => {
              tempLoadingFlag.value = false;
            });
        }
      }
    });
  }

  function logDetail(rowObj) {
    // Object.assign(detailForm.value, row);
    let row = proxy.$_.cloneDeep(toRaw(rowObj));
    detailForm.value = proxy.$_.cloneDeep(toRaw(detailFormInit));
    // 设置编辑弹窗回显数据
    for (let key in detailForm.value) {
      if (key in row) {
        detailForm.value[key] = row[key];
      }
    }
    currLogRowId.value = row.id;
    detailDialog.value = true;
  }

  function tableSortChange(column) {
    let { prop, order } = column;
    if (order) {
      queryParams.sortKey = prop;
      queryParams.sortType = order === 'ascending' ? 'asc' : 'desc';
      getSendLogList();
    }
  }

  // 读取发送日志列表
  function getSendLogList() {
    queryParams.loading = true;
    logTable.length = 0;
    sendLogList(queryParams)
      .then(res => {
        if (res.code === 200) {
          let data = res.data;
          queryParams.total = data.totalElements;
          logTable.push(...data.content);
        } else {
          queryParams.total = 0;
        }
      })
      .catch(() => {
        queryParams.total = 0;
      })
      .finally(() => {
        queryParams.loading = false;
      });
  }

  // 读取邮件模版列表
  function getList() {
    tempLoadingFlag.value = true;
    tempTableData.value.originalTableData = [];
    templateList()
      .then(res => {
        let data = res.data;
        if (!isArrEmpty(data)) {
          tempTableData.value.originalTableData = data;
          tempTableData.value.changeNum++;
        }
      })
      .then(() => {
        getSendLogList();
      })
      .finally(() => {
        tempLoadingFlag.value = false;
      });
  }

  getList();
</script>

<style lang="scss" scoped>
  :deep(.el-textarea__inner) {
    border-radius: 12px;
  }

  .collapse {
    height: 30px;
    overflow: hidden;
    white-space: nowrap; /* 确保文本在一行内显示 */
    text-overflow: ellipsis; /* 使用省略号表示溢出的文本 */
  }

  .meta-svg {
    width: 20px;
    height: 20px;
    margin-right: 0.5rem;
    cursor: pointer;
  }

  .send-svg {
    width: 23px;
    height: 23px;
    margin-right: 0.5rem;
    cursor: pointer;
    position: relative;
    bottom: -1px;
  }

  :deep(.el-upload-dragger) {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 16px;
    background-color: #eff3f9;

    .el-icon--upload {
      margin-right: 0.5rem;
      font-size: 60px;
      //color: #fe7f2b;
      margin-bottom: 0;
    }
  }

  .file-pre {
    width: 100%;
    height: 500px;
    max-height: 700px;
    overflow-y: auto;
    border: 1px solid #d9d5d5;
    border-radius: 5px;
  }
</style>
<style lang="scss">
  .el-popper {
    max-width: 350px !important;
  }
</style>
