<template>
  <div class="app-container">
    <div class="card list">
      <el-form ref="searchForm" :model="queryParams" :inline="true">
        <el-form-item label="文章名称" prop="articleName">
          <el-input
            v-model="queryParams.articleName"
            clearable
            @keyup.enter="getDataList"
          ></el-input>
        </el-form-item>

        <el-form-item label="期刊" label-width="95" prop="publication">
          <el-input
            v-model="queryParams.publication"
            clearable
            @keyup.enter="getDataList"
          ></el-input>
        </el-form-item>

        <el-form-item label="类型ID" label-width="95" prop="typeId">
          <el-input
            v-model="queryParams.typeId"
            clearable
            @keyup.enter="getDataList"
          ></el-input>
        </el-form-item>

        <el-form-item label="DOI" prop="doi">
          <el-input
            v-model="queryParams.doi"
            clearable
            @keyup.enter="getDataList"
          ></el-input>
        </el-form-item>

        <el-form-item label="创建人" prop="createEmail">
          <el-input
            v-model="queryParams.createEmail"
            clearable
            @keyup.enter="getDataList"
          ></el-input>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-select
            v-model="queryParams.status"
            clearable
            style="width: 200px"
          >
            <el-option value="enable">启用</el-option>
            <el-option value="disable">禁用</el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="已删除" prop="deleted">
          <el-select
            v-model="queryParams.deleted"
            clearable
            style="width: 200px"
          >
            <el-option :value="1" label="是">是</el-option>
            <el-option :value="0" label="否">否</el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="创建日期" label-width="95">
          <el-date-picker
            v-model="dateRange"
            clearable
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="getDataList"
            >搜索
          </el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button icon="Plus" type="primary" @click="handleAdd"
            >新增
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            icon="Download"
            type="warning"
            style="float: right"
            @click="handleExport"
            >导出
          </el-button>
        </el-col>
      </el-row>

      <el-table
        v-loading="loading"
        :data="tableData"
        :tooltip-options="{
          enterable: true,
          popperOptions: { strategy: 'fixed' },
        }"
        style="width: 100%; margin-bottom: 20px"
        :header-cell-style="{
          backgroundColor: '#f2f2f2',
          color: '#333333',
          fontWeight: 700,
        }"
        :default-sort="defaultSort"
        border
        @sort-change="handleSortChange"
      >
        <el-table-column
          show-overflow-tooltip
          prop="id"
          label="记录编号"
          width="210"
        />

        <el-table-column
          show-overflow-tooltip
          prop="articleName"
          label="文章名称"
          min-width="140"
        />
        <el-table-column
          show-overflow-tooltip
          prop="publication"
          sortable
          label="期刊"
          min-width="160"
        />
        <el-table-column prop="type" label="类型" width="95" sortable />

        <el-table-column
          prop="typeID"
          label="类型ID"
          show-overflow-tooltip
          width="115"
        >
          <template #default="scope">
            <a
              class="text-primary"
              href="javascript:void(0)"
              @click="showDetail(scope.row)"
            >
              {{ scope.row.typeId }}
            </a>
          </template>
        </el-table-column>

        <el-table-column
          prop="doi"
          label="DOI"
          sortable
          show-overflow-tooltip
          min-width="140"
        >
          <template #default="scope">
            <a
              target="_blank"
              :href="`https://doi.org/${scope.row.doi}`"
              class="text-primary cursor-pointer"
              >{{ scope.row.doi }}</a
            >
          </template>
        </el-table-column>

        <el-table-column
          prop="pmid"
          label="PMID"
          width="100"
          sortable
          show-overflow-tooltip
        >
          <template #default="scope">
            <a
              target="_blank"
              :href="`https://pubmed.ncbi.nlm.nih.gov/${scope.row.pmid}/`"
              class="text-primary cursor-pointer"
            >
              {{ scope.row.pmid }}</a
            >
          </template>
        </el-table-column>

        <el-table-column
          show-overflow-tooltip
          prop="reference"
          label="参考文献"
          min-width="120"
        />

        <el-table-column
          prop="createEmail"
          label="创建人"
          width="200"
          show-overflow-tooltip
        />

        <el-table-column
          show-overflow-tooltip
          prop="sort"
          label="排序"
          width="80"
          sortable
        />

        <el-table-column label="状态" align="center" prop="status" width="90">
          <template #default="scope">
            <el-tag
              :disable-transitions="true"
              :type="scope.row.status === 'disable' ? 'danger' : 'primary'"
              >{{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          prop="createDate"
          label="创建日期"
          width="160"
          sortable
        >
          <template #default="scope">
            {{ parseTime(scope.row.createDate) }}
          </template>
        </el-table-column>

        <el-table-column prop="deleted" label="已删除" width="100">
          <template #default="scope">
            <el-tag
              :disable-transitions="true"
              :type="scope.row.deleted ? 'danger' : 'primary'"
              >{{ scope.row.deleted ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="90" fixed="right">
          <template #default="scope">
            <div v-if="!scope.row.deleted">
              <el-tooltip content="编辑">
                <svg-icon
                  icon-class="edits"
                  class-name="meta-svg"
                  @click="handleEdit(scope.row)"
                ></svg-icon>
              </el-tooltip>
              <el-tooltip content="删除">
                <svg-icon
                  icon-class="delete"
                  class-name="meta-svg"
                  @click="handleDelete(scope.row.id)"
                ></svg-icon>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        class="mb-1"
        @pagination="getDataList"
      />
    </div>

    <!-- 添加修改public -->
    <el-dialog
      v-model="displayDialog"
      :title="addType ? '新增发表' : '编辑发表'"
      class="edit-publish"
      width="680"
      append-to-body
    >
      <el-form
        ref="publishEditRef"
        :model="form"
        :rules="rules"
        :inline="true"
        label-width="120px"
      >
        <el-form-item label="文章名称" prop="articleName">
          <el-input v-model="form.articleName" />
        </el-form-item>

        <el-form-item label="期刊" prop="publication">
          <el-input v-model="form.publication" />
        </el-form-item>

        <el-form-item label="类型ID" prop="typeId" required>
          <el-input v-model="form.typeId" />
        </el-form-item>

        <el-form-item label="DOI" prop="doi">
          <el-input v-model="form.doi" style="width: 470px" />
          <el-tooltip content="点击自动填充。">
            <img
              src="@/assets/images/plosp.png"
              alt=""
              style="margin-left: 2px; width: 20px; cursor: pointer"
              @click="getFromPlosp(form.doi)"
            />
          </el-tooltip>
        </el-form-item>

        <el-form-item label="PMID" prop="pmid">
          <el-input v-model="form.pmid" />
        </el-form-item>

        <el-form-item label="参考文献" prop="reference">
          <el-input
            v-model="form.reference"
            type="textarea"
            rows="3"
            style="width: 500px"
          />
        </el-form-item>

        <el-form-item label="排序" prop="sort" required>
          <el-input v-model="form.sort" type="number" min="1" max="99999" />
        </el-form-item>

        <el-form-item label="状态" prop="status" required>
          <el-radio-group v-model="form.status" class="ml-4">
            <el-radio label="enable">启用</el-radio>
            <el-radio label="disable">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="text-center">
          <el-button type="primary" @click="editPublish">确认</el-button>
          <el-button @click="displayDialog = false">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      v-model="previewDialog"
      title="预览"
      class="preview-dialog radius-14"
    >
      <div class="d-flex preview">
        <div class="w-100">
          <span class="title">期刊</span>
          <span class="content" v-text="plospInfo.publication"></span>
        </div>
        <div class="w-100">
          <span class="title">DOI</span>
          <span class="content" v-text="plospInfo.doi"></span>
        </div>
        <div class="w-100">
          <span class="title">PMID</span>
          <span class="content" v-text="plospInfo.pmid"></span>
        </div>
        <div class="w-100">
          <span class="title">标题</span>
          <span class="content" v-text="plospInfo.articleName"></span>
        </div>
        <div class="w-100">
          <span class="title">参考文献</span>
          <span class="content" v-text="plospInfo.reference"></span>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <div class="text-align-center">
            <el-button
              type="primary"
              class="btn-primary btn btn-s btn-shadow"
              round
              @click="fillIn"
              >填入</el-button
            >
            <el-button
              round
              class="btn-primary btn btn-round"
              @click="previewDialog = false"
              >取消</el-button
            >
          </div>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { getCurrentInstance, onMounted, reactive, ref, toRefs } from 'vue';
  import {
    addPublish,
    deletePublish,
    getPubInfoFromPlosp,
    listPublish,
    updatePublish,
  } from '@/api/publish';
  import { createAccessToken } from '@/api/login';
  import { isStrBlank } from '@/utils';

  const { proxy } = getCurrentInstance();

  /** 响应式数据 */
  const data = reactive({
    addType: true,
    displayDialog: false,
    tableData: [],
    total: 0,
    queryParams: {
      name: '',
      pageNum: 1,
      pageSize: 20,
      createEmail: '',
      orderByColumn: 'sort',
      isAsc: 'descending',
    },
    form: {
      status: 'enable',
      sort: 1,
    },
    rules: {
      articleName: [
        {
          required: true,
          message: '请输入标题',
          trigger: 'blur',
        },
      ],
      publication: [
        {
          required: true,
          message: '请输入期刊',
          trigger: 'blur',
        },
      ],
      typeId: [
        {
          required: true,
          message: '请输入类型ID',
          trigger: 'blur',
        },
      ],
      doi: [
        {
          required: true,
          message: '请输入DOI',
          trigger: 'blur',
        },
        {
          pattern: /^10\.\d{4,9}\/[-._;()\/:A-Z0-9]+$/i,
          message: 'DOI格式不正确',
          trigger: 'blur',
        },
      ],
      pmid: [
        {
          pattern: /\b\d{8,9}\b/,
          message: 'PMID格式不正确',
          trigger: 'blur',
        },
      ],
    },
    dateRange: [],
    loading: true,
    defaultSort: { prop: 'createDate', order: 'descending' },
  });

  /** 解构 */
  const {
    tableData,
    total,
    queryParams,
    dateRange,
    loading,
    form,
    defaultSort,
    addType,
    rules,
    displayDialog,
  } = toRefs(data);

  onMounted(() => {
    getDataList();
  });

  /** 查询列表数据*/
  function getDataList() {
    loading.value = true;
    listPublish(proxy.addDateRange(queryParams.value, dateRange.value))
      .then(response => {
        // 将结果赋值给tableData
        tableData.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  /** 排序触发事件 */
  function handleSortChange(column) {
    if (column.order) {
      queryParams.value.orderByColumn = column.prop;
      queryParams.value.isAsc = column.order;
      getDataList();
    }
  }

  function handleExport() {
    if (tableData.value.length === 0) {
      proxy.$modal.msgError('没有数据可导出');
      return;
    }
    proxy.download(
      '/system/publish/export',
      {},
      `publish_${new Date().getTime()}.xlsx`,
    );
  }

  /** 提交 */
  function editPublish() {
    proxy.$refs['publishEditRef'].validate(valid => {
      if (valid) {
        if (form.value.id !== undefined) {
          updatePublish(form.value).then(() => {
            proxy.$modal.msgSuccess('修改成功');
            displayDialog.value = false;
            getDataList();
          });
        } else {
          addPublish(form.value).then(() => {
            proxy.$modal.msgSuccess('添加成功');
            displayDialog.value = false;
            getDataList();
          });
        }
      }
    });
  }

  /** 删除按钮操作 */
  function handleDelete(id) {
    proxy.$modal
      .confirm('确定要删除发表ID为"' + id + '"的数据项吗？')
      .then(function () {
        return deletePublish(id);
      })
      .then(() => {
        getDataList();
        proxy.$modal.msgSuccess('删除成功');
      })
      .catch(() => {});
  }

  function handleAdd() {
    form.value = {
      status: 'enable',
      priority: '1',
    };
    addType.value = true;
    displayDialog.value = true;
    proxy.$refs['publishEditRef'].resetFields();
  }

  function handleEdit(row) {
    addType.value = false;
    form.value = JSON.parse(JSON.stringify(row));
    displayDialog.value = true;
    proxy.$refs['publishEditRef'].resetFields();
  }

  function resetQuery() {
    proxy.resetForm('searchForm');
    dateRange.value = [];
    getDataList();
  }

  function showDetail(row) {
    // 预先生成access_token
    createAccessToken({ memberId: row.creator }).then(response => {
      const token = response.data;
      let href = `${import.meta.env.VITE_APP_WEB_URL}/${row.type}/detail/${
        row.typeId
      }?access-token=${token}`;
      // 打开一个新页面
      window.open(href);
    });
  }

  const getStatusText = status => {
    const statusMap = {
      enable: '启用',
      disable: '禁用',
    };
    return statusMap[status] || status;
  };

  let previewDialog = ref(false);

  let plospInfo = ref({
    id: undefined, // ID
    publication: undefined, // Journal
    doi: undefined,
    pmid: undefined,
    articleName: undefined, // Title
    reference: undefined,
  });

  function getFromPlosp(doi) {
    if (isStrBlank(doi) || !/10\.\d{4,}\/\S+/.test(doi)) {
      proxy.$modal.msgError('请输入正确的DOI！');
      return;
    }

    // 从PLOSP获取数据
    proxy.$modal.loading('加载中...');
    getPubInfoFromPlosp(doi)
      .then(response => {
        if (!response.data) {
          proxy.$modal.alertError('未找到发表信息！');
          return;
        }
        plospInfo.value = response.data;
        previewDialog.value = true;
      })
      .catch(e => {
        proxy.$modal.alertError(e);
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  function fillIn() {
    proxy.$modal.msgSuccess('发表信息填入成功！');

    form.value.publication = plospInfo.value.publication;
    form.value.pmid = plospInfo.value.pmid;
    form.value.doi = plospInfo.value.doi;
    form.value.articleName = plospInfo.value.articleName;
    form.value.reference = plospInfo.value.reference;

    previewDialog.value = false;
  }
</script>

<style lang="scss" scoped>
  .el-dialog {
    .el-input,
    .el-select {
      width: 500px;
    }
  }

  .meta-svg {
    width: 20px;
    height: 20px;
    margin-right: 0.5rem;
    cursor: pointer;
  }
</style>
<style lang="scss">
  .el-popper.is-dark {
    max-width: 350px !important;
  }
</style>
