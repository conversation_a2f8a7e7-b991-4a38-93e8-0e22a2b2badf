<template>
  <div class="app-container">
    <div class="card list">
      <el-form ref="formRef" :model="queryParams" :inline="true">
        <el-form-item label="下载时间">
          <el-date-picker
            v-model="dateRange"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 220px"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="用户" prop="userEmail">
          <el-input
            v-model="queryParams.userEmail"
            placeholder="请输入用户邮箱"
            clearable
            style="width: 220px"
            @keyup.enter="getDataList"
          />
        </el-form-item>
        <el-form-item label="IP地址" prop="ip">
          <el-input
            v-model="queryParams.ip"
            placeholder="请输入IP地址"
            clearable
            style="width: 220px"
            @keyup.enter="getDataList"
          />
        </el-form-item>
        <el-form-item label="下载类型" prop="downloadType">
          <el-input
            v-model="queryParams.downloadType"
            placeholder="请输入下载类型"
            clearable
            style="width: 220px"
            @keyup.enter="getDataList"
          />
        </el-form-item>
        <el-form-item label="类型ID" prop="typeNo">
          <el-input
            v-model="queryParams.typeNo"
            placeholder="请输入类型ID"
            clearable
            style="width: 220px"
            @keyup.enter="getDataList"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="getDataList"
            >搜索
          </el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%; margin-bottom: 20px"
        :header-cell-style="{
          backgroundColor: '#f2f2f2',
          color: '#333333',
          fontWeight: 700,
        }"
        border
        :default-sort="defaultSort"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="createTime" label="下载时间">
          <template #default="scope"
            >{{ parseTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="userEmail" label="用户" />
        <el-table-column prop="ip" label="IP地址" />
        <el-table-column prop="downloadType" label="下载类型" />
        <el-table-column prop="country" label="国家" />
        <el-table-column prop="type" label="类型" />
        <el-table-column prop="typeNo" label="类型ID" />
      </el-table>

      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :layout="'total, sizes, prev, next'"
        :total="total"
        class="mb-1"
        @pagination="getDataList"
      />
    </div>
  </div>
</template>

<script setup>
  import { getCurrentInstance, onMounted, reactive, toRefs } from 'vue';
  import { parseTime } from '@/utils/ruoyi';
  import { listDownloadLog } from '@/api/log/downloadLog';

  const { proxy } = getCurrentInstance();

  const data = reactive({
    tableData: [],
    total: 0,
    queryParams: {
      userEmail: '',
      ip: '',
      typeNo: '',
      downloadType: '',
      pageNum: 1,
      pageSize: 20,
      orderByColumn: 'createTime',
      isAsc: 'descending',
    },
    dateRange: [],
    loading: true,
    defaultSort: { prop: 'createDate', order: 'descending' },
  });

  /** 解构 */
  const { tableData, total, queryParams, dateRange, loading, defaultSort } =
    toRefs(data);

  onMounted(() => {
    getDataList();
  });

  /** 查询列表数据*/
  function getDataList() {
    loading.value = true;
    listDownloadLog(proxy.addDateRange(queryParams.value, dateRange.value))
      .then(response => {
        // 将结果赋值给tableData
        tableData.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  /** 排序触发事件 */
  function handleSortChange(column) {
    if (column.order) {
      queryParams.value.orderByColumn = column.prop;
      queryParams.value.isAsc = column.order;
      getDataList();
    }
  }

  function resetQuery() {
    dateRange.value = [];
    proxy.$refs['formRef'].resetFields();
    getDataList();
  }
</script>

<style lang="scss" scoped></style>
<style lang="scss">
  .el-popper {
    max-width: 350px !important;
  }
</style>
