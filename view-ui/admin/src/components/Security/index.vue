<template>
  <div>
    <el-dialog
      v-model="securityDialog"
      title="安全状态更改确认"
      width="1200"
      class="radius-14 security"
    >
      <div class="text-main-color font-600">1.筛选您要修改安全状态的数据：</div>
      <div class="bg-gray mt-05 form">
        <div class="d-flex align-items-center">
          <div class="font-16 label mr-2">安全级别</div>
          <el-radio-group v-model="fromSecurity" @change="selectSecurity">
            <el-radio label="Private">私有（Private）</el-radio>
            <el-radio label="Restricted">受限（Restricted）</el-radio>
            <el-radio label="Public">公开（Public）</el-radio>
          </el-radio-group>
        </div>
        <div class="d-flex align-items-center mt-05">
          <div v-if="showExp" class="font-16 label mr-2">实验ID</div>
          <el-select
            v-if="showExp"
            v-model="experimentNo"
            filterable
            clearable
            :teleported="false"
            remote
            reserve-keyword
            placeholder="请输入关键词"
            :remote-method="
              (queryString, cb) => {
                remoteSelect(queryString, 'expNo');
              }
            "
            :loading="selectLoading"
            style="width: 200px"
          >
            <el-option
              v-for="(value, key) in selectOption['expNo']"
              :key="'exp-option-' + key"
              :label="value"
              :value="value"
            ></el-option>
          </el-select>

          <div v-if="showSap" class="font-16 label mr-2">样本</div>
          <el-select
            v-if="showSap"
            v-model="sampleSearchType"
            :teleported="false"
            clearable
            placeholder="选择"
            style="width: 170px"
            @change="changeSample"
          >
            <el-option label="样本ID" value="sapNo"></el-option>
            <el-option label="样本名称" value="sapName"></el-option>
            <el-option label="样本类型" value="sapType"></el-option>
            <el-option label="样本物种" value="organism"></el-option>
          </el-select>

          <el-select
            v-if="showSap"
            v-model="sampleSearchText"
            filterable
            class="ml-1"
            clearable
            remote
            reserve-keyword
            placeholder="请输入关键词"
            :remote-method="
              (queryString, cb) => {
                remoteSelect(queryString, sampleSearchType);
              }
            "
            :loading="selectLoading"
            style="width: 200px"
          >
            <el-option
              v-for="(value, key) in selectOption[sampleSearchType]"
              :key="'sap-option-' + key"
              :label="value"
              :value="value"
            ></el-option>
          </el-select>

          <div v-if="showSap || showExp" class="text-center">
            <el-button
              round
              :icon="Search"
              type="primary"
              class="radius-8 ml-2"
              @click="getDataList(false)"
              >筛选
            </el-button>
          </div>
        </div>
      </div>
      <el-tabs v-model="activeTabName" class="mt-05">
        <el-tab-pane
          :label="type === 'analysis' ? '分析数据' : '数据'"
          name="Data"
        >
          <el-table
            ref="rawTable"
            v-loading="loading"
            :data="rawData"
            style="width: 100%; margin-bottom: 20px"
            :header-cell-style="{
              backgroundColor: '#f2f2f2',
              color: '#333333',
              fontWeight: 700,
            }"
            max-height="300"
            border
            tooltip-effect="dark"
            :row-key="row => row.datNo"
            :sort-orders="['ascending', 'descending']"
            @sort-change="relTableSortChange"
            @selection-change="selectionDataChange"
          >
            <el-table-column
              type="selection"
              :reserve-selection="true"
              width="45"
            />
            <el-table-column prop="datNo" label="数据ID" width="120" sortable>
            </el-table-column>
            <el-table-column
              prop="name"
              label="数据名称"
              sortable
              show-overflow-tooltip
            />
            <el-table-column
              v-if="type !== 'analysis'"
              prop="expName"
              label="实验"
              sort-by="expNo"
              sortable
              show-overflow-tooltip
            >
              <template #default="scope">
                <router-link
                  :to="`/experiment/detail/${scope.row.expNo}`"
                  class="text-primary"
                  target="_blank"
                >
                  {{ scope.row.expNo }} ({{ scope.row.expName }})
                </router-link>
              </template>
            </el-table-column>
            <el-table-column
              v-if="type !== 'analysis'"
              prop="sapName"
              label="样本"
              sort-by="sapNo"
              sortable
              show-overflow-tooltip
            >
              <template #default="scope">
                <router-link
                  :to="`/sample/detail/${scope.row.sapNo}`"
                  class="text-primary"
                  target="_blank"
                >
                  {{ scope.row.sapNo }} ({{ scope.row.sapName }})
                </router-link>
              </template>
            </el-table-column>
            <el-table-column
              v-if="type !== 'analysis'"
              prop="runName"
              label="批次"
              sort-by="runNo"
              sortable
              show-overflow-tooltip
            >
              <template #default="scope">
                <router-link
                  :to="`/run/detail/${scope.row.runNo}`"
                  class="text-primary"
                  target="_blank"
                >
                  {{ scope.row.runNo }} ({{ scope.row.runName }})
                </router-link>
              </template>
            </el-table-column>

            <el-table-column
              v-if="type === 'analysis'"
              label="分析"
              sortable
              sort-by="analNo"
              show-overflow-tooltip
            >
              <template #default="scope">
                <router-link
                  :to="`/analysis/detail/${scope.row.analNo}`"
                  class="text-primary"
                  target="_blank"
                >
                  {{ scope.row.analNo }} ({{ scope.row.analName }})
                </router-link>
              </template>
            </el-table-column>
            <el-table-column
              prop="dataType"
              label="数据类型"
              width="115"
              sortable
            />

            <el-table-column
              width="160"
              prop="uploadTime"
              label="上传时间"
              sortable
            />
          </el-table>

          <pagination
            v-show="queryPageAndSort.totalCount > 0"
            v-model:page="queryPageAndSort.pageNum"
            v-model:limit="queryPageAndSort.pageSize"
            :page-sizes="[5, 10, 100, 500, 1000]"
            class="mb-1 mt-2 justify-center"
            :total="queryPageAndSort.totalCount"
            @pagination="pageDataList"
          />
        </el-tab-pane>

        <!--Related Analysis Data-->
        <el-tab-pane label="相关分析数据" name="Related Analysis Data">
          <el-table
            ref="analysisTable"
            v-loading="loading"
            :data="relatedAnalysisData"
            style="width: 100%; margin-bottom: 20px"
            :header-cell-style="{
              backgroundColor: '#f2f2f2',
              color: '#333333',
              fontWeight: 700,
            }"
            max-height="300"
            border
            tooltip-effect="light"
            :row-key="row => row.datNo"
            @selection-change="selectionRelatedAnalysisDataChange"
          >
            <el-table-column
              type="selection"
              :reserve-selection="true"
              width="43"
            />
            <el-table-column
              prop="datNo"
              label="数据ID"
              min-width="50"
              sortable
            />
            <el-table-column prop="name" label="名称/安全级别" sortable>
            </el-table-column>
            <el-table-column prop="analNo" label="分析ID" width="120" sortable>
              <template #default="scope">
                <router-link
                  :to="`/analysis/detail/${scope.row.analNo}`"
                  class="text-primary"
                  target="_blank"
                >
                  {{ scope.row.analNo }}
                </router-link>
              </template>
            </el-table-column>
            <el-table-column prop="analName" label="分析名称" sortable />
            <el-table-column prop="dataType" label="数据类型" sortable />
            <el-table-column prop="uploadTime" label="上传时间" sortable />
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <!--      <div class="mb-05">
              Get all data list, please export data links
              <el-button class="ml-05 radius-8" size="small" type="primary"
                >Export Data Links
              </el-button>
            </div>-->
      <div>
        <div class="text-main-color font-600 d-flex align-items-center">
          2.将上述查询的数据和分析数据从 &nbsp;
          <el-button class="radius-8" size="small" :type="securityColor">
            {{ fromSecurity }}
          </el-button>
          &nbsp;更改为:&nbsp;
          <el-radio-group v-model="toSecurity" size="small">
            <el-radio-button value="Private" label="私有（Private）" />
            <el-radio-button value="Restricted" label="受限（Restricted）" />
            <el-radio-button value="Public" label="公开（Public）" />
          </el-radio-group>
        </div>
        <div v-if="toSecurity === 'Restricted'" class="bg-gray p-10-15 mt-05">
          <el-radio-group v-model="restrictedSelect">
            <el-radio label="untilOne"
              >直到
              <el-input
                v-model="untilOne"
                class="ml-1"
                disabled
                style="width: 150px"
              />
            </el-radio>
            <el-radio label="untilTwo"
              >直到
              <el-input
                v-model="untilTwo"
                class="ml-1"
                disabled
                style="width: 150px"
              />
            </el-radio>
            <el-radio label="requireRequest">需要申请</el-radio>
          </el-radio-group>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer text-center">
          <el-button class="radius-8" type="primary" @click="updateSecurity">
            确认
          </el-button>
          <el-button
            plain
            class="radius-8"
            type="primary"
            @click="securityDialog = false"
            >取消
          </el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="recordDialog" width="1000" class="radius-14 security">
      <div class="text-main-color font-600 mb-1">
        根据《中华人民共和国人类遗传资源管理条例》和《人类遗传资源管理条例实施细则》，
        人类遗传资源数据的公开需要通过人类遗传资源审查进行审查和备案。
        请如实填写相应的备案号。如有疑问，请联系
        <a class="text-primary" href="mailto:<EMAIL>"
          ><EMAIL></a
        >
        or 010-88225151
      </div>
      <el-radio-group
        v-if="params.recordSampleType === 'Human'"
        v-model="params.recordRadio"
      >
        <el-radio
          label="可能含有极少量脱落、残留或游离细胞或基因的尿液、粪便、血清、血浆等生物样本"
          size="large"
        ></el-radio>
        <el-radio label="其他样本" size="large"></el-radio>

        <el-input
          v-show="params.recordRadio === '其他样本'"
          v-model="params.recordValue"
          placeholder="请输入备案号"
        />
      </el-radio-group>

      <el-radio-group
        v-else-if="params.recordSampleType === 'Environment host'"
        v-model="params.recordRadio"
        class="w-100"
      >
        <div class="d-flex align-items-center w-100 mb-1">
          <el-radio label="非人类" size="large"></el-radio>
          <el-radio label="人类" size="large"></el-radio>
          <el-input
            v-show="params.recordRadio === '人类'"
            v-model="params.recordValue"
            placeholder="请输入备案号"
          />
        </div>
      </el-radio-group>

      <el-radio-group
        v-else-if="params.recordSampleType === 'Cell line'"
        v-model="params.recordRadio"
      >
        <el-radio label="非人类" size="large"></el-radio>
        <el-radio label="人类商业细胞系" size="large"></el-radio>

        <el-radio label="人类非商业细胞系" size="large"></el-radio>
        <el-input
          v-show="params.recordRadio === '人类非商业细胞系'"
          v-model="params.recordValue"
          placeholder="请输入备案号"
        />
      </el-radio-group>

      <template #footer>
        <div class="dialog-footer text-center">
          <el-button
            class="radius-8"
            type="primary"
            @click="submitUpdateDataSecurity"
          >
            确认
          </el-button>
          <el-button
            plain
            class="radius-8"
            type="primary"
            @click="recordDialog = false"
            >取消
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { getCurrentInstance, ref } from 'vue';
  import { Search } from '@element-plus/icons-vue';
  import {
    getSecurityDataList,
    searchSelectWord,
    updateDataSecurity,
    verifyDataHumanType,
  } from '@/api/metadata/security';
  import { parseTime } from '@/utils/ruoyi';
  import { trimStr } from '@/utils';
  import { ElMessageBox } from 'element-plus';

  const { proxy } = getCurrentInstance();
  const securityDialog = ref(false);

  const type = ref('');
  const typeNo = ref('');

  const queryPageAndSort = ref({
    sortKey: '',
    sortType: '',
    pageNum: 1,
    pageSize: 10,
    totalCount: 0,
  });

  // 最上方的security筛选类型
  const fromSecurity = ref('Private');

  const showExp = ref(true);
  const experimentNo = ref('');

  const showSap = ref(true);

  // sample的检索类型
  const sampleSearchType = ref('');

  // sample的检索值
  const sampleSearchText = ref('');

  // sample的下拉框
  const selectOption = ref({});
  const samples = ref({});

  const activeTabName = ref('Data');

  const loading = ref(false);

  function init(optionType, optionNo) {
    type.value = optionType;
    typeNo.value = optionNo;

    if (optionType === 'experiment' || optionType === 'sample') {
      showExp.value = false;
    }

    if (optionType === 'run' || optionType === 'analysis') {
      showExp.value = false;
      showSap.value = false;
    }

    fromSecurity.value = 'Private';

    resetSearchForm();

    getDataList();

    initUntilDate();
    securityDialog.value = true;
  }

  const rawData = ref([]);
  const relatedAnalysisData = ref([]);

  /** 数据分页 */
  function pageDataList(pageData) {
    queryPageAndSort.value.pageSize = pageData.limit;
    queryPageAndSort.value.pageNum = pageData.page;
    getDataList();
  }

  function relTableSortChange(column) {
    let { prop, order } = column;
    if (order) {
      queryPageAndSort.value.sortKey = prop;
      queryPageAndSort.value.sortType = order === 'ascending' ? 'asc' : 'desc';
      getDataList();
    }
  }

  function getDataList() {
    let params = {
      type: type.value,
      typeNo: typeNo.value,
      security: fromSecurity.value,
      expNo: experimentNo.value,
    };

    if (sampleSearchType.value && sampleSearchText.value) {
      params[sampleSearchType.value] = sampleSearchText.value;
    }

    let pagePram = queryPageAndSort.value;

    params = { ...params, ...pagePram };

    loading.value = true;
    getSecurityDataList(params)
      .then(response => {
        rawData.value = response.data.dataVos || [];

        queryPageAndSort.value.totalCount = response.data.total;

        relatedAnalysisData.value = response.data.relatedAnalysisDataVos || [];

        // 如果RawData没有数据,而relatedAnalysisData有数据
        if (
          rawData.value?.length === 0 &&
          relatedAnalysisData.value?.length !== 0
        ) {
          activeTabName.value = 'Related Analysis Data';
        }
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const selectLoading = ref(false);

  function remoteSelect(queryStr, field) {
    queryStr = trimStr(queryStr);

    let params = {
      type: type.value,
      typeNo: typeNo.value,
      security: fromSecurity.value,
      field: field,
      keyword: queryStr,
    };

    selectLoading.value = true;
    searchSelectWord(params)
      .then(response => {
        selectOption.value[field] = response.data || [];
      })
      .finally(() => {
        selectLoading.value = false;
      });
  }

  const recordDialog = ref(false);

  const params = ref({
    securityType: undefined,
    pubDate: undefined,
    dataNos: undefined,
    recordSampleType: undefined,
    recordRadio: undefined,
    recordValue: undefined,
  });

  function updateSecurity() {
    if (!toSecurity.value) {
      proxy.$modal.alertWarning('请选择要修改的安全级别！');
      return;
    }

    if (
      selectedDataRows.value.length === 0 &&
      selectedRelatedAnalysisRows.value.length === 0
    ) {
      proxy.$modal.alertWarning('请选择要更新安全级别的数据');
      return;
    }

    // 合并选中的data、analysis data、relatedAnalysis
    let rawDataIds = selectedDataRows.value.map(it => it.datNo);
    const relatedAnalysisDataIds = selectedRelatedAnalysisRows.value.map(
      it => it.datNo,
    );

    if (!rawDataIds) {
      rawDataIds = [];
    }

    if (relatedAnalysisDataIds && relatedAnalysisDataIds.length !== 0) {
      rawDataIds = rawDataIds.concat(relatedAnalysisDataIds);
    }

    let pubDate = undefined;
    if (toSecurity.value === 'Restricted') {
      if (restrictedSelect.value === 'untilOne') {
        pubDate = untilOne.value;
      }
      if (restrictedSelect.value === 'untilTwo') {
        pubDate = untilTwo.value;
      }
    }

    params.value.securityType = toSecurity.value;
    params.value.pubDate = pubDate;
    params.value.dataNos = rawDataIds;
    params.value.recordSampleType = undefined;
    params.value.recordRadio = undefined;
    params.value.recordValue = undefined;

    if (toSecurity.value === 'Public') {
      verifyDataHumanType(params.value).then(response => {
        const hasRecordType = response.data;

        if (hasRecordType && hasRecordType.length !== 0) {
          if (hasRecordType.length > 1) {
            proxy.$modal.alertWarning(
              `样本类型中包含 ${hasRecordType.join(', ')}。请逐一选择样本类型来更改数据的安全级别。`,
            );
            return;
          }
          // 弹框要求用户填写备案号
          params.value.recordSampleType = hasRecordType[0];
          if (params.value.recordSampleType === 'Human') {
            params.value.recordRadio = 'Other samples';
          } else if (params.value.recordSampleType === 'Environment host') {
            params.value.recordRadio = 'human';
          } else if (params.value.recordSampleType === 'Cell line') {
            params.value.recordRadio = 'human non-commercial cell lines';
          }
          recordDialog.value = true;
        } else {
          // 直接提交
          submitUpdateDataSecurity();
        }
      });
    } else {
      // 直接提交
      submitUpdateDataSecurity();
    }
  }

  function submitUpdateDataSecurity() {
    if (
      params.value.recordRadio === 'Other samples' ||
      params.value.recordRadio === 'human' ||
      params.value.recordRadio === 'human non-commercial cell lines'
    ) {
      if (!params.value.recordValue) {
        proxy.$modal.alertError('请填写备案号');
        return;
      } else {
        const regex = /^[a-zA-Z0-9-]+$/;
        if (!regex.test(params.value.recordValue)) {
          proxy.$modal.alertError('格式无效，仅支持数字、字母、-');
          return;
        }
      }
    } else {
      params.value.recordValue = undefined;
    }

    let dataSize = params.value.dataNos.length;
    if (dataSize > 1000) {
      proxy.$modal.alertError('选择的数据数量超过1000的限制');
      return;
    }
    proxy.$modal.loading('正在提交');

    updateDataSecurity(params.value)
      .then(response => {
        if (response.data) {
          proxy.$modal.alertError(response.data.join('</br>'), true);
          proxy.$modal.closeLoading();
        } else {
          let timeout = Math.floor(dataSize / 10) + 6;
          // 最多120s
          if (timeout > 120) {
            timeout = 120;
          }
          // console.log('timeout:', timeout);
          // 延迟刷新，等待ES索引同步完成
          setTimeout(() => {
            proxy.$modal.closeLoading();
            securityDialog.value = false;
            if (timeout < 120) {
              proxy.$modal.msgSuccess('修改成功');
              window.location.reload();
            } else {
              ElMessageBox.alert(
                '修改成功！如果您发现数据的安全级别没有改变，请耐心等待几分钟后再次刷新浏览器',
                '提示',
                {
                  confirmButtonText: '确定',
                  callback: () => {
                    window.location.reload();
                  },
                },
              );
            }
          }, timeout * 1000);
        }
      })
      .catch(() => {
        proxy.$modal.closeLoading();
      });
  }

  function changeSample() {
    selectOption[sampleSearchType] = [];
    sampleSearchText.value = '';
  }

  /** 清空过滤条件 */
  function resetSearchForm() {
    experimentNo.value = '';

    sampleSearchType.value = 'sapType';
    sampleSearchText.value = '';

    activeTabName.value = 'Data';

    samples.value = [];

    proxy.$refs['rawTable']?.clearSelection();
    proxy.$refs['analysisTable']?.clearSelection();

    queryPageAndSort.value = {
      sortKey: '',
      sortType: '',
      pageNum: 1,
      pageSize: 100,
      totalCount: 0,
    };
  }

  const securityColor = ref('danger');
  const toSecurity = ref('Restricted');

  const untilOne = ref('');
  const untilTwo = ref('');
  const restrictedSelect = ref('untilOne');

  function initUntilDate() {
    let d1 = new Date();
    // 未来1年
    d1.setFullYear(d1.getFullYear() + 1);
    untilOne.value = parseTime(d1, '{yy}-{mm}-{dd}');

    // 未来2年
    d1.setFullYear(d1.getFullYear() + 1);
    untilTwo.value = parseTime(d1, '{yy}-{mm}-{dd}');
  }

  const selectedDataRows = ref([]);

  function selectionDataChange(selection) {
    selectedDataRows.value = selection;
  }

  const selectedRelatedAnalysisRows = ref([]);

  function selectionRelatedAnalysisDataChange(selection) {
    selectedRelatedAnalysisRows.value = selection;
  }

  const selectSecurity = () => {
    if (fromSecurity.value === 'Private') {
      securityColor.value = 'danger';
    } else if (fromSecurity.value === 'Restricted') {
      securityColor.value = 'warning';
      toSecurity.value = 'Public';
    } else {
      securityColor.value = 'success';
    }
    resetSearchForm();
    getDataList();
  };

  defineExpose({
    init,
  });
</script>

<style lang="scss" scoped>
  .form {
    padding: 6px 10px;
  }

  .svg-security {
    width: 35px;
    height: 35px;
    cursor: pointer;

    &:focus {
      outline: none;
    }
  }

  .selected-data {
    padding: 10px;
    font-size: 16px;
  }

  .label {
    font-weight: 600;
    width: 120px;
    text-align: right;
  }

  :deep(.el-dialog__body) {
    padding: 0 20px !important;
  }

  :deep(.el-select__wrapper) {
    border-radius: 12px;
  }

  .collapse {
    height: 46px;
    overflow: hidden;
  }

  .arrow {
    cursor: pointer;
    position: relative;
    top: 6px;
  }

  .security {
    :deep(.el-dialog__body) {
      padding-top: 0;
    }
  }

  :deep(.el-dialog) {
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-height: calc(100% - 30px);
    max-width: calc(100% - 30px);
  }

  :deep(.el-dialog .el-dialog__body) {
    flex: 1;
    overflow: auto;
  }

  .text-main-color {
    text-align: justify;
  }
</style>
