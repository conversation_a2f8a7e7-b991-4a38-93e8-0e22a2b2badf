{"name": "node-admin", "version": "3.6.3", "description": "Node管理后台系统", "author": "上海南方基因信息科技有限公司", "license": "MIT", "scripts": {"dev": "vite", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview"}, "repository": {"type": "git", "url": "http://dev.biosino.org/git/platform/multipleOmics/node.git"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@handsontable/vue3": "^14.3.0", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "10.9.0", "axios": "1.6.8", "echarts": "^5.5.0", "element-plus": "2.7.3", "file-saver": "2.0.5", "fuse.js": "7.0.0", "handsontable": "^14.3.0", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "json-editor-vue3": "^1.1.1", "nprogress": "0.2.0", "pinia": "2.1.7", "vue": "3.4.27", "vue-cropper": "1.0.3", "vue-router": "4.3.2"}, "devDependencies": {"@originjs/vite-plugin-commonjs": "^1.0.3", "@vitejs/plugin-vue": "4.6.2", "@vue/compiler-sfc": "3.4.27", "@vue/eslint-config-standard": "^8.0.1", "eslint": "8.57.0", "eslint-config-prettier": "^8.10.0", "eslint-plugin-html": "^8.1.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.26.0", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.14", "sass": "1.77.2", "unplugin-auto-import": "0.17.6", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "4.5.2", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1"}}